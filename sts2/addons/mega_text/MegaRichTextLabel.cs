using System;
using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Entities.Text;
using MegaCrit.Sts2.Core.RichTextTags;

namespace MegaCrit.Sts2.addons.mega_text;

[Tool]
public partial class MegaRichTextLabel : RichTextLabel
{
    private const float _sizeComparisonEpsilon = 0.01f;
    private const string _fontOverrideName = "normal_font";

    private bool _isAutoSizeEnabled = true;
    private int _minFontSize = 8;
    private int _maxFontSize = 100;
    private int _lastSetSize;
    private bool _isVerticallyBound = true;
    private bool _isHorizontallyBound;

    private Vector2 _oldSize;

    private static readonly string[] _richTextFontSizeNames =
    [
        "bold_font_size",
        "bold_italics_font_size",
        "italics_font_size",
        "mono_font_size"
    ];

    private static readonly AbstractMegaRichTextEffect[] _textEffects =
    [
        new RichTextBlue(),
        new RichTextFadeIn(),
        new RichTextFlyIn(),
        new RichTextGold(),
        new RichTextGreen(),
        new RichTextJitter(),
        new RichTextOrange(),
        new RichTextPurple(),
        new RichTextRed(),
        new RichTextSine(),
        new RichTextThinkyDots(),
    ];

    [Export]
    public bool AutoSizeEnabled
    {
        get => _isAutoSizeEnabled;
        set
        {
            if (value && FitContent)
            {
                GD.PushWarning("Auto Size is not compatible with Fit Content, disabling Auto Size...");
                _isAutoSizeEnabled = false;
                return;
            }

            if (AutoSizeEnabled == value) return;

            _isAutoSizeEnabled = value;
            AdjustFontSize();
        }
    }

    [Export]
    public int MinFontSize
    {
        get => _minFontSize;
        set
        {
            if (_minFontSize == value) return;

            _minFontSize = value;
            AdjustFontSize();
        }
    }

    [Export]
    public int MaxFontSize
    {
        get => _maxFontSize;
        set
        {
            if (_maxFontSize == value) return;

            _maxFontSize = value;
            AdjustFontSize();
        }
    }

    [Export]
    public bool IsVerticallyBound
    {
        get => _isVerticallyBound;
        set
        {
            _isVerticallyBound = value;
            AdjustFontSize();
        }
    }

    [Export]
    public bool IsHorizontallyBound
    {
        get => _isHorizontallyBound;
        set
        {
            _isHorizontallyBound = value;
            AdjustFontSize();
        }
    }

    // Since auto-sizing the label can change the size of the label, leading to a resize notification and another
    // auto-size call, this prevents infinite recursion
    private bool _isAutoSizing;

    public override void _Ready()
    {
        MegaLabelHelper.AssertThemeFontOverride(this, _fontOverrideName);
        _oldSize = Size;
        InstallEffectsIfNeeded();
        AdjustFontSize();

        // TODO: May not be needed after 4.5
        ParseBbcode(Text);
    }

    public override void _Notification(int what)
    {
        switch (what)
        {
            case (int)NotificationResized:
                // Sometimes, stuff gets resized a very small amount for reasons unknown. When this happens, it's not only
                // wasteful to auto-size, but also, auto-sizing re-parses BBCode, which resets timing for stuff like
                // RichTextFadeIn.
                // Not sure why the tiny amount of resizing is happening, it's most noticeable in event description text
                if (_oldSize.DistanceSquaredTo(Size) < _sizeComparisonEpsilon * _sizeComparisonEpsilon) return;
                _oldSize = Size;

                if (AutoSizeEnabled)
                {
                    AdjustFontSize();
                }

                break;

            case (int)NotificationEditorPreSave:
                // Before saving, clear the custom effects so we don't try to serialize them.
                CustomEffects.Clear();
                break;

            case (int)NotificationEditorPostSave:
                // After saving, restore the custom effects so they keep showing up in the editor.
                InstallEffectsIfNeeded();
                break;
        }
    }

    private void InstallEffectsIfNeeded()
    {
        if (!BbcodeEnabled) return;

        // See here for why we don't use InstallEffects: https://github.com/godotengine/godot/issues/103630
        Godot.Collections.Array array = new Godot.Collections.Array();

        foreach (AbstractMegaRichTextEffect effect in _textEffects)
        {
            // Note: This will install effects that might not currently be used by this label. This is necessary for new
            // bbcode tags to be immediately displayed in the editor.
            array.Add(effect);
        }

        CustomEffects = array;
    }

    private bool HasEffect(AbstractMegaRichTextEffect effect) => CustomEffects.Contains(effect);

    /// <summary>
    /// Unfortunately, there's no way to override the setting of text for a Label. So if you want the text size to
    /// automatically adjust after being updated during gameplay, you must use this method instead of setting the
    /// Text property directly.
    /// </summary>
    /// <param name="text"></param>
    public void SetTextAutoSize(string text)
    {
        base.Text = text;
        InstallEffectsIfNeeded();

        if (AutoSizeEnabled)
        {
            CallDeferred(nameof(AdjustFontSize));
        }
    }

    // Unfortunately, we can't override the Text property, so we have to use this method instead.
    public new string Text
    {
        get => base.Text;
        set => SetTextAutoSize(value);
    }

    private void AdjustFontSize()
    {
        if (!AutoSizeEnabled || _isAutoSizing) return;

        _isAutoSizing = true;

        Font font = GetThemeFont(_fontOverrideName);
        float lineSpacing = GetThemeConstant("line_spacing");
        float prevVisibleRatio = VisibleRatio;
        VisibleRatio = 1f;

        Vector2 rectSize = GetRect().Size;

        int curMinSize = MinFontSize;
        int curMaxSize = MaxFontSize;

        TextParagraph paragraph = new();
        List<BbcodeObject> objs = MegaLabelHelper.ParseBbcode(Text);

        while (curMaxSize >= curMinSize)
        {
            int currentSize = curMinSize + (curMaxSize - curMinSize) / 2;

            Vector2 textSize = MegaLabelHelper.EstimateTextSize(paragraph, objs, font, currentSize, rectSize.X, lineSpacing);
            bool exceedsHorizontal = textSize.X > rectSize.X && _isHorizontallyBound;
            bool exceedsVertical = textSize.Y > rectSize.Y && _isVerticallyBound;

            if (exceedsVertical || exceedsHorizontal)
            {
                curMaxSize = currentSize - 1;
            }
            else
            {
                curMinSize = currentSize + 1;
            }
        }

        SetFontSize(Math.Min(curMinSize, curMaxSize));
        VisibleRatio = prevVisibleRatio;
        _isAutoSizing = false;
    }

    private void SetFontSize(int size)
    {
        if (_lastSetSize == size) return;

        _lastSetSize = size;
        AddThemeFontSizeOverride("normal_font_size", size);

        if (BbcodeEnabled)
        {
            foreach (string name in _richTextFontSizeNames)
            {
                AddThemeFontSizeOverride(name, size);
            }

            ParseBbcode(Text);
        }
    }
    //
}
