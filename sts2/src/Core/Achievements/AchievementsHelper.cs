using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Platform;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Timeline;

namespace MegaCrit.Sts2.Core.Achievements;

public static class AchievementsHelper
{
    public static void AfterClimbEnded(ClimbState state, Player player, bool isVictory)
    {
        // Check for victory achievements
        if (isVictory)
        {
            CharacterModel character = player.Character;

            // In default tests, ignore these achievements
            if (character is not Deprived)
            {
                if (!AchievementsUtil.IsUnlocked(character.ClimbWonAchievement))
                {
                    Log.Info($"Unlocked run won achievement: {character.ClimbWonAchievement}");
                    AchievementsUtil.Unlock(character.ClimbWonAchievement, player);
                }

                if (state.AscensionLevel == AscensionManager.maxAscensionAllowed &&
                    !AchievementsUtil.IsUnlocked(character.Ascension10WonAchievement))
                {
                    Log.Info($"Unlocked max ascension won achievement: {character.Ascension10WonAchievement}");
                    AchievementsUtil.Unlock(character.Ascension10WonAchievement, player);
                }
            }

            if (!AchievementsUtil.IsUnlocked(Achievement.NoRelicWin) &&
                !player.Relics.Select(r => r.CanonicalInstance).Except(character.StartingRelics).Any())
            {
                Log.Info("Player only had starting relics in their run, unlocking achievement");
                AchievementsUtil.Unlock(Achievement.NoRelicWin, player);
            }

            if (!AchievementsUtil.IsUnlocked(Achievement.AllCardsUpgraded) &&
                player.Deck.Cards.Count > 0 &&
                player.Deck.Cards.All(c => c.IsUpgraded))
            {
                Log.Info("Player's deck was fully upgraded, unlocking achievement");
                AchievementsUtil.Unlock(Achievement.AllCardsUpgraded, player);
            }
        }

        // Check for completion achievements
        if (!AchievementsUtil.IsUnlocked(Achievement.DiscoverAllCards))
        {
            List<ModelId> unseenCards = ModelDb.Cards.Select(c => c.Id).Except(SaveManager.Instance.ProgressSave.DiscoveredCards).ToList();

            if (unseenCards.Count == 0)
            {
                Log.Info("All cards discovered, unlocking achievement!");
                AchievementsUtil.Unlock(Achievement.DiscoverAllCards, player);
            }
            else if (unseenCards.Count > 10)
            {
                Log.Info($"Cards left to discover: {unseenCards.Count}");
            }
            else
            {
                Log.Info($"Cards left to discover: {string.Join(",", unseenCards)}");
            }
        }

        if (!AchievementsUtil.IsUnlocked(Achievement.DiscoverAllRelics))
        {
            List<ModelId> unseenRelics = ModelDb.Relics.Select(c => c.Id).Except(SaveManager.Instance.ProgressSave.DiscoveredRelics).ToList();

            if (unseenRelics.Count == 0)
            {
                Log.Info("All relics discovered, unlocking achievement!");
                AchievementsUtil.Unlock(Achievement.DiscoverAllRelics, player);
            }
            else if (unseenRelics.Count > 10)
            {
                Log.Info($"Relics left to discover: {unseenRelics.Count}");
            }
            else
            {
                Log.Info($"Relics left to discover: {string.Join(",", unseenRelics)}");
            }
        }

        if (!AchievementsUtil.IsUnlocked(Achievement.DiscoverAllEvents))
        {
            List<ModelId> unseenEvents = ModelDb.Events.Select(c => c.Id).Except(SaveManager.Instance.ProgressSave.DiscoveredEvents).ToList();

            if (unseenEvents.Count == 0)
            {
                Log.Info("All events discovered, unlocking achievement!");
                AchievementsUtil.Unlock(Achievement.DiscoverAllEvents, player);
            }
            else if (unseenEvents.Count > 10)
            {
                Log.Info($"Events left to discover: {unseenEvents.Count}");
            }
            else
            {
                Log.Info($"Events left to discover: {string.Join(",", unseenEvents)}");
            }
        }

        if (SaveManager.Instance.ProgressSave.FloorsClimbed > 10000 &&
            !AchievementsUtil.IsUnlocked(Achievement.FloorTenThousand))
        {
            Log.Info("Climbed ten thousand floors for the first time, unlocking achievement");
            AchievementsUtil.Unlock(Achievement.FloorTenThousand, player);
        }
    }

    public static void CheckTimelineComplete()
    {
        List<string> unseenEpochs = EpochModel.AllIds().Except(SaveManager.Instance.ProgressSave.Epochs.Where(e => e.State >= EpochState.Revealed).Select(e => e.Id)).ToList();

        if (unseenEpochs.Count == 0)
        {
            Log.Info("All epochs discovered, unlocking achievement!");

            // The CompleteTimeline achievement can only be unlocked from the Timeline screen, so we can accurately pass
            // null for the local player.
            AchievementsUtil.Unlock(Achievement.CompleteTimeline, null);
        }
        else if (unseenEpochs.Count > 10)
        {
            Log.Info($"Epochs left to discover: {unseenEpochs.Count}");
        }
        else
        {
            Log.Info($"Epochs left to discover: {string.Join(",", unseenEpochs)}");
        }
    }

    public static void CheckForDefeatedAllEnemiesAchievement(ActModel act, Player localPlayer)
    {
        if (!AchievementsUtil.IsUnlocked(act.DefeatedAllEnemiesAchievement))
        {
            int totalMonsters = act.AllMonsters.Count();
            int totalDefeated = 0;

            foreach (EnemyStats enemy in SaveManager.Instance.ProgressSave.EnemyStats)
            {
                // If this monster is in this Act and the player has defeated at least 1, increment total Defeated
                if (act.AllMonsters.Any(monster => monster.Id == enemy.Id) && enemy.TotalWins > 0)
                {
                    totalDefeated++;
                }
            }

            int undefeatedMonsters = totalMonsters - totalDefeated;

            if (undefeatedMonsters <= 0)
            {
                Log.Info($"All monsters in act {act.Id} defeated, unlocking achievement!");
                AchievementsUtil.Unlock(act.DefeatedAllEnemiesAchievement, localPlayer);
            }
            else if (undefeatedMonsters > 10)
            {
                Log.Info($"Monsters left in act {act.Id} to discover: {undefeatedMonsters}");
            }
            else
            {
                Log.Info($"Monsters left in act {act.Id} to discover: {string.Join(",", undefeatedMonsters)}");
            }
        }
    }

    public static void AfterBossDefeated(Player localPlayer)
    {
        if (!AchievementsUtil.IsUnlocked(Achievement.DefeatOneBoss))
        {
            Log.Info("Boss defeated, unlocking achievement");
            AchievementsUtil.Unlock(Achievement.DefeatOneBoss, localPlayer);
        }
    }
}
