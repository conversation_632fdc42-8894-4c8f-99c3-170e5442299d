using System;
using System.Collections.Generic;

namespace MegaCrit.Sts2.Core.Animation;

public class AnimState
{
    public const string attackAnim = "attack";
    public const string castAnim = "cast";
    public const string dieAnim = "die";
    public const string hurtAnim = "hurt";
    public const string idleAnim = "idle_loop";
    public const string reviveAnim = "revive";

    public string Id { get; }

    /// <summary>
    /// Is this a looping animation?
    /// </summary>
    public bool IsLooping { get; }

    /// <summary>
    /// If this is a looping animation, has it already looped at least once?
    /// </summary>
    public bool HasLooped { get; private set; }

    private readonly Dictionary<string, List<Branch>> _branchedStates;

    private struct Branch
    {
        public AnimState state;
        public Func<bool>? condition;
    }

    // For states that immediately transition to another state on completion
    public AnimState? NextState { get; set; }

    public string? BoundsContainer { get; init; }

    public AnimState(string id, bool isLooping = false)
    {
        Id = id;
        IsLooping = isLooping;
        _branchedStates = new Dictionary<string, List<Branch>>();
    }

    // TODO: document
    public void AddBranch(string trigger, AnimState state, Func<bool>? condition = null)
    {
        Branch branch = new()
        {
            state = state,
            condition = condition
        };

        if (!_branchedStates.TryGetValue(trigger, out List<Branch>? branches))
        {
            branches = [];
            _branchedStates[trigger] = branches;
        }

        branches.Add(branch);
    }

    public AnimState? CallTrigger(string trigger)
    {
        if (_branchedStates.TryGetValue(trigger, out List<Branch>? list))
        {
            foreach (Branch branch in list)
            {
                if (branch.condition?.Invoke() ?? true)
                {
                    return branch.state;
                }
            }
        }

        return null;
    }

    /// <summary>
    /// Mark that this animation has looped at least once.
    /// </summary>
    public void MarkHasLooped()
    {
        HasLooped = true;
    }
}
