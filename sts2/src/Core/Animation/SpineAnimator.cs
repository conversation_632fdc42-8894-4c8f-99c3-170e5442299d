using System;
using Godot;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Animation;

public class SpineAnimator
{
    public const string attackTrigger = "Attack";
    public const string castTrigger = "Cast";
    public const string deathTrigger = "Dead";
    public const string hitTrigger = "Hit";
    public const string reviveTrigger = "Revive";

    private const float _animVariance = 0.1f;

    private readonly SpineSprite _spineController;
    private AnimState _currentState;
    private readonly AnimState _anyState;

    public event Action<string>? BoundsUpdated;

    public SpineAnimator(AnimState initialState, SpineSprite spineController)
    {
        _anyState = new AnimState("anyState");
        _spineController = spineController;
        _spineController.AnimationStarted += OnAnimationStarted;
        _spineController.AnimationCompleted += OnAnimationCompleted;
        _spineController.AnimationInterrupted += OnAnimationInterrupted;

        _currentState = initialState;
        SetNextState(initialState);

        SpineAnimationState skelAnim = _spineController.GetAnimationState();
        SpineTrackEntry te = skelAnim.GetCurrent(0);
        te.SetTrackTime(Rng.Chaotic.NextFloat(te.GetAnimationEnd()));
        skelAnim.Update(0);
        skelAnim.Apply(_spineController.GetSkeleton());
    }

    public void AddAnyState(string trigger, AnimState state, Func<bool>? condition = null)
    {
        _anyState.AddBranch(trigger, state, condition);
    }

    public void SetTrigger(string trigger)
    {
        AnimState? nextState = _anyState.CallTrigger(trigger);

        // only if we haven't been superseded by an any state
        // do we check on the current state
        if (nextState == null)
        {
            nextState = _currentState.CallTrigger(trigger);
        }

        if (nextState != null)
        {
            SetNextState(nextState);
        }
    }

    private void SetNextState(AnimState state)
    {
        _currentState = state;
        SpineAnimationState skelAnim = _spineController.GetAnimationState();

        try
        {
            skelAnim.SetAnimation(_currentState.Id, _currentState.IsLooping, 0);
            if (_currentState.IsLooping)
            {
                OffsetLoopingAnimation(skelAnim.GetCurrent(0));
            }
        }
        catch
        {
            Log.Error($"could not find {_currentState.Id} animation");
            return;
        }

        if (state.BoundsContainer != null)
        {
            BoundsUpdated?.Invoke(state.BoundsContainer);
        }

        if (state.NextState != null)
        {
            AddNextState(state.NextState);
        }
    }

    private void AddNextState(AnimState state)
    {
        SpineAnimationState skelAnim = _spineController.GetAnimationState();

        try
        {
            SpineTrackEntry track = skelAnim.AddAnimation(state.Id, 0, state.IsLooping, 0);
            if (state.IsLooping)
            {
                OffsetLoopingAnimation(track);
            }
        }
        catch
        {
            Log.Error($"could not find {_currentState.Id} animation");
        }

        if (state.NextState != null)
        {
            AddNextState(state.NextState);
        }
    }

    private void OnAnimationStarted(GodotObject _, GodotObject __, GodotObject ___)
    {
        if (_currentState is { HasLooped: false, BoundsContainer: not null })
        {
            BoundsUpdated?.Invoke(_currentState.BoundsContainer);
        }
    }

    private void OnAnimationCompleted(GodotObject _, GodotObject __, GodotObject ___)
    {
        if (_currentState is { HasLooped: false, BoundsContainer: not null })
        {
            BoundsUpdated?.Invoke(_currentState.BoundsContainer);
        }

        if (_currentState is { IsLooping: true, HasLooped: false })
        {
            _currentState.MarkHasLooped();
        }

        if (_currentState.NextState != null)
        {
            _currentState = _currentState.NextState;
        }
    }

    private void OnAnimationInterrupted(GodotObject _, GodotObject __, GodotObject ___)
    {
        if (_currentState.BoundsContainer != null)
        {
            BoundsUpdated?.Invoke(_currentState.BoundsContainer);
        }
    }

    private void OffsetLoopingAnimation(SpineTrackEntry track)
    {
        track.SetTimeScale(Rng.Chaotic.NextFloat(0.90f, 1.1f));

        float animationLength = track.GetAnimationEnd();

        track.SetTrackTime((animationLength + Rng.Chaotic.NextFloat(-_animVariance, _animVariance)) % animationLength);
    }
}
