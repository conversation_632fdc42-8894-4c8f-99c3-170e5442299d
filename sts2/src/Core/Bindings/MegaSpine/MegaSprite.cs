using System;
using Godot;

namespace MegaCrit.Sts2.Core.Bindings.MegaSpine;

/// <summary>
/// C# bindings for SpineSprite.
/// </summary>
public class MegaSprite
{
    private readonly GodotObject _native;

    public MegaSprite(GodotObject native)
    {
        if (native.GetClass() != "SpineSprite")
        {
            throw new InvalidOperationException("MegaSprite can only be created from a SpineSprite");
        }

        _native = native;
    }

    public MegaAnimationState GetAnimationState()
    {
        return new MegaAnimationState((GodotObject)_native.Call("get_animation_state"));
    }

    public MegaSkeleton GetSkeleton()
    {
        return new MegaSkeleton((GodotObject)_native.Call("get_skeleton"));
    }
}
