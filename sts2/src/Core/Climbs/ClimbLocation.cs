using System;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;

namespace MegaCrit.Sts2.Core.Climbs;

public struct ClimbLocation : IEquatable<ClimbLocation>, IComparable<ClimbLocation>, IPacketSerializable
{
    /// <summary>
    /// The act that this location is in.
    /// </summary>
    public int actIndex;

    /// <summary>
    /// The coordinate that this location is at.
    /// Will be null when we're in the map room (at the start of an act, before picking the ancient map point).
    /// </summary>
    public MapCoord? coord;

    public ClimbLocation(MapCoord? coord, int actIndex)
    {
        this.coord = coord;
        this.actIndex = actIndex;
    }

    public void Serialize(PacketWriter writer)
    {
        writer.WriteInt(actIndex, 4);
        writer.WriteBool(coord != null);

        if (coord != null)
        {
            writer.Write(coord.Value);
        }
    }

    public void Deserialize(PacketReader reader)
    {
        actIndex = reader.ReadInt(4);
        bool hasCoord = reader.ReadBool();

        if (hasCoord)
        {
            coord = reader.Read<MapCoord>();
        }
    }

    public static bool operator ==(ClimbLocation first, ClimbLocation second)
    {
        return first.Equals(second);
    }

    public static bool operator !=(ClimbLocation first, ClimbLocation second)
    {
        return !(first == second);
    }

    public bool Equals(ClimbLocation other)
    {
        return actIndex == other.actIndex && coord == other.coord;
    }

    public override bool Equals(object? obj)
    {
        return obj is ClimbLocation other && Equals(other);
    }

    public override int GetHashCode()
    {
        return (actIndex, coord?.col, coord?.row).GetHashCode();
    }

    public int CompareTo(ClimbLocation other)
    {
        if (actIndex != other.actIndex)
        {
            return actIndex.CompareTo(other.actIndex);
        }

        if (coord == null && other.coord == null) return 0;
        if (coord == null && other.coord != null) return -1;
        if (coord != null && other.coord == null) return 1;

        return coord!.Value.CompareTo(other.coord!.Value);
    }

    public override string ToString() => $"act {actIndex} coord ({(coord != null ? $"{coord.Value.col}, {coord.Value.row}" : "null")})";
}
