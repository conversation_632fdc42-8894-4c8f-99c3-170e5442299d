using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Achievements;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs.Metrics;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Daily;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.GameActions;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.RelicPools;
using MegaCrit.Sts2.Core.Models.Singleton;
using MegaCrit.Sts2.Core.Multiplayer;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Multiplayer.Game.Lobby;
using MegaCrit.Sts2.Core.Multiplayer.Game.PeerInput;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Lobby;
using MegaCrit.Sts2.Core.Multiplayer.Replay;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.Nodes.Audio;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Screens;
using MegaCrit.Sts2.Core.Nodes.Screens.Capstones;
using MegaCrit.Sts2.Core.Nodes.Screens.Map;
using MegaCrit.Sts2.Core.Nodes.Screens.Overlays;
using MegaCrit.Sts2.Core.Odds;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Climbs;
using MegaCrit.Sts2.Core.Saves.MapDrawing;
using MegaCrit.Sts2.Core.TestSupport;
using MegaCrit.Sts2.Core.Timeline.Epochs;

namespace MegaCrit.Sts2.Core.Climbs;

public class ClimbManager : IClimbLobbyListener
{
    public static ClimbManager Instance { get; } = new();

    /// <summary>
    /// The ascension manager for this climb.
    /// </summary>
    public AscensionManager AscensionManager { get; private set; } = default!;

    /// <summary>
    /// Whether or not this climb should be saved.
    /// This will always be true for normal players.
    /// For developers, it will be true when starting/continuing a climb from the main menu, but false when starting
    /// from SceneBootstrapper or from tests.
    /// </summary>
    public bool ShouldSave { get; private set; }

    /// <summary>
    /// If set to true, the final climb score will be uploaded to the daily climb leaderboard for today.
    /// </summary>
    public DateTimeOffset? DailyTime { get; private set; }

    /// <summary>
    /// Is there currently a climb in progress?
    /// True when in a climb, false when on the main menu or submenus.
    /// You shouldn't usually have to check this, because most climb-dependent things are only executed within the
    /// context of a climb, but this is a good escape valve if you need it.
    /// </summary>
    public bool IsInProgress => State != null;

    public bool IsGameOver => IsInProgress && State!.IsGameOver;
    public bool IsAbandoned { get; private set; }

    private long _startTime;
    private long _prevClimbTime; // previous climb time before reload
    private long _sessionStartTime; // when you started the current session
    private bool _climbHistoryWasUploaded; // This prevents climb history from getting uploaded twice
    public ClimbHistory? History { get; set; } // Helper reference for the GameOverScreen

    // Core networking
    public INetGameService NetService { get; private set; } = default!;
    public ChecksumTracker ChecksumTracker { get; private set; } = default!;
    public ClimbLocationTargetedMessageBuffer ClimbLocationTargetedBuffer { get; private set; } = default!;
    public CombatReplayWriter CombatReplayWriter { get; private set; } = default!;
    public ClimbLobby? ClimbLobby { get; private set; }

    // State Synchronizers
    public CombatStateSynchronizer CombatStateSynchronizer { get; private set; } = default!;
    public MapSelectionSynchronizer MapSelectionSynchronizer { get; private set; } = default!;
    public ActChangeSynchronizer ActChangeSynchronizer { get; private set; } = default!;
    public PlayerChoiceSynchronizer PlayerChoiceSynchronizer { get; private set; } = default!;
    public EventSynchronizer EventSynchronizer { get; private set; } = default!;
    public RewardSynchronizer RewardSynchronizer { get; private set; } = default!;
    public RestSiteSynchronizer RestSiteSynchronizer { get; private set; } = default!;
    public OneOffSynchronizer OneOffSynchronizer { get; private set; } = default!;
    public TreasureRoomRelicSynchronizer TreasureRoomRelicSynchronizer { get; private set; } = default!;

    // Visual Synchronizers
    public FlavorSynchronizer FlavorSynchronizer { get; private set; } = default!;
    public PeerInputSynchronizer InputSynchronizer { get; private set; } = default!;
    public HoveredModelTracker HoveredModelTracker { get; private set; } = default!;

    // Action handling classes
    public ActionQueueSet ActionQueueSet { get; private set; } = default!;
    public ActionExecutor ActionExecutor { get; private set; } = default!;
    public ActionQueueSynchronizer ActionQueueSynchronizer { get; private set; } = default!;

    public long ClimbTime => DateTimeOffset.UtcNow.ToUnixTimeSeconds() - _sessionStartTime + _prevClimbTime;

    /// <summary>
    /// This flag returns true if we are in a singleplayer session or we are faking a multiplayer session.
    /// Sometimes, for testing, we add dummy players through the bootstrapper. In those cases, we still want end turn
    /// and other functions not to wait for multiple players, because there's only one acting player.
    /// </summary>
    public bool IsSinglePlayerOrFakeMultiplayer => IsInProgress && NetService.Type == NetGameType.Singleplayer;

    // Used in debug scenarios. If this is set, then after the post-rewards "proceed" button is pressed, this is called
    // and nothing else happens.
    public Action? debugAfterCombatRewardsOverride;

    public SerializableMapDrawings? MapDrawingsToLoad { get; set; }

    public event Action<ClimbState>? ClimbStarted;

    // Triggered whenever a room is entered
    public event Action? RoomEntered;

    // Triggered whenever an act is entered
    public event Action? ActEntered;

    private ClimbState? State { get; set; }

    private GameMode GameMode
    {
        get
        {
            if (State?.Modifiers.Count > 0)
            {
                return DailyTime != null ? GameMode.Daily : GameMode.Custom;
            }
            else
            {
                return GameMode.Standard;
            }
        }
    }

    private ClimbManager() { }

    /// <summary>
    /// Set up a brand-new single-player climb.
    /// This includes running initialization code for things that should happen at the start of a climb (obtaining the
    /// characters' starting deck and relic, setting an empty potion belt, etc.).
    /// </summary>
    /// <param name="state">ClimbState that should be used for the climb.</param>
    /// <param name="shouldSave">
    /// Whether or not the climb should be saved to disk.
    /// True during normal gameplay, false during tests and bootstrap.
    /// </param>
    /// <param name="dailyTime">
    /// If non-null, then the final climb score will be uploaded to the daily climb leaderboard for the passed time.
    /// </param>
    public void SetUpNewSinglePlayer(ClimbState state, bool shouldSave, DateTimeOffset? dailyTime = null)
    {
        if (State != null) throw new InvalidOperationException("State is already set.");

        State = state;

        INetGameService netService = new NetSingleplayerGameService();
        InitializeShared(
            netService,
            new PeerInputSynchronizer(netService),
            shouldSave,
            dailyTime,
            DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
            0
        );

        InitializeNewClimb();
        GenerateRooms();
    }

    /// <summary>
    /// Set up a brand-new multiplayer climb.
    /// This includes running initialization code for things that should happen at the start of a climb (obtaining the
    /// characters' starting deck and relic, setting an empty potion belt, etc.).
    /// </summary>
    /// <param name="state">ClimbState that should be used for the climb.</param>
    /// <param name="lobby">The multiplayer lobby containing the players that will go on the climb together.</param>
    /// <param name="shouldSave">
    /// Whether or not the climb should be saved to disk.
    /// True during normal gameplay, false during tests and bootstrap.
    /// </param>
    /// <param name="dailyTime">
    /// If non-null, then the final climb score will be uploaded to the daily climb leaderboard for the passed time.
    /// </param>
    public void SetUpNewMultiPlayer(ClimbState state, StartClimbLobby lobby, bool shouldSave, DateTimeOffset? dailyTime = null)
    {
        if (State != null) throw new InvalidOperationException("State is already set.");

        State = state;

        InitializeShared(
            lobby.NetService,
            lobby.InputSynchronizer,
            shouldSave,
            dailyTime,
            DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
            0
        );

        ClimbLobby = new ClimbLobby(GameMode, lobby.NetService, this, State, lobby.Players.Select(p => p.id));
        ClimbLobby.RemotePlayerDisconnected += RemotePlayerDisconnected;

        InitializeNewClimb();
        GenerateRooms();
    }

    /// <summary>
    /// Set up a single-player climb that's been loaded from a save file.
    /// No start-of-climb initialization code will be run here, since we're loading an existing state.
    /// </summary>
    /// <param name="state">ClimbState that should be used for the climb.</param>
    /// <param name="save">
    /// The serialized version of the climb. This may contain extra data that's not part of the deserialized ClimbState.
    /// </param>
    public void SetUpSavedSinglePlayer(ClimbState state, SerializableClimb save)
    {
        if (State != null) throw new InvalidOperationException("State is already set.");

        State = state;

        INetGameService netService = new NetSingleplayerGameService();
        InitializeShared(
            netService,
            new PeerInputSynchronizer(netService),
            true,
            save.DailyTime,
            save.StartTime,
            save.ClimbTime
        );

        InitializeSavedClimb(save);
    }

    /// <summary>
    /// Set up a multiplayer climb that's been loaded from a save file.
    /// No start-of-climb initialization code will be run here, since we're loading an existing state.
    /// </summary>
    /// <param name="state">ClimbState that should be used for the climb.</param>
    /// <param name="lobby">
    /// The multiplayer lobby containing the players that will go on the climb together.
    /// The lobby also contains the serialized version of the climb. This may contain extra data that's not part of the
    /// deserialized ClimbState.
    /// </param>
    public void SetUpSavedMultiPlayer(ClimbState state, LoadClimbLobby lobby)
    {
        if (State != null) throw new InvalidOperationException("State is already set.");

        State = state;

        SerializableClimb save = lobby.Climb;
        InitializeShared(
            lobby.NetService,
            lobby.InputSynchronizer,
            true,
            save.DailyTime,
            save.StartTime,
            save.ClimbTime
        );

        ClimbLobby = new ClimbLobby(GameMode, lobby.NetService, this, State, lobby.ConnectedPlayerIds);
        ClimbLobby.RemotePlayerDisconnected += RemotePlayerDisconnected;

        InitializeSavedClimb(save);
    }

    /// <summary>
    /// Set up a climb that's been loaded from a CombatReplay file.
    /// No start-of-climb initialization code will be run here, since we're loading an existing state.
    /// </summary>
    /// <param name="state">ClimbState that should be used for the climb.</param>
    /// <param name="replay">
    /// CombatReplay that is being replayed in this climb.
    /// The replay also contains the serialized version of the climb. This may contain extra data that's not part of the
    /// deserialized ClimbState.
    /// </param>
    public void SetUpReplay(ClimbState state, CombatReplay replay)
    {
        if (State != null) throw new InvalidOperationException("State is already set.");

        State = state;

        SerializableClimb save = replay.serializableClimb;
        ulong playerIdToLoad = save.Players[0].NetId;
        NetReplayGameService netService = new(playerIdToLoad);

        InitializeShared(
            netService,
            new PeerInputSynchronizer(netService),
            true,
            save.DailyTime,
            save.StartTime,
            save.ClimbTime
        );

        InitializeSavedClimb(save);
    }

    /// <summary>
    /// Set up a brand-new climb to be used in an automated test. Should only be used in the test project.
    /// This includes running initialization code for things that should happen at the start of a climb (obtaining the
    /// characters' starting deck and relic, setting an empty potion belt, etc.).
    /// </summary>
    /// <param name="state">ClimbState that should be used for the climb.</param>
    /// <param name="gameService">The mock INetGameService to use.</param>
    /// <param name="disableCombatStateSync">
    /// Whether or not combat state synchronization should be disabled. This is only relevant if you are testing
    /// multiplayer scenarios.
    /// </param>
    /// <param name="shouldSave">
    /// If true, saving will be performed. Remember to call <see cref="SaveManager.MockInstanceForTesting"/>.
    /// </param>
    public void SetUpTest(
        ClimbState state,
        INetGameService gameService,
        bool disableCombatStateSync = true,
        bool shouldSave = false
    )
    {
        if (State != null) throw new InvalidOperationException("State is already set.");

        State = state;

        InitializeShared(
            gameService,
            new PeerInputSynchronizer(gameService),
            shouldSave,
            null,
            DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
            0
        );

        ClimbLobby = new ClimbLobby(GameMode, gameService, this, State, state.Players.Select(p => p.NetId));
        CombatStateSynchronizer.IsDisabled = disableCombatStateSync;
        ClimbLobby.RemotePlayerDisconnected += RemotePlayerDisconnected;

        InitializeNewClimb();
    }

    private void InitializeShared(INetGameService netService, PeerInputSynchronizer inputSynchronizer, bool shouldSave, DateTimeOffset? dailyTime, long startTime, long climbTime)
    {
        if (State == null) throw new InvalidOperationException("State is not set.");

        NetService = netService;
        ulong localPlayerId = NetService.NetId;

        ChecksumTracker = new ChecksumTracker(NetService, State);
        ClimbLocationTargetedBuffer = new ClimbLocationTargetedMessageBuffer(NetService);

        FlavorSynchronizer = new FlavorSynchronizer(NetService, State, localPlayerId);

        ActionQueueSet = new ActionQueueSet(State.Players);
        ActionExecutor = new ActionExecutor(ActionQueueSet);
        ActionQueueSynchronizer = new ActionQueueSynchronizer(State, ActionQueueSet, ClimbLocationTargetedBuffer, NetService);

        PlayerChoiceSynchronizer = new PlayerChoiceSynchronizer(NetService, State);
        CombatStateSynchronizer = new CombatStateSynchronizer(NetService, State);
        MapSelectionSynchronizer = new MapSelectionSynchronizer(NetService, ActionQueueSynchronizer, State);
        ActChangeSynchronizer = new ActChangeSynchronizer(State);
        EventSynchronizer = new EventSynchronizer(ClimbLocationTargetedBuffer, NetService, State, localPlayerId, State.Rng.Seed);
        RewardSynchronizer = new RewardSynchronizer(ClimbLocationTargetedBuffer, NetService, State, localPlayerId);
        RestSiteSynchronizer = new RestSiteSynchronizer(ClimbLocationTargetedBuffer, NetService, State, localPlayerId);
        OneOffSynchronizer = new OneOffSynchronizer(ClimbLocationTargetedBuffer, NetService, State, localPlayerId);
        TreasureRoomRelicSynchronizer = new TreasureRoomRelicSynchronizer(
            State,
            localPlayerId,
            ActionQueueSynchronizer,
            State.SharedRelicGrabBag,
            State.Rng.TreasureRoomRelics
        );

        CombatReplayWriter = new CombatReplayWriter(PlayerChoiceSynchronizer, ActionQueueSet, ActionQueueSynchronizer, ChecksumTracker);
        CombatReplayWriter.IsEnabled = !TestMode.IsOn;

        ActionExecutor.AfterActionExecuted += SendPostActionChecksum;
        ChecksumTracker.StateDiverged += StateDiverged;

        ActionExecutor.Pause();

        AscensionManager = new AscensionManager(State.AscensionLevel);
        ShouldSave = shouldSave;
        DailyTime = dailyTime;
        _startTime = startTime;
        _prevClimbTime = climbTime;
        _sessionStartTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        InputSynchronizer = inputSynchronizer;
        HoveredModelTracker = new HoveredModelTracker(InputSynchronizer, State);
    }

    /// <summary>
    /// Call this when starting a brand new climb, not when loading a saved climb.
    /// </summary>
    private void InitializeNewClimb()
    {
        State!.SharedRelicGrabBag.Populate(ModelDb.RelicPool<SharedRelicPool>().Relics, State.Rng.UpFront);

        foreach (Player player in State.Players)
        {
            player.PopulateRelicGrabBagIfNecessary(State.Rng.UpFront);
            ApplyAscensionEffects(player);
        }

        SetStartedWithNeowFlag();

        foreach (ModifierModel modifier in State.Modifiers)
        {
            modifier.OnClimbCreated(State);
        }
    }

    /// <summary>
    /// Call this when loading a saved climb, not when starting a brand new climb.
    /// </summary>
    private void InitializeSavedClimb(SerializableClimb save)
    {
        AfterLocationChanged();

        // Map node is not initialized yet, so we have to set this for the NClimb to read later
        MapDrawingsToLoad = save.MapDrawings;

        // Initialize climb state in all of the modifiers (if any)
        foreach (ModifierModel modifier in State!.Modifiers)
        {
            modifier.AfterClimbLoaded(State);
        }
    }

    private void SendPostActionChecksum(GameAction action)
    {
        // Only do this if we're in combat, as that is the only time actions must be deterministic
        if (!CombatManager.Instance.IsInProgress) return;

        // EndTurnAction and SwitchSidesAction have slightly non-deterministic behavior with their timing since they
        // set a flag and then immediately end while end-of-turn hooks are taking place. We ignore these actions in
        // favor of running our own specific checksums in CombatManager.
        if (action is EndPlayerTurnAction or ReadyToBeginEnemyTurnAction) return;

        ChecksumTracker.GenerateChecksum($"after executing action {action}", action);
    }

    /// <summary>
    /// Call this when we start a new run to set the StartedWithNeow extra field.
    /// </summary>
    private void SetStartedWithNeowFlag()
    {
        State!.ExtraFields.StartedWithNeow =
            // Always start with Neow in test mode.
            TestMode.IsOn ||
            // Always start with Neow in multiplayer.
            NetService.Type.IsMultiplayer() ||
            // Otherwise, check if Neow is unlocked.
            SaveManager.Instance.HasEpoch<NeowAwakensEpoch>();
    }

    /// <summary>
    /// Call this to validate a save can be loaded without actually creating a climb.
    /// This performs deep validation by instantiating save components to ensure all data is valid.
    ///
    /// This validation occurs AFTER JSON parsing and migration, so the save file has already been
    /// successfully deserialized. However, the content may still be invalid due to:
    /// - Removed modifiers that exist in the save
    /// - Invalid card/relic/potion IDs
    /// - Corrupted game state data
    ///
    /// Unlike MigrationManager.LoadSave(), this method does not automatically handle corruption.
    /// Callers must handle exceptions and rename corrupt files as needed.
    /// </summary>
    /// <param name="save">The save to attempt to load.</param>
    /// <param name="localPlayerId">The player ID of the local (hosting) player. If it is not in the save file, an
    /// exception is thrown.</param>
    public static void ValidateSave(SerializableClimb save, ulong localPlayerId)
    {
        if (save.Players.FirstOrDefault(p => p.NetId == localPlayerId) == null)
        {
            throw new InvalidOperationException($"Save is invalid! Players does not contain local player Id. IDs in save file: {string.Join(",", save.Players.Select(p => p.NetId))}. Local ID: {localPlayerId}.");
        }

        foreach (SerializablePlayer playerSave in save.Players)
        {
            Player _ = Player.FromSerializable(playerSave);
        }

        ClimbRngSet rng = ClimbRngSet.FromSave(save.SerializableRng);
        ClimbOddsSet __ = ClimbOddsSet.FromSerializable(save.SerializableOdds, rng.UnknownMapPoint);
        IEnumerable<ActModel> ___ = save.Acts.Select(ActModel.FromSave);
        IEnumerable<ModifierModel> ____ = save.Modifiers.Select(ModifierModel.FromSerializable);
    }

    public SerializableClimb ToSave()
    {
        SerializableClimb save = new()
        {
            Acts = State!.Acts.Select(a => a.ToSave()).ToList(),
            Modifiers = State.Modifiers.Select(m => m.ToSerializable()).ToList(),
            DailyTime = DailyTime,
            CurrentActIndex = State.CurrentActIndex,
            EventsSeen = State.VisitedEventIds.ToList(),
            SerializableOdds = State.Odds.ToSerializable(),
            SerializableSharedRelicGrabBag = State.SharedRelicGrabBag.ToSerializable(),
            Players = State.Players.Select(p => p.ToSerializable()).ToList(),
            SerializableRng = State.Rng.ToSerializable(),
            VisitedMapCoords = State.VisitedMapCoords.ToList(),
            MapPointHistory = State.MapPointHistory.Select(l => l.ToList()).ToList(),
            SaveTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
            StartTime = _startTime,
            ClimbTime = ClimbTime,
            Ascension = State.AscensionLevel,
            PlatformType = NetService.Platform,
            MapDrawings = NClimb.Instance?.GlobalUi.MapScreen.Drawings.GetSerializableMapDrawings(),
            ExtraFields = State.ExtraFields.ToSerializable()
        };

        return save;
    }

    public ClimbState Launch()
    {
        LocalContext.NetId = NetService.NetId;
        LocManager.Instance.SetClimbState(State!);
        ClimbStarted?.Invoke(State!);

        return State!;
    }

    /// <summary>
    /// Finalize the relics at the start of a climb.
    /// This is called when creating a new climb, but not before loading a saved climb, since
    /// <see cref="RelicModel.AfterObtained"/> is not idempotent.
    /// </summary>
    public async Task FinalizeStartingRelics()
    {
        foreach (Player player in State!.Players)
        {
            foreach (RelicModel startingRelic in player.Relics)
            {
                await startingRelic.AfterObtained();
            }
        }
    }

    /// <summary>
    /// Initialize the rooms for the climb. Don't call this in Start so we can skip it in tests.
    /// </summary>
    public void GenerateRooms()
    {
        List<AncientEventModel> sharedAncients = ModelDb.SharedAncientEvents.ToList().UnstableShuffle(State!.Rng.UpFront).ToList();

        // We skip the first act because it's ancient should always be Neow
        foreach (ActModel act in State.Acts.Skip(1))
        {
            if (sharedAncients.Count == 0) break;

            int subsetCount = State.Rng.UpFront.NextInt(sharedAncients.Count + 1);
            List<AncientEventModel> sharedAncientSubset = sharedAncients.Take(subsetCount).ToList();
            sharedAncients = sharedAncients.Skip(subsetCount).ToList();
            act.AddSharedAncients(sharedAncientSubset);
        }

        foreach (ActModel act in State.Acts)
        {
            act.GenerateRooms(State.Rng.UpFront);

            // Currently, discovery order modifications are tutorial-based. No tutorials should be applied in multiplayer
            if (TestMode.IsOff && !NetService.Type.IsMultiplayer())
            {
                // TODO: Eventually switch # of climbs with num of times we've seen this specific act
                State.Acts[0].ApplyDiscoveryOrderModifications(SaveManager.Instance.ProgressSave.NumberOfClimbs);
            }
        }
    }

    /// <summary>
    /// Generate a new map.
    /// This is called at the start of the climb, between each act, and when relics create an entirely new map.
    /// Don't call this in Start so we can skip it in tests.
    /// </summary>
    public async Task GenerateMap()
    {
        if (State == null) throw new InvalidOperationException("State is not set.");

        // Update player map votes (cancel votes that were pointing to the old map)
        MapSelectionSynchronizer.BeforeMapGenerated();

        ActMap map = Hook.ModifyGeneratedMap(State, StandardActMap.CreateFor(State), State.CurrentActIndex);
        AscensionManager.ApplyMapModifications(map, State.CurrentActIndex);
        await Hook.AfterMapGenerated(State, map, State.CurrentActIndex);

        // If Neow isn't unlocked, then the first room is a Monster room instead of Neow
        if (!State.ExtraFields.StartedWithNeow && State.CurrentActIndex == 0)
        {
            map.StartingMapPoint.PointType = MapPointType.Monster;
        }

        State.Map = map;

        NMapScreen.Instance?.SetMap(map, State.Rng.Seed, true);
    }

    public Task EnterMapCoord(MapCoord coord)
    {
        State!.AddVisitedMapCoord(coord);
        return EnterMapCoordInternal(coord, null, saveGame: true);
    }

    public async Task LoadIntoLatestMapCoord(AbstractRoom? preFinishedRoom)
    {
        if (State!.VisitedMapCoords.Count > 0)
        {
            await EnterMapCoordInternal(
                State.VisitedMapCoords[^1],
                preFinishedRoom,
                // Do not save the game! When this is called, we are loading from a save
                saveGame: false
            );
        }
        else
        {
            // If we haven't visited any map coords yet, it means we saved immediately after starting a new climb
            // (i.e. in the first Map room).
            await EnterRoomInternal(new MapRoom());
        }
    }

    private Task EnterMapCoordInternal(MapCoord coord, AbstractRoom? preFinishedRoom, bool saveGame)
    {
        MapPoint point = State!.Map.GetPoint(coord)!;
        return EnterMapPointInternal(coord.row + 1, point.PointType, preFinishedRoom, saveGame);
    }

    /// <summary>
    /// WARNING: This should only be called by <see cref="ClimbManager"/> and in tests.
    /// </summary>
    public async Task EnterMapPointInternal(int actFloor, MapPointType pointType, AbstractRoom? preFinishedRoom, bool saveGame)
    {
        // Update the MapPointHistoryEntry for previous map point before moving to the next one.
        if (State!.MapPointHistory.Count > 0)
        {
            UpdatePlayerStatsInMapPointHistory();
        }

        State.ActFloor = actFloor;

        // Exit the room before we save the game and generate the replay.
        // This allows rest site and events to generate a checksum, which doesn't get recorded in the replay.
        await ExitCurrentRooms();

        if (preFinishedRoom == null)
        {
            // Start syncing our state early (before the fadeout) so that it is hopefully available to all players by the
            // time we are ready to fade in.
            CombatStateSynchronizer.StartSync();
        }

        if (pointType == MapPointType.Ancient && TestMode.IsOff)
        {
            await NGame.Instance!.Transition.RoomFadeOut();
        }

        ClearScreens();

        // Wait for all state to be synced now before the game is saved.
        // TODO: Very likely we will need a "waiting for players" message if any players are taking a long time to sync
        if (preFinishedRoom == null)
        {
            await CombatStateSynchronizer.WaitForSync();
        }

        // IMPORTANT: Save the game _before_ we roll room odds. Otherwise, the next time we load in, the room odds will
        // be inaccurate since they could have changed based on what room we rolled
        if (saveGame)
        {
            await SaveManager.Instance.SaveClimb(null);
        }

        // We must record the combat state now for the same reasons above we need to record the save game. However, we
        // don't currently know if it'll be useful because the map point hasn't yet resolved into a room type.
        if (CombatReplayWriter.IsEnabled)
        {
            CombatReplayWriter.RecordInitialState(ToSave());
        }

        RoomType roomType;

        if (pointType == MapPointType.Unknown && preFinishedRoom != null)
        {
            // If we're loading into a finished room, skip rolling a room type, since we don't want to double-increment
            // the unknown map point odds RNG.
            roomType = RoomType.Monster;
        }
        else
        {
            // Otherwise, roll for a room type based on the current map point type.
            // If the last map point in the room history contained a shop, pass it in the blacklist so we don't roll two
            // shops in a row.
            HashSet<RoomType> blacklist = [];

            if (State.CurrentMapPointHistoryEntry != null && State.CurrentMapPointHistoryEntry.RoomTypes.Contains(RoomType.Shop))
            {
                blacklist.Add(RoomType.Shop);
            }

            roomType = RollRoomTypeFor(pointType, blacklist);
        }

        // There is already a room on the save file, so we shouldn't have to reroll for one
        AbstractRoom room = preFinishedRoom ?? CreateRoom(roomType, mapPointType: pointType);

        // Pause the action queue until we fully load into the room.
        ActionExecutor.Pause();

        // Only append history if we are not loading into a finished room. IF the room is finished, the save already has
        // a record of the map point being visited.
        if (preFinishedRoom == null)
        {
            State.AppendToMapPointHistory(pointType, room.RoomType);
        }

        if (room is CombatRoom combatRoom)
        {
            State.CurrentMapPointHistoryEntry!.ModelId = combatRoom.Encounter.Id;
        }
        else if (room is EventRoom eventRoom)
        {
            State.CurrentMapPointHistoryEntry!.ModelId = eventRoom.CanonicalEvent.Id;
        }

        await EnterRoom(room);

        if (NClimb.Instance != null)
        {
            NClimb.Instance.GlobalUi.MapScreen.IsTraveling = false;
        }

        AfterLocationChanged();

        await FadeIn();
    }

    private AbstractRoom CreateRoom(RoomType roomType, MapPointType mapPointType = MapPointType.Unassigned, AbstractModel? model = null)
    {
        if (State == null) throw new InvalidOperationException("ClimbState is not set.");

        return roomType switch
        {
            RoomType.Monster or RoomType.Elite or RoomType.Boss => new CombatRoom(
                model as EncounterModel ?? State.Act.PullNextEncounter(roomType).ToMutable(),
                State
            ),
            RoomType.Treasure => new TreasureRoom(State.CurrentActIndex),
            RoomType.Shop => new MerchantRoom(),
            RoomType.Event => new EventRoom(
                model as EventModel ??
                (mapPointType == MapPointType.Ancient ? State.Act.PullAncient() : State.Act.PullNextEvent(State))
            ),
            RoomType.RestSite => new RestSiteRoom(),
            RoomType.Map => new MapRoom(),
            RoomType.Victory => new VictoryRoom(),
            _ => throw new InvalidOperationException($"Unexpected RoomType: {roomType}")
        };
    }

    /// <summary>
    /// Roll for a room type based on the specified map point type.
    /// Most map point type have an idempotent room type mapping, but unknown points need to do an RNG roll to determine
    /// room type, which is why we call this a "roll".
    /// </summary>
    /// <param name="pointType">MapPointType to roll a RoomType for.</param>
    /// <param name="blacklist">Room types that we shouldn't be able to roll for map point types with multiple options.</param>
    /// <returns>RoomType.</returns>
    private RoomType RollRoomTypeFor(MapPointType pointType, IEnumerable<RoomType> blacklist)
    {
        return pointType switch
        {
            MapPointType.Unassigned => RoomType.Unassigned,
            MapPointType.Unknown => State!.Odds.UnknownMapPoint.Roll(blacklist, State),
            MapPointType.Shop => RoomType.Shop,
            MapPointType.Treasure => RoomType.Treasure,
            MapPointType.RestSite => RoomType.RestSite,
            MapPointType.Monster => RoomType.Monster,
            MapPointType.Elite => RoomType.Elite,
            MapPointType.Boss => RoomType.Boss,
            MapPointType.Ancient => RoomType.Event,
            _ => throw new ArgumentOutOfRangeException(nameof(pointType), pointType, null)
        };
    }

    /// <summary>
    /// Helper to call the universal "enter the room" fade in vfx.
    /// </summary>
    private async Task FadeIn(bool showTransition = true)
    {
        if (TestMode.IsOn) return;
        await NGame.Instance!.Transition.RoomFadeIn(showTransition);
    }

    /// <summary>
    /// Resets various UI elements before transitioning to the next room.
    /// </summary>
    private void ClearScreens()
    {
        if (TestMode.IsOn) return;

        NOverlayStack.Instance!.Clear();
        NCapstoneContainer.Instance!.Close();
        NMapScreen.Instance!.Close();
        NControllerManager.Instance!.ClearScreens();
    }

    /// <summary>
    /// Should only be used in tests or dev commands, never in real flows.
    /// </summary>
    public async Task EnterMapCoordDebug(MapCoord coord, RoomType roomType, MapPointType pointType = MapPointType.Unassigned, AbstractModel? model = null, bool showTransition = true)
    {
        State!.AddVisitedMapCoord(coord);
        await EnterRoomDebug(roomType, pointType, model, showTransition);
    }

    /// <summary>
    /// Should only be used in tests or dev commands, never in real flows.
    /// </summary>
    public async Task EnterRoomDebug(RoomType roomType, MapPointType pointType = MapPointType.Unassigned, AbstractModel? model = null, bool showTransition = true)
    {
        if (model is EncounterModel encounter)
        {
            roomType = encounter.RoomType;
        }
        else if (model is EventModel)
        {
            roomType = RoomType.Event;
        }

        if (pointType == MapPointType.Unassigned)
        {
            pointType = roomType switch
            {
                RoomType.Monster => MapPointType.Monster,
                RoomType.Elite => MapPointType.Elite,
                RoomType.Boss => MapPointType.Boss,
                RoomType.Treasure => MapPointType.Treasure,
                RoomType.Shop => MapPointType.Shop,
                RoomType.Event => MapPointType.Unknown,
                RoomType.RestSite => MapPointType.RestSite,
                RoomType.Victory => MapPointType.Boss,
                RoomType.Unassigned => MapPointType.Unassigned,
                RoomType.Map => MapPointType.Unassigned
            };
        }

        if (CombatReplayWriter.IsEnabled)
        {
            CombatReplayWriter.RecordInitialState(ToSave());
        }

        ClearScreens();
        State!.AppendToMapPointHistory(pointType, roomType);

        NClimb.Instance?.GlobalUi.TopBar.FloorAndRoom.DebugSetMapPointTypeOverride(pointType);

        if (State.Map is MockActMap mockMap)
        {
            mockMap.MockCurrentMapPointType(pointType);
        }

        await EnterRoom(CreateRoom(roomType, model: model));
        await FadeIn(showTransition);
    }

    private async Task ExitCurrentRooms()
    {
        while (State!.CurrentRoomCount > 0)
        {
            await ExitCurrentRoom();
        }

        NClimb.Instance?.GlobalUi.TopBar.FloorAndRoom.DebugClearMapPointTypeOverride();
    }

    private async Task<AbstractRoom> ExitCurrentRoom()
    {
        AbstractRoom currentRoom = State!.PopCurrentRoom();
        await currentRoom.Exit(State);
        return currentRoom;
    }

    private async Task EnterRoomInternal(AbstractRoom room)
    {
        State!.PushRoom(room);

        if (room is CombatRoom { IsPreFinished: true } or EventRoom { IsPreFinished: true })
        {
            await room.Enter(State);
        }
        else
        {
            if (room is not MapRoom)
            {
                await Hook.BeforeRoomEntered(State, room);
            }

            await room.Enter(State);

            NClimbMusicController.Instance?.UpdateTrack();

            // Only mark the room as visited in the act if it's the first room in the stack.
            // Nested rooms don't count because they're always part of unique events or relic options.
            if (State.CurrentRoomCount == 1)
            {
                State.Act.MarkRoomVisited(room.RoomType);
            }
        }

        // Combat rooms are in charge of un-pausing the ActionExecutor themselves.
        if (room is not CombatRoom)
        {
            ActionExecutor.Unpause();
        }

        NClimbMusicController.Instance?.UpdateAmbience();
        RoomEntered?.Invoke();
    }

    /// <summary>
    /// Exit all the rooms that the player is currently in and enter the specified room.
    /// NOTE: If you want to enter a room WITHOUT exiting the current rooms first, call
    /// <see cref="EnterRoomWithoutExitingCurrentRoom"/> instead.
    /// </summary>
    /// <param name="room">Room to enter</param>
    public async Task EnterRoom(AbstractRoom room)
    {
        await ExitCurrentRooms();
        await EnterRoomInternal(room);
    }

    /// <summary>
    /// Enter the specified room without exiting any current rooms you may be in.
    /// IMPORTANT NOTE: If you are already in a room and want to exit it first, you should call <see cref="EnterRoom"/>
    /// instead.
    /// If you are already in a room, this will push the new room on top of the "room stack". This is to support effects
    /// where the player should enter a new sub-room, then return to the old one when they're done.
    /// For example, if the player has the Battle Horn relic, enters a rest site, and chooses "Fight", they should
    /// enter a combat room. Then, when they're done with the combat, it should be "popped off" the room stack, and they
    /// should transition back to the rest site.
    /// </summary>
    /// <param name="room"></param>
    public async Task EnterRoomWithoutExitingCurrentRoom(AbstractRoom room)
    {
        // Start syncing our state early (before the fadeout) so that it is hopefully available to all players by the
        // time we are ready to fade in.
        CombatStateSynchronizer.StartSync();

        await NGame.Instance!.Transition.RoomFadeOut();
        ClearScreens();

        await CombatStateSynchronizer.WaitForSync();
        State!.CurrentMapPointHistoryEntry!.RoomTypes.Add(room.RoomType);
        await EnterRoomInternal(room);

        await FadeIn();
    }

    public async Task EnterNextAct()
    {
        // Temporary logic for winning the game in the last act.
        // We do -1 because we don't increment the CurrentActIndex until after we enter the act.
        if (State!.CurrentActIndex >= State.Acts.Count - 1)
        {
            // If we're at the boss room, transition to the victory room first.
            if (State.CurrentRoom is not VictoryRoom)
            {
                if (TestMode.IsOff)
                {
                    await NGame.Instance!.Transition.RoomFadeOut();
                }

                ClearScreens();
                await EnterRoom(new VictoryRoom());
                await FadeIn();
            }
            // If we're at the victory room, kill all players and win the run.
            else
            {
                await TriggerVictory();
            }

            return;
        }

        // If we aren't at the last act, then go to the next act.
        await EnterAct(State.CurrentActIndex + 1);
    }

    public async Task EnterAct(int currentActIndex, bool doTransition = true)
    {
        // Note that this skips the fadeout if we are already faded out (happens in the character select screen)
        if (TestMode.IsOff)
        {
            await NGame.Instance!.Transition.RoomFadeOut();
        }

        ClearScreens();
        await ExitCurrentRooms();
        await DebugSetActDirectly(currentActIndex);

        if (currentActIndex == 0 && State!.ExtraFields.StartedWithNeow)
        {
            if (NClimb.Instance != null)
            {
                _ = TaskHelper.RunSafely(NClimb.Instance.GlobalUi.TopBar.Hp.LerpAtNeow());
                NMapScreen.Instance?.InitMarker(State.Map.StartingMapPoint.coord);
            }

            await EnterMapCoord(State.Map.StartingMapPoint.coord);
        }
        else
        {
            await EnterRoomInternal(new MapRoom());
            ActEntered?.Invoke();
            await FadeIn(doTransition);
        }

        await Hook.AfterActEntered(State!);
    }

    /// <summary>
    /// Should only be used for testing.
    /// </summary>
    /// <param name="actIndex">The act to set.</param>
    public async Task DebugSetActDirectly(int actIndex)
    {
        // Set the index of the next act.
        State!.CurrentActIndex = actIndex;

        AfterLocationChanged();

        await PreloadManager.LoadActAssets(State.Act);
        await GenerateMap();

        NMapScreen.Instance?.SetTravelEnabled(false);
        NClimbMusicController.Instance?.UpdateMusic();
    }

    /// <summary>
    /// Casey Note: Please add example. No idea what this means.
    /// This is called from NRewardsScreen when the screen is terminal (<see cref="NRewardsScreen._isTerminal"/>).
    /// Runs the correct logic for proceeding depending on the state of the climb.
    /// </summary>
    public async Task ProceedFromTerminalRewardsScreen()
    {
        if (State!.CurrentRoomCount > 1)
        {
            // If there are other rooms before us in the stack, resume the previous one.
            await ResumePreviousRoom();
        }
        else
        {
            // If we're in the only room in the stack, show the map so the player can choose what map point to go to
            // next.
            NMapScreen.Instance?.Open();
        }
    }

    // NOTE: Too vague, please add comments.
    private async Task ResumePreviousRoom()
    {
        ClearScreens();
        AbstractRoom exitedRoom = await ExitCurrentRoom();
        await State!.CurrentRoom!.Resume(exitedRoom, State);
        NClimbMusicController.Instance?.UpdateTrack();

        await FadeIn();
    }

    private void AfterLocationChanged()
    {
        // The order of these is important! Map selection must know about the current climb location before it receives
        // messages.
        MapSelectionSynchronizer.OnClimbLocationChanged(State!.CurrentLocation);
        ClimbLocationTargetedBuffer.OnClimbLocationChanged(State.CurrentLocation);
    }

    /// <summary>
    /// This is a placeholder function for when players beat Act 3.
    /// </summary>
    private async Task TriggerVictory()
    {
        NVictoryRoom.Instance?.VictoryTriggered();
        UpdatePlayerStatsInMapPointHistory();
        OnEnded(true);
        await GuaranteeKillAllPlayers();
    }

    /// <summary>
    /// Called when a combat is lost.
    /// </summary>
    /// <param name="combatState">The state of the combat that was lost.</param>
    public void OnCombatLost(CombatState combatState)
    {
        if (!IsInProgress) return;

        if (State!.CurrentMapPointHistoryEntry != null)
        {
            State.CurrentMapPointHistoryEntry!.TurnsTaken = combatState.RoundNumber;
        }

        UpdatePlayerStatsInMapPointHistory();
    }

    /// <summary>
    /// Abandons the climb.
    /// If in multiplayer, you should only call this on the host, and the climb abandonment will be synchronized across
    /// all peers. If called on a multiplayer client, an exception will be thrown.
    /// </summary>
    public void Abandon()
    {
        Log.Info("Abandoning an in-progress climb (player-initiated)");
        if (NetService.Type == NetGameType.Singleplayer)
        {
            TaskHelper.RunSafely(AbandonInternal());
        }
        else
        {
            ClimbLobby!.AbandonClimb();
        }
    }

    void IClimbLobbyListener.ClimbAbandoned()
    {
        Log.Info("The host told us to abandon the climb");
        TaskHelper.RunSafely(AbandonInternal());
    }

    private async Task AbandonInternal()
    {
        try
        {
            NCapstoneContainer.Instance!.Close();
            NMapScreen.Instance!.Close();
            ActionQueueSet.Reset();
        }
        catch (Exception e)
        {
            Log.Error($"Exception thrown while trying to abandon climb: {e}");
        }

        IsAbandoned = true;
        await GuaranteeKillAllPlayers();

        // If we're SP/Host, we just hit the abandon climb button, so it's self-explanatory that we abandoned climb.
        // If we're the client, there's no context for what just happened, so emit a popup
        if (NetService.Type == NetGameType.Client)
        {
            NErrorPopup? popup = NErrorPopup.Create(new NetErrorInfo(NetError.HostAbandoned, false));

            if (popup != null)
            {
                NModalContainer.Instance!.AddChildSafely(popup);
            }
        }
    }

    /// <summary>
    /// When you Abandon a Climb you are sentenced to death.
    /// </summary>
    private async Task GuaranteeKillAllPlayers()
    {
        foreach (Player player in State!.Players)
        {
            await CreatureCmd.Kill(player.Creature, true);
            await Cmd.CustomScaledWait(0.25f, 0.5f, 0.5f);
        }
    }

    private void StateDiverged(NetFullCombatState state)
    {
        if (NetService.Type == NetGameType.Replay) return;

        // TODO: This is not the final state of this. We should, at the very least, automatically gather logs and upload
        // them somewhere. Ideally, we would automatically re-synchronize the player's state with the host's.
        Log.Info("Abandoning climb and returning to main menu because our state diverged from host's");
        WriteReplay(true);

        // When the host disconnects us from the climb, we will display an error to the user
    }

    public void WriteReplay(bool stopRecording)
    {
        CombatReplayWriter.WriteReplay("user://replays/latest.mcr", stopRecording);
    }

    /// <summary>
    /// Cleans up the climb and disconnects us from any multiplayer peers.
    /// </summary>
    /// <param name="graceful">If true, messages are allowed to be sent before closing the multiplayer connection. Pass
    /// false if the game window is being closed.</param>
    public void CleanUp(bool graceful = true)
    {
        if (State == null) return;

        _climbHistoryWasUploaded = false;

        NAudioManager.Instance?.StopAllLoops();
        NOverlayStack.Instance?.Clear();
        NCapstoneContainer.Instance?.CleanUp();
        NMapScreen.Instance?.CleanUp();

        // if the application is closing don't bother with this
        // will cause you to see power removed vfx for a split second
        if (graceful)
        {
            CombatManager.Instance.Reset();
        }

        NControllerManager.Instance?.ClearScreens();

        CombatReplayWriter.Dispose();
        ActionQueueSynchronizer.Dispose();
        PlayerChoiceSynchronizer.Dispose();
        RewardSynchronizer.Dispose();
        RestSiteSynchronizer.Dispose();
        FlavorSynchronizer.Dispose();
        ChecksumTracker.Dispose();

        if (ClimbLobby != null)
        {
            ClimbLobby.RemotePlayerDisconnected -= RemotePlayerDisconnected;
            ClimbLobby.Dispose();
        }

        NetService.Disconnect(NetError.Quit, !graceful);

        LocalContext.NetId = null;
        LocManager.Instance.ClearClimbState();

        State = null;
    }

    public void OnEnded(bool isVictory)
    {
        // Check if we've already uploaded the climb history this session.
        // This occurs because we call OnEnded when the player dies and when we win and
        // provides safety for other potential goofs.
        if (_climbHistoryWasUploaded) return;

        _climbHistoryWasUploaded = true;

        SerializableClimb serializableClimb = ToSave();
        SaveManager.Instance.UpdateProgressSaveWithClimbData(serializableClimb, isVictory, State!);
        AchievementsHelper.AfterClimbEnded(State!, LocalContext.GetMe(State)!, isVictory);

        if (ShouldSave)
        {
            ClimbHistoryUtilities.CreateClimbHistoryEntry(serializableClimb, isVictory, IsAbandoned, NetService.Platform);

            // TODO: When metrics upload fails, this should not stop the entire end of game sequence.
            MetricUtilities.UploadClimbMetrics(serializableClimb, isVictory, NetService.NetId);

            if (NetService.Type == NetGameType.Singleplayer)
            {
                SaveManager.Instance.DeleteCurrentClimb();
            }
            else if (NetService.Type == NetGameType.Host)
            {
                SaveManager.Instance.DeleteCurrentMultiplayerClimb();
            }
        }

        if (DailyTime != null)
        {
            if (NetService.Type is NetGameType.Host or NetGameType.Singleplayer)
            {
                // In multiplayer climbs, the real score is uploaded only by the host
                ulong localPlayerId = LocalContext.NetId!.Value;
                int score = ScoreUtility.CalculateScore(serializableClimb, localPlayerId, isVictory);
                TaskHelper.RunSafely(DailyClimbUtility.UploadScore(DailyTime.Value, score, serializableClimb.Players));
            }
            else if (NetService.Type is NetGameType.Client)
            {
                // Multiplayer clients upload a sentinel value that only indicates that they cannot upload a new score for today
                TaskHelper.RunSafely(DailyClimbUtility.UploadScore(DailyTime.Value, ScoreUtility.clientScore, serializableClimb.Players));
            }
        }
    }

    private void UpdatePlayerStatsInMapPointHistory()
    {
        if (TestMode.IsOn) return;

        foreach (Player player in State!.Players)
        {
            PlayerMapPointHistoryEntry entry = State.CurrentMapPointHistoryEntry!.GetEntry(player.NetId);
            entry.CurrentGold = player.Gold;
            entry.CurrentHp = player.Creature.CurrentHp;
            entry.MaxHp = player.Creature.MaxHp;
        }
    }

    // Warning: Be careful when using this outside of Climb.cs. External functions should
    // call AscensionHelper.HasAscension instead.
    public bool HasAscension(AscensionLevel level)
    {
        if (!IsInProgress) return false;

        return AscensionManager.HasLevel(level);
    }

    public void ApplyAscensionEffects(Player player)
    {
        AscensionManager.ApplyEffectsTo(player);
    }

    public ClientRejoinResponseMessage GetRejoinMessage()
    {
        return new ClientRejoinResponseMessage
        {
            serializableClimb = ToSave(),
            combatState = NetFullCombatState.FromClimb(State!, null)
        };
    }

    public void LocalPlayerDisconnected(NetErrorInfo info)
    {
        // Remove all remote mouse cursors
        foreach (Player player in State!.Players)
        {
            if (!LocalContext.IsMe(player))
            {
                InputSynchronizer.OnPlayerDisconnected(player.NetId);
            }
        }

        // If we are abandoning due to the host reaching a game-over state, then do nothing; we should also reach the
        // game over state soon (or are already there). This prevents the following scenario:
        // - Client is in slow mode, Host is in fast mode
        // - Host reaches the game over screen and hits the "main menu" button very quickly.
        // - Client is still in combat (perhaps playing the enemy attack animations). They receive a notification that
        //   the host quit the game, which makes them quit the game too, before reaching the end game screen.
        // Instead, we send a special quit reason at game over that causes the client not to quit to the main menu as well.
        if (info.GetReason() == NetError.QuitGameOver) return;

        // If the climb was already abandoned by the host, then no need to do anything; we've already abandoned.
        if (IsAbandoned) return;

        // If the climb is already over (won or lost), then don't return to the main menu if the host leaves.
        if (State.IsGameOver) return;

        // Return to main menu and display error if necessary
        TaskHelper.RunSafely(ReturnToMainMenuWithError(info));
    }

    private void RemotePlayerDisconnected(ulong playerId)
    {
        InputSynchronizer.OnPlayerDisconnected(playerId);
    }

    private async Task ReturnToMainMenuWithError(NetErrorInfo info)
    {
        NCapstoneContainer.Instance?.Close();
        NMapScreen.Instance?.Close();
        ActionQueueSet.Reset();

        if (TestMode.IsOff)
        {
            await NGame.Instance!.ReturnToMainMenuAfterClimb();

            NErrorPopup? popup = NErrorPopup.Create(info);

            if (popup != null)
            {
                NGame.Instance.MainMenu!.AddChildSafely(popup);
            }
        }
    }

    /// <summary>
    /// THIS IS TEMPORARY AND SHOULD ONLY BE USED IN TESTS
    /// </summary>
    // ReSharper disable once ReturnTypeCanBeNotNullable
    public ClimbState? DebugOnlyGetState() => State;
}
