using System.Text.Json.Serialization;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;

namespace MegaCrit.Sts2.Core.Climbs.History;

public struct CardTransformationHistoryEntry(ModelId originalCard, ModelId finalCard) : IPacketSerializable
{
    [JsonPropertyName("original_card")]
    public ModelId OriginalCard { get; set; } = originalCard;

    [JsonPropertyName("final_card")]
    public ModelId FinalCard { get; set; } = finalCard;

    public void Serialize(PacketWriter writer)
    {
        writer.WriteModelEntry(OriginalCard);
        writer.WriteModelEntry(FinalCard);
    }

    public void Deserialize(PacketReader reader)
    {
        OriginalCard = reader.ReadModelIdAssumingType<CardModel>();
        FinalCard = reader.ReadModelIdAssumingType<CardModel>();
    }
}
