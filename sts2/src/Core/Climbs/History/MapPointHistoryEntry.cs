using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Climbs.History;

public class MapPointHistoryEntry : IPacketSerializable
{
    [JsonPropertyName("map_point_type")]
    public MapPointType MapPointType { get; set; }

    /// <summary>
    /// All the room types entered in this map point. Usually contains just 1, but can contain more when one type of
    /// room transitions into another (like when the Dense Vegetation event transitions into the Wrigglers combat).
    /// Accessing RoomTypes[0] will usually give you what you want, but keep an eye out for cases where you may care
    /// about other rooms entered in the same map point.
    /// </summary>
    [JsonPropertyName("room_types")]
    [JsonSerializeCondition(SerializationCondition.SaveIfNotCollectionEmptyOrNull)]
    public List<RoomType> RoomTypes { get; set; } = [];

    /// <summary>
    /// The model id of the encounter or event in this map point. ie CEREMONIAL_BEAST
    /// </summary>
    [JsonPropertyName("model_id")]
    public ModelId ModelId { get; set; } = ModelId.none;

    /// <summary>
    /// The model IDs of the monsters in the encounter, if this was an encounter.
    /// This contains the actual monsters that the encounter resolved to, if the encounter has random monsters.
    /// </summary>
    [JsonPropertyName("monster_ids")]
    [JsonSerializeCondition(SerializationCondition.SaveIfNotCollectionEmptyOrNull)]
    public List<ModelId> MonsterIds { get; set; } = [];

    // For combat
    [JsonPropertyName("turns_taken")]
    public int TurnsTaken { get; set; }

    [JsonPropertyName("player_stats")]
    public List<PlayerMapPointHistoryEntry> PlayerStats { get; set; } = [];

    // For serialization
    public MapPointHistoryEntry() { }

    public MapPointHistoryEntry(MapPointType mapPointType, IPlayerCollection playerCollection)
    {
        MapPointType = mapPointType;
        foreach (Player player in playerCollection.Players)
        {
            PlayerStats.Add(new PlayerMapPointHistoryEntry { PlayerId = player.NetId });
        }
    }

    public PlayerMapPointHistoryEntry GetEntry(ulong playerId)
    {
        PlayerMapPointHistoryEntry? playerEntry = PlayerStats.FirstOrDefault(e => e.PlayerId == playerId);
        if (playerEntry == null) throw new InvalidOperationException($"Player with ID {playerId} not found in player stats for this climb history!");

        return playerEntry;
    }

    public void Serialize(PacketWriter writer)
    {
        writer.WriteEnum(MapPointType);
        writer.WriteInt(RoomTypes.Count);

        foreach (RoomType type in RoomTypes)
        {
            writer.WriteEnum(type);
        }

        writer.WriteFullModelId(ModelId);
        writer.WriteModelEntriesInList(MonsterIds);
        writer.WriteInt(TurnsTaken);
        writer.WriteList(PlayerStats);
    }

    public void Deserialize(PacketReader reader)
    {
        MapPointType = reader.ReadEnum<MapPointType>();
        int roomTypeCount = reader.ReadInt();

        for (int i = 0; i < roomTypeCount; i++)
        {
            RoomTypes.Add(reader.ReadEnum<RoomType>());
        }

        ModelId = reader.ReadFullModelId();
        MonsterIds = reader.ReadModelIdListAssumingType<MonsterModel>();
        TurnsTaken = reader.ReadInt();
        PlayerStats = reader.ReadList<PlayerMapPointHistoryEntry>();
    }
}
