using System;
using System.Collections.Generic;
using System.Linq;
using JetBrains.Annotations;
using MegaCrit.Sts2.Core.Climbs.History;

namespace MegaCrit.Sts2.Core.Climbs.Metrics;

public struct AncientMetric
{
    [UsedImplicitly]
    public readonly string picked;

    [UsedImplicitly]
    public readonly List<string> skipped;

    /// <param name="entry">The map point history entry.</param>
    /// <param name="playerEntry">The record of the player actions taken by the local player in the map point.</param>
    /// <exception cref="InvalidOperationException"></exception>
    public AncientMetric(MapPointHistoryEntry entry, PlayerMapPointHistoryEntry playerEntry)
    {
        AncientChoiceHistoryEntry? pickedEntry = playerEntry.AncientChoices.FirstOrDefault(o => o.WasChosen);

        if (pickedEntry == null) throw new InvalidOperationException($"Failed to find chosen ancient choice! {entry.ModelId} {playerEntry.PlayerId}");

        picked = pickedEntry.TextKey;
        skipped = playerEntry.AncientChoices.Where(o => !o.Was<PERSON>).Select(o => o.Text<PERSON>ey).ToList();
    }
}
