using System.Collections.Generic;
using JetBrains.Annotations;
using MegaCrit.Sts2.Core.Climbs.History;

namespace MegaCrit.Sts2.Core.Climbs.Metrics;

// we need a separate metric for this because
// you may be able to select multiple cards
public struct CardChoiceMetric
{
    [UsedImplicitly]
    public readonly List<string> picked;

    [UsedImplicitly]
    public readonly List<string> skipped;

    public CardChoiceMetric(List<ModelChoiceHistoryEntry> choices)
    {
        picked = [];
        skipped = [];

        foreach (ModelChoiceHistoryEntry choice in choices)
        {
            if (choice.wasPicked)
            {
                picked.Add(choice.choice.Entry);
            }
            else
            {
                skipped.Add(choice.choice.Entry);
            }
        }
    }
}
