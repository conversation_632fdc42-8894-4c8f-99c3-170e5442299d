namespace MegaCrit.Sts2.Core.Climbs;

public enum OddsRollMethod
{
    None = 0,
    // Normal rolling method. Used conjunction with CombatGenerationSource.
    // Rolls the current odds for the generation source and updates the odds based on the result.
    Standard = 1,

    // Forces rolling card rarity odds as if it were rolling for base regular encounter odds. Only rolls the base odds
    // and does not update the odds based on what is rolled.
    RegularEncounter = 2,
}
