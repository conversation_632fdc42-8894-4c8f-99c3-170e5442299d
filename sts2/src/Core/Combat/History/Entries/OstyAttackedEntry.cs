using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Text;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Monsters;

namespace MegaCrit.Sts2.Core.Combat.History.Entries;

public class OstyAttackedEntry : CombatHistoryEntry
{
    [SuppressMessage("ReSharper", "MemberCanBePrivate.Global", Justification = "Public API")]
    public IReadOnlyList<Creature> Targets { get; }

    public override string Description
    {
        get
        {
            Osty osty = (Osty)Actor.Monster!;
            StringBuilder builder = new($"{osty.Id.Entry} attacked");

            foreach (Creature target in Targets)
            {
                string targetName = target.IsPlayer ? target.Player!.Character.Id.Entry : target.Monster!.Id.Entry;
                builder.Append($"targeting {targetName}");
            }

            return builder.ToString();
        }
    }

    public OstyAttackedEntry(Osty osty, IReadOnlyList<Creature> targets, int roundNumber, CombatSide currentSide, CombatHistory history) :
        base(osty.Creature, roundNumber, currentSide, history)
    {
        Targets = targets;
    }
}