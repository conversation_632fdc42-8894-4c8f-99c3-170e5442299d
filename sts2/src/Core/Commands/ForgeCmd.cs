using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Commands;

public static class ForgeCmd
{
    /// <summary>
    /// Applies Forge to the player. Adds Sovereign Blade to their hand if they haven't
    /// forged this combat, and adds the amount of damage to it.
    /// </summary>
    /// <param name="amount">Amount of damage to add to Forge.</param>
    /// <param name="player">Player to give the stars to.</param>
    /// <returns>All of the player's Sovereign Blade cards.</returns>
    public static async Task<IEnumerable<SovereignBlade>> Forge(decimal amount, Player player)
    {
        if (CombatManager.Instance.IsAboutToEnd) return [];

        List<SovereignBlade> currentBlades = GetSovereignBlades(player).ToList();

        if (currentBlades.Count == 0)
        {
            SovereignBlade sovereignBlade = player.Creature.CombatState!.CreateCard<SovereignBlade>(player);
            sovereignBlade.CreatedThroughForge = true;
            await CardPileCmd.AddGeneratedCardToCombat(sovereignBlade, PileType.Hand, true);
        }
        else
        {
            // Move any Exhausted Sovereign Blades back to the player's hand
            List<SovereignBlade> exhaustedBlades = currentBlades.Where(c => c.Pile?.Type == PileType.Exhaust).ToList();

            if (exhaustedBlades.Count > 0)
            {
                await CardPileCmd.Add(exhaustedBlades, PileType.Hand);
            }
        }

        IncrementSovereignBladeDamage(amount, player);

        await Hook.AfterForge(player.Creature.CombatState!, amount, player);

        return GetSovereignBlades(player);
    }

    public static void IncrementSovereignBladeDamage(decimal amount, Player player)
    {
        // we check this here just in case we proc Forge off of a card play and the dupe that is heading towards
        // the limbo pile is still in your play pile, meaning it would still get caught by AllCards
        foreach (SovereignBlade card in GetSovereignBlades(player))
        {
            card.AddDamage(amount);
            PlayCombatRoomForgeVfx(player, card);
        }

        PreviewSovereignBlade(GetSovereignBlades(player).ToList());
    }

    public static void MultiplySovereignBladeDamage(decimal amount, Player player)
    {
        // we check this here just in case we proc Forge off of a card play and the dupe that is heading towards
        // the limbo pile is still in your play pile, meaning it would still get caught by AllCards
        foreach (SovereignBlade card in GetSovereignBlades(player))
        {
            card.MultiplyDamage(amount);
            PlayCombatRoomForgeVfx(player, card);
        }

        PreviewSovereignBlade(GetSovereignBlades(player).ToList());
    }

    private static IEnumerable<SovereignBlade> GetSovereignBlades(Player player) => player.PlayerCombatState!.AllCards.Where(c => !c.IsDupe).OfType<SovereignBlade>();

    private static void PreviewSovereignBlade(List<SovereignBlade> sovereignBlades)
    {
        if (TestMode.IsOn) return;
        if (!LocalContext.IsMine(sovereignBlades[0])) return; // assumes if 1 sovereign blade belongs to you, that they all do

        IEnumerable<SovereignBlade> inHand = sovereignBlades.Where(c => c.Pile!.Type == PileType.Hand);
        IEnumerable<SovereignBlade> outOfHand = sovereignBlades.Where(c => c.Pile!.Type != PileType.Hand);

        foreach (SovereignBlade sovereignBlade in inHand)
        {
            NCardSmithVfx inHandVfx = NCardSmithVfx.Create(NCombatRoom.Instance!.Ui.Hand.GetCard(sovereignBlade)!)!;
            NClimb.Instance!.GlobalUi.AboveTopBarVfxContainer.AddChildSafely(inHandVfx);
        }

        if (outOfHand.Any())
        {
            NCardSmithVfx vfx = NCardSmithVfx.Create(outOfHand)!;
            NClimb.Instance!.GlobalUi.CardPreviewContainer.AddChildSafely(vfx);
        }
    }

    public static void PlayCombatRoomForgeVfx(Player player, CardModel card)
    {
        NCreature? creature = NCombatRoom.Instance?.GetCreatureNode(player.Creature);
        if (creature == null) return;

        NSovereignBladeVfx? blade = SovereignBlade.GetVfxNode(player, card);

        bool newBlade = blade == null;
        if (newBlade)
        {
            blade = NSovereignBladeVfx.Create(card);
            creature.AddChildSafely(blade);
            blade!.Position = Vector2.Zero;
        }

        blade!.Forge(card.DynamicVars.Damage.IntValue, newBlade);

        if (newBlade)
        {
            // Balance orbit progress of all Sovereign Blade VFXs.
            List<SovereignBlade> currentBlades = GetSovereignBlades(player).ToList();

            for (int i = 0; i < currentBlades.Count; i++)
            {
                NSovereignBladeVfx? vfx = SovereignBlade.GetVfxNode(player, currentBlades[i]);

                if (vfx != null)
                {
                    vfx.OrbitProgress = i / (float)currentBlades.Count;
                }
            }
        }
    }
}
