using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Screens;
using MegaCrit.Sts2.Core.Odds;
using MegaCrit.Sts2.Core.Rewards;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Commands;

public static class RewardsCmd
{
    /// <summary>
    /// Generate a list of rewards to offer for the specified room.
    /// </summary>
    /// <param name="player">The player for which to generate rewards.</param>
    /// <param name="room">Room to generate rewards for.</param>
    /// <returns>Rewards that should be offered for the room.</returns>
    public static async Task<IReadOnlyList<Reward>> GenerateForRoom(Player player, AbstractRoom room)
    {
        if (room.RoomType == RoomType.Boss && player.ClimbState.CurrentActIndex >= player.ClimbState.Acts.Count - 1)
        {
            // No rewards for the boss in the final act
            return [];
        }

        List<Reward> rewards = GenerateRewardsFor(player, room);
        HashSet<Reward> initialRewards = rewards.ToHashSet();

        foreach (Reward reward in rewards)
        {
            await reward.Populate();
        }

        IEnumerable<AbstractModel> modifiers = Hook.ModifyRewards(player.ClimbState, player, rewards, room);

        // Populate any new rewards that need it.
        foreach (Reward newReward in rewards.Except(initialRewards))
        {
            if (newReward.IsPopulated) continue;
            await newReward.Populate();
        }

        await Hook.AfterModifyingRewards(player.ClimbState, modifiers);

        rewards.Sort((x, y) => GetRewardIndex(x).CompareTo(GetRewardIndex(y)));

        // This is for if we get additional rewards from things like Forbidden Grimoire
        if (room is CombatRoom combatRoom)
        {
            if (combatRoom.ExtraRewards.TryGetValue(player, out List<Reward>? value))
            {
                rewards.AddRange(value);
            }
        }

        return rewards;
    }

    /// <returns>An index to use with CompareTo to order rewards.</returns>
    private static int GetRewardIndex(Reward reward)
    {
        // The only real important ordering here is relics, which we want to sort after cards. Everything else is arbitrary
        return reward switch
        {
            LinkedRewardSet linkedRewardSet => linkedRewardSet.Rewards.Max(GetRewardIndex),
            PotionReward => 1,
            GoldReward => 2,
            RelicReward => 3,
            CardReward => 4,
            _ => throw new ArgumentOutOfRangeException(nameof(reward))
        };
    }

    /// <summary>
    /// Offer a set of rewards to the player.
    /// If the player is not the local player, nothing happens.
    /// </summary>
    /// <param name="player">Player who triggered the rewards.</param>
    /// <param name="rewards">Rewards to offer.</param>
    /// <param name="isTerminal">Whether or not the rewards screen is terminal. See NRewardsScreen.IsTerminal.</param>
    public static async Task Offer(Player player, IReadOnlyList<Reward> rewards, bool isTerminal)
    {
        await Hook.BeforeRewardsOffered(player.ClimbState, player, rewards);

        foreach (Reward reward in rewards)
        {
            if (reward.IsPopulated) continue;

            await reward.Populate();
        }

        if (LocalContext.IsMe(player))
        {
            if (TestMode.IsOn)
            {
                // Test mode, automatically give all rewards to the player.
                foreach (Reward reward in rewards)
                {
                    await reward.OnSelectWrapper();
                }
            }
            else
            {
                // Game mode, let the player choose.
                NRewardsScreen rewardsScreen = NRewardsScreen.ShowScreen(isTerminal, player.ClimbState);
                rewardsScreen.SetRewards(rewards);
                await rewardsScreen.ClosedTask;
            }
        }
    }

    /// <summary>
    /// Generates a list of rewards to use for the specified room.
    /// This list will include extra options added by models like the Prayer Wheel relic if the player has them.
    /// Calling this may increment RNG counters and make other climb state changes.
    /// </summary>
    /// <param name="player">Player to generate rewards for.</param>
    /// <param name="room">Room to generate rewards for.</param>
    /// <returns>List of rewards.</returns>
    private static List<Reward> GenerateRewardsFor(Player player, AbstractRoom room)
    {
        if (ClimbManager.Instance == null) throw new InvalidOperationException("Only valid during a climb.");

        List<Reward> rewards = [];

        switch (room)
        {
            case CombatRoom combatRoom:
                switch (room.RoomType)
                {
                    case RoomType.Monster:
                        if (combatRoom.GoldProportion > 0)
                        {
                            GoldReward goldReward = new(
                                (int)Math.Round(GoldReward.defaultMinGoldAmount * combatRoom.GoldProportion),
                                (int)Math.Round(GoldReward.defaultMaxGoldAmount * combatRoom.GoldProportion),
                                player
                            );
                            rewards.Add(goldReward);
                        }

                        RollForPotionAndAddTo(rewards, player, room.RoomType);
                        rewards.Add(new CardReward(CardCreationSource.RegularEncounter, 3, player));
                        break;
                    case RoomType.Elite:
                        rewards.Add(new GoldReward(35, 45, player));
                        RollForPotionAndAddTo(rewards, player, room.RoomType);
                        rewards.Add(new CardReward(CardCreationSource.EliteEncounter, 3, player));
                        rewards.Add(new RelicReward(player));
                        break;
                    case RoomType.Boss:
                        rewards.Add(new GoldReward(100, player));
                        RollForPotionAndAddTo(rewards, player, room.RoomType);
                        rewards.Add(new CardReward(CardCreationSource.BossEncounter, 3, player));
                        break;
                }

                break;
            case TreasureRoom:
                // By default, treasure room handles rewards itself, but extra rewards may be granted by relics so we
                // return empty rewards here
                break;
            default:
                throw new InvalidOperationException($"Tried to generate a reward for invalid room type: {room.GetType().Name}");
        }

        return rewards;
    }

    private static void RollForPotionAndAddTo(ICollection<Reward> rewards, Player player, RoomType roomType)
    {
        PotionRewardOdds odds = player.PlayerOdds.PotionReward;
        AscensionManager ascension = ClimbManager.Instance.AscensionManager;
        if (!odds.Roll(player, ascension, roomType)) return;

        rewards.Add(new PotionReward(player));
    }
}
