namespace MegaCrit.Sts2.Core.ControllerInput;

public static class MegaInput
{
    // maps to godot default navigation input controls. Make sure to remove the "ui_" for the var name
    public const string cancel = "ui_cancel";
    public const string up = "ui_up";
    public const string down = "ui_down";
    public const string left = "ui_left";
    public const string right = "ui_right";
    public const string accept = "ui_accept"; // confirming upgrades in rest sites, proceeding between rooms
    public const string select = "ui_select"; // ie selecting cards to be played, selecting rewards to take

    // maps to custom input/hotkeys. Make sure to remove the "mega_" for the var name
    public const string topPanel = "mega_top_panel";
    public const string viewDrawPile = "mega_view_draw_pile";
    public const string viewDiscardPile = "mega_view_discard_pile";
    public const string viewDeckAndTabLeft = "mega_view_deck_and_tab_left";
    public const string viewExhaustPileAndTabRight = "mega_view_exhaust_pile_and_tab_right";
    public const string viewMap = "mega_view_map";
    public const string pauseAndBack = "mega_pause_and_back";
    public const string back = "mega_back"; // this is the back button on the mouse. We don't want this to trigger the pause screen
    public const string endTurn = "mega_end_turn";

    public static string[] AllInputs =>
    [
        cancel,
        up,
        down,
        left,
        right,
        accept,
        select,
        viewDeckAndTabLeft,
        topPanel,
        viewDrawPile,
        viewDiscardPile,
        viewExhaustPileAndTabRight,
        viewMap,
        pauseAndBack,
        endTurn,
    ];
}
