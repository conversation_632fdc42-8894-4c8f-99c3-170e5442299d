namespace MegaCrit.Sts2.Core.Debug;

public static class DebugHotkey
{
    public const string hideCombatUi = "debug_hide_combat_ui";
    public const string hideEventUi = "debug_hide_event_ui";
    public const string hideHand = "debug_hide_hand";
    public const string hideHoverTips = "debug_hide_hovertips";
    public const string hideHpBars = "debug_hide_hp_bars";
    public const string hideIntents = "debug_hide_intents";
    public const string hidePlayContainer = "debug_hide_play_container";
    public const string hideProceedButton = "debug_hide_proceed_button";
    public const string hideRestSite = "debug_hide_rest_site";
    public const string hideTargetingUi = "debug_hide_targeting_ui";
    public const string hideTextVfx = "debug_hide_text_vfx";
    public const string hideTopBar = "debug_hide_top_bar";
    public const string hideVersionInfo = "debug_hide_version_info";
    public const string slowRewards = "debug_slow_rewards";
    public const string speedDown = "debug_speed_down";
    public const string speedUp = "debug_speed_up";
    public const string unlockCharacters = "debug_unlock_characters";
}
