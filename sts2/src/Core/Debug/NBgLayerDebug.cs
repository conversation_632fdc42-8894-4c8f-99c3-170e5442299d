using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Logging;

namespace MegaCrit.Sts2.Core.Debug;

[Tool]
// This file is only used in debug builds because it's used to add debug layers in the editor
public partial class NBgLayerDebug : Control
{
    private const string _layerNodePrefix = "Layer_";
    private PackedScene? _layerA;
    private PackedScene? _layerB;
    private PackedScene? _layerC;

    public enum LayerVisibility
    {
        A,
        B,
        C
    }

    private LayerVisibility _visibleLayer = LayerVisibility.A;

    [Export]
    public PackedScene? LayerA
    {
        get => _layerA;
        set => SetLayer(ref _layerA, value);
    }

    [Export]
    public PackedScene? LayerB
    {
        get => _layerB;
        set => SetLayer(ref _layerB, value);
    }

    [Export]
    public PackedScene? LayerC
    {
        get => _layerC;
        set => SetLayer(ref _layerC, value);
    }

    [Export]
    public LayerVisibility VisibleLayer
    {
        get => _visibleLayer;
        set
        {
            _visibleLayer = value;
            if (Engine.IsEditorHint())
            {
                UpdateLayers();
            }
        }
    }

    private void SetLayer(ref PackedScene? layerField, PackedScene? value)
    {
        if (layerField == value) return;

        layerField = value;
        if (!Engine.IsEditorHint()) return;
        UpdateLayers();
    }

    private void UpdateLayers()
    {
        ClearLayers();
        if (_visibleLayer == LayerVisibility.A)
        {
            AddLayer(LayerVisibility.A, _layerA);
        }

        if (_visibleLayer == LayerVisibility.B)
        {
            AddLayer(LayerVisibility.B, _layerB);
        }

        if (_visibleLayer == LayerVisibility.C)
        {
            AddLayer(LayerVisibility.C, _layerC);
        }
    }

    private void AddLayer(LayerVisibility name, PackedScene? layerPath)
    {
        if (layerPath == null)
        {
            Log.Info("Skipping null layer path");
            return;
        }

        Control layer = layerPath.Instantiate<Control>();
        layer.Name = ToLayerName(name);
        this.AddChildSafely(layer);
    }

    private static string ToLayerName(LayerVisibility layer) => $"{_layerNodePrefix}{layer}";

    private IEnumerable<Control> GetLayerNodes()
    {
        foreach (Node child in GetChildren())
        {
            if (child.Name.ToString().StartsWith(_layerNodePrefix))
            {
                yield return (Control)child;
            }
        }
    }

    private void ClearLayers()
    {
        foreach (Control child in GetLayerNodes())
        {
            this.RemoveChildSafely(child);
            child.QueueFree();
        }
    }
}
