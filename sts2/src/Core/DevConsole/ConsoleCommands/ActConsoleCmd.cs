using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Nodes.Screens.Map;

namespace MegaCrit.Sts2.Core.DevConsole.ConsoleCommands;

// ReSharper disable once UnusedType.Global
// Reason: Used in reflection
public class ActConsoleCmd : AbstractConsoleCmd
{
    public override string CmdName => "act";
    public override string Args => $"<int: act>";
    public override string Description => "Jumps to an act";
    public override bool IsNetworked => true;

    public override CmdResult Process(Player? issuingPlayer, string[] args)
    {
        if (!int.TryParse(args[0], out int act))
        {
            return new CmdResult(false, "The first argument must be an int.");
        }

        if (issuingPlayer?.ClimbState == null)
        {
            return new CmdResult(false, "This command only works during a climb.");
        }

        int actCount = issuingPlayer.ClimbState.Acts.Count;
        if (act > actCount || act < 1)
        {
            return new CmdResult(false, $"The act you are trying to navigate to does not exist. Select act indexes between: 1-{actCount}");
        }

        int actIndex = act - 1; // convert the act index to the act index
        Task task = NextAct(actIndex);
        return new CmdResult(task, true, $"Navigated to act '{act}'.");
    }

    private static async Task NextAct(int actIndex)
    {
        NMapScreen.Instance!.SetTravelEnabled(true);
        await ClimbManager.Instance.EnterAct(actIndex);
    }

    public override void AutoComplete(Player? localPlayer, ref string inputBuffer, ref string outputBuffer, string[] parsedArgs) { }
}
