using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Exceptions;

namespace MegaCrit.Sts2.Core.DevConsole.ConsoleCommands;

// ReSharper disable once UnusedType.Global
// Reason: Used in reflection
public class EnchantConsoleCmd : AbstractConsoleCmd
{
    public override string CmdName => "enchant";
    public override string Args => $"<id:string> [amount:int] [hand-index:int]";
    public override string Description => "Enchants a card in the player's hand with the specified enchantment.";
    public override bool IsNetworked => true;

    public override CmdResult Process(Player? issuingPlayer, string[] args)
    {
        if (args.Length == 0) return new CmdResult(false, "Must specify an enchantment ID!");
        if (!CombatManager.Instance.IsInProgress) return new CmdResult(false, "Combat is not currently in progress!");

        ModelId enchantmentId = new(ModelId.SlugifyCategory<EnchantmentModel>(), args[0].ToUpper());
        EnchantmentModel enchantment;

        try
        {
            enchantment = ModelDb.GetById<EnchantmentModel>(enchantmentId).ToMutable();
        }
        catch (ModelNotFoundException)
        {
            return new CmdResult(false, $"Enchantment '{enchantmentId.Entry}' not found");
        }

        int enchantmentCount = 0;
        int handIndex = 0;

        if (args.Length > 1)
        {
            int.TryParse(args[1], out enchantmentCount);
        }

        if (args.Length > 2)
        {
            int.TryParse(args[2], out handIndex);
        }

        CardPile hand = PileType.Hand.GetPile(issuingPlayer!);
        IReadOnlyList<CardModel> cards = hand.Cards;
        int handSize = cards.Count;

        if (handIndex >= handSize)
        {
            return new CmdResult(false, $"The index={handIndex} exceeds the hand size={handSize}.");
        }

        CardModel card = hand.Cards[handIndex];
        CardCmd.Enchant(enchantment, card, enchantmentCount);

        return new CmdResult(true, $"Enchanted card {card.Title} with {enchantmentCount} {enchantment.Title.GetFormattedText()}");
    }

    public override void AutoComplete(Player? localPlayer, ref string inputBuffer, ref string outputBuffer, string[] parsedArgs)
    {
        // Do the autocomplete for the enchantments
        List<string> names = ModelDb
            .DebugEnchantments
            .Select(e => e.Id.Entry)
            .ToList();

        if (parsedArgs.Length == 0 || string.IsNullOrWhiteSpace(parsedArgs[0]))
        {
            outputBuffer = string.Join("\n", names);
        }
        else
        {
            DevConsole.PartialComplete(parsedArgs.Last(), names, ref inputBuffer, ref outputBuffer);
        }
    }
}
