using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using static MegaCrit.Sts2.Core.Commands.CreatureCmd;

namespace MegaCrit.Sts2.Core.DevConsole.ConsoleCommands;

// ReSharper disable once UnusedType.Global
// Reason: Used in reflection
public class KillConsoleCmd : AbstractConsoleCmd
{
    public override string CmdName => "kill";
    public override string Args => $"<target-index:int>|'all'";
    public override string Description => "Will kill one target if the index is given, or all if 'all', or the first if no arguments.";
    public override bool IsNetworked => true;

    public override CmdResult Process(Player? issuingPlayer, string[] args)
    {
        if (!CombatManager.Instance.IsInProgress) return new CmdResult(false, "This doesn't appear to be a combat!");

        List<Creature> killed = [];
        IReadOnlyList<Creature> enemies = CombatManager.Instance.DebugOnlyGetState()!.Enemies.ToList();

        if (args.Length > 0 && args[0].Equals("all"))
        {
            // Kill all enemies
            foreach (Creature enemy in enemies)
            {
                killed.Add(enemy);
            }
        }
        else if (args.Length > 0 && int.TryParse(args[0], out int targetIndex))
        {
            // Kill a specific enemy
            if (targetIndex >= enemies.Count)
            {
                return new CmdResult(false, $"The target index='{targetIndex}' exceeds the enemy count='{enemies.Count}'");
            }

            killed.Add(enemies[targetIndex]);
        }
        else
        {
            // Kill the first enemy
            killed.Add(enemies[0]);
        }

        IEnumerable<string> creatureNames = killed
            .Select(c => c.Monster)
            .Where(m => m != null)
            .Select(m => m!.Id.Entry.ToString());

        TaskHelper.RunSafely(DoKill(killed));
        return new CmdResult(true, $"Killed: [{string.Join(",", creatureNames)}]");
    }

    private async Task DoKill(List<Creature> toKill)
    {
        foreach (Creature creature in toKill)
        {
            await Kill(creature);
        }

        await CombatManager.Instance.CheckWinCondition();
    }

    public override void AutoComplete(Player? localPlayer, ref string inputBuffer, ref string outputBuffer, string[] parsedArgs)
    {
        // We only auto-complete the first argument
        if (parsedArgs.Length != 1)
        {
            return;
        }

        string firstArg = parsedArgs[0];
        // Check if the first argument is an int, and if so skip since we can't complete an int
        if (int.TryParse(firstArg, out int _))
        {
            return;
        }

        // Check if the first argument is a substring of the word 'all', if so, auto-complete
        if (firstArg.Length > 0 && "all".Contains(firstArg, System.StringComparison.CurrentCultureIgnoreCase))
        {
            List<string> possibleTokens = ["all"];
            DevConsole.PartialComplete(firstArg, possibleTokens, ref inputBuffer, ref outputBuffer);
        }
    }
}
