using System;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Logging;

namespace MegaCrit.Sts2.Core.DevConsole.ConsoleCommands;

// ReSharper disable once UnusedType.Global
// Reason: Used in reflection
public class LogConsoleCmd : AbstractConsoleCmd
{
    public override string CmdName => "log";
    public override string Args => "[type:string] <level:string>";
    public override string Description => $"Set log level for specific log types. Type can be: {string.Join(",", Enum.GetNames<LogType>())}. Levels can be: {string.Join(",", Enum.GetNames<LogLevel>())}";
    public override bool IsNetworked => false;

    public override CmdResult Process(Player? issuingPlayer, string[] args)
    {
        if (args.Length <= 0) return new CmdResult(false, "At least one arg must be supplied!");

        LogType? logType;

        if (TryParseEnumCaseInsensitive(args[0], out LogLevel? logLevel))
        {
            logType = LogType.Generic;
        }
        else if (TryParseEnumCaseInsensitive(args[0], out logType))
        {
            if (args.Length <= 0) return new CmdResult(false, "Must supply a log level as the second argument!");

            if (!TryParseEnumCaseInsensitive(args[1], out logLevel))
            {
                return new CmdResult(false, $"Second argument '{args[1]}' could not be parsed as a log level!");
            }
        }
        else
        {
            return new CmdResult(false, $"First argument '{args[0]}' could not be parsed as either a log level or type!");
        }

        Logger.logLevelTypeMap[logType!.Value] = logLevel!.Value;
        return new CmdResult(true, $"Logging level for {logType} set to {logLevel}");
    }

    // Like Enum.Parse but case insensitive
    public static bool TryParseEnumCaseInsensitive<T>(string str, out T? enumVal) where T : struct, Enum
    {
        foreach (T val in Enum.GetValues<T>())
        {
            if (!str.Equals(val.ToString(), StringComparison.CurrentCultureIgnoreCase)) continue;

            enumVal = val;
            return true;
        }

        enumVal = null;
        return false;
    }

    public override void AutoComplete(Player? localPlayer, ref string inputBuffer, ref string outputBuffer, string[] parsedArgs) { }
}
