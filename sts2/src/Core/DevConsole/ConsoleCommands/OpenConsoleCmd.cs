using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;
using Godot;
using MegaCrit.Sts2.Core.Entities.Players;

namespace MegaCrit.Sts2.Core.DevConsole.ConsoleCommands;

// ReSharper disable once UnusedType.Global
// Reason: Used in reflection
public class OpenConsoleCmd : AbstractConsoleCmd
{
    public override string CmdName => "open";
    public override string Args => $"logs|saves|root|build-logs";
    public override string Description => "Opens a common path in the local OS file browser.";
    public override bool IsNetworked => false;

    public override CmdResult Process(Player? issuingPlayer, string[] args)
    {
        List<string> options = ["logs", "saves", "root", "build-logs"];
        if (args.Length < 1)
        {
            return new CmdResult(false, $"No argument specified.\n{Args}");
        }

        if (!options.Contains(args[0]))
        {
            return new CmdResult(false, $"Argument '{args[0]}' unrecognized.\n{Args}");
        }

        string? userDataDir = OS.GetUserDataDir();
        if (userDataDir == null)
        {
            return new CmdResult(false, "Unable to open the user data directory.");
        }

        string dataDir = OS.GetDataDir();
        string pathToOpen = args[0] switch
        {
            "logs" => Path.Combine(userDataDir, "logs"),
            "saves" => Path.Combine(userDataDir, "saves"),
            "root" => userDataDir,
            "build-logs" => Path.Combine(dataDir, "Godot", "mono", "build_logs"),
            _ => throw new ArgumentOutOfRangeException()
        };

        // Make the path separators consistent if it's windows
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        {
            pathToOpen = pathToOpen.Replace('/', '\\');
        }

        Error error = OS.ShellShowInFileManager(pathToOpen);
        if (error != Error.Ok)
        {
            return new CmdResult(false, $"Error {error}: Cannot open OS file manager.");
        }

        return new CmdResult(true, $"Opened '{pathToOpen}'");
    }

    public override void AutoComplete(Player? localPlayer, ref string inputBuffer, ref string outputBuffer, string[] parsedArgs)
    {
        List<string> possibleTokens = ["logs", "saves", "root", "build-logs"];
        DevConsole.PartialComplete(parsedArgs[0], possibleTokens, ref inputBuffer, ref outputBuffer);
    }
}
