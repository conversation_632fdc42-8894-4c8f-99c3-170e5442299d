using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.DevConsole.ConsoleCommands;

// ReSharper disable once UnusedType.Global
// Reason: Used in reflection
public class RoomConsoleCmd : AbstractConsoleCmd
{
    public override string CmdName => "room";
    public override string Args => "<id:string>";
    public override string Description => "Jumps a player to a specific room.";
    public override bool IsNetworked => true;

    public override CmdResult Process(Player? issuingPlayer, string[] args)
    {
        if (args.Length == 0) return new CmdResult(false, "No room name specified.");
        if (!ClimbManager.Instance.IsInProgress) return new CmdResult(false, "A climb is currently not in progress!");

        string roomName = args[0].ToUpper();
        bool success = Enum.TryParse(roomName, ignoreCase: true, out RoomType roomType);

        if (!success)
        {
            return new CmdResult(false, $"Room '{roomName}' not found");
        }

        Task task = ClimbManager.Instance.EnterRoomDebug(roomType);

        return new CmdResult(task, true, $"Jumped to room: '{roomType}'");
    }

    public override void AutoComplete(Player? localPlayer, ref string inputBuffer, ref string outputBuffer, string[] parsedArgs)
    {
        // Do the autocomplete for the rooms
        List<string> names = Enum.GetNames(typeof(RoomType))
            .Where(n => !n.Equals(nameof(RoomType.Unassigned)))
            .ToList();

        if (parsedArgs.Length == 0 || string.IsNullOrWhiteSpace(parsedArgs[0]))
        {
            outputBuffer = string.Join("\n", names);
        }
        else
        {
            DevConsole.PartialComplete(parsedArgs.Last(), names, ref inputBuffer, ref outputBuffer);
        }
    }
}
