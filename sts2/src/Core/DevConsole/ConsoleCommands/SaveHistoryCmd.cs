using System;
using System.IO;
using Godot;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Nodes.Debug;

namespace MegaCrit.Sts2.Core.DevConsole.ConsoleCommands;

// ReSharper disable once UnusedType.Global
// Reason: Used in reflection
public class SaveHistoryCmd : AbstractConsoleCmd
{
    public override string CmdName => "log-history";
    public override string Args => "";
    public override string Description => "Saves command history and opens a common path to it in the local OS file browser.";
    public override bool IsNetworked => false;

    public override CmdResult Process(Player? issuingPlayer, string[] args)
    {
        string? dataDir = OS.GetUserDataDir();

        string pathToOpen = Path.Combine(dataDir, "debug_history");
        Directory.CreateDirectory(pathToOpen);

        Error error = OS.ShellShowInFileManager(pathToOpen);

        string filePath = Path.Combine(pathToOpen, $"history-{DateTime.Now.ToFileTime()}.log");
        string content = NCommandHistory.GetHistory();

        // Write the content to the file
        File.WriteAllText(filePath, content);

        if (error != Error.Ok)
        {
            return new CmdResult(false, $"Error {error}: Cannot open OS file manager.");
        }

        return new CmdResult(true, $"Opened '{pathToOpen}'");
    }

    public override void AutoComplete(Player? localPlayer, ref string inputBuffer, ref string outputBuffer, string[] parsedArgs) { }
}
