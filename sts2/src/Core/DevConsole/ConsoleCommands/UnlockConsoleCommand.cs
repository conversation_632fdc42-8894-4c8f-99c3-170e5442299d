using System;
using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Timeline;

namespace MegaCrit.Sts2.Core.DevConsole.ConsoleCommands;

// ReSharper disable once UnusedType.Global
// Reason: Used in reflection
public class UnlockConsoleCmd : AbstractConsoleCmd
{
    public override string CmdName => "unlock";
    public override string Args => "<type:string>";
    public override string Description => "Marks all cards/relics/potions/events/epochs/ascensions as discovered, or 'all' to unlock everything.";
    public override bool IsNetworked => false;

    public override CmdResult Process(Player? issuingPlayer, string[] args)
    {
        if (args.Length < 1)
        {
            return new CmdResult(false, $"No argument specified.\n{Args}");
        }

        return UnlockAll(args[0]);
    }

    private CmdResult UnlockAll(string discoveryType)
    {
        if (discoveryType == "cards")
        {
            UnlockCards();
        }
        else if (discoveryType == "potions")
        {
            UnlockPotions();
        }
        else if (discoveryType == "relics")
        {
            UnlockRelics();
        }
        else if (discoveryType == "events")
        {
            UnlockEvents();
        }
        else if (discoveryType == "epochs")
        {
            UnlockEpochs();
        }
        else if (discoveryType == "ascensions")
        {
            UnlockAscensions();
        }
        else if (discoveryType == "all")
        {
            UnlockCards();
            UnlockPotions();
            UnlockRelics();
            UnlockEvents();
            UnlockEpochs();
            UnlockAscensions();
        }
        else
        {
            return new CmdResult(false, $"Argument {discoveryType} not recognized as a discovery type.\n{Args}");
        }

        SaveManager.Instance.SaveProgressFile();
        return new CmdResult(true, $"Unlocked {discoveryType}");
    }

    private void UnlockCards()
    {
        SaveManager.Instance.ProgressSave.DiscoveredCards.AddRange(ModelDb.Cards.Select(c => c.Id).Except(SaveManager.Instance.ProgressSave.DiscoveredCards));
    }

    private void UnlockRelics()
    {
        SaveManager.Instance.ProgressSave.DiscoveredRelics.AddRange(ModelDb.Relics.Select(c => c.Id).Except(SaveManager.Instance.ProgressSave.DiscoveredRelics));
    }

    private void UnlockPotions()
    {
        SaveManager.Instance.ProgressSave.DiscoveredPotions.AddRange(ModelDb.AllPotions.Select(c => c.Id).Except(SaveManager.Instance.ProgressSave.DiscoveredPotions));
    }

    private void UnlockEvents()
    {
        SaveManager.Instance.ProgressSave.DiscoveredEvents.AddRange(ModelDb.Events.Select(c => c.Id).Except(SaveManager.Instance.ProgressSave.DiscoveredEvents));
    }

    private void UnlockEpochs()
    {
        IEnumerable<SerializableEpoch> epochsToUnlock = EpochModel.AllIds()
            .Except(SaveManager.Instance.ProgressSave.Epochs.Select(e => e.Id)) // Exclude already unlocked
            .Where(i => EpochModel.Get(i).Era != EpochEra.Banana) // Exclude debug
            .Select(i => new SerializableEpoch(i, EpochState.Obtained)); // Create new serializable epoch

        SaveManager.Instance.ProgressSave.Epochs.AddRange(epochsToUnlock);

        foreach (SerializableEpoch serializable in SaveManager.Instance.ProgressSave.Epochs)
        {
            serializable.ObtainDate = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        }
    }

    private void UnlockAscensions()
    {
        SaveManager.Instance.ProgressSave.MaxMultiplayerAscension = AscensionManager.maxAscensionAllowed;

        foreach (CharacterModel character in ModelDb.Characters)
        {
            CharacterStats? stats = SaveManager.Instance.ProgressSave.CharStats.FirstOrDefault(s => s.Id == character.Id);

            if (stats == null)
            {
                stats = new CharacterStats { Id = character.Id };
                SaveManager.Instance.ProgressSave.CharStats.Add(stats);
            }

            stats.MaxAscension = AscensionManager.maxAscensionAllowed;
        }
    }

    public override void AutoComplete(Player? localPlayer, ref string inputBuffer, ref string outputBuffer, string[] parsedArgs) { }
}
