using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;

namespace MegaCrit.Sts2.Core.Entities.Ascension;

/// <summary>
/// Manages ascension logic for climbs.
/// Responsible for tracking ascension levels and applying ascension effects.
/// </summary>
public class AscensionManager
{
    /// <summary>
    /// The maximum ascension level allowed in the game.
    /// </summary>
    public const int maxAscensionAllowed = 9;

    /// <summary>
    /// The current ascension level for the climb.
    /// </summary>
    private readonly int _level;

    /// <summary>
    /// Create a new AscensionManager with the specified ascension level.
    /// </summary>
    /// <param name="level">The ascension level for this climb.</param>
    public AscensionManager(int level)
    {
        _level = level;
    }

    /// <summary>
    /// Create a new AscensionManager with the specified ascension level.
    /// </summary>
    /// <param name="level">The ascension level enum value for this climb.</param>
    public AscensionManager(AscensionLevel level)
    {
        _level = (int)level;
    }

    /// <summary>
    /// Check if a given ascension level is active in the current climb.
    /// </summary>
    /// <param name="level">The ascension level to check.</param>
    /// <returns>True if the specified level is active.</returns>
    public bool HasLevel(AscensionLevel level)
    {
        return _level >= (int)level;
    }

    /// <summary>
    /// Apply ascension effects to a player based on the current ascension level.
    /// </summary>
    /// <param name="player">The player to apply effects to.</param>
    public void ApplyEffectsTo(Player player)
    {
        CardModel? curse = null;

        if (HasLevel(AscensionLevel.AscendersScorn))
        {
            curse = player.ClimbState.CreateCard<ScornedAscendersBane>(player);
        }
        else if (HasLevel(AscensionLevel.AscendersBane))
        {
            curse = player.ClimbState.CreateCard<AscendersBane>(player);
        }

        if (curse != null)
        {
            player.Deck.AddInternal(curse, -1, true);
        }
    }

    /// <summary>
    /// Apply ascension-specific modifications to the map.
    /// </summary>
    /// <param name="map">The map to modify.</param>
    /// <param name="actIndex">The current act index.</param>
    public void ApplyMapModifications(ActMap map, int actIndex)
    {
        if (HasLevel(AscensionLevel.Warden) && actIndex == 2)
        {
            IEnumerable<MapPoint> treasureRooms = map.GetAllMapPoints().Where(p => p.PointType == MapPointType.Treasure);
            foreach (MapPoint mapPoint in treasureRooms)
            {
                mapPoint.PointType = MapPointType.Elite;
            }
        }
    }
}
