using System;
using MegaCrit.Sts2.Core.Localization;

namespace MegaCrit.Sts2.Core.Entities.Cards;

public enum CardRarity
{
    None = 0,
    Basic = 1,
    Common = 2,
    Uncommon = 3,
    Rare = 4,
    Ancient = 5,
    Token = 6,
    Status = 7,
    Curse = 8,
    Event = 9,
    Quest = 10,
}

public static class CardRarityExtensions
{
    public static CardRarity GetNextHighestRarity(this CardRarity cardRarity)
    {
        return cardRarity switch
        {
            CardRarity.None => CardRarity.None,
            CardRarity.Basic => CardRarity.Common,
            CardRarity.Common => CardRarity.Uncommon,
            CardRarity.Uncommon => CardRarity.Rare,
            CardRarity.Rare => CardRarity.None,
            CardRarity.Status => CardRarity.None,
            CardRarity.Curse => CardRarity.None,
            CardRarity.Event => CardRarity.None,
            CardRarity.Token => CardRarity.None,
            CardRarity.Quest => CardRarity.None,
            CardRarity.Ancient => CardRarity.None,
            _ => throw new ArgumentOutOfRangeException(nameof(cardRarity), cardRarity, null)
        };
    }

    public static LocString ToLocString(this CardRarity cardRarity)
    {
        const string table = "gameplay_ui";
        return cardRarity switch
        {
            CardRarity.Basic => new LocString(table, "CARD_RARITY.BASIC"),
            CardRarity.Common => new LocString(table, "CARD_RARITY.COMMON"),
            CardRarity.Uncommon => new LocString(table, "CARD_RARITY.UNCOMMON"),
            CardRarity.Rare => new LocString(table, "CARD_RARITY.RARE"),
            CardRarity.Status => new LocString(table, "CARD_RARITY.STATUS"),
            CardRarity.Curse => new LocString(table, "CARD_RARITY.CURSE"),
            CardRarity.Event => new LocString(table, "CARD_RARITY.EVENT"),
            CardRarity.Token => new LocString(table, "CARD_RARITY.TOKEN"),
            CardRarity.Quest => new LocString(table, "CARD_RARITY.QUEST"),
            CardRarity.Ancient => new LocString(table, "CARD_RARITY.ANCIENT"),
            _ => throw new ArgumentOutOfRangeException(nameof(cardRarity), cardRarity, null)
        };
    }
}
