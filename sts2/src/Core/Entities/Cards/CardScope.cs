using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Potions;

namespace MegaCrit.Sts2.Core.Entities.Cards;

public enum CardScope
{
    /// <summary>
    /// A card that has no scope. Used in places like the Card Library.
    /// Should never be used during a climb (even things like upgrade previews should have a scope).
    /// </summary>
    None,

    /// <summary>
    /// A card that exists for an entire climb.
    /// Cards in the player's deck have the Climb scope, as do cards offered at the merchant, during end-of-combat
    /// rewards, shown in the upgrade preview at the rest site, etc.
    /// </summary>
    Climb,

    /// <summary>
    /// A card that exists for a single combat.
    /// Cards in the player's draw/discard/exhaust/hand piles have the Combat scope, as do cards generated by effects
    /// like <see cref="AttackPotion"/>, shown in the upgrade preview for <see cref="Armaments"/>, etc.
    /// </summary>
    Combat
}
