namespace MegaCrit.Sts2.Core.Entities.Cards;

/// <summary>
/// Card Tags are behavior-less metadata that other models can use to look things up.
/// 
/// A card tag should NEVER automatically add behavior to a specific card, which means non-model areas of the codebase
/// should probably never reference individual ones. If you're here to try and add automatic behavior to a card, check
/// out CardKeyword instead.
///
/// The proper way to use Card Tags is for other models to look them up to power their own behavior.
///
/// For example, Perfected Strike ("Deal 6 damage. Deals 2 additional damage for ALL your cards containing 'Strike'.")
/// can look up all cards with the Strike keyword to determine how much extra damage to deal.
/// </summary>
public enum CardTag
{
    None = 0,
    
    /// <summary>
    /// Makes this card a "Strike" card.
    ///
    /// Used by effects that apply to Strikes, generally in tandem with a Basic rarity check (like Pandora's Box),
    /// but also sometimes without (like Perfected Strike and Hellraiser).
    /// </summary>
    Strike = 1,
    
    /// <summary>
    /// Makes this card a "Defend" card.
    ///
    /// Used by effects that apply to Defends, generally in tandem with a Basic rarity check (like Pandora's Box),
    /// but also sometimes without (like <PERSON><PERSON>).
    /// </summary>
    Defend = 2,
    
    /// <summary>
    /// Makes this card a "Minion" card.
    ///
    /// Used by effects that apply to Minion cards, like Minion Dive Bomb.
    /// </summary>
    Minion = 3
}