using System;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes;

namespace MegaCrit.Sts2.Core.Entities.Merchant;

/// <summary>
/// Manages the card removal option of a merchant shop
/// </summary>
public sealed class MerchantCardRemovalEntry : MerchantEntry
{
    public bool Used { get; private set; }
    public override bool IsStocked => !Used;

    public MerchantCardRemovalEntry(Player player) : base(player)
    {
        CalcCost();
    }

    public override void CalcCost()
    {
        _cost = Mathf.RoundToInt(75) + 25 * _player.ExtraFields.CardShopRemovalsUsed;
    }

    public int CalcPriceIncrease()
    {
        float increase = 25;
        if (AscensionHelper.HasAscension(AscensionLevel.GreedyShopkeeper))
        {
            increase *= 1.2f;
        }

        return (int)Math.Round(increase);
    }

    protected override async Task<bool> OnTryPurchase(MerchantInventory? inventory)
    {
        if (Used) return false;

        // Since this uses CardSelectCmd, we have to run this on all multiplayer peers
        bool removedCard = await ClimbManager.Instance.OneOffSynchronizer.DoLocalMerchantCardRemoval(Cost);

        if (removedCard)
        {
            // This part runs only on local player
            _player.ExtraFields.CardShopRemovalsUsed++;
            NClimb.Instance?.MerchantRoom?.MerchantRug.OnCardRemovalUsed();
        }

        return removedCard;
    }

    public void SetUsed()
    {
        Used = true;
    }
}
