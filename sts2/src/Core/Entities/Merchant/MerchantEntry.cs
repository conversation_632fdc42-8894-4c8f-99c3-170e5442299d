using System;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Localization;

namespace MegaCrit.Sts2.Core.Entities.Merchant;

/// <summary>
/// Represents purchasable entry in the merchant shop.
/// Wrapped in an NMerchant_Node that manages the UI interactions.
/// </summary>
public abstract class MerchantEntry
{
    protected readonly Player _player;
    protected int _cost = 0;

    public int Cost
    {
        get
        {
            int cost = (int)Hook.ModifyMerchantPrice(_player.ClimbState, _player, this, _cost);

            if (AscensionHelper.HasAscension(AscensionLevel.GreedyShopkeeper))
            {
                cost = (int)Math.Round(cost * 1.2m);
            }

            return cost;
        }
    }

    public bool EnoughGold => Cost <= _player.Gold;

    public abstract bool IsStocked { get; }
    public event Action<PurchaseStatus, MerchantEntry>? PurchaseCompleted;
    public void InvokePurchaseCompleted(MerchantEntry entry) => PurchaseCompleted?.Invoke(PurchaseStatus.Success, entry);

    public event Action<PurchaseStatus>? PurchaseFailed;
    public void InvokePurchaseFailed(PurchaseStatus status) => PurchaseFailed?.Invoke(status);

    public event Action? EntryUpdated;

    protected MerchantEntry(Player player)
    {
        _player = player;
    }

    protected virtual void UpdateEntry()
    {
        // no-op
    }

    public void OnMerchantInventoryUpdated()
    {
        UpdateEntry();
        EntryUpdated?.Invoke();
    }


    public abstract void CalcCost();

    public async Task<bool> OnTryPurchaseWrapper(MerchantInventory? inventory)
    {
        if (!EnoughGold)
        {
            InvokePurchaseFailed(PurchaseStatus.FailureMoney);
            return false;
        }

        bool success = await OnTryPurchase(inventory);

        if (success)
        {
            await Hook.AfterItemPurchased(_player.ClimbState, _player, this, Cost);
            InvokePurchaseCompleted(this);
        }

        return success;
    }

    protected abstract Task<bool> OnTryPurchase(MerchantInventory? inventory);

    protected static LocString L10NLookup(string entryName)
    {
        return new LocString("merchant_room", entryName);
    }
}
