using System;
using MegaCrit.Sts2.Core.Platform.Steam;
#if !DISABLESTEAMWORKS
using Steamworks;
#endif

namespace MegaCrit.Sts2.Core.Entities.Multiplayer;

/// <summary>
/// Contains information about why a network operation failed, or why we disconnected from a multiplayer session.
/// Prefer passing this instead of NetError, as this contains more information about the underlying error that might
/// have been mapped to our NetError.
/// </summary>
public readonly struct NetErrorInfo
{
    // Only one of the following enumerations should be set:
    // Used for errors that occur above the platform level, in our transport layers.
    private readonly NetError? _reason;

    // Used when the disconnection was raised from the JoinFlow.
    private readonly ConnectionFailureReason? _connectionReason;

    #if !DISABLESTEAMWORKS
    // Used when the disconnection was raised from the Steam transport.
    private readonly SteamDisconnectionReason? _steamReason;

    // Used for Steam hosting when the lobby creation fails.
    private readonly EResult? _lobbyCreationResult;

    // Use for Steam joining when entering the lobby fails. Even though it refers to "chat room", it really means "lobby".
    private readonly EChatRoomEnterResponse? _lobbyEnterResponse;
    #endif

    // Used specifically for ENet hosting when lobby creation fails.
    // If Godot errors occur elsewhere, expand the mapping for this error.
    private readonly Godot.Error? _godotError;

    // String that accompanies the disconnection. Currently only used on the Steam platform.
    private readonly string? _debugReason;

    // Set to true if the disconnection was initiated by the local peer. For example, if the reason is Quit and this is
    // true, then the disconnection was caused by a quit initiated locally. If the reason is Quit and this is false, then
    // the disconnection was caused by a quit initiated remotely.
    public bool SelfInitiated { get; }

    public NetErrorInfo(NetError reason, bool selfInitiated)
    {
        _reason = reason;
        SelfInitiated = selfInitiated;
    }

    public NetErrorInfo(ConnectionFailureReason reason)
    {
        _connectionReason = reason;
        SelfInitiated = false;
    }

    #if !DISABLESTEAMWORKS
    public NetErrorInfo(SteamDisconnectionReason steamReason, string? debugReason, bool selfInitiated)
    {
        _steamReason = steamReason;
        _debugReason = debugReason;
        SelfInitiated = selfInitiated;
    }

    public NetErrorInfo(EChatRoomEnterResponse lobbyEnterResponse)
    {
        _lobbyEnterResponse = lobbyEnterResponse;
        SelfInitiated = true;
    }

    public NetErrorInfo(EResult lobbyCreationResult)
    {
        _lobbyCreationResult = lobbyCreationResult;
        SelfInitiated = true;
    }
    #endif

    public NetErrorInfo(Godot.Error error)
    {
        _godotError = error;
        SelfInitiated = true;
    }

    /// <summary>
    /// Returns a disconnection reason mapped from the underlying transport disconnection reason.
    /// </summary>
    public NetError GetReason()
    {
        if (_reason != null) return _reason.Value;

        if (_connectionReason != null)
        {
            return _connectionReason.Value switch
            {
                ConnectionFailureReason.None => NetError.None,
                ConnectionFailureReason.LobbyFull => NetError.LobbyFull,
                ConnectionFailureReason.ClimbInProgress => NetError.ClimbInProgress,
                ConnectionFailureReason.NotInSaveGame => NetError.NotInSaveGame,
                ConnectionFailureReason.VersionMismatch => NetError.VersionMismatch,
                _ => throw new ArgumentOutOfRangeException(nameof(_connectionReason), _connectionReason, "Invalid ConnectionReason")
            };
        }

#if !DISABLESTEAMWORKS
        if (_steamReason != null)
        {
            return _steamReason.Value.ToApp();
        }

        if (_lobbyCreationResult != null)
        {
            return NetError.FailedToHost;
        }

        if (_lobbyEnterResponse != null)
        {
            return _lobbyEnterResponse.Value switch
            {
                EChatRoomEnterResponse.k_EChatRoomEnterResponseDoesntExist => NetError.InvalidJoin,
                EChatRoomEnterResponse.k_EChatRoomEnterResponseNotAllowed => NetError.InternalError,
                EChatRoomEnterResponse.k_EChatRoomEnterResponseFull => NetError.LobbyFull,
                EChatRoomEnterResponse.k_EChatRoomEnterResponseError => NetError.UnknownNetworkError,
                EChatRoomEnterResponse.k_EChatRoomEnterResponseBanned => NetError.JoinBlockedByUser,
                EChatRoomEnterResponse.k_EChatRoomEnterResponseLimited => NetError.UnknownNetworkError,
                EChatRoomEnterResponse.k_EChatRoomEnterResponseClanDisabled => NetError.JoinBlockedByUser,
                EChatRoomEnterResponse.k_EChatRoomEnterResponseCommunityBan => NetError.JoinBlockedByUser,
                EChatRoomEnterResponse.k_EChatRoomEnterResponseMemberBlockedYou => NetError.JoinBlockedByUser,
                EChatRoomEnterResponse.k_EChatRoomEnterResponseYouBlockedMember => NetError.JoinBlockedByUser,
                EChatRoomEnterResponse.k_EChatRoomEnterResponseRatelimitExceeded => NetError.TryAgainLater,
                _ => throw new ArgumentOutOfRangeException()
            };
        }
#endif

        if (_godotError != null)
        {
            return NetError.FailedToHost;
        }

        throw new InvalidOperationException("Tried to get DisconnectionReason from DisconnectionInfo without any assigned errors");
    }

    /// <summary>
    /// Returns a string with more human-readable details about the underlying cause of the disconnection.
    /// This is suitable for displaying to users as long as platform requirements allow plain error codes in messages.
    /// </summary>
    public string GetErrorString()
    {
        if (_reason != null)
        {
            return _reason.Value.ToString();
        }
        else if (_connectionReason != null)
        {
            return _connectionReason.Value.ToString();
        }
        else if (_steamReason != null)
        {
            return $"{_steamReason} - {_debugReason}";
        }
        else if (_lobbyCreationResult != null)
        {
            return $"Lobby creation failed: {_lobbyCreationResult.Value}";
        }
        else if (_lobbyEnterResponse != null)
        {
            return $"Lobby join failed: {_lobbyEnterResponse.Value}";
        }
        else if (_godotError != null)
        {
            return _godotError.Value.ToString();
        }

        return "<null>";
    }

    /// <summary>
    /// Returns a string that should not be displayed to the user. Use this in debug logs.
    /// </summary>
    public override string ToString()
    {
        if (_reason != null)
        {
            return $"DisconnectionReason {_reason.Value} {SelfInitiated}";
        }
        else if (_connectionReason != null)
        {
            return $"ConnectionFailureReason {_connectionReason.Value} {SelfInitiated}";
        }
        else if (_steamReason != null)
        {
            return $"SteamDisconnectionReason {_steamReason.Value} {_debugReason} {SelfInitiated}";
        }
        else if (_lobbyCreationResult != null)
        {
            return $"EResult {_lobbyCreationResult.Value} {SelfInitiated}";
        }
        else if (_lobbyEnterResponse != null)
        {
            return $"EChatRoomEnterResponse {_lobbyEnterResponse.Value} {SelfInitiated}";
        }
        else if (_godotError != null)
        {
            return $"Godot.Error {_godotError.Value} {SelfInitiated}";
        }

        return "<null>";
    }
}
