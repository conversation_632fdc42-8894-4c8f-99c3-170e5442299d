using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;

namespace MegaCrit.Sts2.Core.Entities.RestSite;

public sealed class HealRestSiteOption : RestSiteOption
{
    public override string OptionId => "HEAL";

    public static decimal GetHealAmount(Player player)
    {
        return Hook.ModifyRestSiteHealAmount(player.ClimbState, player.Creature, GetBaseHealAmount(player.Creature));
    }

    public override LocString Description
    {
        get
        {
            LocString desc = base.Description;

            HealVar healVar = new(GetBaseHealAmount(Owner.Creature))
            {
                PreviewValue = GetHealAmount(Owner)
            };

            desc.Add("Character", Owner.Character.Id.Entry);
            desc.Add(healVar);

            IReadOnlyList<LocString> extraText = Hook.ModifyExtraRestSiteHealText(Owner.ClimbState, []);

            if (extraText.Any())
            {
                desc.Add("ExtraText", $"\n{string.Join("\n", extraText.Select(s => s.GetFormattedText()))}");
            }
            else
            {
                desc.Add("ExtraText", string.Empty);
            }

            return desc;
        }
    }

    public HealRestSiteOption(Player owner) : base(owner) { }

    public override async Task<bool> OnSelect()
    {
        await CreatureCmd.Heal(Owner.Creature, GetHealAmount(Owner));
        await Hook.AfterRestSiteHeal(Owner.ClimbState, Owner);
        return true;
    }

    public override async Task DoLocalPostSelectVfx()
    {
        PlayRestSiteHealSfx();
        NRestSiteRoom.Instance!.AddChildSafely(NRestSmokeVfx.Create());
        NRestSiteRoom.Instance!.AddChildSafely(NDesaturateTransitionVfx.Create());
        await Cmd.CustomScaledWait(1.5f, 2.5f);
    }

    public override Task DoRemotePostSelectVfx()
    {
        NDebugAudioManager.Instance?.Play(TmpSfx.restSiteHealBlanket, 0.5f, PitchVariance.Small);
        // Heal VFX is played by CreatureCmd.Heal
        return Task.CompletedTask;
    }

    public static decimal GetBaseHealAmount(Creature creature) => creature.MaxHp * 0.3m;

    public static void PlayRestSiteHealSfx()
    {
        NDebugAudioManager.Instance?.Play(TmpSfx.restSiteHealJingle, 1f, PitchVariance.None);
        NDebugAudioManager.Instance?.Play(TmpSfx.restSiteHealBlanket, 1f, PitchVariance.Small);
    }
}
