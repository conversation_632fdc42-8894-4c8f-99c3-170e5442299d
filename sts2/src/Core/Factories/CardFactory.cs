using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.CardPools;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Factories;

public static class CardFactory
{
    private static decimal UpgradedCardOddScaling => AscensionHelper.GetValueIfAscension(AscensionLevel.Scarcity, 0.125m, 0.25m);

    /// <summary>
    /// Creates a set of cards to display to the player at the merchant. Takes in a card type, and rarity is rolled.
    /// </summary>
    /// <param name="player">The player for which we're creating cards to display.</param>
    /// <param name="options">The cards to pull from.</param>
    /// <param name="types">The card types to generate. The amount of entries specifies how many cards will be generated.</param>
    public static IEnumerable<CardCreationResult> CreateForMerchant(Player player, IEnumerable<CardModel> options, IEnumerable<CardType> types)
    {
        if (player.Character is Deprived)
        {
            throw new InvalidOperationException("Merchant inventory can't be generated for the test character. Update your test to use Ironclad.");
        }

        HashSet<ModelId> cardsSeen = [];

        options = Hook.ModifyMerchantCardPool(player.ClimbState, player, options);
        options = options.Where(c => c.Rarity != CardRarity.Basic);
        CardModel[] optionsArr = options.ToArray();

        List<CardCreationResult> results = [];

        foreach (CardType cardType in types)
        {
            CardRarity rolledRarity = Hook.ModifyMerchantCardRarity(player.ClimbState, player, player.PlayerOdds.CardRarity.RollWithoutChangingFutureOdds(CardCreationSource.Shop));

            // ReSharper disable once AccessToModifiedClosure
            // REASON: Closure is executed immediately.
            // See https://www.jetbrains.com/help/resharper/AccessToModifiedClosure.html for the problems this can cause
            // when execution is delayed.
            List<CardModel> currentOptions = optionsArr.Where(c => c.Rarity == rolledRarity && c.Type == cardType && !cardsSeen.Contains(c.Id)).ToList();

            while (currentOptions.Count == 0)
            {
                rolledRarity = rolledRarity.GetNextHighestRarity();

                if (rolledRarity == CardRarity.None)
                {
                    throw new InvalidOperationException(
                        "Can't generate a valid rarity for the merchant card options passed."
                    );
                }

                // ReSharper disable once AccessToModifiedClosure
                // REASON: Closure is executed immediately.
                // See https://www.jetbrains.com/help/resharper/AccessToModifiedClosure.html for the problems this can
                // cause when execution is delayed.
                currentOptions = optionsArr.Where(c => c.Rarity == rolledRarity && c.Type == cardType && !cardsSeen.Contains(c.Id)).ToList();
            }

            CardModel card = player.ClimbState.CreateCard(player.PlayerRng.Shops.NextItem(currentOptions)!, player);

            // Merchant should never roll an upgrade card unless you have a relic that does so
            // i.e. toxic/frozen/molten egg
            RollForUpgrade(player, card, -Really.bigNumber);

            cardsSeen.Add(card.Id);
            results.Add(new CardCreationResult(card));
        }

        return results;
    }

    /// <summary>
    /// Creates a set of cards to display to the player at the merchant.
    /// The rarity of cards are affected by the source.
    /// </summary>
    /// <param name="player">The player for which we're creating cards to display.</param>
    /// <param name="options">The cards to pull from.</param>
    /// <param name="rarities">The card rarities to generate. The amount of entries specifies how many cards will be generated.</param>
    public static IEnumerable<CardCreationResult> CreateForMerchant(Player player, IEnumerable<CardModel> options, IEnumerable<CardRarity> rarities)
    {
        HashSet<ModelId> cardsSeen = [];

        options = Hook.ModifyMerchantCardPool(player.ClimbState, player, options);
        options = options.Where(c => c.Rarity != CardRarity.Basic);
        CardModel[] optionsArr = options.ToArray();

        List<CardCreationResult> results = [];

        foreach (CardRarity rarity in rarities)
        {
            CardRarity modifiedRarity = Hook.ModifyMerchantCardRarity(player.ClimbState, player, rarity);
            IEnumerable<CardModel> currentOptions = optionsArr.Where(c => c.Rarity == modifiedRarity && !cardsSeen.Contains(c.Id));
            CardModel card = player.ClimbState.CreateCard(player.PlayerRng.Shops.NextItem(currentOptions)!, player);

            // Merchant should never roll an upgrade card unless you have a relic that does so
            // i.e. toxic/frozen/molten egg
            RollForUpgrade(player, card, -Really.bigNumber);

            cardsSeen.Add(card.Id);
            results.Add(new CardCreationResult(card));
        }

        return results;
    }

    /// <summary>
    /// Creates a set of cards for the player to choose 1 from.
    /// The rarity of cards are affected by the source.
    /// </summary>
    /// <param name="player">The player for which we're creating rewards.</param>
    /// <param name="cardPool">Pool to pull cards from.</param>
    /// <param name="source">
    ///     Source to generate the reward for (normal combat, elite combat, shop, etc.).
    ///     This impacts how rarities are rolled.
    /// </param>
    /// <param name="options">How many choices to offer (usually 3).</param>
    /// <param name="rollMethod">
    ///     How the reward should be rolled. See <see cref="OddsRollMethod"/> for more details.
    /// </param>
    public static IEnumerable<CardCreationResult> CreateForReward(Player player, CardPoolModel cardPool, CardCreationSource source, int options, OddsRollMethod rollMethod = OddsRollMethod.Standard)
    {
        List<CardModel> cards = [];
        List<CardCreationResult> cardResults = [];

        for (int i = 0; i < options; i++)
        {
            CardModel card = CreateForReward(player, cardPool, source, cards, rollMethod);

            cards.Add(card);
            cardResults.Add(new CardCreationResult(card));
            RollForUpgrade(player, card, 0);
        }

        if (Hook.TryModifyCardRewardOptions(player.ClimbState, player, cardResults, source, out List<AbstractModel> modifiers))
        {
            TaskHelper.RunSafely(Hook.AfterModifyingCardRewardOptions(player.ClimbState, modifiers));
        }

        return cardResults;
    }

    /// <summary>
    /// Creates a set of cards for the player to choose 1 from.
    /// </summary>
    /// <param name="player">The player for which we're creating cards to display.</param>
    /// <param name="customPool">
    /// List of cards to offer rewards from.
    /// All cards in this list have equal likelihood of being selected.
    /// </param>
    /// <param name="options">How many choices to offer (usually 3).</param>
    public static IEnumerable<CardCreationResult> CreateForReward(Player player, IEnumerable<CardModel> customPool, int options)
    {
        List<CardCreationResult> cardResults = CreateForRewardWithoutModifications(player, customPool, options).Select(c => new CardCreationResult(c)).ToList();

        if (Hook.TryModifyCardRewardOptions(player.ClimbState, player, cardResults, CardCreationSource.None, out List<AbstractModel> modifiers))
        {
            TaskHelper.RunSafely(Hook.AfterModifyingCardRewardOptions(player.ClimbState, modifiers));
        }

        return cardResults;
    }

    /// <summary>
    /// Creates a set of cards for the player to choose 1 from.
    /// The HookBus is not called to modify the results. Use this in instances where the list must be static.
    /// </summary>
    /// <param name="player">The player for which we're creating cards to display.</param>
    /// <param name="customPool">
    /// List of cards to offer rewards from.
    /// All cards in this list have equal likelihood of being selected.
    /// </param>
    /// <param name="options">How many choices to offer (usually 3).</param>
    public static IEnumerable<CardModel> CreateForRewardWithoutModifications(Player player, IEnumerable<CardModel> customPool, int options)
    {
        List<CardModel> choices = customPool.ToList();
        List<CardModel> cards = [];

        for (int i = 0; i < options; i++)
        {
            CardModel card = CreateForReward(player, choices);

            choices.RemoveAll(c => c.Id == card.Id);
            cards.Add(card);
            RollForUpgrade(player, card, 0);
        }

        return cards;
    }

    /// <summary>
    /// Get a set of distinct cards for use in combat generation effects like Attack Potion or Discovery.
    /// </summary>
    /// <param name="player">The owner of the newly created cards.</param>
    /// <param name="cards">Cards to choose from.</param>
    /// <param name="count">Number of cards to get.</param>
    /// <param name="rng">RNG to use.</param>
    /// <returns>Mutable cards owned by the player & created within its combat state, for combat generation effects.</returns>
    public static IEnumerable<CardModel> GetDistinctForCombat(Player player, IEnumerable<CardModel> cards, int count, Rng rng)
    {
        return FilterForCombat(cards)
            .TakeRandom(count, rng)
            .Select(c => player.Creature.CombatState!.CreateCard(c, player));
    }

    /// <summary>
    /// Get a set of cards for use in combat generation effects like Calamity.
    /// Can include multiple of the same card, unlike <see cref="GetDistinctForCombat"/>.
    /// </summary>
    /// <param name="player">The owner of the newly created cards.</param>
    /// <param name="cards">Cards to choose from.</param>
    /// <param name="count">Number of cards to get.</param>
    /// <param name="rng">RNG to use.</param>
    /// <returns>Cards for combat generation effects.</returns>
    public static IEnumerable<CardModel> GetForCombat(Player player, IEnumerable<CardModel> cards, int count, Rng rng)
    {
        List<CardModel> filteredCards = FilterForCombat(cards).ToList();
        List<CardModel> result = [];

        for (int i = 0; i < count; i++)
        {
            CardModel canonical = rng.NextItem(filteredCards)!;
            CardModel mutable = player.Creature.CombatState!.CreateCard(canonical, player);
            result.Add(mutable);
        }

        return result;
    }

    /// <summary>
    /// Filter out cards that should not be included in combat card generation effects like Attack Potion or Discovery.
    /// </summary>
    /// <param name="cards">Cards to filter.</param>
    /// <returns>Cards for combat generation effects.</returns>
    [SuppressMessage("ReSharper", "MemberCanBePrivate.Global", Justification = "Public API")]
    public static IEnumerable<CardModel> FilterForCombat(IEnumerable<CardModel> cards)
    {
        return cards.Where(c =>
            // Filter out cards that explicitly cannot be generated in combat (usually healing or other permanent
            // effect cards).
            c.CanBeGeneratedInCombat &&

            // Basic cards are too weak to appear in combat card generation effects.
            c.Rarity != CardRarity.Basic &&
            // We don't want any ancient cards or event cards showing up.
            c.Rarity != CardRarity.Ancient &&
            c.Rarity != CardRarity.Event
        ).Distinct();
    }

    public static IEnumerable<CardModel> GetDefaultTransformationOptions(CardModel original)
    {
        // Since quest & event cards are usually tied to specific events, we don't want to be able to obtain a different
        // quest/event/tokens card through a transformation. So we default to the colorless card pool.
        bool shouldUseCardPool =
            original.Type != CardType.Quest &&
            original.Rarity != CardRarity.Event &&
            original.Rarity != CardRarity.Token;

        IEnumerable<CardModel> cards = shouldUseCardPool ? original.Pool.Cards : ModelDb.CardPool<ColorlessCardPool>().Cards;

        return GetFilteredTransformationOptions(original, cards);
    }

    public static CardModel CreateRandomCardForTransform(CardModel original, Rng rng)
    {
        IEnumerable<CardModel> options = GetDefaultTransformationOptions(original);
        return original.CardScope!.CreateCard(rng.NextItem(options)!, original.Owner);
    }

    private static CardModel[] GetFilteredTransformationOptions(CardModel original, IEnumerable<CardModel> originalOptions)
    {
        IEnumerable<CardModel> cards = originalOptions;

        if (original.Rarity != CardRarity.Curse && original.Rarity != CardRarity.Status)
        {
            cards = cards.Where(c => c.Rarity is CardRarity.Common or CardRarity.Uncommon or CardRarity.Rare);
        }

        if (original.Pile?.IsCombatPile ?? false)
        {
            cards = cards.Where(c => c.CanBeGeneratedInCombat);
        }

        cards = cards.Where(c => c.Id != original.Id).ToList();

        CardModel[] cardsArr = cards.ToArray();

        if (cardsArr.Length == 0) throw new InvalidOperationException($"All transformation options provided are invalid! Original options: {string.Join(",", originalOptions)}");

        return cardsArr;
    }

    public static CardModel CreateRandomCardForTransform(CardModel original, IEnumerable<CardModel> options, Rng rng)
    {
        CardModel[] optionsArr = GetFilteredTransformationOptions(original, options);
        return original.CardScope!.CreateCard(rng.NextItem(optionsArr)!, original.Owner);
    }

    private static CardModel CreateForReward(Player player, IEnumerable<CardModel> customPool)
    {
        IEnumerable<CardModel> options = Hook.ModifyCardRewardCardPool(player.ClimbState, player, customPool, CardCreationSource.Custom);

        // Never allow basic card rewards.
        options = options.Where(c => c.Rarity != CardRarity.Basic && c.Rarity != CardRarity.Ancient);
        return player.ClimbState.CreateCard(player.PlayerRng.Rewards.NextItem(options)!, player);
    }

    private static CardModel CreateForReward(Player player, CardPoolModel cardPool, CardCreationSource source, IEnumerable<CardModel> blacklist, OddsRollMethod rollMethod = OddsRollMethod.Standard)
    {
        HashSet<CardRarity> allowedRarities = cardPool.Cards.Select(c => c.Rarity).ToHashSet();

        CardRarity rarity = rollMethod switch
        {
            OddsRollMethod.Standard => player.PlayerOdds.CardRarity.Roll(source),
            OddsRollMethod.RegularEncounter => player.PlayerOdds.CardRarity.RollWithBaseOdds(CardCreationSource.RegularEncounter),
            _ => throw new ArgumentOutOfRangeException(nameof(rollMethod), rollMethod, null)
        };

        // If this pool doesn't have any cards of this rarity, try the next highest one.
        // Handles cases like the Colorless card pool, which has no Common cards.
        while (!allowedRarities.Contains(rarity))
        {
            rarity = rarity.GetNextHighestRarity();

            if (rarity == CardRarity.None)
            {
                throw new InvalidOperationException($"Can't generate a valid rarity for {cardPool.Id}.");
            }
        }

        IEnumerable<CardModel> options = Hook.ModifyCardRewardCardPool(player.ClimbState, player, cardPool.Cards, source);

        IEnumerable<CardModel> validCards = options.Where(card =>
        {
            if (card.Rarity != rarity) return false;
            if (card.Type is not (CardType.Attack or CardType.Skill or CardType.Power)) return false;
            if (blacklist.Any(c => c.Id == card.Id)) return false;

            return true;
        });

        return player.ClimbState.CreateCard(player.PlayerRng.Rewards.NextItem(validCards)!, player);
    }

    private static void RollForUpgrade(Player player, CardModel card, decimal baseChance)
    {
        // Note: We intentionally roll before exiting early so that the RNG counter still increments.
        decimal roll = (decimal)player.PlayerRng.Rewards.NextFloat();

        if (!card.IsUpgradable) return;

        decimal upgradeOdds = baseChance;

        // On non-Rare cards, Act 2 (index 1) adds 25% chance for an upgrade, act 3 adds 50% chance, etc.
        if (card.Rarity != CardRarity.Rare)
        {
            int multiplier = player.ClimbState.CurrentActIndex;
            upgradeOdds += multiplier * UpgradedCardOddScaling;
        }

        // Give hooks a chance to modify the upgrade odds.
        upgradeOdds = Hook.ModifyCardRewardUpgradeOdds(player.ClimbState, player, card, upgradeOdds);

        if (roll <= upgradeOdds)
        {
            CardCmd.Upgrade(card);
        }
    }
}
