using System;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;

namespace MegaCrit.Sts2.Core.GameActions;

/// <summary>
/// This action is enqueued when the player chooses to discard a potion from their belt. It runs both in combat and
/// outside of combat.
/// </summary>
public class DiscardPotionGameAction : GameAction
{
    private readonly Player _player;
    private readonly uint _potionSlotIndex;
    public override ulong OwnerId => _player.NetId;
    public override GameActionType ActionType => GameActionType.NonCombat;

    public DiscardPotionGameAction(Player player, uint potionSlotIndex)
    {
        _player = player;
        _potionSlotIndex = potionSlotIndex;
    }

    protected override async Task ExecuteAction()
    {
        if (_potionSlotIndex >= _player.PotionSlots.Count)
        {
            throw new IndexOutOfRangeException($"Tried to discard potion at slot index {_potionSlotIndex}, but player {_player.NetId} only has {_player.PotionSlots.Count} potion slots!");
        }

        PotionModel? potion = _player.PotionSlots[(int)_potionSlotIndex];

        if (potion == null)
        {
            throw new InvalidOperationException($"Tried to discard potion at slot index {_potionSlotIndex}, but player {_player.NetId} has no potion in that slot!");
        }

        await PotionCmd.Discard(potion);
    }

    public override INetAction ToNetAction()
    {
        return new NetDiscardPotionGameAction { potionSlotIndex = _potionSlotIndex };
    }

    public override string ToString() => $"{nameof(NetDiscardPotionGameAction)} for player {_player.NetId} potion slot: {_potionSlotIndex}";
}

public struct NetDiscardPotionGameAction : INetAction
{
    public uint potionSlotIndex;

    public GameAction ToGameAction(Player player)
    {
        return new DiscardPotionGameAction(player, potionSlotIndex);
    }

    public void Serialize(PacketWriter writer)
    {
        writer.WriteUInt(potionSlotIndex, 4);
    }

    public void Deserialize(PacketReader reader)
    {
        potionSlotIndex = reader.ReadUInt(4);
    }

    public override string ToString() => $"{nameof(NetDiscardPotionGameAction)} slot {potionSlotIndex}";
}
