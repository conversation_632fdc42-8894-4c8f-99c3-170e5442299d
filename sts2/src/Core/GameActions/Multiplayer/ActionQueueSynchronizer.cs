using System;
using System.Collections.Generic;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Game;

namespace MegaCrit.Sts2.Core.GameActions.Multiplayer;

/// <summary>
/// Responsible for synchronizing actions across all peers in a deterministically-ordered manner.
/// </summary>
public class ActionQueueSynchronizer
{
    private readonly ActionQueueSet _actionQueueSet;
    private readonly INetGameService _netService;
    private readonly Logger _logger;
    private readonly IPlayerCollection _playerCollection;
    private readonly ClimbLocationTargetedMessageBuffer _messageBuffer;

    private readonly List<GenericHookGameAction> _hookActions = [];
    private uint _nextHookId;

    /// <summary>
    /// This list contains actions that were queued during the enemy turn or draw phase, but can only be executed during
    /// the player's play phase.
    /// </summary>
    private readonly List<GameAction> _requestedActionsWaitingForPlayerTurn = [];

    /// <summary>
    /// Next ID assigned to hook actions. Exposed only so the combat replay writer has access to it.
    /// </summary>
    public uint NextHookId => _nextHookId;

    /// <summary>
    /// Current combat state. Dictates the paused state of queues.
    /// </summary>
    public ActionSynchronizerCombatState CombatState { get; private set; } = ActionSynchronizerCombatState.NotInCombat;

    public ActionQueueSynchronizer(IPlayerCollection players, ActionQueueSet actionQueueSet, ClimbLocationTargetedMessageBuffer messageBuffer, INetGameService netService)
    {
        _playerCollection = players;
        _actionQueueSet = actionQueueSet;
        _netService = netService;
        _messageBuffer = messageBuffer;

        _messageBuffer.RegisterMessageHandler<RequestEnqueueActionMessage>(HandleRequestEnqueueActionMessage);
        _messageBuffer.RegisterMessageHandler<ActionEnqueuedMessage>(HandleActionEnqueuedMessage);
        _messageBuffer.RegisterMessageHandler<RequestEnqueueHookActionMessage>(HandleRequestEnqueueHookActionMessage);
        _messageBuffer.RegisterMessageHandler<HookActionEnqueuedMessage>(HandleHookActionEnqueuedMessage);
        _messageBuffer.RegisterMessageHandler<RequestResumeActionAfterPlayerChoiceMessage>(HandleRequestResumeActionAfterPlayerChoiceMessage);
        _messageBuffer.RegisterMessageHandler<ResumeActionAfterPlayerChoiceMessage>(HandleResumeActionAfterPlayerChoiceMessage);
        _logger = new Logger($"{nameof(ActionQueueSynchronizer)}", LogType.Actions);
    }

    public void Dispose()
    {
        _messageBuffer.UnregisterMessageHandler<RequestEnqueueActionMessage>(HandleRequestEnqueueActionMessage);
        _messageBuffer.UnregisterMessageHandler<ActionEnqueuedMessage>(HandleActionEnqueuedMessage);
        _messageBuffer.UnregisterMessageHandler<RequestEnqueueHookActionMessage>(HandleRequestEnqueueHookActionMessage);
        _messageBuffer.UnregisterMessageHandler<HookActionEnqueuedMessage>(HandleHookActionEnqueuedMessage);
        _messageBuffer.UnregisterMessageHandler<RequestResumeActionAfterPlayerChoiceMessage>(HandleRequestResumeActionAfterPlayerChoiceMessage);
        _messageBuffer.UnregisterMessageHandler<ResumeActionAfterPlayerChoiceMessage>(HandleResumeActionAfterPlayerChoiceMessage);
    }

    /// <summary>
    /// Call this when combat begins, ends, or the turn changes within combat.
    /// Certain actions, like card plays or potion usages, should always occur after the hooks that execute at the start
    /// of the player turn. However, the player is able to queue these actions before their turn actually starts. The
    /// synchronizer holds onto those actions until the start of the player turn, and then it sends all of them out.
    /// </summary>
    /// <param name="combatState">The new combat state.</param>
    public void SetCombatState(ActionSynchronizerCombatState combatState)
    {
        if (CombatState == combatState) return;

        ActionSynchronizerCombatState oldCombatState = CombatState;
        CombatState = combatState;

        if (oldCombatState == ActionSynchronizerCombatState.NotInCombat)
        {
            _logger.Debug($"Combat state was previously {oldCombatState}. Signaling queues that combat has begun");
            _actionQueueSet.CombatStarted();
        }

        switch (combatState)
        {
            case ActionSynchronizerCombatState.NotInCombat:
                _logger.Debug($"Combat state becomes {combatState}. Cancelling deferred actions and cancelling all remaining combat actions");
                _actionQueueSet.CombatEnded();
                _actionQueueSet.UnpauseAllPlayerQueues();
                _requestedActionsWaitingForPlayerTurn.Clear();
                break;
            case ActionSynchronizerCombatState.PlayPhase:
                _logger.Debug($"Combat state becomes {combatState}. Requesting {_requestedActionsWaitingForPlayerTurn.Count} actions and unpausing action queues");

                foreach (GameAction action in _requestedActionsWaitingForPlayerTurn)
                {
                    RequestEnqueue(action);
                }

                _requestedActionsWaitingForPlayerTurn.Clear();
                _actionQueueSet.UnpauseAllPlayerQueues();
                break;
            case ActionSynchronizerCombatState.EndTurnPhaseOne:
                _logger.Debug($"Combat state becomes {combatState} (from {oldCombatState}). Starting to cancel all player-driven actions");
                _actionQueueSet.StartCancellingAllPlayerDrivenCombatActions();
                break;
            case ActionSynchronizerCombatState.NotPlayPhase:
                _logger.Debug($"Combat state becomes {combatState}. Pausing all player queues");
                _actionQueueSet.PauseAllPlayerQueues();
                break;
        }
    }

    /// <summary>
    /// Main entry point for enqueueing a GameAction.
    /// If you are the host, the GameAction is directly enqueued, and a message is sent to notify clients about the action.
    /// If you are the client, the GameAction is not enqueued. Instead, a message is sent to the host to request that
    /// the action be enqueued. The host sends back a confirmation, preserving ordering of the action queues.
    /// </summary>
    public void RequestEnqueue(GameAction action)
    {
        if (action.ActionType == GameActionType.CombatPlayPhaseOnly && CombatState == ActionSynchronizerCombatState.NotPlayPhase)
        {
            _logger.Debug($"Attempted to request enqueue of action {action} during the enemy turn. Deferring the action until the player turn");
            _requestedActionsWaitingForPlayerTurn.Add(action);
            return;
        }

        switch (_netService.Type)
        {
            case NetGameType.Client:
                _logger.Debug($"Sending message to host requesting to enqueue action {action} at {Log.Timestamp}");
                RequestEnqueueActionMessage message = default;
                message.action = action.ToNetAction();
                message.location = _messageBuffer.CurrentLocation;
                _netService.SendMessage(message);
                break;
            case NetGameType.Host:
            case NetGameType.Singleplayer:
                EnqueueAction(action, _netService.NetId);
                break;
        }
    }

    public GenericHookGameAction GenerateHookAction(ulong ownerId)
    {
        GenericHookGameAction action = GetHookActionForId(_nextHookId, ownerId);
        _nextHookId++;
        return action;
    }

    /// <summary>
    /// Requests that a GenericHookGameAction be enqueued.
    /// Hook game actions are a little different than normal game actions. They contain arbitrary logic that is executed
    /// as part of a hook. Since hooks are executed on all peers, we can identify the actions by an incrementing ID.
    /// If on host, the action is immediately enqueued and a message is sent to clients.
    /// If on client, a message is sent to the host, and the host sends a message back to confirm that the message is
    /// enqueued.
    /// </summary>
    /// <returns></returns>
    public void RequestEnqueueHookAction(GenericHookGameAction action)
    {
        switch (_netService.Type)
        {
            case NetGameType.Client:
                _logger.Debug($"Sending message to host requesting to enqueue HOOK action with id {action.HookId} at {Log.Timestamp}");
                RequestEnqueueHookActionMessage message = default;
                message.hookActionId = action.HookId;
                message.location = _messageBuffer.CurrentLocation;
                _netService.SendMessage(message);
                break;
            case NetGameType.Host:
            case NetGameType.Singleplayer:
                EnqueueHookAction(action);
                break;
        }
    }

    /// <summary>
    /// Requests that an action, paused for player choice, be resumed.
    /// If on the host, the action is immediately resumed and a message is sent to clients indicating this.
    /// If on a client, a message is sent to the host to ensure correct ordering, and the host sends a message back
    /// to confirm resumption of the action.
    /// </summary>
    /// <param name="action">The GameAction to resume.</param>
    public void RequestResumeActionAfterPlayerChoice(GameAction action)
    {
        switch (_netService.Type)
        {
            case NetGameType.Client:
            {
                _logger.Debug($"Sending message to host requesting resumption of action {action}, id {action.Id}, at {Log.Timestamp}");
                RequestResumeActionAfterPlayerChoiceMessage afterPlayerChoiceMessage = default;
                afterPlayerChoiceMessage.actionId = action.Id!.Value;
                afterPlayerChoiceMessage.location = _messageBuffer.CurrentLocation;
                _netService.SendMessage(afterPlayerChoiceMessage);
                break;
            }
            case NetGameType.Host:
            case NetGameType.Singleplayer:
                ResumeActionAfterPlayerChoice(action.Id!.Value);
                break;
        }
    }

    private void EnqueueAction(GameAction action, ulong actionOwnerId)
    {
        if (_netService.Type == NetGameType.Host)
        {
            _logger.Debug($"Sending message to client to enqueue action {action} at {Log.Timestamp}");
            ActionEnqueuedMessage message = default;
            message.playerId = actionOwnerId;
            message.location = _messageBuffer.CurrentLocation;
            message.action = action.ToNetAction();
            _netService.SendMessage(message);
        }

        _logger.Debug($"Enqueueing action {action} from owner {actionOwnerId}");
        _actionQueueSet.EnqueueWithoutSynchronizing(action);
    }

    private void EnqueueHookAction(GenericHookGameAction gameAction)
    {
        if (_netService.Type == NetGameType.Host)
        {
            _logger.Debug($"Sending message to client to enqueue hook action {gameAction} at {Log.Timestamp}");
            HookActionEnqueuedMessage message = default;
            message.hookActionId = gameAction.HookId;
            message.ownerId = gameAction.OwnerId;
            message.location = _messageBuffer.CurrentLocation;
            _netService.SendMessage(message);
        }

        _logger.Debug($"Enqueueing HOOK action with id {gameAction.HookId}");
        _actionQueueSet.EnqueueWithoutSynchronizing(gameAction);
    }

    /// <summary>
    /// Resumes a GameAction after a player choice. This call is synchronized, so it should only be called from the host
    /// or in response to messages.
    /// </summary>
    private void ResumeActionAfterPlayerChoice(uint id)
    {
        if (_netService.Type == NetGameType.Host)
        {
            _logger.Debug($"Sending message to client to resume action id {id} at {Log.Timestamp}");
            ResumeActionAfterPlayerChoiceMessage afterPlayerChoiceMessage = default;
            afterPlayerChoiceMessage.actionId = id;
            afterPlayerChoiceMessage.location = _messageBuffer.CurrentLocation;
            _netService.SendMessage(afterPlayerChoiceMessage);
        }

        _logger.Debug($"Resuming action with ID {id}");
        _actionQueueSet.ResumeActionWithoutSynchronizing(id);
    }

    private void HandleRequestEnqueueActionMessage(RequestEnqueueActionMessage message, ulong senderId)
    {
        if (_netService.Type != NetGameType.Host) throw new InvalidOperationException($"Received action enqueue request while not host!");
        _logger.Debug($"Received request enqueue action for action {message.action} ({senderId}) at {Log.Timestamp}");
        GameAction action = NetActionToGameAction(message.action, senderId);
        EnqueueAction(action, senderId);
    }

    // DO NOT use the sender ID here, it is always the host! Use message.playerId instead
    private void HandleActionEnqueuedMessage(ActionEnqueuedMessage message, ulong _)
    {
        if (_netService.Type != NetGameType.Client) throw new InvalidOperationException($"Received action enqueued message while not client!");
        _logger.Debug($"Received handle action enqueued message {message} at {Log.Timestamp}");
        GameAction action = NetActionToGameAction(message.action, message.playerId);
        EnqueueAction(action, message.playerId);
    }

    private void HandleRequestEnqueueHookActionMessage(RequestEnqueueHookActionMessage message, ulong senderId)
    {
        if (_netService.Type != NetGameType.Host) throw new InvalidOperationException($"Received hook action enqueue request while not host!");
        _logger.Debug($"Received HOOK request enqueue action from {senderId}, hook id: {message.hookActionId}, at {Log.Timestamp}");
        GenericHookGameAction action = GetHookActionForId(message.hookActionId, senderId);
        EnqueueHookAction(action);
    }

    private void HandleHookActionEnqueuedMessage(HookActionEnqueuedMessage message, ulong _)
    {
        if (_netService.Type != NetGameType.Client) throw new InvalidOperationException($"Received hook action enqueued message while not host!");
        _logger.Debug($"Received HOOK request enqueue action, hook id: {message.hookActionId}, at {Log.Timestamp}");
        GenericHookGameAction action = GetHookActionForId(message.hookActionId, message.ownerId);
        EnqueueHookAction(action);
    }

    /// <summary>
    /// Called on the host when a client wishes to resume an action after a player choice is made.
    /// </summary>
    private void HandleRequestResumeActionAfterPlayerChoiceMessage(RequestResumeActionAfterPlayerChoiceMessage afterPlayerChoiceMessage, ulong senderId)
    {
        if (_netService.Type != NetGameType.Host) throw new InvalidOperationException($"Received action ready request message while not host!");
        _logger.Debug($"Received action ready request message for action {afterPlayerChoiceMessage.actionId}, sender {senderId}, at {Log.Timestamp}");
        ResumeActionAfterPlayerChoice(afterPlayerChoiceMessage.actionId);
    }

    /// <summary>
    /// Called on the client when a host resumes an action.
    /// </summary>
    private void HandleResumeActionAfterPlayerChoiceMessage(ResumeActionAfterPlayerChoiceMessage afterPlayerChoiceMessage, ulong _)
    {
        if (_netService.Type != NetGameType.Client) throw new InvalidOperationException($"Received action ready message while not client!");
        _logger.Debug($"Received action ready message for action {afterPlayerChoiceMessage.actionId} at {Log.Timestamp}");
        ResumeActionAfterPlayerChoice(afterPlayerChoiceMessage.actionId);
    }

    private GameAction NetActionToGameAction(INetAction action, ulong actionOwnerId)
    {
        Player? player = _playerCollection.GetPlayer(actionOwnerId);
        if (player == null) throw new InvalidOperationException($"Action owner ID {actionOwnerId} for action {action} could not be mapped to a Player!");

        GameAction gameAction = action.ToGameAction(player);
        return gameAction;
    }

    /// <summary>
    /// This should only ever be called outside this class in replay playback!
    /// </summary>
    public GenericHookGameAction GetHookActionForId(uint id, ulong ownerId)
    {
        GenericHookGameAction? action = _hookActions.Find(a => a.HookId == id);

        if (action == null)
        {
            action = new GenericHookGameAction(id, ownerId);
            action.ExecutionStartedTask.ContinueWith(_ => HookActionStarted(action));
            _hookActions.Add(action);
        }
        else if (action.OwnerId != ownerId)
        {
            throw new InvalidOperationException($"Attempted to get hook for owner {ownerId} with hook ID {id}, but the hook already existed and had owner {action.OwnerId}!");
        }

        return action;
    }

    private void HookActionStarted(GenericHookGameAction action)
    {
        // Once the hook action begins, we don't need to track it anymore.
        _hookActions.Remove(action);
    }

    public void FastForwardHookId(uint hookId)
    {
        _nextHookId = hookId;
    }
}
