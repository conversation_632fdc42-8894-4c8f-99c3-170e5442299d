using System;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models;

namespace MegaCrit.Sts2.Core.GameActions.Multiplayer;

/// <summary>
/// A player choice context for use in hooks that do not run as part of a GameAction.
/// When player choice is required, a new game action is created and enqueued directly to the player's action queue. From
/// there, flow proceeds like a normal game action. No game action is created if player choice is not requested.
/// </summary>
public class HookPlayerChoiceContext : PlayerChoiceContext
{
    private readonly ulong _localPlayerId;

    // The GameAction that is created when player choice runs. If no player choice is run as part of the hook, this may
    // remain null.
    private GenericHookGameAction? _gameAction;

    // Completed when a task is assigned through AssignTaskAndWaitForPause.
    private readonly TaskCompletionSource _taskAssignedCompletionSource = new();

    // Completed when player choice is requested and the GameAction is created for the first time.
    private readonly TaskCompletionSource _pausedCompletionSource = new();

    // Fields for testing
    private ActionQueueSynchronizer? _actionQueueSynchronizer;
    private ActionQueueSet? _actionQueueSet;
    private ActionExecutor? _actionExecutor;

    private ActionQueueSynchronizer ActionQueueSynchronizer => _actionQueueSynchronizer ?? ClimbManager.Instance.ActionQueueSynchronizer;
    private ActionQueueSet ActionQueueSet => _actionQueueSet ?? ClimbManager.Instance.ActionQueueSet;
    private ActionExecutor ActionExecutor => _actionExecutor ?? ClimbManager.Instance.ActionExecutor;

    // The model on which we are executing the hook. Null if Owner is set.
    // For example, if Task is executing Toolbox.AfterTurnStart, this should be set to the Toolbox instance.
    public AbstractModel? Source { get; }

    // The task which executes the hook, which may be interrupted by this player choice context.
    public Task? Task { get; private set; }

    // The player who owns the executing hook.
    public Player? Owner { get; }

    // The GameAction that was generated when the hook was paused for player choice. Null if the hook completed without
    // requesting player choice, or if the hook is still running.
    public GenericHookGameAction? GameAction => _gameAction;

    public HookPlayerChoiceContext(Player owner, ulong localPlayerId, Task? task = null)
    {
        _localPlayerId = localPlayerId;
        Owner = owner;
        Task = task;
    }

    public HookPlayerChoiceContext(AbstractModel source, ulong localPlayerId, CombatState combatState, Task? task = null)
    {
        _localPlayerId = localPlayerId;
        Source = source;

        // It feels a little uncertain to me that the owner of the model will always be the owner of the GameAction.
        // If this ever changes, then we may need to find a more complex way to determine the owner of the GameAction.
        Owner = Source switch
        {
            CardModel cardModel => cardModel.Owner,
            RelicModel relicModel => relicModel.Owner,
            PotionModel potionModel => potionModel.Owner,
            AfflictionModel afflictionModel => afflictionModel.Card.Owner,
            EnchantmentModel enchantmentModel => enchantmentModel.Card.Owner,
            _ => null
        };

        if (Source is PowerModel powerModel)
        {
            // Powers are a little special since they can be owned by the enemy. We treat them as "owned" by the host,
            // for the purposes of who is running the hook. Note that there is currently no situation in which an enemy-
            // owned power can trigger a player choice, so this is largely theoretical.
            if (powerModel.Owner.IsPlayer)
            {
                Owner = powerModel.Owner.Player;
            }
            else
            {
                Owner = combatState.Players[0];
            }
        }

        PushModel(Source);

        Task = task;
    }

    public void MockDependenciesForTest(ActionQueueSynchronizer? actionQueueSynchronizer, ActionQueueSet? actionQueueSet, ActionExecutor? actionExecutor)
    {
        _actionQueueSet = actionQueueSet;
        _actionQueueSynchronizer = actionQueueSynchronizer;
        _actionExecutor = actionExecutor;
    }

    /// <summary>
    /// Most hooks take a PlayerChoiceContext as an argument and return a Task. However, this player choice context must
    /// know about the task in SignalPlayerChoiceBegun, when it is time to create a GameAction. There is no way to pass
    /// the Task to this method in the constructor, as the Task is not created yet. Thus, this method should be called
    /// after the hook returns the Task, for two reasons:
    /// - To assign the Task to this context
    /// - To wait for the synchronous parts of the hook to complete
    /// </summary>
    /// <returns>True if the task was completed, false if it was paused. If it was paused, the caller should await GameAction.Finished.</returns>
    public async Task<bool> AssignTaskAndWaitForPauseOrCompletion(Task task)
    {
        if (Source != null)
        {
            Task = ExecuteTaskThenInvokeExecutionFinished(task);
        }
        else
        {
            Task = task;
        }

        _taskAssignedCompletionSource.SetResult();
        await TaskHelper.WhenAny(task, _pausedCompletionSource.Task);
        return task.IsCompleted;
    }

    /// <summary>
    /// At the end of all hooks that involve models, the ExecutionFinished event must be executed to update some visuals
    /// for things that are listening to changes to the model. This task wraps the hook and calls ExecutionFinished at
    /// the end of it, if a model is involved with the hook.
    /// </summary>
    private async Task ExecuteTaskThenInvokeExecutionFinished(Task task)
    {
        await task;
        Source?.InvokeExecutionFinished();
    }

    public override async Task SignalPlayerChoiceBegun(PlayerChoiceOptions options)
    {
        // We are passed a task. However, calling a method returning a Task begins that task immediately. That task-returning
        // method might call this method even before whatever created us has a chance to pass us the task. This yield is
        // intended to give an opportunity for the creator to pass us the task.
        if (Task == null)
        {
            await _taskAssignedCompletionSource.Task;

            if (Task == null)
            {
                throw new InvalidOperationException("HookPlayerChoiceContext was never passed a task to await!");
            }
        }

        // If we're pausing for player choice for a second time, the game action might already be created. Don't create another one.
        if (_gameAction != null)
        {
            if (ActionExecutor.CurrentlyRunningAction != _gameAction)
            {
                Log.Error($"Tried to interrupt action {_gameAction} but the currently running action is {ActionExecutor.CurrentlyRunningAction}!");
                return;
            }
        }
        else
        {
            // This is the first time we're pausing for player choice. We must create a GameAction and synchronize its
            // order with other GameActions to preserve determinism.

            // This can occur if an affliction or enchantment is subscribed to the HookBus but the card does not have an
            // owner. There is currently no content for which this is possible, but it could be in the future.
            if (Owner == null)
            {
                throw new InvalidOperationException($"HookPlayerChoiceContext is assigned a model {Source} with no owner, but the model has requested a player choice! This is not supported");
            }

            // Generate the hook action. This assigns it an ID which is consistent across all peers because of the
            // deterministic execution of hooks. If the hook action was already enqueued from a remote source, this may
            // also return an existing action, which is why we cannot instantiate it directly here.
            _gameAction = ActionQueueSynchronizer.GenerateHookAction(Owner.NetId);

            // Since the action may already have been created as part of a different player's request, we set our local
            // choice context here, which allows the action to continue
            _gameAction.SetChoiceContext(this);

            // If we are the owner of the hook, request that the action is enqueued, ensuring correct ordering on the host
            if (_gameAction!.OwnerId == _localPlayerId)
            {
                ActionQueueSynchronizer.RequestEnqueueHookAction(_gameAction);
            }

            // Set this result to allow SetTaskAndWaitForPause to continue
            _pausedCompletionSource.SetResult();

            // Wait for two conditions before allowing the action to continue executing:
            // - The host has enqueued the action
            // - The action is the topmost in the player's queue
            await _gameAction.ExecutionStartedTask;
        }

        ActionQueueSet.PauseActionForPlayerChoice(_gameAction, options);
    }

    public override async Task SignalPlayerChoiceEnded()
    {
        // This should run only for the owning player - only one player resumes the action
        if (_gameAction!.OwnerId == _localPlayerId)
        {
            ActionQueueSynchronizer.RequestResumeActionAfterPlayerChoice(_gameAction!);
        }

        await _gameAction!.WaitForActionToResumeExecutingAfterPlayerChoice();
    }
}
