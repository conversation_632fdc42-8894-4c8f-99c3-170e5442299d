using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Game;

namespace MegaCrit.Sts2.Core.GameActions.Multiplayer;

public delegate Task<PlayerChoiceResult> PlayerChoiceDelegate();

public delegate Task PlayerChoiceContinuation(PlayerChoiceResult result);

/// <summary>
/// Synchronizes player choices that are gathered in the middle of a model's execution. Examples:
/// * The card picked for discarding when playing Survivor
/// * The card chosen to add to hand when playing Discovery
/// * The card chosen to add to hand at the beginning of combat from Toolbox
///
/// The basic scheme here is:
/// * All players (local and remote) generate an ID for the selection.
/// * The owning player brings up the card selection UI on their side.
/// * Once the owning player has made a choice, they send a message to all other players with the selection ID indicating
///   what they chose.
/// </summary>
public class PlayerChoiceSynchronizer : IDisposable
{
    private readonly List<uint> _choiceIds = [];
    private readonly List<ReceivedChoice> _receivedChoices = [];
    private readonly Logger _logger = new(nameof(PlayerChoiceSynchronizer), LogType.Actions);
    private readonly INetGameService _netService;
    private readonly IPlayerCollection _players;

    public IReadOnlyList<uint> ChoiceIds => _choiceIds;

    // This is called with both local and remote actions.
    // You should not attempt to turn NetPlayerChoiceResult into a PlayerChoiceResult when this is called! The player
    // choice result may reference a card that has not yet been created. This event is intended only for serialization
    // purposes.
    public event Action<Player, uint, NetPlayerChoiceResult>? PlayerChoiceReceived;

    private struct ReceivedChoice
    {
        public ulong senderId;
        public uint choiceId;
        public TaskCompletionSource<NetPlayerChoiceResult> completionSource;
    }

    public PlayerChoiceSynchronizer(INetGameService netService, IPlayerCollection players)
    {
        _players = players;
        _netService = netService;
        netService.RegisterMessageHandler<PlayerChoiceMessage>(OnPlayerChoiceMessageReceived);
    }

    public void Dispose()
    {
        _netService.UnregisterMessageHandler<PlayerChoiceMessage>(OnPlayerChoiceMessageReceived);
    }

    /// <summary>
    /// Reserves a choice ID to be passed to SyncLocalChoice or WaitForLocalChoice.
    /// If called during combat, this must be done in a deterministic context (i.e. before a GameAction is paused) so
    /// that checksums match up after action execution. SyncLocalChoice and WaitForRemoteChoice can be called outside of
    /// deterministic contexts (i.e. while a GameAction is paused and executing locally).
    /// </summary>
    /// <param name="player">The player for which the choice ID will be reserved.</param>
    public uint ReserveChoiceId(Player player)
    {
        int slotIndex = _players.GetPlayerSlotIndex(player);

        while (_choiceIds.Count <= slotIndex)
        {
            _choiceIds.Add(0);
        }

        uint choiceId = _choiceIds[slotIndex];

        // Increment the choice ID so the next choice we wait for is the new one.
        _choiceIds[slotIndex] = choiceId + 1;

        _logger.VeryDebug($"Reserved choice id {choiceId} for player {player.NetId}, next is {_choiceIds[slotIndex]}");
        return choiceId;
    }

    /// <summary>
    /// Sync a locally-chosen player choice with other peers.
    /// This should be called whenever a player choice is made by the local player. In tandem, WaitForRemoteChoice
    /// should be awaited on all remote peers.
    /// </summary>
    /// <param name="player">The player that chose the player choice. Should always be the local player.</param>
    /// <param name="choiceId">The ID of the choice, reserved through ReserveChoiceId.</param>
    /// <param name="result">The result of the player choice.</param>
    public void SyncLocalChoice(Player player, uint choiceId, PlayerChoiceResult result)
    {
        if (!ValidateChoiceId(player, choiceId)) throw new InvalidOperationException($"Tried to sync local choice with ID {choiceId} for player {player.NetId}, but player's choice ID is {GetChoiceId(player)}!");

        PlayerChoiceMessage message = new() {
            result = result.ToNetData(),
            choiceId = choiceId
        };

        _logger.Debug($"Sending player choice id {choiceId} for player {player.NetId}, result {result}");

        PlayerChoiceReceived?.Invoke(player, choiceId, message.result);
        _netService.SendMessage(message);
    }

    /// <summary>
    /// Waits for a choice to be received from a remote peer.
    /// This should be called on all remote peers whenever we expect a player choice to be made. On the machine of the
    /// player who is making a choice, SyncLocalChoice should be called.
    /// </summary>
    /// <param name="player">The player for whom we are awaiting a choice.</param>
    /// <param name="choiceId">The choice ID to await, obtained through ReserveChoiceId.</param>
    /// <returns>The result of the player choice that was made.</returns>
    public async Task<PlayerChoiceResult> WaitForRemoteChoice(Player player, uint choiceId)
    {
        if (_netService.Type == NetGameType.Singleplayer) throw new InvalidOperationException("Cannot wait for remote choice in singleplayer!");
        if (!ValidateChoiceId(player, choiceId)) throw new InvalidOperationException($"Tried to wait for remote choice with ID {choiceId} for player {player.NetId}, but player's choice ID is {GetChoiceId(player)}!");

        // The choice could already have come in while we were waiting for it, so check if we already received it first
        int existingIndex = _receivedChoices.FindIndex(c => c.choiceId == choiceId && c.senderId == player.NetId);
        ReceivedChoice choice;

        if (existingIndex >= 0)
        {
            _logger.Debug($"Was going to wait for remote choice {choiceId} for player {player.NetId} but we've already received it");
            choice = _receivedChoices[existingIndex];
            _receivedChoices.RemoveAt(existingIndex);
        }
        else
        {
            // Otherwise, create a new choice and wait for the result
            choice = new ReceivedChoice
            {
                choiceId = choiceId,
                senderId = player.NetId,
                completionSource = new TaskCompletionSource<NetPlayerChoiceResult>()
            };

            _logger.Debug($"Awaiting remote choice {choiceId} for player {player.NetId}");
            _receivedChoices.Add(choice);
        }

        NetPlayerChoiceResult netResult = await choice.completionSource.Task;
        PlayerChoiceResult result = PlayerChoiceResult.FromNetData(player, _players, netResult);
        _logger.Debug($"Finished waiting for remote choice {choiceId} for player {player.NetId}: {result}");
        return result;
    }

    private void OnPlayerChoiceMessageReceived(PlayerChoiceMessage message, ulong senderId)
    {
        _logger.Debug($"Received choice from {senderId} for choice ID {message.choiceId}: {message.result}");

        Player player = _players.GetPlayer(senderId)!;
        OnReceivePlayerChoice(player, message.choiceId, message.result);
    }

    public void FastForwardChoiceIds(List<uint> choiceIds)
    {
        _logger.Debug($"Fast-forwarded choice IDs to: {string.Join(",", choiceIds)}");

        _choiceIds.Clear();
        _choiceIds.AddRange(choiceIds);
    }

    public void ReceiveReplayChoice(Player player, uint choiceId, NetPlayerChoiceResult result)
    {
        OnReceivePlayerChoice(player, choiceId, result);
    }

    private void OnReceivePlayerChoice(Player player, uint choiceId, NetPlayerChoiceResult result)
    {
        PlayerChoiceReceived?.Invoke(player, choiceId, result);

        // We could already be waiting for the choice
        int existingIndex = _receivedChoices.FindIndex(c => c.choiceId == choiceId && c.senderId == player.NetId);
        ReceivedChoice choice;

        if (existingIndex >= 0)
        {
            _logger.Debug("We are already waiting for the choice, fulfilling the task");
            choice = _receivedChoices[existingIndex];
            _receivedChoices.RemoveAt(existingIndex);
        }
        else
        {
            // Otherwise, create a new choice
            choice = new ReceivedChoice
            {
                choiceId = choiceId,
                senderId = player.NetId,
                completionSource = new TaskCompletionSource<NetPlayerChoiceResult>()
            };

            _logger.Debug("We are not yet waiting for the choice, creating a new received choice");
            _receivedChoices.Add(choice);
        }

        choice.completionSource.SetResult(result);
    }

    private bool ValidateChoiceId(Player player, uint choiceId)
    {
        // Choice IDs which are awaited upon must be less than the next choice ID we expect to reserve for the player
        return choiceId < GetChoiceId(player);
    }

    private uint GetChoiceId(Player player)
    {
        int slotIndex = _players.GetPlayerSlotIndex(player);

        if (slotIndex >= _choiceIds.Count)
        {
            return 0;
        }

        return _choiceIds[slotIndex];
    }
}
