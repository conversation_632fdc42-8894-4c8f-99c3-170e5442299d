using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.CardPools;

namespace MegaCrit.Sts2.Core.Helpers;

public static class EnergyHelper
{
    /// <summary>
    /// Get the string that should be used as the prefix for a model's energy icon path.
    ///
    /// The logic here is a little more complicated than you'd expect; it's easy if it's a card in the player's deck
    /// during a climb (just use the owner's card pool), but it gets trickier before the card is in the deck (like in
    /// card rewards) or outside of a climb entirely (like in the card library).
    /// </summary>
    /// <param name="model">Model whose energy icon we want.</param>
    public static string GetIconPrefix(AbstractModel model) => GetPool(model).EnergyColorName;

    /// <summary>
    /// Get the string that should be used as the specified model's energy icon path.
    ///
    /// The logic here is a little more complicated than you'd expect; it's easy if it's a card in the player's deck
    /// during a climb (just use the owner's card pool), but it gets trickier before the card is in the deck (like in
    /// card rewards) or outside of a climb entirely (like in the card library).
    /// </summary>
    /// <param name="model">Model whose energy icon we want.</param>
    public static string GetIconPath(AbstractModel model)
    {
        return ImageHelper.GetImagePath($"atlases/ui_atlas.sprites/card/energy_{GetIconPrefix(model).ToLower()}.tres");
        //  return ImageHelper.GetImagePath($"packed/card_template/card_energy_{GetIconPrefix(model).ToLower()}_s.png");
    }

    /// <summary>
    /// Get the color that should be used for the text in a card's energy cost.
    /// Depends on a whole bunch of tricky rules, see comments and tests for details.
    /// </summary>
    /// <param name="card">Card whose energy cost color we want.</param>
    /// <param name="state">Combat state that the color depends on. Null outside of combat (like in the Card Library).</param>
    /// <returns>EnergyCostColor for the specified card in the specified combat state.</returns>
    public static EnergyCostColor GetCostColor(CardModel card, CombatState? state)
    {
        // If we're out of combat entirely, we are probably in the Card Library or in a weird spot during room
        // transition, so just always return the normal color.
        if (state == null) return EnergyCostColor.Unmodified;

        // Rule 1: Check if the card is unplayable. That wins over any other rules.
        if (card.BaseEnergyCost < 0 || !card.CanPlay(out UnplayableReason _, out _)) return EnergyCostColor.Unplayable;

        // Special rule: Cost X cards' energy is always rendered WHITE even if you have 0 energy.
        if (card.HasEnergyCostX) return EnergyCostColor.Unmodified;

        // Rule 2: Check if the card's energy cost is being dynamically modified by a hook. Examples:
        // * The next attack you play costs 0 (Unrelenting)
        // * Your cards cost an additional 1 energy (Enthralled)
        if (TryModifyCostWithHooks(card, state, out decimal hookModifiedCost))
        {
            // If a card's cost is modified by a HOOK...
            if (hookModifiedCost > card.BaseEnergyCost)
            {
                // ...and the cost of the card is ABOVE its base cost, the color should be BLUE.
                return EnergyCostColor.Increased;
            }
            else
            {
                // ...and the cost of the card is the SAME OR LOWER than its base, the color should be GREEN.
                return EnergyCostColor.Decreased;
            }
        }

        // Rule 3: Check if the card's energy cost has been modified by a time-limited effect. Examples:
        //
        // * Until the card is played (King's Gambit)
        // * For the rest of the turn OR until the card is played, whichever comes first (Infernal Blade).
        // * For the entire combat (Havoc).
        if (card.TemporaryEnergyCost != null)
        {
            return GetColorForTemporaryCost(card.TemporaryEnergyCost, card.BaseEnergyCost);
        }

        // At this point, we know no modifications have been made to the card's energy cost.
        return EnergyCostColor.Unmodified;
    }

    private static IPoolModel GetPool(AbstractModel model)
    {
        if (model is IPoolModel poolModel) return poolModel;

        Player? modelOwner = null;

        IPoolModel? pool = null;

        switch (model)
        {
            case CardModel card:
                if (card.IsMutable)
                {
                    modelOwner = card.Owner;
                }

                pool = card.Pool;
                break;
            case EnchantmentModel enchantment:
                if (enchantment.HasCard)
                {
                    modelOwner = enchantment.Card.Owner;
                    pool = enchantment.Card.Pool;
                }
                else
                {
                    // TODO: This is temporary, eventually we should be passing a player viewer to this method that we
                    // can use here.
                    pool = ModelDb.CardPool<ColorlessCardPool>();
                }

                break;
            case PotionModel potion:
                if (potion.IsMutable)
                {
                    modelOwner = potion.Owner;
                }

                pool = potion.Pool;
                break;
            case RelicModel relic:
                if (relic.IsMutable)
                {
                    modelOwner = relic.Owner;
                }

                pool = relic.Pool;
                break;
            case PowerModel power:
                if (power is { IsMutable: true, Owner.IsPlayer: true })
                {
                    modelOwner = power.Owner.Player;
                }
                else
                {
                    // TODO: This is temporary, eventually we should be passing a player viewer to this method that we
                    // can use here.
                    pool = ModelDb.CardPool<ColorlessCardPool>();
                }

                break;
        }

        if (modelOwner != null)
        {
            // If we know the associated player owner, return their card pool.
            return modelOwner.Character.CardPool;
        }
        else if (pool != null)
        {
            // If we can't figure out the owner from the model (like if we're in the card library), we can fall back to
            // the model's pool.
            return pool;
        }
        else
        {
            // If we don't have a player or a pool to work from (like if we're showing the HoverTip for a power that
            // hasn't been applied to a player creature), log an error and fall back to the Ironclad energy icon.
            Log.Error($"Model {model.Id} is not in any pool! It was probably deprecated without being removed.");
            return ModelDb.CardPool<IroncladCardPool>();
        }
    }

    private static EnergyCostColor GetColorForTemporaryCost(TemporaryCardCost tempCost, int baseCost)
    {
        if (tempCost.ClearsWhenTurnEnds)
        {
            // If a card's cost is modified for the TURN...
            if (tempCost.Cost > baseCost)
            {
                // ...and the cost of the card is ABOVE its base cost, the color should be BLUE.
                // (this is the same as when it's modified for COMBAT, but keeping them separate makes it easier to
                // follow).
                return EnergyCostColor.Increased;
            }
            else
            {
                // ...and the cost of the card is the SAME OR LOWER than its base, the color should be GREEN.
                return EnergyCostColor.Decreased;
            }
        }
        else
        {
            // If a card's cost is modified for the COMBAT...
            if (tempCost.Cost > baseCost)
            {
                // ...and the cost of the card is ABOVE its base cost, the color should be BLUE.
                // (this is the same as when it's modified for TURN, but keeping them separate makes it easier to
                // follow).
                return EnergyCostColor.Increased;
            }
            else
            {
                // ...and the cost of the card is the SAME OR LOWER than its base, the color should be WHITE.
                return EnergyCostColor.Unmodified;
            }
        }
    }

    private static bool TryModifyCostWithHooks(CardModel card, CombatState state, out decimal hookModifiedCost)
    {
        hookModifiedCost = card.BaseEnergyCost;
        bool modifiedByHook = false;

        foreach (AbstractModel model in state.IterateHookListeners())
        {
            modifiedByHook |= model.TryModifyEnergyCost(card, hookModifiedCost, out hookModifiedCost);
        }

        return modifiedByHook;
    }
}
