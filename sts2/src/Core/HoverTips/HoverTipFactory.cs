using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.HoverTips;

public static class HoverTipFactory
{
    private static readonly Dictionary<CardKeyword, HoverTip> _keywordHoverTips = new();
    private static readonly Dictionary<ModelId, HoverTip> _modelHoverTips = new();
    private static readonly Dictionary<ModelId, HoverTip> _orbHoverTips = new();


    public static IEnumerable<IHoverTip> FromEnchantment<T>(int amount = 1) where T : EnchantmentModel
    {
        EnchantmentModel enchantment = ModelDb.Enchantment<T>().ToMutable();
        enchantment.Amount = amount;
        enchantment.RecalculateValues();

        return enchantment.HoverTips;
    }

    public static IEnumerable<IHoverTip> FromAffliction<T>(int amount = 1) where T : AfflictionModel
    {
        AfflictionModel affliction = ModelDb.Affliction<T>().ToMutable();
        affliction.Amount = amount;

        return affliction.HoverTips;
    }

    public static IHoverTip FromKeyword(CardKeyword keyword)
    {
        // TemporaryEthereal should look the same as Ethereal to the player
        if (keyword == CardKeyword.TemporaryEthereal) return FromKeyword(CardKeyword.Ethereal);

        if (!_keywordHoverTips.ContainsKey(keyword))
        {
            _keywordHoverTips[keyword] = new HoverTip(keyword.GetTitle(), keyword.GetDescription());
        }

        return _keywordHoverTips[keyword];
    }

    public static IHoverTip FromPower<T>() where T : PowerModel
    {
        PowerModel model = ModelDb.Power<T>();
        return FromPower(model);
    }

    public static IHoverTip FromPower(PowerModel model)
    {
        _modelHoverTips.TryAdd(model.Id, model.DumbHoverTip);
        return _modelHoverTips[model.Id];
    }

    public static IHoverTip FromPotion<T>() where T : PotionModel
    {
        PotionModel model = ModelDb.Potion<T>();
        return FromPotion(model);
    }

    public static IHoverTip FromPotion(PotionModel model)
    {
        _modelHoverTips.TryAdd(model.Id, model.HoverTip);
        SaveManager.Instance.MarkPotionAsSeen(model);
        return _modelHoverTips[model.Id];
    }

    public static IHoverTip FromOrb<T>() where T : OrbModel
    {
        OrbModel model = ModelDb.Orb<T>();

        if (!_orbHoverTips.ContainsKey(model.Id))
        {
            _orbHoverTips[model.Id] = model.DumbHoverTip;
        }

        return _orbHoverTips[model.Id];
    }

    public static IEnumerable<IHoverTip> FromRelic<T>() where T : RelicModel
    {
        RelicModel relic = ModelDb.Relic<T>();
        return FromRelic(relic);
    }

    public static IEnumerable<IHoverTip> FromRelicExcludingItself<T>() where T : RelicModel
    {
        RelicModel relic = ModelDb.Relic<T>();
        return FromRelicExcludingItself(relic);
    }

    public static IEnumerable<IHoverTip> FromRelic(RelicModel relic)
    {
        SaveManager.Instance.MarkRelicAsSeen(relic);
        return relic.HoverTips;
    }

    public static IEnumerable<IHoverTip> FromRelicExcludingItself(RelicModel relic)
    {
        SaveManager.Instance.MarkRelicAsSeen(relic);
        return relic.HoverTipsExcludingRelic;
    }

    /// <summary>
    /// Generate a HoverTip-sized preview of the specified card along with the card's hovertips.
    /// </summary>
    /// <param name="inheritsUpgrades">Whether or not this HoverTip should inherit upgrades from the card generating it. Only relevant for card HoverTips.</param>
    /// <typeparam name="T">CardModel subclass</typeparam>
    /// <returns>HoverTip card preview</returns>
    public static IEnumerable<IHoverTip> FromCardWithCardHoverTips<T>(bool inheritsUpgrades = false) where T : CardModel => ((IHoverTip[]) [FromCard<T>()]).Concat(ModelDb.Card<T>().HoverTips);

    /// <summary>
    /// Generate a HoverTip-sized preview of the specified card.
    /// </summary>
    /// <param name="upgrade">Whether the card should appear upgraded.</param>
    /// <typeparam name="T">CardModel subclass</typeparam>
    /// <returns>HoverTip card preview</returns>
    public static IHoverTip FromCard<T>(bool upgrade = false) where T : CardModel
    {
        return FromCard(ModelDb.Card<T>(), upgrade);
    }

    /// <summary>
    /// Generate a HoverTip-sized preview of the specified card.
    /// Note: You should usually use the generic version of this method. Only use this version if you need to modify
    /// the base version of the card (like to show an upgrade or something).
    /// </summary>
    /// <param name="card">CardModel to preview</param>
    /// <param name="upgrade">Whether the card should appear upgraded.</param>
    /// <returns>HoverTip card preview</returns>
    public static IHoverTip FromCard(CardModel card, bool upgrade = false)
    {
        if (upgrade)
        {
            // We use MutableClone instead of ToMutable here because we may have passed an already-mutable card (like
            // when Thieving Hopper displays the card it stole), but we don't want this upgrade preview to affect that
            // card.
            card = (CardModel)card.MutableClone();
            card.UpgradeInternal();
            card.FinalizeUpgradeInternal();
        }

        SaveManager.Instance.MarkCardAsSeen(card);
        return new CardHoverTip(card);
    }

    public static IHoverTip Static(StaticHoverTip tip, params DynamicVar[] vars)
    {
        string key = StringHelper.Slugify(tip.ToString());
        LocString title = L10NStatic($"{key}.title");
        LocString description = L10NStatic($"{key}.description");

        foreach (DynamicVar dynamicVar in vars)
        {
            title.Add(dynamicVar);
            description.Add(dynamicVar);
        }

        return new HoverTip(title, description);
    }

    public static IHoverTip ForEnergy(CardModel card) => ForEnergyWithIconPath(EnergyHelper.GetIconPath(card));
    public static IHoverTip ForEnergy(PotionModel potion) => ForEnergyWithIconPath(EnergyHelper.GetIconPath(potion));
    public static IHoverTip ForEnergy(PowerModel power) => ForEnergyWithIconPath(EnergyHelper.GetIconPath(power));
    public static IHoverTip ForEnergy(Player player) => ForEnergyWithIconPath(EnergyHelper.GetIconPath(player.Character.CardPool));
    public static IHoverTip ForEnergy(RelicModel relic) => ForEnergyWithIconPath(EnergyHelper.GetIconPath(relic));

    private static IHoverTip ForEnergyWithIconPath(string path)
    {
        return new HoverTip(
            L10NStatic("ENERGY.title"),
            L10NStatic("ENERGY.description"),
            PreloadManager.Cache.GetTexture2D(path)
        );
    }

    public static IEnumerable<IHoverTip> FromForge() => [FromCard<SovereignBlade>(), Static(StaticHoverTip.Forge)];

    private static LocString L10NStatic(string entry)
    {
        return new LocString("static_hover_tips", entry);
    }
}
