using System.Collections.Generic;
using System.Linq;

namespace MegaCrit.Sts2.Core.HoverTips;

public interface IHoverTip
{
    public string Id { get; }

    public bool IsSmart { get; }

    public bool IsDebuff { get; }

    public bool IsInstanced { get; }

    public static IEnumerable<IHoverTip> RemoveDupes(IEnumerable<IHoverTip> tips)
    {
        List<IHoverTip> results = [];

        foreach (IHoverTip hoverTip in tips)
        {
            if (string.IsNullOrEmpty(hoverTip.Id))
            {
                // Always add HoverTips with no ID.
                results.Add(hoverTip);
            }
            else
            {
                IHoverTip? existing = results.FirstOrDefault(tip => tip.Id == hoverTip.Id && !tip.IsInstanced);

                if (existing == null)
                {
                    // If we don't have an existing tip with the same ID, we can safely add it.
                    results.Add(hoverTip);
                }
                else if (!existing.IsSmart || hoverTip.IsSmart)
                {
                    // If the new tip is at least as smart as the existing one, remove the existing one and add the new
                    // one.
                    // Removing the existing one even when the new one is equally smart lets us show "shared" HoverTips
                    // beneath all the models they belong to. For example, if two relics give block, this will cause the
                    // block HoverTip to appear after both of them.
                    results.Remove(existing);
                    results.Add(hoverTip);
                }
            }
        }

        return results;
    }
}

public static class HoverTipExtensions
{
    /// <summary>
    /// Tries to add the tip to the list if the list doesn't already contain a tip of the same type that is equal or
    /// smarter than it.
    /// </summary>
    /// <param name="tips">List we are trying to add to.</param>
    /// <param name="tip">Tip we are trying to add.</param>
    public static void MegaTryAddingTip(this ICollection<IHoverTip> tips, IHoverTip tip)
    {
        IHoverTip? tipCopy = tips.FirstOrDefault(t => t.Id == tip.Id);

        if (tipCopy is { IsInstanced: false })
        {
            // If our tip is smarter, then replace the old tip with it.
            if (!tipCopy.IsSmart && tip.IsSmart)
            {
                tips.Remove(tipCopy);
                tips.Add(tip);
            }
        }
        else
        {
            // We don't have this tip yet.
            tips.Add(tip);
        }
    }
}
