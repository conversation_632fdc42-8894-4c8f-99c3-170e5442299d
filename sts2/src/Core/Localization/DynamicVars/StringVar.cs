namespace MegaCrit.Sts2.Core.Localization.DynamicVars;

public class StringVar : DynamicVar
{
    // ReSharper disable once UnusedAutoPropertyAccessor.Global
    // Reason: Used by localization.
    public string StringValue { get; set; }

    public StringVar(string name, string baseValue = "") : base(name, 0)
    {
        StringValue = baseValue;
    }

    public override string ToString() => StringValue;
}