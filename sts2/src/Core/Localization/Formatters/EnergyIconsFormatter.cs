using System;
using System.Linq;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Characters;
using SmartFormat.Core.Extensions;

namespace MegaCrit.Sts2.Core.Localization.Formatters;

public class EnergyIconsFormatter : IFormatter
{
    private IClimbState _climbState;

    public string Name
    {
        get => "energyIcons";
        set => throw new NotImplementedException();
    }

    public bool CanAutoDetect { get; set; }

    public EnergyIconsFormatter(IClimbState climbState)
    {
        _climbState = climbState;
    }

    public void SetClimbState(IClimbState climbState)
    {
        _climbState = climbState;
    }

    public bool TryEvaluateFormat(IFormattingInfo formattingInfo)
    {
        CharacterModel character = LocalContext.GetMe(_climbState)?.Character ?? ModelDb.Character<Ironclad>();
        string energyPrefix = EnergyHelper.GetIconPrefix(character.CardPool);
        int count;

        switch (formattingInfo.CurrentValue)
        {
            case EnergyVar energyVar:
                // Format: {Energy:energyIcons()}
                count = Convert.ToInt32(energyVar.PreviewValue);
                energyPrefix = string.IsNullOrEmpty(energyVar.ColorPrefix) ? energyPrefix : energyVar.ColorPrefix;
                break;
            case decimal value:
                // Format: {someNumber:energyIcons()}
                count = (int)value;
                break;
            case int value:
                // Format: {someNumber:energyIcons()}
                count = value;
                break;
            case string prefix:
                // Format: {energyPrefix:energyIcons(1)}
                if (!int.TryParse(formattingInfo.FormatterOptions, out count))
                {
                    return false;
                }

                energyPrefix = prefix;
                break;
            default:
                throw new LocException($"Unknown value='{formattingInfo.CurrentValue}' type={formattingInfo.CurrentValue?.GetType()}");
        }

        string text;
        if (count < 4)
        {
            text = string.Concat(Enumerable.Repeat($"[img]res://images/packed/sprite_fonts/{energyPrefix}_energy_icon.png[/img]", count));
        }
        else
        {
            text = $"{count}[img]res://images/packed/sprite_fonts/{energyPrefix}_energy_icon.png[/img]";
        }

        formattingInfo.Write(text);

        return true;
    }
}
