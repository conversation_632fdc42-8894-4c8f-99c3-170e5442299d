using System;
using System.Globalization;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using SmartFormat.Core.Extensions;

namespace MegaCrit.Sts2.Core.Localization.Formatters;

public class PercentMoreFormatter : IFormatter
{
    public string Name
    {
        get => "percentMore";
        set => throw new NotImplementedException();
    }

    public bool CanAutoDetect { get; set; }

    public bool TryEvaluateFormat(IFormattingInfo formattingInfo)
    {
        decimal startingValue;

        switch (formattingInfo.CurrentValue)
        {
            case DynamicVar dynamicVar:
                startingValue = dynamicVar.BaseValue;
                break;
            default:
                try
                {
                    startingValue = Convert.ToDecimal(formattingInfo.CurrentValue);
                }
                catch (FormatException)
                {
                    return false;
                }
                catch (InvalidCastException)
                {
                    return false;
                }

                break;
        }

        int formattedValue = Convert.ToInt32((startingValue - 1) * 100);
        formattingInfo.Write(formattedValue.ToString(CultureInfo.InvariantCulture));
        return true;
    }
}