using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text.Json.Serialization;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Localization;

/// <summary>
/// A Localized String, uses:
/// https://github.com/axuno/SmartFormat
/// </summary>
public class LocString(string locTable, string locEntryKey) : IComparable<LocString>
{
    [JsonIgnore]
    private readonly Dictionary<string, object> _variables = new();

    [JsonPropertyName("table")]
    public string LocTable => locTable;

    [JsonPropertyName("key")]
    public string LocEntryKey => locEntryKey;

    [JsonIgnore]
    [SuppressMessage(
        "ReSharper",
        "MemberCanBePrivate.Global",
        Justification = "Part of LocString API."
    )]
    public bool IsEmpty => string.IsNullOrEmpty(LocEntryKey) && string.IsNullOrEmpty(LocTable);

    [JsonIgnore]
    public IReadOnlyDictionary<string, object> Variables => _variables;

    public static bool Exists(string table, string key) => LocManager.Instance.GetTable(table).HasEntry(key);
    public static LocString? GetIfExists(string table, string key) => Exists(table, key) ? new LocString(table, key) : null;

    /// <summary>
    /// Returns a formatted string, ie:
    /// "Deal 6 damage."
    /// rather than:
    /// "Deal {Damage:diff()} damage."
    /// </summary>
    public string GetFormattedText() => LocManager.Instance.SmartFormat(this, _variables);

    /// <summary>
    /// Returns the raw text in the JSON file, ie:
    /// "Deal {Damage:diff()} damage."
    /// rather than:
    /// "Deal 6 damage."
    /// </summary>
    public string GetRawText() => LocManager.Instance.GetTable(LocTable).GetRawText(LocEntryKey);

    public bool Exists() => Exists(LocTable, LocEntryKey);

    public static bool IsNullOrWhitespace(LocString? locString)
    {
        return locString == null || locString.IsEmpty || string.IsNullOrWhiteSpace(locString.GetRawText());
    }

    public static void SubscribeToLocaleChange(LocManager.LocaleChangeCallback callback)
    {
        // TODO: Maybe keep track of a name for each of these? It might make it easier to track to prevent memory-leaking
        LocManager.Instance.SubscribeToLocaleChange(callback);
    }

    public static void UnsubscribeToLocaleChange(LocManager.LocaleChangeCallback callback)
    {
        LocManager.Instance.UnsubscribeToLocaleChange(callback);
    }

    public void Add(DynamicVar dynamicVar) => AddObj(dynamicVar.Name, dynamicVar);
    public void Add(string name, decimal variable) => AddObj(name, variable);
    public void Add(string name, bool variable) => AddObj(name, variable);
    public void Add(string name, string variable) => AddObj(name, variable);
    public void Add(string name, IList<string> variable) => AddObj(name, variable);

    public void Add(string name, LocString variable)
    {
        // Warning: There's a chance doing the formatting here is too early. If so, it should be done in a
        // source or formatter. But for now, it's simpler and more performant to do upfront.
        AddObj(name, variable.GetFormattedText());
    }

    public void AddObj(string name, object variable)
    {
        ArgumentException.ThrowIfNullOrEmpty(name);
        ArgumentNullException.ThrowIfNull(variable);

        name = name.Replace(' ', '-');

        if (!_variables.TryAdd(name, variable))
        {
            _variables[name] = variable;
        }
    }

    private object this[string key]
    {
        get => _variables[key];
        set => _variables[key] = value;
    }

    // Example: "Intents/DEFEND.title" is parsed to load the "DEFEND.title" key from the "Intents" table
    public static LocString KeyPathToLocString(string keyPath)
    {
        string[] locParts = keyPath.Split('/');
        return new LocString(locParts[0], locParts[1]);
    }

    public void AddVariablesFrom(LocString smartDescription)
    {
        foreach (string key in smartDescription._variables.Keys)
        {
            AddObj(key, smartDescription[key]);
        }
    }

    /// <summary>
    /// Helper function to grab a random tableEntry for when we want to get a random loc string.
    ///
    /// Example:
    /// If there are multiple entries of MAP_POINT_HISTORY.abandon in climb_history.json and you want to get a random
    /// entry:
    ///
    /// The JSON would be constructed like:
    /// "MAP_POINT_HISTORY.abandon.0": "{character} had simply given up.",
    /// "MAP_POINT_HISTORY.abandon.1": "{character} was tired...",
    /// "MAP_POINT_HISTORY.abandon.2": "{character} abandoned {pronounPossessive} destiny.",
    ///
    /// You can grab a random entry via:
    /// LocString abandonMessage = new(
    ///     "climb_history",
    ///     LocString.GetRandomKey("climb_history", "MAP_POINT_HISTORY.abandon.")
    /// );
    ///
    /// </summary>
    /// <param name="table">Name of the table to pull the keys from.</param>
    /// <param name="keyPrefix">Prefix for the keys we want.</param>
    /// <param name="rng">Optional rng. Chaotic will be used if not specified.</param>
    /// <returns></returns>
    public static string GetRandomKey(string table, string keyPrefix, Rng? rng = null)
    {
        IEnumerable<string> options = LocManager.Instance.GetTable(table).Keys.Where(k => k.StartsWith(keyPrefix));
        rng ??= Rng.Chaotic;
        return rng.NextItem(options)!;
    }

    public int CompareTo(LocString? other)
    {
        if (locTable != other?.LocTable)
        {
            return string.Compare(LocTable, other?.LocTable, StringComparison.Ordinal);
        }

        return string.Compare(LocEntryKey, other.LocEntryKey, StringComparison.Ordinal);
    }
}
