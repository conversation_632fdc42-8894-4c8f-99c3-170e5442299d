using System.Collections.Generic;
using System.Linq;

namespace MegaCrit.Sts2.Core.Localization;

public class LocTable
{
    private readonly string _name;
    private readonly Dictionary<string, string> _translations;

    public LocTable(string name, Dictionary<string, string> data)
    {
        _name = name;
        _translations = data;
    }

    public IEnumerable<string> Keys => _translations.Keys;

    public void MergeWith(Dictionary<string, string> otherTable)
    {
        foreach (KeyValuePair<string, string> otherPair in otherTable)
        {
            _translations[otherPair.Key] = otherPair.Value;
        }
    }

    public LocString GetLocString(string key)
    {
        if (!_translations.ContainsKey(key))
        {
            throw new LocException($"Key={key} not found in table={_name}");
        }

        return new LocString(_name, key);
    }

    public string GetRawText(string key)
    {
        if (!_translations.TryGetValue(key, out string? text))
        {
            throw new LocException($"Key={key} not found in table={_name}");
        }

        return text;
    }

    public IReadOnlyList<LocString> GetLocStringsWithPrefix(string keyPrefix)
    {
        return _translations.Where(kvp => kvp.Key.Contains(keyPrefix))
            .Select(kvp => new LocString(_name, kvp.Key))
            .ToList();
    }

    public bool HasEntry(string key) => _translations.ContainsKey(key);
}
