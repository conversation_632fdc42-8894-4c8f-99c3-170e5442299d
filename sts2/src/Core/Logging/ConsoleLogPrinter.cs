using System.Diagnostics;
using Godot;

namespace MegaCrit.Sts2.Core.Logging;

// This log printer is used when the game is run from an IDE, CLI, production, etc. It prints logs without color codes
// and only prints the stacktrace for errors. It's intended to be performant and easy to read when in plain text form.
public class ConsoleLogPrinter : ILogPrinter
{
    public void Print(LogLevel logLevel, string text, int skipFrames)
    {
        skipFrames++; // Skip this method
        string logLevelStr = logLevel.ToString().ToUpper();
        switch (logLevel)
        {
            case LogLevel.Error:
            {
                StackTrace stackTrace = new(skipFrames, true);
                GD.PrintErr($"[{logLevelStr}] {text}\n{stackTrace}"); // Errors are printed to stderr with a stacktrace
                break;
            }
            case LogLevel.Warn:
                GD.Print($"[{logLevelStr}] {text}"); // Warnings are printed to stdout without a stacktrace
                break;
            case LogLevel.Load:
            case LogLevel.VeryDebug:
            case LogLevel.Debug:
            case LogLevel.Info:
            default:
                GD.Print($"[{logLevelStr}] {text}"); // All other log levels are printed to stdout with no stacktrace
                break;
        }
    }
}
