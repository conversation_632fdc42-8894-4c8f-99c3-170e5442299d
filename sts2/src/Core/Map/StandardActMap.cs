using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Map;

// Handles the creation of a standard Act map, abiding by the point placement rules.
public sealed class StandardActMap : ActMap
{
    // can have up to 15 as part of BellOfTwilight.
    // required for the elite grab bag.
    public const int maxElites = 15;

    private const int _iterations = 7;
    private const int _mapWidth = 7;

    private readonly MapPointTypeCounts _pointTypeCounts;

    private readonly int _mapLength;
    private readonly Rng _rng;

    public override MapPoint BossMapPoint { get; }
    public override MapPoint StartingMapPoint { get; }

    // We only want the Bell of Twilight relic to proc on a map once
    // since in multiplayer, different players can have the relic
    public bool IsAffectedByBellOfTwilight { get; set; }

    // TODO: if we get another one of these, probably want to build something more robust
    public bool IsAffectedByMysteryMachine { get; set; }
    protected override MapPoint?[,] Grid { get; }

    /// <summary>
    /// Creates a standard map used for most acts.
    /// </summary>
    /// <param name="mapRng">The rng for the map.</param>
    /// <param name="actModel">The act to generate the map for </param>
    /// <param name="isMultiplayer">In multiplayer, we remove one node from each map.</param>
    /// <param name="mapPointTypeCountsOverride">Overrides the map point type counts from the map passed in</param>
    /// <param name="enablePruning">Used for tests, should always be true if used for gameplay logic</param>
    public StandardActMap(Rng mapRng, ActModel actModel, bool isMultiplayer, MapPointTypeCounts? mapPointTypeCountsOverride = null, bool enablePruning = true)
    {
        _mapLength = actModel.NumberOfRooms + 1; // extra row to account for the ancient node

        if (isMultiplayer)
        {
            // In multiplayer, all maps are one node shorter
            _mapLength--;
        }

        Grid = new MapPoint[_mapWidth, _mapLength];
        _rng = mapRng;

        // If specified, use override, otherwise just assign the act map point types
        _pointTypeCounts = mapPointTypeCountsOverride ?? actModel.GetMapPointTypes(mapRng);

        BossMapPoint = new MapPoint(GetColumnCount() / 2, GetRowCount(), _rng.NextInt());
        StartingMapPoint = new MapPoint(GetColumnCount() / 2, 0, _rng.NextInt());

        GenerateMap();
        AssignPointTypes();

        if (enablePruning) // enabled for automated tests
        {
            int count = 0;
            List<List<MapPoint[]>> segments = FindMatchingSegments();
            while (PrunePaths(segments))
            {
                count += 1;
                if (count > 50) throw new InvalidOperationException($"Unable to prune matching segments in {count} iterations");
                segments = FindMatchingSegments();
            }
        }

        // Post-process: adjust the MapPoint positions on the grid to make the map look better
        Grid = MapPostProcessing.CenterGrid(Grid);
        Grid = MapPostProcessing.SpreadAdjacentMapPoints(Grid);
        Grid = MapPostProcessing.StraightenPaths(Grid);
    }

    public static StandardActMap CreateFor(ClimbState climbState)
    {
        return new StandardActMap(
            // We generate a separate RNG here so that it can generate from a clean slate when loading.
            new Rng(climbState.Rng.Seed, $"act_{climbState.CurrentActIndex + 1}_map"),
            climbState.Act,
            climbState.Players.Count > 1
        );
    }

    private MapPoint GetOrCreateMapPoint(MapCoord coord) => GetOrCreatePoint(coord.col, coord.row);

    private MapPoint GetOrCreatePoint(int col, int row)
    {
        // If the point for that row and column exists,
        MapPoint? child = GetPoint(col, row);
        if (child != null) return child;

        // Otherwise, create a new one and assign it to that row and column.
        child = new MapPoint(col, row, _rng.NextInt());
        Grid[col, row] = child;

        return child;
    }

    private void PathGenerate(MapPoint startingPoint)
    {
        MapPoint current = startingPoint;
        while (current.coord.row < _mapLength - 1)
        {
            MapCoord next = GenerateNextCoord(current);
            MapPoint child = GetOrCreateMapPoint(next);
            current.AddChildPoint(child);

            // And move on to the next point.
            current = child;
        }
    }

    private MapCoord GenerateNextCoord(MapPoint current)
    {
        int centerOfCurrent = current.coord.col;
        int leftOfCurrent = Mathf.Max(0, centerOfCurrent - 1);
        int rightOfCurrent = Mathf.Min(centerOfCurrent + 1, _mapWidth - 1);

        // Choose random integer -1, 0, or 1 which represent left, center, or right.
        List<int> directions = [-1, 0, 1];
        directions.StableShuffle(_rng);
        foreach (int direction in directions)
        {
            int nextNodeRow = current.coord.row + 1;
            int nextNodeCol = direction switch
            {
                -1 => leftOfCurrent,
                0 => centerOfCurrent,
                1 => rightOfCurrent,
                _ => throw new InvalidOperationException("This isn't possible")
            };
            // Avoid choosing a direction which would cause a path crossover
            if (HasInvalidCrossover(current, nextNodeCol)) continue;
            return new MapCoord { col = nextNodeCol, row = nextNodeRow };
        }

        throw new InvalidOperationException($"Cannot find next node: seed={_rng.Seed}");
    }

    // This function checks if a point path to the next child would cross over an adjacent point's path
    // which isn't allowed.
    private bool HasInvalidCrossover(MapPoint current, int targetX)
    {
        // If the xDirection is > 0, we're going to the right, if < 0 we're going to the left
        int xDirection = targetX - current.coord.col;
        System.Diagnostics.Debug.Assert(Math.Abs(xDirection) < 2, "The path is somehow crossing over more than one X-value");
        if (xDirection is 0 or _mapWidth) return false;

        MapPoint? neighbor = Grid[targetX, current.coord.row];
        // If current's neighbor in the direction of our offset has a path in the opposite direction
        if (neighbor == null) return false;

        foreach (MapPoint child in neighbor.Children)
        {
            // check if the paths cross by seeing if our x-direction is opposite that of the neighbor's
            // child's direction.
            int childXDirection = child.coord.col - neighbor.coord.col;

            if (childXDirection == -xDirection)
            {
                return true;
            }
        }

        return false;
    }

    private void GenerateMap()
    {
        // Rotate through each of the starting points.
        for (int i = 0; i < _iterations; i++)
        {
            MapPoint startMapPoint = GetOrCreatePoint(_rng.NextInt(0, _mapWidth), 1);

            // If this is our second iteration, make sure we pick a new start point, there must be at least 2 paths
            if (i == 1)
            {
                while (startMapPoints.Contains(startMapPoint))
                {
                    startMapPoint = GetOrCreatePoint(_rng.NextInt(0, _mapWidth), 1);
                }
            }

            startMapPoints.Add(startMapPoint);

            PathGenerate(startMapPoint);
        }

        // Add the boss point as a child to all the last points
        ForEachInRow(Grid, GetRowCount() - 1, x => x.AddChildPoint(BossMapPoint));
        // Add the ancient as the parent to all the first points
        ForEachInRow(Grid, 1, x => StartingMapPoint.AddChildPoint(x));
    }

    private void RemovePoint(MapPoint mapPoint)
    {
        Grid[mapPoint.coord.col, mapPoint.coord.row] = null;
        startMapPoints.Remove(mapPoint); // remove if it exists

        foreach (MapPoint child in mapPoint.Children.ToList())
        {
            mapPoint.RemoveChildPoint(child);
        }

        foreach (MapPoint parent in mapPoint.parents.ToList())
        {
            parent.RemoveChildPoint(mapPoint);
        }
    }

    private static void ForEachInRow(MapPoint?[,] grid, int rowIndex, Action<MapPoint> processor)
    {
        for (int i = 0; i < grid.GetLength(0); i++)
        {
            MapPoint? mapPoint = grid[i, rowIndex];

            if (mapPoint != null)
            {
                processor(mapPoint);
            }
        }
    }

    private void AssignPointTypes()
    {
        // Last row should be campfires
        ForEachInRow(Grid, GetRowCount() - 1, p => p.PointType = MapPointType.RestSite);

        // The row 7 floors from the top (roughly the middle) should always be treasures
        ForEachInRow(Grid, GetRowCount() - 7, p => p.PointType = MapPointType.Treasure);

        // Bottom row should be monsters
        ForEachInRow(Grid, 1, p => p.PointType = MapPointType.Monster);

        // Create queue and populate with all point types
        List<MapPointType> toBeAssigned = new();

        // Add Rest Sites
        for (int i = 0; i < _pointTypeCounts.NumOfRests; i++)
        {
            toBeAssigned.Add(MapPointType.RestSite);
        }

        // Add Shops
        for (int i = 0; i < _pointTypeCounts.NumOfShops; i++)
        {
            toBeAssigned.Add(MapPointType.Shop);
        }

        // Add Elites
        for (int i = 0; i < _pointTypeCounts.NumOfElites; i++)
        {
            toBeAssigned.Add(MapPointType.Elite);
        }

        // Add Unknown Points
        for (int i = 0; i < _pointTypeCounts.NumOfUnknowns; i++)
        {
            toBeAssigned.Add(MapPointType.Unknown);
        }

        // Convert to a queue
        Queue<MapPointType> pointTypesToBeAssigned = new(toBeAssigned);
        AssignRemainingTypesToRandomPoints(pointTypesToBeAssigned);

        // There should be one shop in rows 2-4
        EnsureRowsContainsPointType(MapPointType.Shop, GetRows(firstRow: 2, lastRow: 4));

        // Fill remaining blank points with monsters
        foreach (MapPoint n in GetAllMapPoints().Where(x => x.PointType == MapPointType.Unassigned))
        {
            n.PointType = MapPointType.Monster;
        }

        // Assign the boss map point to be boss
        BossMapPoint.PointType = MapPointType.Boss;
        StartingMapPoint.PointType = MapPointType.Ancient;
    }

    private void EnsureRowsContainsPointType(MapPointType pointType, List<List<MapPoint>> rows)
    {
        // If the rows already have the type, ignore.
        if (RowsContainPointType(pointType, rows)) return;

        Queue<MapPointType> toAssign = new();
        toAssign.Enqueue(pointType);

        // Otherwise, assign the type to one of those rows
        AssignPointTypesToRandomRows(toAssign, rows);
    }

    private static bool RowsContainPointType(MapPointType pointType, IEnumerable<List<MapPoint>> rows)
    {
        return rows.SelectMany(row => row.Where(p => p.PointType == pointType)).Any();
    }

    // Iterate over the map and return the points at that row
    private List<List<MapPoint>> GetRows(int firstRow, int lastRow)
    {
        List<List<MapPoint>> rows = [];
        for (int y = firstRow; y <= lastRow; y++)
        {
            List<MapPoint> row = [];

            for (int x = 0; x < _mapWidth; x++)
            {
                MapPoint? mapPoint = Grid[x, y];

                if (mapPoint != null)
                {
                    row.Add(mapPoint);
                }
            }

            rows.Add(row);
        }

        return rows;
    }

    /// <summary>
    /// Assigns point types to the map by row to ensure that the point types are more evenly distributed.
    /// </summary>
    /// <param name="pointTypesToBeAssigned">Point types left to be assigned.</param>
    /// <param name="rows">Rows of map points to assign the types to.</param>
    private void AssignPointTypesToRandomRows(Queue<MapPointType> pointTypesToBeAssigned, List<List<MapPoint>> rows)
    {
        // Unstable is used, so we don't have to make equality functions for lists of points.
        rows.UnstableShuffle(_rng);

        foreach (List<MapPoint> row in rows)
        {
            row.StableShuffle(_rng);

            foreach (MapPoint point in row.Where(r => r.PointType == MapPointType.Unassigned))
            {
                MapPointType pointType = GetNextValidPointType(pointTypesToBeAssigned, point);

                point.PointType = pointType; // Assign the point type to the point.
                break; // if successful, move on to the next row
            }
        }
    }

    private void AssignRemainingTypesToRandomPoints(Queue<MapPointType> pointTypesToBeAssigned)
    {
        List<MapPoint> shuffledPoints = GetAllMapPoints().ToList();
        shuffledPoints.StableShuffle(_rng);

        foreach (MapPoint point in shuffledPoints.Where(p => p.PointType == MapPointType.Unassigned))
        {
            point.PointType = GetNextValidPointType(pointTypesToBeAssigned, point);
        }
    }

    private MapPointType GetNextValidPointType(Queue<MapPointType> pointTypesQueue, MapPoint mapPoint)
    {
        for (int i = 0; i < pointTypesQueue.Count; i++)
        {
            MapPointType pointType = pointTypesQueue.Dequeue();

            // If we should ignore the MapPoint rules such as in the case of Bell of Twilight
            if (_pointTypeCounts.ShouldIgnoreMapPointRulesForMapPointType(pointType))
            {
                return pointType;
            }

            // Otherwise, get the next valid MapPointType based on the rules
            if (IsValidPointType(pointType, mapPoint))
            {
                return pointType;
            }

            pointTypesQueue.Enqueue(pointType);
        }

        // If no valid point type, then return Unassigned.
        return MapPointType.Unassigned;
    }

    // This is public so that any nodes we try to change after can still obey the standard rules.
    // ie Bell of Twilight relic
    public bool IsValidPointType(MapPointType pointType, MapPoint mapPoint)
    {
        if (!IsValidForUpper(pointType, mapPoint)) return false;
        if (!IsValidForLower(pointType, mapPoint)) return false;
        if (!IsValidWithParents(pointType, mapPoint)) return false;
        if (!IsValidWithChildren(pointType, mapPoint)) return false;
        if (!IsValidWithSiblings(pointType, mapPoint)) return false;
        return true;
    }

    private static readonly HashSet<MapPointType> _lowerMapPointRestrictions =
    [
        MapPointType.RestSite,
        MapPointType.Elite
    ];

    /// These point types cannot be in the lower part of the map
    private static bool IsValidForLower(MapPointType pointType, MapPoint mapPoint)
    {
        return mapPoint.coord.row >= 5 || !_lowerMapPointRestrictions.Contains(pointType);
    }

    private static readonly HashSet<MapPointType> _upperMapPointRestrictions = [MapPointType.RestSite];

    /// These point types cannot be in the upper part of the map
    private bool IsValidForUpper(MapPointType pointType, MapPoint mapPoint)
    {
        return mapPoint.coord.row < _mapLength - 3 || !_upperMapPointRestrictions.Contains(pointType);
    }

    /// These cannot be 2 times in a row.
    private static readonly HashSet<MapPointType> _parentMapPointRestrictions =
    [
        MapPointType.Elite,
        MapPointType.RestSite,
        MapPointType.Treasure,
        MapPointType.Shop
    ];

    /// These cannot be 2 times in a row.
    private static bool IsValidWithParents(MapPointType pointType, MapPoint mapPoint)
    {
        return !(
            _parentMapPointRestrictions.Contains(pointType) &&
            mapPoint.parents.Concat(mapPoint.Children).Any(p => pointType == p.PointType)
        );
    }

    /// These cannot be 2 times in a row.
    private static readonly HashSet<MapPointType> _childMapPointRestrictions =
    [
        MapPointType.Elite,
        MapPointType.RestSite,
        MapPointType.Treasure,
        MapPointType.Shop
    ];

    /// Checks if a point type is valid by ensuring it doesn't appear twice in a row among children
    private static bool IsValidWithChildren(MapPointType pointType, MapPoint mapPoint)
    {
        return !(
            _childMapPointRestrictions.Contains(pointType) &&
            mapPoint.Children.Any(p => pointType == p.PointType)
        );
    }

    /// Only these point types are allowed to be siblings
    private static readonly HashSet<MapPointType> _siblingPointTypeRestrictions =
    [
        MapPointType.RestSite,
        MapPointType.Monster,
        MapPointType.Unknown,
        MapPointType.Elite,
        MapPointType.Shop
    ];

    /// These point types cannot be siblings of each other.
    private static bool IsValidWithSiblings(MapPointType pointType, MapPoint mapPoint)
    {
        return !(
            _siblingPointTypeRestrictions.Contains(pointType) &&
            GetSiblings(mapPoint).Any(p => pointType == p.PointType)
        );
    }

    private static IEnumerable<MapPoint> GetSiblings(MapPoint mapPoint)
    {
        return mapPoint.parents
            .SelectMany(x => x.Children)
            .Where(x => !Equals(x, mapPoint));
    }

    public static List<List<MapPoint>> FindAllPaths(MapPoint currentMapPoint)
    {
        List<List<MapPoint>> paths = [];

        if (currentMapPoint.PointType == MapPointType.Boss) // if the current point is the boss, we reached the end
        {
            paths.Add([currentMapPoint]);
            return paths;
        }

        foreach (MapPoint child in currentMapPoint.Children)
        {
            List<List<MapPoint>> childPaths = FindAllPaths(child);
            foreach (List<MapPoint> childPath in childPaths)
            {
                List<MapPoint> path = [currentMapPoint];
                path.AddRange(childPath);
                paths.Add(path);
            }
        }

        return paths;
    }

    public List<List<MapPoint[]>> FindMatchingSegments()
    {
        // The ancient is the starting node now, we want to do our detection from it
        List<List<MapPoint>> allPaths = FindAllPaths(StartingMapPoint);

        Dictionary<string, List<MapPoint[]>> segments = new();
        foreach (List<MapPoint> path in allPaths)
        {
            AddSegmentsToDictionary(path, segments);
        }

        return GetDuplicateSegments(segments);
    }

    private static void AddSegmentsToDictionary(IReadOnlyList<MapPoint> path, IDictionary<string, List<MapPoint[]>> segments)
    {
        for (int i = 0; i < path.Count - 1; i++)
        {
            if (!IsValidSegmentStartMapPoint(path[i])) continue;

            // Starting with segments longer than 2 points because we need at least 3 points before we start
            // comparing whether they're matching segments.
            for (int length = 2; length < path.Count - i; length++)
            {
                MapPoint endMapPoint = path[i + length];
                if (!IsValidSegmentEndMapPoint(endMapPoint)) continue;

                MapPoint[] segment = path.Skip(i).Take(length + 1).ToArray();
                string key = GenerateSegmentKey(segment);

                if (!segments.ContainsKey(key))
                {
                    segments[key] = [segment];
                    continue;
                }

                if (!AnyOverlappingSegments(segments[key], segment))
                {
                    segments[key].Add(segment);
                }
            }
        }
    }

    private static bool IsValidSegmentStartMapPoint(MapPoint startMapPoint)
    {
        return startMapPoint.Children.Count > 1 || startMapPoint.coord.row == 0;
    }

    private static bool IsValidSegmentEndMapPoint(MapPoint endMapPoint)
    {
        // if there's only one parent, then this isn't a duplicate segment we can prune
        return endMapPoint.parents.Count >= 2;
    }

    private static string GenerateSegmentKey(IReadOnlyList<MapPoint> segment)
    {
        StringBuilder keyBuilder = new();
        MapPoint startMapPoint = segment[0];
        MapPoint endMapPoint = segment[^1];

        if (startMapPoint.coord.row == 0)
        {
            keyBuilder.Append($"{startMapPoint.coord.row}-{endMapPoint.coord.col},{endMapPoint.coord.row}-");
        }
        else
        {
            keyBuilder.Append($"{startMapPoint.coord.col},{startMapPoint.coord.row}-{endMapPoint.coord.col},{endMapPoint.coord.row}-");
        }

        keyBuilder.Append(string.Join(",", segment.Select(point => (int)point.PointType)));
        return keyBuilder.ToString();
    }

    private static bool AnyOverlappingSegments(IEnumerable<MapPoint[]> existingSegments, IReadOnlyList<MapPoint> segment)
    {
        return existingSegments.Any(existingSegment => OverlappingSegment(existingSegment, segment));
    }

    private static bool OverlappingSegment(IReadOnlyList<MapPoint> a, IReadOnlyList<MapPoint> b)
    {
        if (a.Count < 3 || b.Count < 3) return false;

        for (int index = 1; index <= a.Count - 2; index++)
        {
            if (Equals(a[index], b[index])) return true;
        }

        return false;
    }

    private static List<List<MapPoint[]>> GetDuplicateSegments(Dictionary<string, List<MapPoint[]>> segments)
    {
        return segments.Values.Where(segmentList => segmentList.Count > 1).ToList();
    }

    private bool PrunePaths(IEnumerable<List<MapPoint[]>> matchingSegments)
    {
        foreach (List<MapPoint[]> matches in matchingSegments)
        {
            // shuffle it, otherwise we'll always prune the left-most segment
            matches.UnstableShuffle(_rng);
            int pruned = PruneAllButLast(matches);
            if (pruned != 0)
            {
                return true;
            }

            // if we can't resolve the duplication with pruning, then we'll remove an edge on a point
            // with two children. This will guarantee that the segments don't match.
            if (BreakAParentChildRelationshipInAnySegment(matches))
            {
                return true; // successfully pruned one path, return so we can iterate again
            }
        }

        return false;
    }

    private int PruneAllButLast(IReadOnlyList<MapPoint[]> matches)
    {
        int pruned = 0;

        foreach (MapPoint[] match in matches)
        {
            if (pruned == matches.Count - 1) return pruned;
            if (PruneSegment(match))
            {
                pruned++;
            }
        }

        return pruned;
    }

    public bool IsInMap(MapPoint mapPoint) => Grid[mapPoint.coord.col, mapPoint.coord.row] != null
        || mapPoint.PointType == MapPointType.Ancient
        || mapPoint.PointType == MapPointType.Boss;

    private static bool BreakAParentChildRelationshipInAnySegment(List<MapPoint[]> matches)
    {
        foreach (MapPoint[] segment in matches)
        {
            if (BreakAParentChildRelationshipInSegment(segment)) return true;
        }

        return false;
    }

    private static bool BreakAParentChildRelationshipInSegment(MapPoint[] segment)
    {
        bool brokeRelationship = false;

        for (int i = 0; i < segment.Length - 1; i++)
        {
            MapPoint mapPoint = segment[i];
            if (mapPoint.Children.Count < 2) continue;

            MapPoint nextMapPoint = segment[i + 1];
            if (nextMapPoint.parents.Count == 1) continue;

            // Break the connection between the child and parent
            mapPoint.RemoveChildPoint(nextMapPoint);
            nextMapPoint.parents.Remove(mapPoint);
            brokeRelationship = true;
        }

        return brokeRelationship;
    }

    private bool IsRemoved(MapPoint mapPoint)
    {
        return Grid[mapPoint.coord.col, mapPoint.coord.row] == null;
    }

    // Prunes segments according to rules to ensure it doesn't break the map
    private bool PruneSegment(MapPoint[] segment)
    {
        bool pruned = false;

        for (int i = 0; i < segment.Length - 1; i++)
        {
            MapPoint mapPoint = segment[i];

            // Return early if this segment has already been pruned
            if (!IsInMap(mapPoint)) return true;

            // if the point has multiple children or multiple parents
            if (mapPoint.Children.Count > 1 || mapPoint.parents.Count > 1) continue; // skip

            // skip if removing would compromise the previous point's children
            if (mapPoint.parents.Any(n => n.Children.Count == 1 && !IsRemoved(n))) continue;

            MapPoint[] remainingSegment = segment.Skip(i).ToArray();

            // Look ahead to see if any remaining points in the path have more than one child and just one parent...
            bool wouldBeOrphaned = remainingSegment
                .Any(n => n.Children.Count > 1 && n.parents.Count == 1);
            if (wouldBeOrphaned) continue; // skip

            // avoid pruning if the last point in the segment would be left stranded
            if (segment[^1].parents.Count == 1) return false;

            bool wouldBreakOverlappingPaths = mapPoint.Children
                .Where(c => !segment.Contains(c))
                .Any(c => c.parents.Count == 1);

            if (wouldBreakOverlappingPaths) continue;

            RemovePoint(mapPoint);
            pruned = true;
        }

        return pruned;
    }
}
