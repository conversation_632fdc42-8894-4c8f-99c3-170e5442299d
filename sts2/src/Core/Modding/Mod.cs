namespace MegaCrit.Sts2.Core.Modding;

/// <summary>
/// Information about a loaded mod.
/// </summary>
public class Mod
{
    /// <summary>
    /// Where the mod originated from.
    /// </summary>
    public ModSource modSource;

    /// <summary>
    /// Name of the PCK file we would load the mod from, without the pck extension.
    /// </summary>
    public required string pckName;

    /// <summary>
    /// Whether the mod was loaded when the game launched.
    /// Since there's no way to unload mods while the game is running, this value cannot change after the initial mod
    /// initialization occurs.
    /// Even if the mod is set to disabled in SettingsSave, this value is the true source of whether or not the mod was
    /// loaded into the game.
    /// </summary>
    public bool wasLoaded;

    /// <summary>
    /// The mod manifest. Null if the mod was not loaded.
    /// </summary>
    public ModManifest? manifest;
}
