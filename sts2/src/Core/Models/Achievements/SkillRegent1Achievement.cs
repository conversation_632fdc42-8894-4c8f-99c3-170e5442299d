using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Achievements;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Platform;

namespace MegaCrit.Sts2.Core.Models.Achievements;

/// <summary>
/// Grants an achievement when the player forges a Sovereign Blade which deals a large amount of damage.
/// </summary>
public class SkillRegent1Achievement : AchievementModel
{
    private const int _damageThreshold = 999;

    public override Task AfterForge(decimal amount, Player forger)
    {
        if (!LocalContext.IsMe(forger)) return Task.CompletedTask;
        if (AchievementsUtil.IsUnlocked(Achievement.CharacterSkillRegent2)) return Task.CompletedTask;

        foreach (SovereignBlade blade in forger.PlayerCombatState!.AllCards.OfType<SovereignBlade>())
        {
            if (blade.DynamicVars.Damage.BaseValue > _damageThreshold)
            {
                AchievementsUtil.Unlock(Achievement.CharacterSkillRegent1, forger);
            }
        }

        return Task.CompletedTask;
    }
}
