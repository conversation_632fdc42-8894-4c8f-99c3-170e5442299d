using System;
using System.Collections.Generic;
using System.Linq;
using Godot;
using MegaCrit.Sts2.Core.Achievements;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models.Acts;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Models;

public abstract class ActModel : AbstractModel
{
    public LocString Title => new("acts", $"{Id.Entry}.title");

    protected virtual string FilePathIdentifier => Id.Entry.ToLower();

    private string RestSiteBackgroundPath => SceneHelper.GetScenePath($"rest_site/{FilePathIdentifier}_rest_site");
    public Control CreateRestSiteBackground() => PreloadManager.Cache.GetScene(RestSiteBackgroundPath).Instantiate<Control>();

    public string MapTopBgPath => ImageHelper.GetImagePath($"packed/map/map_bgs/{FilePathIdentifier}/map_top_{FilePathIdentifier}.png");
    public Texture2D MapTopBg => PreloadManager.Cache.GetCompressedTexture2D(MapTopBgPath);
    public string MapMidBgPath => ImageHelper.GetImagePath($"packed/map/map_bgs/{FilePathIdentifier}/map_middle_{FilePathIdentifier}.png");
    public Texture2D MapMidBg => PreloadManager.Cache.GetCompressedTexture2D(MapMidBgPath);

    public string MapBotBgPath => ImageHelper.GetImagePath($"packed/map/map_bgs/{FilePathIdentifier}/map_bottom_{FilePathIdentifier}.png");
    public Texture2D MapBotBg => PreloadManager.Cache.GetCompressedTexture2D(MapBotBgPath);

    /// <summary>
    /// Color of the dots on the map after you've traveled through them.
    /// Also affects the color of the Boss and Ancient nodes.
    /// </summary>
    public abstract Color MapTraveledColor { get; }

    /// <summary>
    /// Color of the dots on the map for paths you haven't traveled.
    /// Also affects the color of the Boss node before you get adjacent to it.
    /// </summary>
    public abstract Color MapUntraveledColor { get; }

    public abstract Color MapBgColor { get; }

    public IEnumerable<string> AssetPaths => new[]
    {
        [
            BackgroundScenePath,
            RestSiteBackgroundPath,
            MapBotBgPath,
            MapMidBgPath,
            MapTopBgPath,
            ChestSpineResourcePath
        ],
        _rooms.ancient.MapNodeAssetPaths,
        _rooms.boss.MapNodeAssetPaths
    }.SelectMany(p => p);

    public abstract string[] BgMusicOptions { get; }

    public abstract string AmbientSfx { get; }

    protected virtual int NumberOfWeakEncounters => 3;
    public abstract int NumberOfRooms { get; }

    // Number of rooms + boss room + ancient room
    public int NumberOfFloors => NumberOfRooms + 2;

    protected RoomSet _rooms = default!;

    public abstract IEnumerable<EncounterModel> Encounters { get; }

    private IEnumerable<EncounterModel>? _weakEncounters;
    public IEnumerable<EncounterModel> WeakEncounters => _weakEncounters ??= Encounters.Where(e => e is { RoomType: RoomType.Monster, IsWeak: true });

    private IEnumerable<EncounterModel>? _regularEncounters;
    public IEnumerable<EncounterModel> RegularEncounters => _regularEncounters ??= Encounters.Where(e => e is { RoomType: RoomType.Monster, IsWeak: false });

    private IEnumerable<EncounterModel>? _eliteEncounters;
    public IEnumerable<EncounterModel> EliteEncounters => _eliteEncounters ??= Encounters.Where(e => e.RoomType == RoomType.Elite);

    private IEnumerable<EncounterModel>? _bossEncounters;
    public IEnumerable<EncounterModel> BossEncounters => _bossEncounters ??= Encounters.Where(e => e.RoomType == RoomType.Boss);

    public virtual string ChestSpineResourcePath => $"res://animations/backgrounds/treasure_room/chest_room_act_{FilePathIdentifier}_skel_data.tres";
    public virtual SpineSkeletonDataResource ChestSpineResource => PreloadManager.Cache.GetAsset<SpineSkeletonDataResource>(ChestSpineResourcePath);

    public IEnumerable<MonsterModel> AllMonsters => Encounters.SelectMany(e => e.AllPossibleMonsters).Distinct();
    public Achievement DefeatedAllEnemiesAchievement => Enum.Parse<Achievement>($"Defeat{Id.Entry.Capitalize()}Enemies");

    public abstract IEnumerable<AncientEventModel> UniqueAncients { get; }

    // The shared pool of ancients for all acts.
    private List<AncientEventModel>? _sharedAncients;

    // The pool of potential ancients for this act.
    public IEnumerable<AncientEventModel> Ancients => _sharedAncients != null
        ? UniqueAncients.Concat(_sharedAncients)
        : UniqueAncients;

    public abstract IEnumerable<EventModel> Events { get; }

    private ActModel _canonicalInstance = default!;

    public ActModel CanonicalInstance
    {
        get => IsMutable ? _canonicalInstance : this;
        private set
        {
            AssertMutable();
            _canonicalInstance = value;
        }
    }

    protected override void DeepCloneFields()
    {
        _rooms = new RoomSet();
    }

    public override bool ShouldReceiveCombatHooks => false;

    protected string GetFullLayerPath(string layerName)
    {
        return $"res://scenes/backgrounds/{FilePathIdentifier}/layers/{FilePathIdentifier}_{layerName}.tscn";
    }

    public void AddSharedAncients(List<AncientEventModel> ancients)
    {
        AssertMutable();
        _sharedAncients = new List<AncientEventModel>();
        _sharedAncients.AddRange(ancients);
    }

    public void GenerateRooms(Rng rng)
    {
        AssertMutable();

        //########//
        // Events //
        //########//

        // We should move to a grab bag model if we need to weight events.
        _rooms.events.AddRange(Events.Concat(ModelDb.SharedEvents).ToList().UnstableShuffle(rng));

        //#################//
        // Weak encounters //
        //#################//

        GrabBag<EncounterModel> weakGrabBag = new();

        for (int i = 0; i < NumberOfWeakEncounters; i++)
        {
            // Refill the grab bag if it's empty. This should only happen on the first iteration, but we put it
            // inside the loop in case we run out of elements during debug.
            if (!weakGrabBag.Any())
            {
                foreach (EncounterModel encounter in WeakEncounters)
                {
                    weakGrabBag.Add(encounter, 1.0);
                }
            }

            AddWithoutRepeatingTags(_rooms.normalEncounters, weakGrabBag, rng);
        }

        //####################//
        // Regular encounters //
        //####################//

        GrabBag<EncounterModel> regularGrabBag = new();

        // Start after the last weak encounter and fill up the rest of the list.
        for (int i = NumberOfWeakEncounters; i < NumberOfRooms; i++)
        {
            // Refill the grab bag if it's empty. This should only happen on the first iteration, but we put it
            // inside the loop in case we run out of elements during debug.
            if (!regularGrabBag.Any())
            {
                foreach (EncounterModel encounter in RegularEncounters)
                {
                    regularGrabBag.Add(encounter, 1.0);
                }
            }

            AddWithoutRepeatingTags(_rooms.normalEncounters, regularGrabBag, rng);
        }

        //##################//
        // Elite encounters //
        //##################//

        GrabBag<EncounterModel> eliteGrabBag = new();

        for (int i = 0; i < StandardActMap.maxElites; i++)
        {
            // Refill the grab bag if it's empty. This should only happen on the first iteration, but we put it
            // inside the loop in case we run out of elements during debug.
            if (!eliteGrabBag.Any())
            {
                foreach (EncounterModel encounter in EliteEncounters)
                {
                    eliteGrabBag.Add(encounter, 1.0);
                }
            }

            AddWithoutRepeatingTags(_rooms.eliteEncounters, eliteGrabBag, rng);
        }

        //######//
        // Boss //
        //######//

        _rooms.boss = rng.NextItem(BossEncounters)!;
        _rooms.ancient = rng.NextItem(Ancients)!;
    }

    // Manages changes to rooms based on number of times we have seen this act before.
    // see https://app.clickup.com/10583894/v/dc/a2zup-2325/a2zup-26314
    public abstract void ApplyDiscoveryOrderModifications(int index);

    private static void AddWithoutRepeatingTags(ICollection<EncounterModel> encounters, GrabBag<EncounterModel> grabBag, Rng rng)
    {
        EncounterModel? encounter = grabBag.GrabAndRemove(rng, e => !e.SharesTagsWith(encounters.LastOrDefault()) && e != encounters.LastOrDefault());

        // If we couldn't find an encounter without a repeating tag, get one with a repeating tag.
        if (encounter == null)
        {
            encounter = grabBag.GrabAndRemove(rng);
        }

        // If there really are no encounters left, don't add null.
        if (encounter != null)
        {
            encounters.Add(encounter);
        }
    }

    public EventModel PullAncient() => _rooms.ancient;

    public EventModel PullNextEvent(ClimbState climbState)
    {
        _rooms.EnsureNextEventIsValid(climbState);
        EventModel nextEvent = Hook.ModifyNextEvent(climbState, _rooms.NextEvent);
        climbState.AddVisitedEvent(nextEvent);
        return nextEvent;
    }

    public EncounterModel PullNextEncounter(RoomType roomType)
    {
        return roomType switch
        {
            RoomType.Monster => _rooms.NextNormalEncounter,
            RoomType.Elite => _rooms.NextEliteEncounter,
            RoomType.Boss => BossEncounter,
            _ => throw new ArgumentOutOfRangeException(nameof(roomType), roomType, null)
        };
    }

    public void MarkRoomVisited(RoomType roomType) => _rooms.MarkVisited(roomType);

    public EncounterModel BossEncounter => _rooms.boss;

    public AncientEventModel Ancient => _rooms.ancient;

    public string BackgroundScenePath => SceneHelper.GetScenePath($"backgrounds/{FilePathIdentifier}/{FilePathIdentifier}_background");

    public BackgroundAssets GenerateBackgroundAssets() => new BackgroundAssets(FilePathIdentifier);

    public void SetBossEncounter(EncounterModel encounter)
    {
        if (encounter.RoomType != RoomType.Boss)
        {
            throw new ArgumentException("the encounter must be a boss");
        }

        _rooms.boss = encounter;
    }

    public void RemoveEventFromSet(EventModel eventModel)
    {
        eventModel.AssertCanonical();
        _rooms.events.Remove(eventModel);
    }

    public ActModel ToMutable()
    {
        AssertCanonical();
        ActModel clone = (ActModel)MutableClone();
        clone.CanonicalInstance = this;
        return clone;
    }

    public SerializableActModel ToSave()
    {
        AssertMutable();

        return new SerializableActModel
        {
            Id = Id,
            SerializableRooms = _rooms.ToSave()
        };
    }

    public static ActModel FromSave(SerializableActModel save)
    {
        ActModel act = ModelDb.GetById<ActModel>(save.Id!).ToMutable();
        act._rooms = RoomSet.FromSave(save.SerializableRooms);

        return act;
    }

    // Return the distribution of map room types
    public abstract MapPointTypeCounts GetMapPointTypes(Rng mapRng);

    /// <summary>
    /// Get a list of random ActModels.
    /// The ActModel at index 0 will be Act 1, index 1 will be Act 2, etc.
    /// This list is not _truly_ random; a given ActModel will only be rolled for the act index it belongs in.
    /// For example, Overgrowth may or may not be at index 0, but it will never be at index 1 or 2.
    /// </summary>
    /// <param name="seed">Seed to use for randomly rolling alternate acts.</param>
    /// <returns>Randomized list of acts.</returns>
    public static IEnumerable<ActModel> GetRandomList(string seed)
    {
        List<ActModel> acts = GetDefaultList().ToList();
        Rng rng = new((uint)StringHelper.GetDeterministicHashCode(seed));

        if (rng.NextBool())
        {
            acts[0] = ModelDb.Act<Underdocks>();
        }

        return acts;
    }

    /// <summary>
    /// Get the default list of ActModels.
    /// Act 1 (index 0) is <see cref="Overgrowth"/>.
    /// Act 2 is <see cref="Hive"/>.
    /// Act 3 is <see cref="Glory"/>.
    /// </summary>
    /// <returns></returns>
    public static IReadOnlyList<ActModel> GetDefaultList() =>
    [
        ModelDb.Act<Overgrowth>(),
        ModelDb.Act<Hive>(),
        ModelDb.Act<Glory>()
    ];
}
