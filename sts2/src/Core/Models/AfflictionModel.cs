using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization;

namespace MegaCrit.Sts2.Core.Models;

public abstract class AfflictionModel : AbstractModel
{
    public const string locTable = "afflictions";

    #region Events

    public event Action<int, int>? AmountChanged;

    #endregion

    #region Text

    public LocString Title => new(locTable, $"{Id.Entry}.title");
    public LocString Description => new(locTable, $"{Id.Entry}.description");
    public LocString ExtraCardText => new(locTable, $"{Id.Entry}.extraCardText");
    public virtual bool HasExtraCardText => false;

    public LocString DynamicDescription
    {
        get
        {
            LocString description = Description;
            description.Add("Amount", Amount);
            return description;
        }
    }

    public LocString? DynamicExtraCardText
    {
        get
        {
            if (!HasExtraCardText) return null;
            LocString extraCardText = ExtraCardText;
            extraCardText.Add("Amount", Amount);
            return extraCardText;
        }
    }

    #endregion

    #region Overlay

    public string OverlayPath => SceneHelper.GetScenePath($"cards/overlays/afflictions/{Id.Entry.ToLower()}");

    public Control CreateOverlay() => PreloadManager.Cache.GetScene(OverlayPath).Instantiate<Control>();

    public bool HasOverlay => ResourceLoader.Exists(OverlayPath);

    public IEnumerable<string> AssetPaths => HasOverlay ? [OverlayPath] : [];

    #endregion

    #region Gameplay logic

    private CardModel? _card;

    /// <summary>
    /// Get the card that this is afflicting.
    /// Will technically be null on a canonical affliction model, but we should never be checking that, so we leave this
    /// as non-nullable for convenience.
    /// </summary>
    public CardModel Card
    {
        get
        {
            AssertMutable();
            return _card!;
        }
        set
        {
            AssertMutable();
            value.AssertMutable();

            if (_card != null)
            {
                throw new InvalidOperationException("Afflictions cannot be moved from one card to another.");
            }

            SortingOrderChanged();
            _card = value;
        }
    }

    private int _amount;

    public int Amount
    {
        get => _amount;
        set
        {
            AssertMutable();
            if (_amount == value) return;

            int oldAmount = _amount;
            _amount = value;

            // Need this to show a Hover Tip for an enchantment with an amount before it has been added to a card
            if (_card != null)
            {
                _card.Owner.PlayerCombatState!.RecalculateCardValues();
            }

            AmountChanged?.Invoke(oldAmount, _amount);
        }
    }

    /// <summary>
    /// Get the CombatState of the card that this is afflicting.
    /// Will never be null, since Afflictions are combat-only.
    /// </summary>
    public CombatState CombatState => Card.CombatState!;

    public virtual bool CanAfflictCardType(CardType cardType) => cardType is not (CardType.Curse or CardType.Status);

    /// <summary>
    /// Can this afflict Unplayable cards?
    /// Usually true, because Unplayable cards sometimes have Sly and we might want the affliction's effect to still
    /// trigger. Some exceptions for when it doesn't make sense (like one that makes the card cost more energy).
    /// </summary>
    public virtual bool CanAfflictUnplayableCards => true;

    public virtual bool IsStackable => false;

    /// <summary>
    /// Checks whether the specified card can be afflicted with this affliction. For example, Ringing can only
    /// afflict attacks, so this will return true if an Attack is passed, but false if a Skill is passed.
    ///
    /// Note: Do not override this method to REMOVE restrictions, just to ADD them. When you override it, make sure to
    /// call `base.CanAfflict`, and then add your own restrictions afterwards. You can also override some other methods
    /// to add specific types of restrictions (check AfflictionModel.cs for details).
    /// </summary>
    /// <param name="card">Card to check validity of.</param>
    /// <returns>Whether or not the specified card is valid to afflict with this.</returns>
    public virtual bool CanAfflict(CardModel card)
    {
        // Make sure we can afflict the specified card type.
        if (!CanAfflictCardType(card.Type)) return false;

        // Make sure the card isn't Unplayable (or we can afflict Unplayable cards).
        if (card.Keywords.Contains(CardKeyword.Unplayable) && !CanAfflictUnplayableCards) return false;

        // Make sure the card isn't already afflicted (or we're attempting to stack this affliction).
        if (card.Affliction != null && (!IsStackable || card.Affliction.GetType() != GetType())) return false;

        return true;
    }

    public virtual void AfterApplied() { }

    public virtual void BeforeRemoved() { }

    public virtual Task OnPlay(PlayerChoiceContext choiceContext, Creature? target) => Task.CompletedTask;

    #endregion

    private AfflictionModel _canonicalInstance = default!;

    public AfflictionModel CanonicalInstance
    {
        get => IsMutable ? _canonicalInstance : this;
        private set
        {
            AssertMutable();
            _canonicalInstance = value;
        }
    }

    public AfflictionModel ToMutable()
    {
        AssertCanonical();
        AfflictionModel clone = (AfflictionModel)MutableClone();
        clone.CanonicalInstance = this;
        return clone;
    }

    protected override void AfterCloned()
    {
        base.AfterCloned();

        AmountChanged = null;

        // If we are cloning an Affliction, that usually means we need to assigned it to a new cloned card.
        _card = null;
    }

    public override bool ShouldReceiveCombatHooks => true;

    protected virtual IEnumerable<IHoverTip> ExtraHoverTips => [];

    public HoverTip HoverTip => new(this, DynamicDescription);

    public IEnumerable<IHoverTip> HoverTips
    {
        get
        {
            List<IHoverTip> tips = [HoverTip];
            tips.AddRange(ExtraHoverTips);
            return tips;
        }
    }

    /// <summary>
    /// Chooses random targets out of a set of cards to afflict.
    /// </summary>
    /// <param name="rngSet">The RNG set to use. Does not use Card.Owner because we may be the canonical version.</param>
    /// <param name="cards">The possible cards to afflict.</param>
    /// <param name="count">Number of cards to afflict.</param>
    /// <returns>The cards to afflict.</returns>
    public IReadOnlyList<CardModel> PickRandomTargets(ClimbRngSet rngSet, IEnumerable<CardModel> cards, int count)
    {
        List<CardModel> targets = cards
            .Where(CanAfflict)
            .ToList()
            .UnstableShuffle(rngSet.CombatCardGeneration);

        targets.RemoveRange(Math.Clamp(targets.Count - 1, 0, count), Math.Max(0, targets.Count - count));

        return targets;
    }

    /// <summary>
    /// Remove this affliction from its card.
    /// Should only be called by <see cref="CardModel.ClearAfflictionInternal"/> and in tests.
    /// </summary>
    public void ClearInternal()
    {
        BeforeRemoved();
        _card = null;
    }
}
