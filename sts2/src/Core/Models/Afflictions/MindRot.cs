using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Models.Powers;

namespace MegaCrit.Sts2.Core.Models.Afflictions;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class MindRot : AfflictionModel
{
    private const decimal _vigorAmount = 4;
    public override bool HasExtraCardText => true;
    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromPower<Vigor>()];

    public override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        Creature? knowledgeDemon = CombatState
            .GetOpponentsOf(Card.Owner.Creature)
            .FirstOrDefault(c => c.Monster is KnowledgeDemon);
        if (knowledgeDemon == null) return;

        await PowerCmd.Apply<Vigor>(knowledgeDemon, _vigorAmount, null, Card);
    }

    public override Task BeforeTurnEnd(PlayerChoiceContext choiceContext, CombatSide side)
    {
        CardCmd.ClearAffliction(Card);
        return Task.CompletedTask;
    }
}
