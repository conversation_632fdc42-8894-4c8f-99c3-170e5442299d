using System.Collections.Generic;
using System.Linq;
using Godot;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;

namespace MegaCrit.Sts2.Core.Models.CardPools;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class MockCardPool : CardPoolModel
{
    public override string Title => "test";
    public override string EnergyColorName => ColorlessCardPool.energyColorName;
    public override string CardFrameMaterialPath => "card_frame_colorless";

    public override Color DeckEntryCardColor => Colors.White;
    public override bool IsColorless => false;

    private List<CardModel>? _customCards;

    public override IEnumerable<CardModel> Cards => base.Cards.Concat(_customCards ?? []);

    protected override CardModel[] GenerateCards() =>
    [
        MockCard<MockAttackCard>(CardRarity.Common),
        MockCard<MockAttackCard>(CardRarity.Uncommon),
        MockCard<MockAttackCard>(CardRarity.Rare),

        MockCard<MockPowerCard>(CardRarity.Common),
        MockCard<MockPowerCard>(CardRarity.Uncommon),
        MockCard<MockPowerCard>(CardRarity.Rare),

        MockCard<MockSkillCard>(CardRarity.Common),
        MockCard<MockSkillCard>(CardRarity.Uncommon),
        MockCard<MockSkillCard>(CardRarity.Rare),

        MockCard<MockCurseCard>(CardRarity.Curse),
        MockCard<MockStatusCard>(CardRarity.Status),
    ];

    protected override void DeepCloneFields()
    {
        base.DeepCloneFields();
        _customCards = [];
    }

    public void Add(CardModel card)
    {
        AssertMutable();
        _customCards!.Add(card);
    }

    private static MockCardModel MockCard<T>(CardRarity rarity) where T : MockCardModel
    {
        // We don't use ICardCreator here because this ends up creating fake canonical cards.
        return ((MockCardModel)ModelDb.Card<T>().ToMutable()).MockRarity(rarity).MockCanonical();
    }
}
