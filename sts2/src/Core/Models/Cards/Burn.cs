using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Burn : CardModel
{
    public override int CanonicalEnergyCost => -1;
    public override CardType Type => CardType.Status;
    public override CardRarity Rarity => CardRarity.Status;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.None;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.None;

    public override int MaxUpgradeLevel => 0;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new DamageVar(2, DamageProps.cardUnpowered)
    ];

    public override IEnumerable<CardKeyword> CanonicalKeywords =>
    [
        CardKeyword.Unplayable
    ];

    public override bool HasTurnEndInHandEffect => true;

    protected override IEnumerable<string> ExtraAssetPaths => NGroundFireVfx.AssetPaths;

    public override async Task OnTurnEndInHand()
    {
        NCombatRoom.Instance?.CombatVfxContainer.AddChildSafely(NGroundFireVfx.Create(Owner.Creature));
        NDebugAudioManager.Instance?.Play("STS_SFX_BurnCard_v1.ogg", 0.6f, PitchVariance.Medium);
        await CreatureCmd.Damage(Owner.Creature, DynamicVars.Damage, this);
    }
}
