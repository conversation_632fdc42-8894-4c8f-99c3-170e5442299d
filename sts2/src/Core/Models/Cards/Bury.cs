using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Bury : CardModel
{
    private const string _baseDamageKey = "BaseDamage";
    private const string _damagePerCardKey = "DamagePerCard";

    public override int CanonicalEnergyCost => 3;

    public override CardType Type => CardType.Attack;
    public override CardRarity Rarity => CardRarity.Uncommon;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.Any;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.None;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_baseDamageKey, 13),
        new(_damagePerCardKey, 2),
        new DamageVar(0, DamageProps.card)
    ];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        ArgumentNullException.ThrowIfNull(target);

        RecalculateValues();

        await DamageCmd.Attack(DynamicVars.Damage.BaseValue, 1)
            .FromCard(this)
            .Targeting(target)
            .WithHitFx(VfxCmd.bluntPath, tmpSfx: TmpSfx.bluntAttack)
            .Execute();
    }

    protected override void OnUpgrade()
    {
        DynamicVars[_damagePerCardKey].UpgradeValueBy(1);
    }

    public override void RecalculateValues()
    {
        DynamicVars.Damage.BaseValue =
            DynamicVars[_baseDamageKey].BaseValue +
            DynamicVars[_damagePerCardKey].BaseValue *
            PileType.Discard.GetPile(Owner).Cards.Count;
    }
}
