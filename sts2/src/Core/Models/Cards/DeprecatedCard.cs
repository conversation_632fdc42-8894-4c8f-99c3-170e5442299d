using System.Collections.Generic;
using MegaCrit.Sts2.Core.Entities.Cards;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.

/// <summary>
/// Placeholder event if we can't find the card we are looking for (such as in the case that it's been deprecated)
/// </summary>
public sealed class DeprecatedCard : CardModel
{
    public override bool ShouldShowInCardLibrary => false;
    public override int CanonicalEnergyCost => -1;
    public override CardType Type => CardType.Curse;
    public override CardRarity Rarity => CardRarity.Curse;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.None;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.None;

    public override int MaxUpgradeLevel => 0;

    public override IEnumerable<CardKeyword> CanonicalKeywords =>
    [
        CardKeyword.Unplayable
    ];
}
