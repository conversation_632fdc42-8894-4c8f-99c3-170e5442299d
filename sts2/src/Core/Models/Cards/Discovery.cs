using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Factories;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
namespace MegaCrit.Sts2.Core.Models.Cards;

public sealed class Discovery : CardModel
{
    public override CardType Type => CardType.Skill;
    public override CardRarity Rarity => CardRarity.Uncommon;
    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.None;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.Self;
    public override int CanonicalEnergyCost => 1;

    public override IEnumerable<CardKeyword> CanonicalKeywords => [CardKeyword.Exhaust];

    private CardModel? _mockSelectedCard;

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        CardModel? card;

        if (_mockSelectedCard == null)
        {
            List<CardModel> options = CardFactory.GetDistinctForCombat(
                Owner,
                Owner.Character.CardPool.Cards,
                3,
                Owner.ClimbState.Rng.CombatCardGeneration
            ).ToList();

            card = await CardSelectCmd.FromChooseACardScreen(choiceContext, options, Owner, true);
        }
        else
        {
            card = _mockSelectedCard;
        }

        if (card != null)
        {
            card.SetEnergyCostThisTurn(0);
            await CardPileCmd.AddGeneratedCardToCombat(card, PileType.Hand, true);
        }
    }

    protected override void OnUpgrade()
    {
        RemoveKeyword(CardKeyword.Exhaust);
    }

    /// <summary>
    /// ONLY USE THIS IN TESTS!
    /// </summary>
    public void MockSelectedCard(CardModel card)
    {
        AssertMutable();
        _mockSelectedCard = card;
    }
}
