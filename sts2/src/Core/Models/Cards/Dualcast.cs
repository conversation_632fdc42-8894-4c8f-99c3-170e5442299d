using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Dualcast : CardModel
{
    public override int CanonicalEnergyCost => 1;
    public override CardType Type => CardType.Skill;
    public override CardRarity Rarity => CardRarity.Basic;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.None;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.Self;

    public override bool EvokesOrbs => true;

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        if (Owner.PlayerCombatState!.OrbQueue.Orbs.Count <= 0) return;

        await CreatureCmd.TriggerAnim(Owner.Creature, SpineAnimator.castTrigger, Owner.Character.CastAnimDelay);
        await OrbCmd.EvokeNext(choiceContext, Owner, false);

        await Cmd.CustomScaledWait(0.1f, 0.25f);
        await OrbCmd.EvokeNext(choiceContext, Owner);
    }

    protected override void OnUpgrade()
    {
        UpgradeEnergyCostBy(-1);
    }
}
