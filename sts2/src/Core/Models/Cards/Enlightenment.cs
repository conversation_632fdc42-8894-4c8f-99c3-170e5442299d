using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
namespace MegaCrit.Sts2.Core.Models.Cards;

public sealed class Enlightenment : CardModel
{
    public override CardType Type => CardType.Skill;
    public override CardRarity Rarity => CardRarity.Event;
    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.None;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.Self;
    public override int CanonicalEnergyCost => 0;

    public override IEnumerable<CardKeyword> CanonicalKeywords => [CardKeyword.Exhaust];

    protected override Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        IEnumerable<CardModel> cards = PileType.Hand.GetPile(Owner).Cards.Where(c => c.CurrentEnergyCost > 1);

        foreach (CardModel card in cards)
        {
            if (IsUpgraded)
            {
                card.SetEnergyCostThisCombat(1);
            }
            else
            {
                card.SetEnergyCostThisTurn(1);
            }
        }

        return Task.CompletedTask;
    }
}
