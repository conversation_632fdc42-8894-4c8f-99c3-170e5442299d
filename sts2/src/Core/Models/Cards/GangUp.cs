using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Combat.History.Entries;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class GangUp : CardModel
{
    private const string _damagePerAttackKey = "DamagePerAttack";
    private const string _trueDamageKey = "TrueDamage";

    public override int CanonicalEnergyCost => 1;
    public override CardType Type => CardType.Attack;
    public override CardRarity Rarity => CardRarity.Uncommon;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.Any;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.None;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new (_damagePerAttackKey, 7), // Not DamageVar, only final damage is affected by powers
        new DamageVar(_trueDamageKey, 0, DamageProps.card),
    ];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        ArgumentNullException.ThrowIfNull(target);

        RecalculateValues(target);

        await DamageCmd.Attack(DynamicVars[_trueDamageKey].BaseValue, 1)
            .FromCard(this)
            .Targeting(target)
            .WithHitFx(VfxCmd.bluntPath, tmpSfx: TmpSfx.bluntAttack)
            .Execute();
    }

    public override void RecalculateValues(Creature target)
    {
        int numOfAttacks = CombatManager.Instance.History.Entries.OfType<DamageReceivedEntry>().Count(e =>
            e.Receiver == target &&
            e.Result.Props.IsPoweredAttack() &&
            e.HappenedThisTurn(CombatState) &&
            e.Dealer != Owner.Creature
            // Note: no check for unblocked damage here
        );

        DynamicVars[_trueDamageKey].BaseValue = numOfAttacks * DynamicVars[_damagePerAttackKey].BaseValue;
    }

    public override void RecalculateValues()
    {
        DynamicVars[_trueDamageKey].BaseValue = 0;
    }

    protected override void OnUpgrade()
    {
        DynamicVars[_damagePerAttackKey].UpgradeValueBy(2);
    }
}
