using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Combat.History.Entries;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Invoke : CardModel
{
    private const string _baseDamageKey = "BaseDamaage";
    public override int CanonicalEnergyCost => 0;
    public override CardType Type => CardType.Attack;
    public override CardRarity Rarity => CardRarity.Common;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.Any;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.None;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new DamageVar(_baseDamageKey, 4, DamageProps.card),
        new DamageVar(4, DamageProps.card),
    ];

    protected override bool ShouldGlowGoldInternal => DrawnThisTurn;

    private bool DrawnThisTurn => CombatManager.Instance.History.Entries.OfType<CardDrawnEntry>().Any(e => e.Card == this && !e.FromHandDraw && e.HappenedThisTurn(CombatState));

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        ArgumentNullException.ThrowIfNull(target);

        RecalculateValues();
        await DamageCmd.Attack(DynamicVars.Damage.BaseValue, 1)
            .FromCard(this)
            .Targeting(target)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }

    public override void RecalculateValues()
    {
        DynamicVars.Damage.BaseValue = DynamicVars[_baseDamageKey].BaseValue;

        if (DrawnThisTurn)
        {
            DynamicVars.Damage.BaseValue *= 3;
        }
    }

    protected override void OnUpgrade()
    {
        DynamicVars.Damage.UpgradeValueBy(2);
        DynamicVars[_baseDamageKey].UpgradeValueBy(2);
    }
}
