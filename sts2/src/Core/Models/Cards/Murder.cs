using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Murder : CardModel
{
    private const string _baseDamageKey = "BaseDamage";
    private const string _extraDamage = "ExtraDamage";

    public override int CanonicalEnergyCost => 2;
    public override CardType Type => CardType.Attack;
    public override CardRarity Rarity => CardRarity.Rare;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.Any;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.None;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new DamageVar(0, DamageProps.card),
        new(_baseDamageKey, 7),
        new(_extraDamage, 7),
    ];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        ArgumentNullException.ThrowIfNull(target);

        RecalculateValues();
        await DamageCmd.Attack(DynamicVars.Damage.BaseValue, 1)
            .FromCard(this)
            .Targeting(target)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }

    public override void RecalculateValues()
    {
        DynamicVars.Damage.BaseValue = DynamicVars[_baseDamageKey].BaseValue + DynamicVars[_extraDamage].BaseValue * (CombatState!.RoundNumber);
    }

    protected override void OnUpgrade()
    {
        DynamicVars[_baseDamageKey].UpgradeValueBy(2);
        DynamicVars[_extraDamage].UpgradeValueBy(2);
    }
}
