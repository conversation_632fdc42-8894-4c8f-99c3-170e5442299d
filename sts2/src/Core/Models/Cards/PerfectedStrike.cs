using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class PerfectedStrike : CardModel
{
    private const string _baseDamageKey = "BaseDamage";
    private const string _extraDamageKey = "ExtraDamage";

    public override int CanonicalEnergyCost => 2;
    public override CardType Type => CardType.Attack;
    public override CardRarity Rarity => CardRarity.Common;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.Any;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.None;

    protected override HashSet<CardTag> CanonicalTags => [CardTag.Strike];

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new DamageVar(0, DamageProps.card),
        new(_baseDamageKey, 6),
        new(_extraDamageKey, 2)
    ];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        ArgumentNullException.ThrowIfNull(target);
        RecalculateValues();

        await DamageCmd.Attack(DynamicVars.Damage.BaseValue, 1)
            .FromCard(this)
            .Targeting(target)
            .WithHitFx(vfx: VfxCmd.slashPath, tmpSfx: TmpSfx.heavyAttack)
            .Execute();
    }

    public override void RecalculateValues()
    {
        int strikes = Owner.PlayerCombatState!.AllCards.Count(c => c.Tags.Contains(CardTag.Strike));
        DynamicVars.Damage.BaseValue = DynamicVars[_baseDamageKey].BaseValue + DynamicVars[_extraDamageKey].IntValue * strikes;
    }

    protected override void OnUpgrade()
    {
        DynamicVars[_extraDamageKey].UpgradeValueBy(1);
    }
}
