using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Combat.History.Entries;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Radiate : CardModel
{
    public override int CanonicalEnergyCost => 0;

    public override CardType Type => CardType.Attack;
    public override CardRarity Rarity => CardRarity.Uncommon;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.All;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.None;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new DamageVar(3, DamageProps.card),
        new RepeatVar(0),
        new StarsVar(1)
    ];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        RecalculateValues();

        await DamageCmd.Attack(DynamicVars.Damage.BaseValue, DynamicVars.Repeat.IntValue)
            .FromCard(this)
            .TargetingAll(CombatState!.HittableEnemies)
            .WithHitFx(VfxCmd.giantHorizontalSlashPath, tmpSfx: TmpSfx.slashAttack)
            .Execute();
    }

    public override void RecalculateValues()
    {
        DynamicVars.Repeat.BaseValue = CombatManager.Instance.History.Entries
            .OfType<StarsModifiedEntry>()
            .Where(e => e.HappenedThisTurn(CombatState) && e.Amount > 0)
            .Sum(e => e.Amount);
    }

    protected override void OnUpgrade()
    {
        DynamicVars.Damage.UpgradeValueBy(1);
    }
}
