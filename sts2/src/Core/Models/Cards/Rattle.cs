using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Combat.History.Entries;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Rattle : CardModel
{
    private const string _baseDamageKey = "BaseDamage";
    private const string _additionalDamage = "AdditionalDamage";

    public override int CanonicalEnergyCost => 1;

    public override CardType Type => CardType.Attack;
    public override CardRarity Rarity => CardRarity.Common;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.Any;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.None;
    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.Static(StaticHoverTip.SummonStatic)];

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new OstyDamageVar(_baseDamageKey, 5, DamageProps.card),
        new IntVar(_additionalDamage, 3),
        new OstyDamageVar(0, DamageProps.card),
    ];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        ArgumentNullException.ThrowIfNull(target);

        if (Osty.CheckMissingWithAnim(Owner)) return;

        RecalculateValues();

        await OstyCmd.Attack(
            target,
            DynamicVars.OstyDamage,
            1,
            Owner.Osty!,
            this
        );
    }

    public override void RecalculateValues()
    {
        int timesSummonedThisTurn = CombatManager.Instance.History.Entries.OfType<SummonedEntry>().Count(e =>
            e.Actor == Owner.Creature &&
            e.HappenedThisTurn(CombatState)
        );
        DynamicVars.OstyDamage.BaseValue = DynamicVars[_baseDamageKey].BaseValue + DynamicVars[_additionalDamage].BaseValue * timesSummonedThisTurn;
    }

    protected override void OnUpgrade()
    {
        DynamicVars[_additionalDamage].UpgradeValueBy(1);
    }
}
