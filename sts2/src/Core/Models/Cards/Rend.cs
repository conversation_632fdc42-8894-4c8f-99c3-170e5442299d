using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Rend : CardModel
{
    private const string _baseDamageKey = "BaseDamage";
    private const string _extraDamageKey = "ExtraDamage";

    public override int CanonicalEnergyCost => 2;

    public override CardType Type => CardType.Attack;
    public override CardRarity Rarity => CardRarity.Rare;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.Any;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.None;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_baseDamageKey, 15),
        new(_extraDamageKey, 5),
        new DamageVar(15, DamageProps.card),
    ];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        ArgumentNullException.ThrowIfNull(target);

        RecalculateValues(target);

        await DamageCmd.Attack(DynamicVars.Damage.BaseValue, 1)
            .FromCard(this)
            .Targeting(target)
            .WithHitFx(vfx: VfxCmd.bluntPath, tmpSfx: TmpSfx.bluntAttack)
            .Execute();
    }

    public override void RecalculateValues()
    {
        DynamicVars.Damage.BaseValue = DynamicVars[_baseDamageKey].BaseValue;
    }

    public override void RecalculateValues(Creature target)
    {
        decimal debuffs = target.Powers.Count(p => p.TypeForCurrentAmount == PowerType.Debuff);
        DynamicVars.Damage.BaseValue = DynamicVars[_baseDamageKey].BaseValue + DynamicVars[_extraDamageKey].BaseValue * debuffs;
    }

    protected override void OnUpgrade()
    {
        DynamicVars.Damage.UpgradeValueBy(3);
        DynamicVars[_extraDamageKey].UpgradeValueBy(3);
        DynamicVars[_baseDamageKey].UpgradeValueBy(3);
    }
}
