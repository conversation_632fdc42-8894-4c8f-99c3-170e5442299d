using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.CardSelection;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SecretTechnique : CardModel
{
    public override int CanonicalEnergyCost => 0;

    public override CardType Type => CardType.Skill;
    public override CardRarity Rarity => CardRarity.Rare;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.None;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.Self;

    public override IEnumerable<CardKeyword> CanonicalKeywords => [CardKeyword.Exhaust];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        CardSelectorPrefs prefs = new(SelectionScreenPrompt, 1);
        List<CardModel> validCards = PileType.Draw.GetPile(Owner).Cards.Where(c => c.Type == CardType.Skill).ToList();
        IEnumerable<CardModel> cards = await CardSelectCmd.FromSimpleGrid(
            choiceContext,
            validCards,
            Owner,
            prefs
        );

        CardModel? card = cards.FirstOrDefault();

        if (card != null)
        {
            await CardPileCmd.Add(card, PileType.Hand);
        }
    }

    protected override void OnUpgrade()
    {
        RemoveKeyword(CardKeyword.Exhaust);
    }
}
