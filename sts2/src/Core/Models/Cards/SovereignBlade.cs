using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Commands.Builders;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SovereignBlade : CardModel
{
    private const string _seekingEdgeKey = "SeekingEdge";
    private const int _baseDamage = 10;

    protected override IEnumerable<string> ExtraAssetPaths => NSovereignBladeVfx.AssetPaths;

    public override CardType Type => CardType.Attack;
    public override CardRarity Rarity => CardRarity.Token;

    public override UiTargetEnemy TargetEnemy => HasSeekingEdge ? UiTargetEnemy.All : UiTargetEnemy.Any;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.None;

    private bool _createdThroughForge;

    public bool CreatedThroughForge
    {
        get => _createdThroughForge;
        set
        {
            AssertMutable();
            _createdThroughForge = value;
        }
    }

    private decimal _currentDamage;

    private decimal CurrentDamage
    {
        get => _currentDamage;
        set
        {
            AssertMutable();
            _currentDamage = value;
        }
    }

    public override IEnumerable<CardKeyword> CanonicalKeywords =>
    [
        CardKeyword.Retain
    ];

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new DamageVar(_baseDamage, DamageProps.card),
        new BoolVar(_seekingEdgeKey)
    ];

    public override int CanonicalEnergyCost => 2;

    private bool HasSeekingEdge =>
        CombatManager.Instance.IsInProgress &&
        Owner.Creature.HasPower<SeekingEdgePower>();

    protected override void AfterCloned()
    {
        base.AfterCloned();
        CreatedThroughForge = false;
    }

    protected override void AfterDowngraded()
    {
        base.AfterDowngraded();
        DynamicVars.Damage.BaseValue = CurrentDamage;
    }

    public override void AfterTransformedFrom()
    {
        RemoveSovereignBladeNode();
    }

    public override Task AfterCardChangedPiles(CardModel card, PileType oldPileType, AbstractModel? source)
    {
        if (card != this) return Task.CompletedTask;

        if ((!CreatedThroughForge && oldPileType == PileType.None) || oldPileType == PileType.Exhaust)
        {
            ForgeCmd.PlayCombatRoomForgeVfx(Owner, this);
        }

        if (card.Pile!.Type == PileType.Exhaust)
        {
            RemoveSovereignBladeNode();
        }

        return Task.CompletedTask;
    }

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        IReadOnlyList<Creature> enemies;

        if (HasSeekingEdge)
        {
            enemies = CombatState!.HittableEnemies;
        }
        else
        {
            ArgumentNullException.ThrowIfNull(target);
            enemies = [target];
        }

        AttackCommand attack = await DamageCmd.Attack(DynamicVars.Damage.BaseValue, 1)
            .FromCard(this)
            .TargetingAll(enemies)
            .BeforeDamage(() =>
            {
                NSovereignBladeVfx? bladeNode = GetVfxNode(Owner, this);
                NCreature? targetNode = NCombatRoom.Instance?.GetCreatureNode(enemies[0]);

                if (bladeNode != null && targetNode != null)
                {
                    bladeNode.Attack(targetNode.VfxSpawnPosition);
                }

                return Task.CompletedTask;
            })
            .WithAttackerAnim(SpineAnimator.castTrigger, Owner.Character.AttackAnimDelay)
            .WithHitFx(VfxCmd.slashPath, tmpSfx: TmpSfx.slashAttack)
            .Execute();

        ParryPower? parry = Owner.Creature.GetPower<ParryPower>();

        if (parry != null)
        {
            await parry.AfterSovereignBladePlayed(Owner.Creature, attack.Results);
        }
    }

    public override void RecalculateValues()
    {
        ((BoolVar)DynamicVars[_seekingEdgeKey]).BoolVal = HasSeekingEdge;
    }

    protected override void OnUpgrade()
    {
        UpgradeEnergyCostBy(-1);
    }

    public void AddDamage(decimal amount)
    {
        DynamicVars.Damage.BaseValue += amount;
        CurrentDamage = DynamicVars.Damage.BaseValue;
    }

    public void MultiplyDamage(decimal amount)
    {
        DynamicVars.Damage.BaseValue *= amount;
        CurrentDamage = DynamicVars.Damage.BaseValue;
    }

    public static NSovereignBladeVfx? GetVfxNode(Player player, CardModel card)
    {
        // If this is a dupe, we want the blade node of the original so we don't create a second blade node.
        CardModel originalCard = card.DupeOf ?? card;

        NCreature? playerNode = NCombatRoom.Instance?.GetCreatureNode(player.Creature);
        return playerNode?.GetChildren().OfType<NSovereignBladeVfx>().FirstOrDefault(b => b.Card == originalCard);
    }

    public void RemoveSovereignBladeNode()
    {
        GetVfxNode(Owner, this)?.RemoveSovereignBlade();
    }
}
