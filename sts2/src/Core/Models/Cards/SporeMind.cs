using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SporeMind : CardModel
{
    public override int CanonicalEnergyCost => -1;
    public override CardType Type => CardType.Curse;
    public override CardRarity Rarity => CardRarity.Curse;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.None;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.None;

    public override int MaxUpgradeLevel => 0;

    public override IEnumerable<CardKeyword> CanonicalKeywords =>
    [
        CardKeyword.Unplayable
    ];

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new DamageVar(2, DamageProps.cardHpLoss)
    ];

    public override async Task AfterCardPlayed(PlayerChoiceContext context, CardModel card, Creature? target, int playCount)
    {
        if (card.Owner != Owner) return;
        if (card.Type != CardType.Power) return;
        if (Pile!.Type != PileType.Hand) return;

        await CreatureCmd.Damage(card.Owner.Creature, DynamicVars.Damage, this);
    }

}
