using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.CardPools;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.PotionPools;
using MegaCrit.Sts2.Core.Models.RelicPools;
using MegaCrit.Sts2.Core.Models.Relics;

namespace MegaCrit.Sts2.Core.Models.Characters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Ironclad : CharacterModel
{
    public const string energyColorName = "ironclad";
    public override Color NameColor => StsColors.red;
    public override int StartingHp => 80;
    public override int StartingGold => 99;

    public override CardPoolModel CardPool => ModelDb.CardPool<IroncladCardPool>();
    public override PotionPoolModel PotionPool => ModelDb.PotionPool<IroncladPotionPool>();
    public override RelicPoolModel RelicPool => ModelDb.RelicPool<IroncladRelicPool>();

    public override IEnumerable<CardModel> StartingDeck =>
    [
        ModelDb.Card<StrikeIronclad>(),
        ModelDb.Card<StrikeIronclad>(),
        ModelDb.Card<StrikeIronclad>(),
        ModelDb.Card<StrikeIronclad>(),
        ModelDb.Card<StrikeIronclad>(),
        ModelDb.Card<DefendIronclad>(),
        ModelDb.Card<DefendIronclad>(),
        ModelDb.Card<DefendIronclad>(),
        ModelDb.Card<DefendIronclad>(),
        ModelDb.Card<Bash>()
    ];

    public override IEnumerable<IReadOnlyList<CardModel>> CardBundles =>
    [
        [ModelDb.Card<Headbutt>(), ModelDb.Card<TrueGrit>(), ModelDb.Card<Rampage>()],
        [ModelDb.Card<SwordBoomerang>(), ModelDb.Card<Stomp>(), ModelDb.Card<Inflame>()],
        [ModelDb.Card<PerfectedStrike>(), ModelDb.Card<SetupStrike>(), ModelDb.Card<ExpectAFight>()],
        [ModelDb.Card<Anger>(), ModelDb.Card<Tremble>(), ModelDb.Card<BattleTrance>()],
        [ModelDb.Card<Havoc>(), ModelDb.Card<Cinder>(), ModelDb.Card<HowlFromBeyond>()],
        [ModelDb.Card<BodySlam>(), ModelDb.Card<ShrugItOff>(), ModelDb.Card<Unmovable>()],
        [ModelDb.Card<Breakthrough>(), ModelDb.Card<BloodWall>(), ModelDb.Card<Rupture>()],
        [ModelDb.Card<Thunderclap>(), ModelDb.Card<MoltenFist>(), ModelDb.Card<Dominate>()]
    ];

    public override IReadOnlyList<RelicModel> StartingRelics => [ModelDb.Relic<BurningBlood>()];

    public override float AttackAnimDelay => 0.15f;
    public override float CastAnimDelay => 0.25f;
    public override Color EnergyLabelOutlineColor => new("801212FF");
    public override string CharacterSelectSfx => TmpSfx.heavyAttack;
    public override Color MapDrawingColor => new("CB282B");

    public override Color RemoteTargetingLineColor => new("E15847FF");
    public override Color RemoteTargetingLineOutline => new("801212FF");
}
