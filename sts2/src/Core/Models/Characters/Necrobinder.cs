using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.CardPools;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.PotionPools;
using MegaCrit.Sts2.Core.Models.RelicPools;
using MegaCrit.Sts2.Core.Models.Relics;

namespace MegaCrit.Sts2.Core.Models.Characters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Necrobinder : CharacterModel
{
    public const string energyColorName = "necrobinder";
    public override Color NameColor => StsColors.purple;
    public override int StartingHp => 66;
    public override int StartingGold => 99;

    public override CardPoolModel CardPool => ModelDb.CardPool<NecrobinderCardPool>();
    public override RelicPoolModel RelicPool => ModelDb.RelicPool<NecrobinderRelicPool>();
    public override PotionPoolModel PotionPool => ModelDb.PotionPool<NecrobinderPotionPool>();

    protected override IEnumerable<string> ExtraAssetPaths => [SceneHelper.GetScenePath(healOstyPath)];

    public override IEnumerable<CardModel> StartingDeck =>
    [
        ModelDb.Card<StrikeNecrobinder>(),
        ModelDb.Card<StrikeNecrobinder>(),
        ModelDb.Card<StrikeNecrobinder>(),
        ModelDb.Card<StrikeNecrobinder>(),
        ModelDb.Card<StrikeNecrobinder>(),
        ModelDb.Card<DefendNecrobinder>(),
        ModelDb.Card<DefendNecrobinder>(),
        ModelDb.Card<DefendNecrobinder>(),
        ModelDb.Card<DefendNecrobinder>(),
        ModelDb.Card<Bodyguard>()
    ];

    public override IEnumerable<IReadOnlyList<CardModel>> CardBundles =>
    [
        [ModelDb.Card<Poke>(), ModelDb.Card<Swipe>(), ModelDb.Card<FavoriteStick>()],
        [ModelDb.Card<Fear>(), ModelDb.Card<DeathsVisage>(), ModelDb.Card<Veilpiercer>()],
        [ModelDb.Card<Cleanse>(), ModelDb.Card<Invoke>(), ModelDb.Card<Severance>()],
        [ModelDb.Card<Undeath>(), ModelDb.Card<DrainPower>(), ModelDb.Card<Bury>()],
        [ModelDb.Card<TagIn>(), ModelDb.Card<Rattle>(), ModelDb.Card<BoneWall>()],
        [ModelDb.Card<CarveGhost>(), ModelDb.Card<GraveWarden>(), ModelDb.Card<PullFromBelow>()],
        [ModelDb.Card<BlightStrike>(), ModelDb.Card<Scourge>(), ModelDb.Card<DeathsDoor>()],
        [ModelDb.Card<Wisp>(), ModelDb.Card<Reap>(), ModelDb.Card<DanseMacabre>()],
    ];

    public override IReadOnlyList<RelicModel> StartingRelics => [ModelDb.Relic<BoundPhylactery>()];

    public override float AttackAnimDelay => 0.15f;
    public override float CastAnimDelay => 0.25f;
    public override Color EnergyLabelOutlineColor => new("702D6FFF");
    public override string CharacterSelectSfx => TmpSfx.applyDoom;
    public const string healOstyPath = "vfx/vfx_heal_osty";
    public override Color MapDrawingColor => new("AC0486");

    public override Color RemoteTargetingLineColor => new("FD98C9FF");
    public override Color RemoteTargetingLineOutline => new("702D6FFF");
}
