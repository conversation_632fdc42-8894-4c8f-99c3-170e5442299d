using MegaCrit.Sts2.Core.Entities.Cards;

namespace MegaCrit.Sts2.Core.Models.Enchantments;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Instinct : EnchantmentModel
{
    public override bool CanEnchantCardType(CardType cardType) => cardType == CardType.Attack;

    public override bool CanEnchant(CardModel card)
    {
        return base.CanEnchant(card)
            && !card.Keywords.Contains(CardKeyword.Unplayable)
            && card is { HasEnergyCostX: false, BaseEnergyCost: > 0 };
    }

    protected override void OnEnchant()
    {
        Card.UpgradeEnergyCostBy(-1);
    }
}
