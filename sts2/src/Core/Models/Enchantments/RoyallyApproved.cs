using MegaCrit.Sts2.Core.Entities.Cards;

namespace MegaCrit.Sts2.Core.Models.Enchantments;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class RoyallyApproved : EnchantmentModel
{
    public override bool CanEnchantCardType(CardType cardType) => cardType is CardType.Attack or CardType.Skill;

    protected override void OnEnchant()
    {
        Card.UpgradeEnergyCostBy(-Card.BaseEnergyCost);
        Card.AddKeyword(CardKeyword.Innate);
        Card.AddKeyword(CardKeyword.Exhaust);
    }
}
