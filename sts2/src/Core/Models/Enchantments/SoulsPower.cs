using System.Collections.Generic;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.HoverTips;

namespace MegaCrit.Sts2.Core.Models.Enchantments;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SoulsPower : EnchantmentModel
{
    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.Static(StaticHoverTip.ReplayStatic)];

    public override bool CanEnchant(CardModel card)
    {
        if (!base.CanEnchant(card)) return false;
        if (!card.Keywords.Contains(CardKeyword.Exhaust)) return false;

        // If we've made it here and the card is NOT upgradable, we're safe to enchant it.
        if (!card.IsUpgradable) return true;

        // If we've made it here, the card IS upgradable, so we need to make sure that the upgraded version of the card
        // doesn't lose Exhaust.
        CardModel upgradedCard;

        // We're safe to manually use ToMutable() and MutableClone() here, since we're just creating the card in order
        // to check if it has Exhaust, and then throwing it out rather than adding it to a climb/combat.
        if (card.IsMutable)
        {
            upgradedCard = (CardModel)card.MutableClone();
        }
        else
        {
            upgradedCard = card.ToMutable();
        }

        upgradedCard.UpgradeInternal();

        return upgradedCard.Keywords.Contains(CardKeyword.Exhaust);
    }

    protected override void OnEnchant()
    {
        Card.ReplayCount++;
    }
}
