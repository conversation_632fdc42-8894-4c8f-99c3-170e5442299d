using System.Collections.Generic;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.HoverTips;

namespace MegaCrit.Sts2.Core.Models.Enchantments;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TezcatarasEmber : EnchantmentModel
{
    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromKeyword(CardKeyword.Eternal)];

    protected override void OnEnchant()
    {
        Card.UpgradeEnergyCostBy(-Card.BaseEnergyCost);
        Card.AddKeyword(CardKeyword.Eternal);
    }
}
