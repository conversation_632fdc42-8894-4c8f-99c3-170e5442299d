using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Enchantments;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Enchantments;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Vigorous : EnchantmentModel
{
    public override bool CanEnchantCardType(CardType cardType) => cardType == CardType.Attack;

    public override bool ShowAmount => true;

    /// <summary>
    /// NOTE: We use ModifyDamageGiven here instead of <see cref="EnchantmentModel.OnEnchant"/> because this damage
    /// modification is dynamic. Instead of changing the base amount of damage that its card does, it should modify the
    /// final damage after other effects (upgrades, etc.) have had a chance to modify the base.
    /// </summary>
    public override decimal ModifyDamageGiven(Creature? dealer, decimal amount, ValueProp props, Creature? _, CardModel? card)
    {
        if (card != Card) return amount;
        if (Status != EnchantmentStatus.Normal) return amount;
        return amount + Amount;
    }

    public override Task AfterCardPlayed(PlayerChoiceContext context, CardModel card, Creature? target, int playCount)
    {
        if (card != Card) return Task.CompletedTask;
        Status = EnchantmentStatus.Disabled;
        return Task.CompletedTask;
    }
}
