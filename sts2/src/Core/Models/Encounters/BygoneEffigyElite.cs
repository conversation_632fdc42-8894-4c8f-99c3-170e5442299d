using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class BygoneEffigyElite : EncounterModel
{
    public override RoomType RoomType => RoomType.Elite;

    public override float GetCameraScaling(CombatState combatState) => 0.88f;

    public override Vector2 GetCameraOffset(CombatState combatState) => Vector2.Down * 50f;

    public override IEnumerable<MonsterModel> AllPossibleMonsters => [ModelDb.Monster<BygoneEffigy>()];

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters() =>
    [
        (ModelDb.Monster<BygoneEffigy>().ToMutable(), null)
    ];
}
