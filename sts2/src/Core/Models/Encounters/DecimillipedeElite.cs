using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class DecimillipedeElite : EncounterModel
{
    public override bool HasScene => true;

    private const string _segmentSlot = "segment";

    public override IReadOnlyList<string> Slots =>
    [
        $"{_segmentSlot}1",
        $"{_segmentSlot}2",
        $"{_segmentSlot}3"
    ];

    public override float GetCameraScaling(CombatState combatState) => 0.87f;

    public override Vector2 GetCameraOffset(CombatState combatState) => Vector2.Down * 50f;

    public override RoomType RoomType => RoomType.Elite;
    public override IEnumerable<MonsterModel> AllPossibleMonsters => [ModelDb.Monster<DecimillipedeSegmentFront>(), ModelDb.Monster<DecimillipedeSegmentMiddle>(), ModelDb.Monster<DecimillipedeSegmentBack>()];

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters()
    {
        DecimillipedeSegment front = (DecimillipedeSegment)ModelDb.Monster<DecimillipedeSegmentFront>().ToMutable();
        DecimillipedeSegment middle = (DecimillipedeSegment)ModelDb.Monster<DecimillipedeSegmentMiddle>().ToMutable();
        DecimillipedeSegment back = (DecimillipedeSegment)ModelDb.Monster<DecimillipedeSegmentBack>().ToMutable();

        int offset = Rng.NextInt(3);
        front.StarterMove = offset;
        middle.StarterMove = (offset + 1) % 3;
        back.StarterMove = (offset + 2) % 3;

        return
        [
            (front, $"{_segmentSlot}1"),
            (middle, $"{_segmentSlot}2"),
            (back, $"{_segmentSlot}3")
        ];
    }
}
