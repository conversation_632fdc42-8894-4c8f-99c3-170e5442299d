using System.Collections.Generic;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

/// <summary>
/// Represents an encounter that has been removed from the game. Mostly used for the climb history.
/// </summary>
// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class DeprecatedEncounter : EncounterModel
{
    public override RoomType RoomType => RoomType.Monster;
    public override bool IsDebugEncounter => true;
    public override IEnumerable<MonsterModel> AllPossibleMonsters => [];

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters() => [];
}
