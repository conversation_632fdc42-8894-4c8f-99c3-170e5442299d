using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class KnowledgeDemonBoss : EncounterModel
{
    public override RoomType RoomType => RoomType.Boss;

    public override float GetCameraScaling(CombatState combatState) => 0.9f;
    public override Vector2 GetCameraOffset(CombatState combatState) => Vector2.Down * 50f;

    public override string BossNodePath => $"res://images/packed/map/placeholder/{Id.Entry.ToLower()}_icon.png";
    public override SpineSkeletonDataResource? BossNodeSpineResource => null;
    public override IEnumerable<MonsterModel> AllPossibleMonsters => [ModelDb.Monster<KnowledgeDemon>()];

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters() =>
    [
        (ModelDb.Monster<KnowledgeDemon>().ToMutable(), null)
    ];

    protected override bool HasCustomBackground => true;
}
