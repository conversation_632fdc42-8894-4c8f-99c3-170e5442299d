using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class OvicopterNormal : EncounterModel
{
    public override bool HasScene => true;
    public override float GetCameraScaling(CombatState combatState) => 0.85f;

    public override Vector2 GetCameraOffset(CombatState combatState) => Vector2.Down * 50f + Vector2.Left * 100f;

    private const string _ovicopterSlot = "ovicopter";
    private const string _eggSlotPrefix = "egg";

    public override IReadOnlyList<string> Slots =>
    [
        $"{_eggSlotPrefix}1",
        $"{_eggSlotPrefix}2",
        $"{_eggSlotPrefix}3",
        $"{_eggSlotPrefix}4",
        $"{_eggSlotPrefix}5",
        _ovicopterSlot,
    ];

    public override RoomType RoomType => RoomType.Monster;
    public override IEnumerable<MonsterModel> AllPossibleMonsters => [ModelDb.Monster<Ovicopter>(), ModelDb.Monster<ToughEgg>()];

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters()
    {
        List<(MonsterModel, string?)> monsters = [];
        monsters.Add((ModelDb.Monster<Ovicopter>().ToMutable(), _ovicopterSlot));

        return monsters;
    }
}
