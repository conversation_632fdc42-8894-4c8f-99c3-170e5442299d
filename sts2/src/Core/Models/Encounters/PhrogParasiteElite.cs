using System.Collections.Generic;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class PhrogParasiteElite : EncounterModel
{
    private const string _wrigglerSlotPrefix = "wriggler";
    private const string _phrogSlot = "phrog";

    public override RoomType RoomType => RoomType.Elite;

    public override IReadOnlyList<string> Slots =>
    [
        _phrogSlot,
        $"{_wrigglerSlotPrefix}1",
        $"{_wrigglerSlotPrefix}2",
        $"{_wrigglerSlotPrefix}3",
        $"{_wrigglerSlotPrefix}4"
    ];

    public override bool HasScene => true;

    public static string GetWrigglerSlotName(int index) => $"{_wrigglerSlotPrefix}{index + 1}";

    public override IEnumerable<MonsterModel> AllPossibleMonsters => [ModelDb.Monster<PhrogParasite>(), ModelDb.Monster<Wriggler>()];

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters() =>
    [
        (ModelDb.Monster<PhrogParasite>().ToMutable(), _phrogSlot)
    ];
}
