using System.Collections.Generic;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TerrorEelElite : EncounterModel
{
    public override RoomType RoomType => RoomType.Elite;
    public override float GetCameraScaling(CombatState combatState) => 0.9f;

    public override IEnumerable<MonsterModel> AllPossibleMonsters => [ModelDb.Monster<TerrorEel>()];

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters() =>
    [
        (ModelDb.Monster<TerrorEel>().ToMutable(), null),
    ];
}
