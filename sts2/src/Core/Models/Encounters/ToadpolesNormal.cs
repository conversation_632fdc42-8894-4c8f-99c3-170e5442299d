using System.Collections.Generic;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class ToadpolesNormal : EncounterModel
{
    public override RoomType RoomType => RoomType.Monster;

    public override IEnumerable<MonsterModel> AllPossibleMonsters => [ModelDb.Monster<Toadpole>()];

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters()
    {
        Toadpole frontMonster = (Toadpole)ModelDb.Monster<Toadpole>().ToMutable();
        frontMonster.IsFront = true;

        Toadpole backMonster = (Toadpole)ModelDb.Monster<Toadpole>().ToMutable();
        backMonster.IsFront = false;

        return
        [
            (frontMonster, null),
            (ModelDb.Monster<Seapunk>().ToMutable(), null),
            (backMonster, null),
        ];
    }
}
