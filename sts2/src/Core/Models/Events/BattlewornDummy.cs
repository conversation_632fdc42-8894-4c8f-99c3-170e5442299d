using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Factories;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Encounters;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Models.PotionPools;
using MegaCrit.Sts2.Core.Rewards;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class BattlewornDummy : EventModel
{
    private const string _setting1HpKey = "Setting1Hp";
    private const string _setting2HpKey = "Setting2Hp";
    private const string _setting3HpKey = "Setting3Hp";

    public override bool IsShared => true;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_setting1HpKey, ModelDb.Monster<BattleFriendV1>().MinInitialHp),
        new(_setting2HpKey, ModelDb.Monster<BattleFriendV2>().MinInitialHp),
        new(_setting3HpKey, ModelDb.Monster<BattleFriendV3>().MinInitialHp)
    ];

    protected override IReadOnlyList<EventOption> GenerateInitialOptions()
    {
        // Even though this could be done in the CanonicalVars getter, we do it here because Owner doesn't exist when
        // the GameInfo collector runs.
        int playerCount = Owner?.ClimbState.Players.Count ?? 1;
        int actIndex = Owner?.ClimbState.CurrentActIndex ?? 0;
        DynamicVars[_setting1HpKey].BaseValue = Creature.ScaleHpForMultiplayer(ModelDb.Monster<BattleFriendV1>().MinInitialHp, ModelDb.Encounter<BattlewornDummyEventEncounter>(), playerCount, actIndex);
        DynamicVars[_setting2HpKey].BaseValue = Creature.ScaleHpForMultiplayer(ModelDb.Monster<BattleFriendV2>().MinInitialHp, ModelDb.Encounter<BattlewornDummyEventEncounter>(), playerCount, actIndex);
        DynamicVars[_setting3HpKey].BaseValue = Creature.ScaleHpForMultiplayer(ModelDb.Monster<BattleFriendV3>().MinInitialHp, ModelDb.Encounter<BattlewornDummyEventEncounter>(), playerCount, actIndex);

        return [
            new EventOption(Setting1, "BATTLEWORN_DUMMY.pages.INITIAL.options.SETTING_1"),
            new EventOption(Setting2, "BATTLEWORN_DUMMY.pages.INITIAL.options.SETTING_2"),
            new EventOption(Setting3, "BATTLEWORN_DUMMY.pages.INITIAL.options.SETTING_3"),
        ];
    }

    private Task Setting1()
    {
        StartCombat(BattlewornDummyEventEncounter.DummySetting.Setting1);
        return Task.CompletedTask;
    }

    private Task Setting2()
    {
        StartCombat(BattlewornDummyEventEncounter.DummySetting.Setting2);
        return Task.CompletedTask;
    }

    private Task Setting3()
    {
        StartCombat(BattlewornDummyEventEncounter.DummySetting.Setting3);
        return Task.CompletedTask;
    }

    public override async Task Resume(AbstractRoom room)
    {
        CombatRoom combatRoom = (CombatRoom)room;
        BattlewornDummyEventEncounter encounter = (BattlewornDummyEventEncounter)combatRoom.Encounter;

        if (encounter.RanOutOfTime)
        {
            SetEventFinished(L10NLookup("BATTLEWORN_DUMMY.pages.DEFEAT.description"));
        }
        else
        {
            SetEventFinished(L10NLookup("BATTLEWORN_DUMMY.pages.VICTORY.description"));

            switch (encounter.Setting)
            {
                case BattlewornDummyEventEncounter.DummySetting.Setting1:
                    // Procure 1 random potion.
                    IEnumerable<PotionModel> options = Owner!.Character.PotionPool.Potions
                        .Concat(ModelDb.PotionPool<SharedPotionPool>().Potions);

                    PotionModel? potion = Owner!.PlayerRng.Rewards.NextItem(options);

                    if (potion != null)
                    {
                        await RewardsCmd.Offer(Owner!, [new PotionReward(potion.ToMutable(), Owner)], false);
                    }

                    break;
                case BattlewornDummyEventEncounter.DummySetting.Setting2:
                    // Upgrade 2 random cards.
                    IEnumerable<CardModel> cards = PileType.Deck.GetPile(Owner!).Cards
                        .Where(c => c is { IsUpgradable: true })
                        .ToList()
                        .StableShuffle(Owner!.ClimbState.Rng.Niche)
                        .Take(2);

                    foreach (CardModel card in cards)
                    {
                        CardCmd.Upgrade(card);
                    }

                    break;
                case BattlewornDummyEventEncounter.DummySetting.Setting3:
                    // Obtain a random relic.
                    RelicModel relic = RelicFactory.PullNextRelicFromFront(Owner!).ToMutable();
                    await RelicCmd.Obtain(relic, Owner!);
                    break;
                default:
                    throw new InvalidOperationException($"{nameof(BattlewornDummyEventEncounter.Setting)} must be set!");
            }
        }
    }

    private void StartCombat(BattlewornDummyEventEncounter.DummySetting setting)
    {
        BattlewornDummyEventEncounter encounter = (BattlewornDummyEventEncounter)ModelDb.Encounter<BattlewornDummyEventEncounter>().ToMutable();
        encounter.Setting = setting;
        EnterCombatWithoutExitingEvent(encounter);
    }
}
