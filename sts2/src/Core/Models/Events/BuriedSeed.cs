using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.CardSelection;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Enchantments;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.Nodes.Vfx;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class BuriedSeed : EventModel
{
    private const int _sownAmount = 6;
    private const string _relicKey = "Relic";
    private const string _enchantmentKey = "Enchantment";

    protected override IReadOnlyList<EventOption> GenerateInitialOptions() =>
    [
        new(Eat, "BURIED_SEED.pages.INITIAL.options.EAT", HoverTipFactory.FromRelic<Relics.BuriedSeed>()),
        new(Plant, "BURIED_SEED.pages.INITIAL.options.PLANT", HoverTipFactory.FromEnchantment<Sown>(_sownAmount))
    ];

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new StringVar(_relicKey, ModelDb.Relic<Relics.BuriedSeed>().Title.GetFormattedText()),
        new StringVar(_enchantmentKey, ModelDb.Enchantment<Sown>().Title.GetFormattedText())
    ];

    private async Task Eat()
    {
        await RelicCmd.Obtain<Relics.BuriedSeed>(Owner!);
        SetEventFinished(L10NLookup("BURIED_SEED.pages.EAT.description"));
    }

    private async Task Plant()
    {
        EnchantmentModel sown = ModelDb.Enchantment<Sown>();
        List<CardModel> enchantableCards = PileType.Deck.GetPile(Owner!)
            .Cards
            .Where(c => sown.CanEnchant(c))
            .ToList();

        CardSelectorPrefs prefs = new(CardSelectorPrefs.EnchantSelectionPrompt, 1);
        CardModel? card = (await CardSelectCmd.FromDeckForEnchantment(
            enchantableCards,
            sown,
            _sownAmount,
            prefs
        )).FirstOrDefault();

        if (card != null)
        {
            CardCmd.Enchant<Sown>(card, _sownAmount);

            NCardEnchantVfx? vfx = NCardEnchantVfx.Create(card);
            if (vfx != null)
            {
                NClimb.Instance?.GlobalUi.CardPreviewContainer.AddChildSafely(vfx);
            }
        }

        SetEventFinished(L10NLookup("BURIED_SEED.pages.PLANT.description"));
    }
}
