using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.CardSelection;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class LivingCocoon : EventModel
{
    private const string _stickArmInHpLossKey = "StickArmInHpLoss";
    private const string _stepInsideHealAmountKey = "StepInsideHealAmount";

    protected override IReadOnlyList<EventOption> GenerateInitialOptions() =>
    [
        new(StepInside, "LIVING_COCOON.pages.INITIAL.options.STEP_INSIDE", HoverTipFactory.FromCardWithCardHoverTips<Metamorphosis>()),
        new EventOption(StickArmIn, "LIVING_COCOON.pages.INITIAL.options.STICK_ARM_IN")
            .ThatDoesDamage(DynamicVars[_stickArmInHpLossKey].BaseValue),
    ];

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new HpLossVar(_stickArmInHpLossKey, 9),
        new HealVar(_stepInsideHealAmountKey, 20)
    ];

    private async Task StepInside()
    {
        await CreatureCmd.Heal(Owner!.Creature, DynamicVars[_stepInsideHealAmountKey].BaseValue);

        CardModel card = Owner.ClimbState.CreateCard<Metamorphosis>(Owner);
        CardPileAddResult result = await CardPileCmd.Add(card, PileType.Deck);
        CardCmd.PreviewCardPileAdd(result);

        SetEventFinished(L10NLookup("LIVING_COCOON.pages.STEP_INSIDE.description"));
    }

    private async Task StickArmIn()
    {
        CardSelectorPrefs prefs = new(CardSelectorPrefs.RemoveSelectionPrompt, 1);
        List<CardModel> cards = (await CardSelectCmd.FromDeckForRemoval(Owner!, prefs)).ToList();

        await CardPileCmd.RemoveFromDeck(cards);

        await CreatureCmd.Damage(
            Owner!.Creature,
            DynamicVars[_stickArmInHpLossKey].BaseValue,
            DamageProps.nonCardHpLoss,
            null,
            null
        );

        SetEventFinished(L10NLookup("LIVING_COCOON.pages.STICK_ARM_IN.description"));
    }
}
