using System;
using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.Models.Relics;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class Orobas : AncientEventModel
{
    private static readonly RelicModel[] _discoveryRelics =
    [
        ModelDb.Relic<DemonDiscovery>(),
        ModelDb.Relic<GearDiscovery>(),
        ModelDb.Relic<LichDiscovery>(),
        ModelDb.Relic<NobleDiscovery>(),
        ModelDb.Relic<VenomDiscovery>(),
    ];

    private readonly struct DiscoveryOption : IComparable<DiscoveryOption>
    {
        public readonly RelicModel relic;
        public readonly EventOption option;

        public DiscoveryOption(RelicModel relic, EventOption option)
        {
            this.relic = relic;
            this.option = option;
        }

        public int CompareTo(DiscoveryOption other) => relic.CompareTo(other.relic);
    }

    public override IEnumerable<EventOption> AllPossibleOptions => OptionPool1
        .Concat(OptionPool2)
        .Concat(OptionPool3)
        .Concat(DiscoveryOptions.Select(c => c.option));

    private IEnumerable<DiscoveryOption> DiscoveryOptions
    {
        get { return _discoveryRelics.Select(r => new DiscoveryOption(r, RelicOption(r))); }
    }

    private IEnumerable<EventOption> OptionPool1 =>
    [
        RelicOption<Kaleidoscope>("KALEIDOSCOPIC")
    ];

    private IEnumerable<EventOption> OptionPool2 =>
    [
        RelicOption<SeekingTentacle>("SEEKER"),
        RelicOption<SeveredGill>("REFRACTION"),
        RelicOption<GlassEye>("CONFORMITY")
    ];

    private IEnumerable<EventOption> OptionPool3
    {
        get
        {
            List<EventOption> options = [];
            RecursiveCore recursiveCore = (RecursiveCore)ModelDb.Relic<RecursiveCore>().ToMutable();

            if (Owner != null)
            {
                if (recursiveCore.SetupForPlayer(Owner))
                {
                    options.Add(RelicOption(recursiveCore, "REFINEMENT"));
                }
            }
            else // required for game info
            {
                options.Add(RelicOption(recursiveCore, "REFINEMENT"));
            }

            VibrantHalo vibrantHalo = (VibrantHalo)ModelDb.Relic<VibrantHalo>().ToMutable();

            if (Owner != null)
            {
                if (vibrantHalo.SetupForPlayer(Owner))
                {
                    options.Add(RelicOption(vibrantHalo, "TRANSCENDENCE"));
                }
            }
            else // required for game info
            {
                options.Add(RelicOption(vibrantHalo, "TRANSCENDENCE"));
            }

            return options;
        }
    }

    protected override IReadOnlyList<EventOption> GenerateInitialOptions()
    {
        List<EventOption> optionPool1 = OptionPool1.ToList();
        CharacterModel character = Owner!.Character;

        // Add a random selection from Discovery choices.
        IEnumerable<EventOption> options = DiscoveryOptions
            .Where(o => ((BaseDiscoveryRelic)o.relic).Character.Id != character.Id)
            .Select(o => o.option);
        optionPool1.Add(Rng.NextItem(options)!);

        return
        [
            Rng.NextItem(optionPool1)!,
            Rng.NextItem(OptionPool2)!,
            Rng.NextItem(OptionPool3)!,
        ];
    }
}
