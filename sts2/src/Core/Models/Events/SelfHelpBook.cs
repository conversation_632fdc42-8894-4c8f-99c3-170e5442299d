using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.CardSelection;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Enchantments;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.Nodes.Vfx.Events;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SelfHelpBook : EventModel
{
    private static readonly LocString _readTheBackDescription = L10NLookup("SELF_HELP_BOOK.pages.READ_THE_BACK.description");
    private static readonly LocString _readPassageDescription = L10NLookup("SELF_HELP_BOOK.pages.READ_PASSAGE.description");
    private static readonly LocString _readEntireBookDescription = L10NLookup("SELF_HELP_BOOK.pages.READ_ENTIRE_BOOK.description");

    private const int _sharpAmount = 2;
    private const int _nimbleAmount = 3;
    private const int _swiftAmount = 2;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new StringVar("Enchantment1", ModelDb.Enchantment<Sharp>().Title.GetFormattedText()),
        new StringVar("Enchantment2", ModelDb.Enchantment<Nimble>().Title.GetFormattedText()),
        new StringVar("Enchantment3", ModelDb.Enchantment<Swift>().Title.GetFormattedText()),
        new IntVar("Enchantment1Amount", _sharpAmount),
        new IntVar("Enchantment2Amount", _nimbleAmount),
        new IntVar("Enchantment3Amount", _swiftAmount)
    ];

    public override void OnRoomEnter()
    {
        NEventRoom.Instance!.Layout!.AddVfxAnchoredToPortrait(NSelfHelpBookVfx.Create());
    }

    protected override IReadOnlyList<EventOption> GenerateInitialOptions()
    {
        List<EventOption> options = [];

        bool hasOption1 = PlayerHasCardsAvailable<Sharp>(Owner!, CardType.Attack);
        bool hasOption2 = PlayerHasCardsAvailable<Nimble>(Owner!, CardType.Skill);
        bool hasOption3 = PlayerHasCardsAvailable<Swift>(Owner!, CardType.Power);

        if (hasOption1 || hasOption2 || hasOption3)
        {
            if (hasOption1)
            {
                options.Add(new EventOption(ReadTheBack, "SELF_HELP_BOOK.pages.INITIAL.options.READ_THE_BACK", HoverTipFactory.FromEnchantment<Sharp>(_sharpAmount)));
            }
            else
            {
                options.Add(new EventOption(null, "SELF_HELP_BOOK.pages.INITIAL.options.READ_THE_BACK_LOCKED"));
            }

            if (hasOption2)
            {
                options.Add(new EventOption(ReadPassage, "SELF_HELP_BOOK.pages.INITIAL.options.READ_PASSAGE", HoverTipFactory.FromEnchantment<Nimble>(_nimbleAmount)));
            }
            else
            {
                options.Add(new EventOption(null, "SELF_HELP_BOOK.pages.INITIAL.options.READ_PASSAGE_LOCKED"));
            }

            if (hasOption3)
            {
                options.Add(new EventOption(ReadEntireBook, "SELF_HELP_BOOK.pages.INITIAL.options.READ_ENTIRE_BOOK", HoverTipFactory.FromEnchantment<Swift>(_swiftAmount)));
            }
            else
            {
                options.Add(new EventOption(null, "SELF_HELP_BOOK.pages.INITIAL.options.READ_ENTIRE_BOOK_LOCKED"));
            }
        }
        else
        {
            options.Add(new EventOption(SkipBook, "SELF_HELP_BOOK.pages.INITIAL.options.NO_OPTIONS"));
        }

        return options;
    }

    private async Task ReadTheBack()
    {
        await SelectAndEnchant<Sharp>(_sharpAmount, CardType.Attack, _readTheBackDescription);
    }

    private async Task ReadPassage()
    {
        await SelectAndEnchant<Nimble>(_nimbleAmount, CardType.Skill, _readPassageDescription);
    }

    private async Task ReadEntireBook()
    {
        await SelectAndEnchant<Swift>(_swiftAmount, CardType.Power, _readEntireBookDescription);
    }

    private bool PlayerHasCardsAvailable<T>(Player player, CardType typeRestriction) where T : EnchantmentModel
    {
        EnchantmentModel enchantment = ModelDb.Enchantment<T>();
        return PileType.Deck.GetPile(player).Cards.FirstOrDefault(c => DeckFilter(c, enchantment, typeRestriction)) != null;
    }

    private async Task SelectAndEnchant<T>(int amount, CardType typeRestriction, LocString finalDescription) where T : EnchantmentModel
    {
        CardSelectorPrefs prefs = new(CardSelectorPrefs.EnchantSelectionPrompt, 1);
        EnchantmentModel enchantment = ModelDb.Enchantment<T>();
        CardModel? selection = (await CardSelectCmd.FromDeckForEnchantment(Owner!, enchantment, amount, c => c!.Type == typeRestriction, prefs)).FirstOrDefault();

        if (selection != null)
        {
            await ApplyEnchantment<T>(selection, amount);
        }

        SetEventFinished(finalDescription);
    }

    private bool DeckFilter(CardModel card, EnchantmentModel enchantment, CardType type)
    {
        return card.Pile!.Type == PileType.Deck &&
            card.Type == type &&
            enchantment.CanEnchant(card);
    }

    private Task ApplyEnchantment<T>(CardModel card, int amount) where T : EnchantmentModel
    {
        CardCmd.Enchant<T>(card, amount);

        NCardEnchantVfx? vfx = NCardEnchantVfx.Create(card);
        if (vfx != null)
        {
            NClimb.Instance?.GlobalUi.CardPreviewContainer.AddChildSafely(vfx);
        }

        return Task.CompletedTask;
    }

    private Task SkipBook()
    {
        SetEventFinished(L10NLookup("SELF_HELP_BOOK.pages.NO_OPTIONS.description"));
        return Task.CompletedTask;
    }
}
