using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Potions;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Rewards;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TheDrowningBeacon : EventModel
{
    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new HpLossVar(6),
    ];

    protected override IReadOnlyList<EventOption> GenerateInitialOptions() =>
    [
        new(DrinkFromTheTidePool, "THE_DROWNING_BEACON.pages.INITIAL.options.DRINK_FROM_THE_TIDE_POOL", HoverTipFactory.FromPotion(ModelDb.Potion<GlowwaterPotion>())),
        new(GraspTheBarnacledObject, "THE_DROWNING_BEACON.pages.INITIAL.options.GRASP_THE_BARNACLED_OBJECT", HoverTipFactory.FromRelic<BarnacledPipe>().Concat(HoverTipFactory.FromCardWithCardHoverTips<Waterlogged>())),
        new(TranscribeTheDownedPsalm, "THE_DROWNING_BEACON.pages.INITIAL.options.TRANSCRIBE_THE_DROWNED_PSALM", HoverTipFactory.FromCardWithCardHoverTips<CrushingDepths>()),
    ];

    private async Task DrinkFromTheTidePool()
    {
        await CreatureCmd.Damage(Owner!.Creature, DynamicVars.HpLoss.IntValue, DamageProps.nonCardHpLoss, null, null);
        await RewardsCmd.Offer(Owner!, [new PotionReward(ModelDb.Potion<GlowwaterPotion>().ToMutable(), Owner)], false);
        SetEventFinished(L10NLookup("THE_DROWNING_BEACON.pages.DRINK_FROM_THE_TIDE_POOL.description"));
    }

    private async Task GraspTheBarnacledObject()
    {
        await RelicCmd.Obtain(ModelDb.Relic<BarnacledPipe>().ToMutable(), Owner!);
        await CardPileCmd.AddCurseToDeck<Waterlogged>(Owner!);
        SetEventFinished(L10NLookup("THE_DROWNING_BEACON.pages.GRASP_THE_BARNACLED_OBJECT.description"));
    }

    private async Task TranscribeTheDownedPsalm()
    {
        CardModel card = Owner!.ClimbState.CreateCard<CrushingDepths>(Owner);
        CardPileAddResult result = await CardPileCmd.Add(card, PileType.Deck);
        CardCmd.PreviewCardPileAdd(result, 2f);

        await RelicCmd.Obtain(ModelDb.Relic<TidesBinding>().ToMutable(), Owner);

        SetEventFinished(L10NLookup("THE_DROWNING_BEACON.pages.TRANSCRIBE_THE_DROWNED_PSALM.description"));
    }
}
