using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.PotionPools;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.Rewards;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TheLegendsWereTrue : EventModel
{
    public override bool IsAllowed(ClimbState climbState) =>
        climbState.CurrentActIndex == 0 &&
        climbState.Players.All(p => p.Deck.Cards.Count > 0);

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new DamageVar(8, DamageProps.nonCardHpLoss)
    ];

    public override IEnumerable<string> AssetPaths
    {
        get
        {
            List<string> paths = base.AssetPaths.ToList();
            paths.Add(NTheLegendsWereTrueVfx.ScenePath);
            return paths;
        }
    }

    public override void OnRoomEnter()
    {
        NEventRoom.Instance!.Layout!.AddVfxAnchoredToPortrait(NTheLegendsWereTrueVfx.Create());
    }

    protected override IReadOnlyList<EventOption> GenerateInitialOptions() =>
    [
        new(
            NabTheMap,
            "THE_LEGENDS_WERE_TRUE.pages.INITIAL.options.NAB_THE_MAP",
            HoverTipFactory.FromCardWithCardHoverTips<SpoilsMap>()
        ),
        new EventOption(
            SlowlyFindAnExit,
            "THE_LEGENDS_WERE_TRUE.pages.INITIAL.options.SLOWLY_FIND_AN_EXIT"
        ).ThatDoesDamage(DynamicVars.Damage.BaseValue)
    ];

    private async Task NabTheMap()
    {
        CardModel card = Owner!.ClimbState.CreateCard<SpoilsMap>(Owner);
        CardPileAddResult result = await CardPileCmd.Add(card, PileType.Deck);
        CardCmd.PreviewCardPileAdd(result);
        await Cmd.CustomScaledWait(0.5f, 1.2f, 1.2f);

        SetEventFinished(L10NLookup("THE_LEGENDS_WERE_TRUE.pages.NAB_THE_MAP.description"));
    }

    private async Task SlowlyFindAnExit()
    {
        await CreatureCmd.Damage(Owner!.Creature, DynamicVars.Damage, null, null);

        if (!Owner.Creature.IsDead)
        {
            IEnumerable<PotionModel> options = Owner!.Character.PotionPool.Potions
                .Concat(ModelDb.PotionPool<SharedPotionPool>().Potions);

            PotionModel? potion = Owner!.PlayerRng.Rewards.NextItem(options);

            if (potion != null)
            {
                await RewardsCmd.Offer(Owner!, [new PotionReward(potion.ToMutable(), Owner)], false);
            }
        }

        SetEventFinished(L10NLookup("THE_LEGENDS_WERE_TRUE.pages.SLOWLY_FIND_AN_EXIT.description"));
    }
}
