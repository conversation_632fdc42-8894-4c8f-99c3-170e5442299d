using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TrashHeap : EventModel
{
    private readonly RelicModel[] _relics =
    [
        ModelDb.Relic<TheBoot>(),
        ModelDb.Relic<DreamCatcher>(),
        ModelDb.Relic<DarkstonePeriapt>(),
        ModelDb.Relic<MawBank>(),
        ModelDb.Relic<HandDrill>(),
    ];

    private readonly CardModel[] _cards =
    [
        ModelDb.Card<Clash>(),
        ModelDb.Card<DualWield>(),
        ModelDb.Card<Entrench>(),
        ModelDb.Card<Outmaneuver>(),
        ModelDb.Card<Caltrops>(),
        ModelDb.Card<Distraction>(),
        ModelDb.Card<HelloWorld>(),
        ModelDb.Card<Rebound>(),
        ModelDb.Card<Stack>(),
    ];

    public override bool IsAllowed(ClimbState climbState)
    {
        // Only acts 1 and 2
        return climbState.CurrentActIndex < 2;
    }

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new HpLossVar(1),
        new GoldVar(15),
    ];

    protected override IReadOnlyList<EventOption> GenerateInitialOptions() =>
    [
        new EventOption(Grab, "TRASH_HEAP.pages.INITIAL.options.GRAB").ThatDoesDamage(DynamicVars.HpLoss.IntValue),
    ];

    private async Task Grab()
    {
        await CreatureCmd.Damage(Owner!.Creature, DynamicVars.HpLoss.IntValue, DamageProps.nonCardHpLoss, null, null);
        await PlayerCmd.GainGold(DynamicVars.Gold.IntValue, Owner);

        DynamicVars.HpLoss.BaseValue++;
        DynamicVars.Gold.BaseValue += 5;

        RelicModel[] validRelics = _relics.Where(r => Owner.GetRelicById(r.Id) == null).ToArray();

        int trashCount = _cards.Length + validRelics.Length;
        int trashIdx = Rng.NextInt(trashCount);

        if (trashIdx < validRelics.Length)
        {
            RelicModel relic = validRelics[trashIdx].ToMutable();
            await RelicCmd.Obtain(relic, Owner);
        }
        else
        {
            CardModel card = Owner.ClimbState.CreateCard(_cards[trashIdx - validRelics.Length], Owner);
            CardPileAddResult result = await CardPileCmd.Add(card, PileType.Deck);
            CardCmd.PreviewCardPileAdd(result, 2f);
        }

        SetEventState(L10NLookup($"TRASH_HEAP.pages.GRAB.description"),
        [
            new EventOption(Grab, "TRASH_HEAP.pages.INITIAL.options.GRAB").ThatDoesDamage(DynamicVars.HpLoss.IntValue),
            new EventOption(Leave, "TRASH_HEAP.pages.INITIAL.options.STOP"),
        ]);
    }

    private Task Leave()
    {
        SetEventFinished(L10NLookup("TRASH_HEAP.pages.STOP.description"));
        return Task.CompletedTask;
    }
}
