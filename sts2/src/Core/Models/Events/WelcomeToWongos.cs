using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.Factories;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Rewards;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class WelcomeToWongos : EventModel
{
    private const int _wongoPointsForBadge = 2000;

    private const string _bargainBinCostKey = "BargainBinCost";
    private const string _featuredItemCostKey = "FeaturedItemCost";
    private const string _mysteryBoxCostKey = "MysteryBoxCost";

    private const string _wongoPointAmountKey = "WongoPointAmount";
    private const string _remainingWongoPointAmountKey = "RemainingWongoPointAmount";
    private const string _totalWongoBadgeAmountKey = "TotalWongoBadgeAmount";
    private const string _randomRelicKey = "RandomRelic";

    private RelicModel? _featuredItem;

    private RelicModel? FeaturedItem
    {
        get => _featuredItem;
        set
        {
            AssertMutable();
            _featuredItem = value;
        }
    }

    public override bool IsAllowed(ClimbState climbState) => climbState.CurrentActIndex == 1;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_bargainBinCostKey, 50),
        new(_featuredItemCostKey, 100),
        new(_mysteryBoxCostKey, 250),

        new(_wongoPointAmountKey, 0),
        new(_remainingWongoPointAmountKey, 0),
        new(_totalWongoBadgeAmountKey, 0),
        new StringVar(_randomRelicKey)
    ];

    protected override IReadOnlyList<EventOption> GenerateInitialOptions()
    {
        Player player = Owner!;

        FeaturedItem = RelicFactory.PullNextRelicFromFront(player);
        ((StringVar)DynamicVars[_randomRelicKey]).StringValue = FeaturedItem.Title.GetFormattedText();

        List<EventOption> options = [];

        if (player.Gold >= DynamicVars[_bargainBinCostKey].BaseValue)
        {
            options.Add(new EventOption(BuyBargainBin, "WELCOME_TO_WONGOS.pages.INITIAL.options.BARGAIN_BIN"));
        }
        else
        {
            options.Add(new EventOption(null, "WELCOME_TO_WONGOS.pages.INITIAL.options.BARGAIN_BIN_LOCKED"));
        }

        if (player.Gold >= DynamicVars[_featuredItemCostKey].BaseValue)
        {
            options.Add(new EventOption(BuyFeaturedItem, "WELCOME_TO_WONGOS.pages.INITIAL.options.FEATURED_ITEM", FeaturedItem.HoverTips));
        }
        else
        {
            options.Add(new EventOption(null, "WELCOME_TO_WONGOS.pages.INITIAL.options.FEATURED_ITEM_LOCKED"));
        }

        if (player.Gold >= DynamicVars[_mysteryBoxCostKey].BaseValue)
        {
            options.Add(new EventOption(BuyMysteryBox, "WELCOME_TO_WONGOS.pages.INITIAL.options.MYSTERY_BOX"));
        }
        else
        {
            options.Add(new EventOption(null, "WELCOME_TO_WONGOS.pages.INITIAL.options.MYSTERY_BOX_LOCKED"));
        }

        options.Add(new EventOption(Leave, "WELCOME_TO_WONGOS.pages.INITIAL.options.LEAVE"));

        return options;
    }

    private async Task<LocString> CheckObtainWongoBadge(int pointsEarned)
    {
        int startTotalWongoPoints = SaveManager.Instance.ProgressSave.WongoPoints;
        int startWongoPoints = startTotalWongoPoints % _wongoPointsForBadge;
        int endWongoPoints = startWongoPoints + pointsEarned;
        int endTotalWongoPoints = startTotalWongoPoints + pointsEarned;

        DynamicVars[_wongoPointAmountKey].BaseValue = endWongoPoints;
        DynamicVars[_remainingWongoPointAmountKey].BaseValue = _wongoPointsForBadge - endWongoPoints;

        // ReSharper disable once PossibleLossOfFraction
        // REASON: We want to floor here because this should be a whole number of badges accumulated
        DynamicVars[_totalWongoBadgeAmountKey].BaseValue = endTotalWongoPoints / _wongoPointsForBadge;
        Owner!.ExtraFields.WongoPoints = pointsEarned;

        if (endWongoPoints > _wongoPointsForBadge)
        {
            await RelicCmd.Obtain<WongoCustomerAppreciationBadge>(Owner!);
            return L10NLookup("WELCOME_TO_WONGOS.pages.AFTER_BUY_RECEIVE_BADGE.description");
        }

        if (DynamicVars[_totalWongoBadgeAmountKey].BaseValue > 0)
        {
            return L10NLookup("WELCOME_TO_WONGOS.pages.AFTER_BUY_BADGE_COUNTER.description");
        }

        return L10NLookup("WELCOME_TO_WONGOS.pages.AFTER_BUY.description");
    }

    private async Task BuyBargainBin()
    {
        await PlayerCmd.LoseGold(DynamicVars[_bargainBinCostKey].BaseValue, Owner!);
        await RelicCmd.Obtain<WongosBargainTicket>(Owner!);

        LocString description = await CheckObtainWongoBadge(8);

        SetEventFinished(description);
    }

    private async Task BuyFeaturedItem()
    {
        await PlayerCmd.LoseGold(DynamicVars[_featuredItemCostKey].BaseValue, Owner!);
        await RelicCmd.Obtain(FeaturedItem!.ToMutable(), Owner!);

        LocString description = await CheckObtainWongoBadge(16);

        SetEventFinished(description);
    }

    private async Task BuyMysteryBox()
    {
        await PlayerCmd.LoseGold(DynamicVars[_mysteryBoxCostKey].BaseValue, Owner!);
        await RewardsCmd.Offer(
            Owner!,
            [new RelicReward(RelicRarity.Common, Owner!), new RelicReward(RelicRarity.Common, Owner!)],
            false
        );

        LocString description = await CheckObtainWongoBadge(32);

        SetEventFinished(description);
    }

    private async Task Leave()
    {
        Player player = Owner!;
        CardModel? cardToDowngrade = Rng.NextItem(player.Deck.Cards.Where(c => c.IsUpgraded));

        if (cardToDowngrade != null)
        {
            CardCmd.Downgrade(cardToDowngrade);
            CardCmd.Preview(cardToDowngrade);
            await Cmd.CustomScaledWait(0.5f, 1.2f, 1.2f);
        }

        SetEventFinished(L10NLookup("WELCOME_TO_WONGOS.pages.LEAVE.description"));
    }
}
