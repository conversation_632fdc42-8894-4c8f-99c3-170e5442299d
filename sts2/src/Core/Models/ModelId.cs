using System;
using MegaCrit.Sts2.Core.Helpers;
using System.Text.Json;

namespace MegaCrit.Sts2.Core.Models;

public record ModelId : IComparable<ModelId>
{
    public static readonly ModelId none = new("NONE", "NONE");
    private const string _bannedSuffix = "_MODEL";

    public string Category { get; }
    public string Entry { get; }

    public ModelId(string category, string entry)
    {
        if (category.EndsWith(_bannedSuffix))
        {
            throw new ArgumentException($"Category cannot end with '{_bannedSuffix}'.", nameof(category));
        }

        Category = category;
        Entry = entry;
    }

    public static ModelId Deserialize(string json)
    {
        string[] tokens = json.Split('.');
        if (tokens.Length != 2) throw new JsonException($"'{json}' does not match the expected ModelId form.");

        return new ModelId(tokens[0], tokens[1]);
    }

    public override string ToString()
    {
        return $"{Category}.{Entry}";
    }

    public int CompareTo(ModelId? other)
    {
        int categoryComparison = string.Compare(Category, other?.Category, StringComparison.Ordinal);
        if (categoryComparison != 0) return categoryComparison;

        return string.Compare(Entry, other?.Entry, StringComparison.Ordinal);
    }

    public static string SlugifyCategory<T>() => SlugifyCategory(typeof(T).Name);

    public static string SlugifyCategory(string category)
    {
        string slug = StringHelper.Slugify(category);

        if (slug.EndsWith(_bannedSuffix))
        {
            slug = slug[..^_bannedSuffix.Length];
        }

        return slug;
    }
}
