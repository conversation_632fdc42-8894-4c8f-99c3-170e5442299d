using System;
using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Factories;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Models.Modifiers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class BigGameHunter : ModifierModel
{
    public override ActMap ModifyGeneratedMap(IClimbState climbState, ActMap map, int actIndex)
    {
        Rng rng = new(climbState.Rng.Seed, $"act_{climbState.CurrentActIndex + 1}_map");

        MapPointTypeCounts pointTypeCounts = new(rng)
        {
            NumOfElites = (int)Math.Round(map.GetAllMapPoints().Count(p => p.PointType == MapPointType.Elite) * 2.5f),
            PointTypesThatIgnoreRules = [MapPointType.Elite]
        };

        return new StandardActMap(rng, climbState.Act, climbState.Players.Count > 1, pointTypeCounts);
    }

    public override bool TryModifyCardRewardOptionsLate(Player player, List<CardCreationResult> options, CardCreationSource source)
    {
        if (source != CardCreationSource.EliteEncounter) return false;

        List<CardModel> seenRares = options.Select(o => o.Card).Where(c => c.Rarity == CardRarity.Rare).ToList();
        foreach (CardCreationResult option in options)
        {
            if (option.Card.Rarity == CardRarity.Rare) continue;

            List<CardModel> rareCards = option.Card.Pool.Cards
                .Where(c => c.Rarity == CardRarity.Rare && seenRares.All(r => r.Id != c.Id))
                .ToList();

            // if the current card pool doesn't have any rare cards, fallback to the players card pool
            if (rareCards.Count <= 0)
            {
                rareCards = player.Character.CardPool.Cards.Where(c => c.Rarity == CardRarity.Rare)
                    .Where(c => c.Rarity == CardRarity.Rare && seenRares.All(r => r.Id != c.Id))
                    .ToList();
            }

            CardModel replacement = CardFactory.CreateForRewardWithoutModifications(player, rareCards, 1).First();
            seenRares.Add(replacement);
            option.ModifyCard(replacement);
        }

        return true;
    }
}
