using System;
using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Models.Modifiers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class CharacterCards : ModifierModel
{
    // Since this is dynamic based on which character is assigned, the title/description are in the character rather
    // than the modifiers loc. They're individual entries rather than one using variable substitution because that's
    // easier for localizers to deal with
    public override LocString Title => ModelDb.GetById<CharacterModel>(CharacterModel).CardsModifierTitle;
    public override LocString Description => ModelDb.GetById<CharacterModel>(CharacterModel).CardsModifierDescription;

    private ModelId? _characterModel;

    [SavedProperty]
    public ModelId CharacterModel
    {
        get => _characterModel ?? throw new InvalidOperationException("CharacterCards modifier used without CharacterModel set!");
        set
        {
            AssertMutable();
            _characterModel = value;
        }
    }

    public override IEnumerable<CardModel> ModifyMerchantCardPool(Player player, IEnumerable<CardModel> options)
    {
        // Only modify the character-related card pools at the merchant, not the colorless ones
        CardPoolModel cardPool = player.Character.CardPool;
        CardModel[] optionsArr = options.ToArray();
        if (optionsArr.Any(c => c.Pool != cardPool)) return optionsArr;

        return optionsArr.Concat(ModelDb.GetById<CharacterModel>(CharacterModel).CardPool.Cards);
    }

    public override IEnumerable<CardModel> ModifyCardRewardCardPool(Player player, IEnumerable<CardModel> options, CardCreationSource source)
    {
        if (source == CardCreationSource.Custom) return options;
        return options.Concat(ModelDb.GetById<CharacterModel>(CharacterModel).CardPool.Cards);
    }

    public override bool IsEquivalent(ModifierModel other)
    {
        return base.IsEquivalent(other) && ((CharacterCards)other)._characterModel == _characterModel;
    }
}
