using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.CardSelection;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Factories;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models.Relics;

namespace MegaCrit.Sts2.Core.Models.Modifiers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class SealedDeck : ModifierModel
{
    public override bool ClearsPlayerDeck => true;

    public override Func<Task> GenerateNeowOption(EventModel eventModel) => () => ChooseCards(eventModel.Owner!);

    private static async Task ChooseCards(Player player)
    {
        IEnumerable<CardCreationResult> options = CardFactory.CreateForReward(player, player.Character.CardPool, CardCreationSource.RegularEncounter, 30).ToList();
        options = options.OrderBy(r => r.Card.Rarity).ThenBy(r => r.Card.Title);

        CardSelectorPrefs prefs = new(new LocString("modifiers", "SEALED_DECK.selectionPrompt"), 10)
        {
            Cancelable = false,
            RequireManualConfirmation = true,
        };

        IEnumerable<CardModel> selection = await CardSelectCmd.FromSimpleGridForRewards(
            new NullPlayerChoiceContext(),
            options.ToList(),
            player,
            prefs);

        await CardPileCmd.Add(selection, PileType.Deck);

        // Disable Pandora's Box from appearing (we have no starter cards)
        foreach (Player climbPlayer in player.ClimbState.Players)
        {
            climbPlayer.RelicGrabBag.Remove<PandorasBox>();
        }

        player.ClimbState.SharedRelicGrabBag.Remove<PandorasBox>();
    }
}
