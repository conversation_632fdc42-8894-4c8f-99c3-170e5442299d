using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Axebot : MonsterModel
{
    private static readonly LocString _bootUpLine = new("monsters", "AXEBOT.moves.BOOT_UP.banter");

    private const int _bootUpBlock = 20;
    private int OneTwoDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 6, 5);
    private const int _oneTwoRepeat = 2;
    private const int _sharpenStrengthGain = 4;
    private int HammerUppercutDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 10, 8);

    private const string _hammerUppercutTrigger = "uppercut";
    private const string _sharpenTrigger = "sharpen";

    private string BuffSfx => $"event:/sfx/enemy/enemy_attacks/{Id.Entry.ToLower()}/{Id.Entry.ToLower()}_buff";

    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 42, 40);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 46, 44);

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Armor;

    private int? _stockOverrideAmount;

    public int StockAmount
    {
        get => _stockOverrideAmount ?? 2;
        set
        {
            AssertMutable();
            _stockOverrideAmount = value;
        }
    }

    public override async Task AfterAddedToRoom()
    {
        if (StockAmount > 0)
        {
            await PowerCmd.Apply<Stock>(Creature, StockAmount, null, null);
        }
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState bootUpState = new("BOOT_UP_MOVE", BootUpMove, new DefendIntent());
        MoveState oneTwoState = new("ONE_TWO_MOVE", OneTwoMove, new MultiAttackIntent(OneTwoDamage, _oneTwoRepeat));
        MoveState sharpenState = new("SHARPEN_MOVE", SharpenMove, new BuffIntent());
        MoveState hammerUppercutState = new("HAMMER_UPPERCUT_MOVE", HammerUppercutMove, new SingleAttackIntent(HammerUppercutDamage), new DebuffIntent());

        RandomBranchState randState = new("RAND_MOVE");

        randState.AddBranch(oneTwoState, 2);
        randState.AddBranch(sharpenState, MoveRepeatType.CannotRepeat);
        randState.AddBranch(hammerUppercutState, 2);

        bootUpState.FollowUpState = randState;
        oneTwoState.FollowUpState = randState;
        sharpenState.FollowUpState = randState;
        hammerUppercutState.FollowUpState = randState;

        states.Add(bootUpState);
        states.Add(oneTwoState);
        states.Add(sharpenState);
        states.Add(hammerUppercutState);
        states.Add(randState);

        // Axe bots that spawn as part of the encounter start in a random state.
        // Axe bots that are newly stocked start in boot up state.
        if (_stockOverrideAmount != null)
        {
            return new MonsterMoveStateMachine(states, bootUpState);
        }
        else
        {
            return new MonsterMoveStateMachine(states, randState);
        }
    }

    private async Task BootUpMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(BuffSfx);
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.castTrigger, 0.6f);

        TalkCmd.Play(_bootUpLine, Creature);

        await Cmd.Wait(0.25f);
        await CreatureCmd.GainBlock(Creature, _bootUpBlock, BlockProps.monsterMove, null);
    }

    private async Task OneTwoMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(OneTwoDamage, _oneTwoRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.6f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .OnlyPlayAnimOnce()
            .Execute();
    }

    private async Task SharpenMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(BuffSfx);
        await CreatureCmd.TriggerAnim(Creature, _sharpenTrigger, 0.6f);

        await PowerCmd.Apply<Strength>(Creature, _sharpenStrengthGain, Creature, null);
    }

    private async Task HammerUppercutMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(HammerUppercutDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(_hammerUppercutTrigger, 0.6f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();
        await PowerCmd.Apply<Weak>(targets, 1, Creature, null);
        await PowerCmd.Apply<Frail>(targets, 1, Creature, null);
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idleAnim = new(AnimState.idleAnim, true);
        AnimState attackAnim = new("attack");
        AnimState uppercutAnim = new("special");
        AnimState sharpenAnim = new("sharpen");

        AnimState hurtAnim = new(AnimState.hurtAnim);
        AnimState deathAnim = new(AnimState.dieAnim);

        attackAnim.NextState = idleAnim;
        uppercutAnim.NextState = idleAnim;
        sharpenAnim.NextState = idleAnim;
        hurtAnim.NextState = idleAnim;

        SpineAnimator animator = new(idleAnim, spineController);
        animator.AddAnyState(SpineAnimator.deathTrigger, deathAnim);
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtAnim);
        animator.AddAnyState(SpineAnimator.attackTrigger, attackAnim);
        animator.AddAnyState(_hammerUppercutTrigger, uppercutAnim);
        animator.AddAnyState(_sharpenTrigger, sharpenAnim);

        return animator;
    }
}
