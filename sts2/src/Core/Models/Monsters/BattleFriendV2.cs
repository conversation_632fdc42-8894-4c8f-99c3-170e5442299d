using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class BattleFriendV2 : MonsterModel
{
    public override int MinInitialHp => 250;
    public override int MaxInitialHp => 250;

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        MoveState nothingState = new("NOTHING_MOVE", _ => Task.CompletedTask);
        nothingState.FollowUpState = nothingState;

        return new MonsterMoveStateMachine([nothingState], nothingState);
    }

    public override async Task AfterAddedToRoom()
    {
        await PowerCmd.Apply<BattlewornDummyTimeLimit>(Creature, 3, null, null);
    }
}
