using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class BowlbugRock : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 46, 45);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 49, 48);

    private const string _stunTrigger = "Stun";
    private const string _unstunTrigger = "Unstun";

    private bool _isOffBalance;
    public static int HeadbuttDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 19, 17);
    public static int MomentumDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 13, 11);

    public bool IsOffBalance
    {
        get => _isOffBalance;
        set
        {
            AssertMutable();
            _isOffBalance = value;
        }
    }

    private const string _buffSfx = "event:/sfx/enemy/enemy_attacks/workbug_rock/workbug_rock_buff";
    private const string _stunSfx = "event:/sfx/enemy/enemy_attacks/workbug_rock/workbug_rock_stun";

    public override string DeathSfx => "event:/sfx/enemy/enemy_attacks/workbug_rock/workbug_rock_die";
    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Insect;

    private const string _spineSkin = "rock";

    public override void SetupSkins()
    {
        NCreature node = NCombatRoom.Instance!.GetCreatureNode(Creature)!;
        SpineSkeletonDataResource skeletonData = node.SpineController!.GetSkeleton().GetData();
        node.SpineController!.GetSkeleton().SetSkin(skeletonData.FindSkin(_spineSkin));
        node.SpineController!.GetSkeleton().SetSlotsToSetupPose();
    }

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        await PowerCmd.Apply<Imbalanced>(Creature, 1, Creature, null);
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];
        MoveState headbuttState = new("HEADBUTT_MOVE", HeadbuttMove, new SingleAttackIntent(HeadbuttDamage));
        MoveState momentumState = new("MOMENTUM_MOVE", MomentumMove, new SingleAttackIntent(MomentumDamage), new BuffIntent());

        // Can't use default CreatureCmd.Stun here because this monster is stunned after
        // performing a move.
        MoveState dizzyState = new("DIZZY_MOVE", DizzyMove, new StunIntent());
        ConditionalBranchState postHeadbuttState = new("POST_HEADBUTT");
        ConditionalBranchState postMomentumState = new("POST_MOMENTUM");

        headbuttState.FollowUpState = postHeadbuttState;
        momentumState.FollowUpState = postMomentumState;
        dizzyState.FollowUpState = headbuttState;

        postHeadbuttState.AddState(dizzyState, (_, _) => IsOffBalance);
        postHeadbuttState.AddState(momentumState, (_, _) => !IsOffBalance);

        postMomentumState.AddState(dizzyState, (_, _) => IsOffBalance);
        postMomentumState.AddState(headbuttState, (_, _) => !IsOffBalance);

        states.Add(dizzyState);
        states.Add(postHeadbuttState);
        states.Add(postMomentumState);

        states.Add(headbuttState);
        states.Add(momentumState);

        return new MonsterMoveStateMachine(states, headbuttState);
    }

    private async Task HeadbuttMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(HeadbuttDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.3f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();

        if (IsOffBalance)
        {
            SfxCmd.Play(_stunSfx);
            await CreatureCmd.TriggerAnim(Creature, _stunTrigger, 0.6f);
        }
    }

    private async Task DizzyMove(IReadOnlyList<Creature> targets)
    {
        IsOffBalance = false;
        await CreatureCmd.TriggerAnim(Creature, _unstunTrigger, 0.6f);
    }

    private async Task MomentumMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(MomentumDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.6f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();

        if (IsOffBalance)
        {
            SfxCmd.Play(_stunSfx);
            await CreatureCmd.TriggerAnim(Creature, _stunTrigger, 0.6f);
        }
        else
        {
            await PowerCmd.Apply<Strength>(Creature, 2, Creature, null);
        }
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idleAnim = new(AnimState.idleAnim, true);
        AnimState castAnim = new("buff");
        AnimState attackAnim = new("headbutt");
        AnimState hurtAnim = new(AnimState.hurtAnim);
        AnimState stunnedHurtAnim = new("hurt_stunned");

        AnimState deathAnim = new(AnimState.dieAnim);

        AnimState stunAnim = new("stun");
        AnimState stunLoop = new("stunned_loop", true);

        castAnim.NextState = idleAnim;
        hurtAnim.NextState = idleAnim;
        attackAnim.NextState = idleAnim;
        stunAnim.NextState = stunLoop;
        stunnedHurtAnim.NextState = stunLoop;

        SpineAnimator animator = new(idleAnim, spineController);

        // AddAnyState means we always check this trigger no matter what the current state we are at
        animator.AddAnyState(SpineAnimator.deathTrigger, deathAnim);
        animator.AddAnyState(SpineAnimator.castTrigger, castAnim);
        animator.AddAnyState(SpineAnimator.attackTrigger, attackAnim);
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtAnim, () => !IsOffBalance);
        animator.AddAnyState(SpineAnimator.hitTrigger, stunnedHurtAnim, () => IsOffBalance);
        animator.AddAnyState(_stunTrigger, stunAnim);
        animator.AddAnyState(_unstunTrigger, idleAnim);

        return animator;
    }
}
