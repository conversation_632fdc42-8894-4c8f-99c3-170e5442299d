using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class DampCultist : MonsterModel
{
    private static readonly LocString _cawCawDialogue = new("monsters", "DAMP_CULTIST.moves.INCANTATION.banter");

    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 46, 45);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 48, 47);

    private int DarkStrikeDamage => 1;
    private int IncantationAmount => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 6, 5);

    public override bool HasDeathSfx => false;
    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Fur;

    public override Vector2 ExtraDeathVfxPadding => new (1.5f, 1.2f);

    public override void SetupSkins()
    {
        NCreature node = NCombatRoom.Instance!.GetCreatureNode(Creature)!;
        SpineSkin compositeSkin = node.SpineController!.NewSkin("custom-skin");
        SpineSkeleton skeleton = node.SpineController.GetSkeleton();
        SpineSkeletonDataResource skeletonData = skeleton.GetData();

        compositeSkin.AddSkin(skeletonData.FindSkin("slug"));

        skeleton.SetSkin(compositeSkin);
        skeleton.SetSlotsToSetupPose();
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState incantationState = new("INCANTATION_MOVE", IncantationMove, new BuffIntent());
        MoveState darkStrikeState = new("DARK_STRIKE_MOVE", DarkStrikeMove, new SingleAttackIntent(DarkStrikeDamage));

        incantationState.FollowUpState = darkStrikeState;
        darkStrikeState.FollowUpState = darkStrikeState;

        states.Add(incantationState);
        states.Add(darkStrikeState);

        return new MonsterMoveStateMachine(states, incantationState);
    }

    private async Task IncantationMove(IReadOnlyList<Creature> targets)
    {
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.castTrigger, 0.6f);
        TalkCmd.Play(_cawCawDialogue, Creature);
        await PowerCmd.Apply<Ritual>(Creature, IncantationAmount, Creature, null);
    }

    private async Task DarkStrikeMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(DarkStrikeDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.2f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idleAnim = new(AnimState.idleAnim, true);
        AnimState castAnim = new("buff");
        AnimState attackAnim = new(AnimState.attackAnim);
        AnimState hurtAnim = new(AnimState.hurtAnim);
        AnimState deathAnim = new(AnimState.dieAnim);

        castAnim.NextState = idleAnim;
        attackAnim.NextState = idleAnim;
        hurtAnim.NextState = idleAnim;

        SpineAnimator animator = new(idleAnim, spineController);

        animator.AddAnyState(SpineAnimator.castTrigger, castAnim);
        animator.AddAnyState(SpineAnimator.attackTrigger, attackAnim);
        animator.AddAnyState(SpineAnimator.deathTrigger, deathAnim);
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtAnim);

        return animator;
    }
}
