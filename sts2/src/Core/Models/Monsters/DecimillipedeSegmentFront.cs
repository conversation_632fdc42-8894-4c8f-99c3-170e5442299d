using MegaCrit.Sts2.Core.Nodes.Animation;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
/// <summary>
/// Monster class exists to connect this to the correct monster visual scene.
/// All logic lives in the <see cref="DecimillipedeSegment"/>.
/// </summary>
public sealed class DecimillipedeSegmentFront : DecimillipedeSegment
{
    protected override void SegmentAttack()
    {
        NCreature? creatureNode = NCombatRoom.Instance?.GetCreatureNode(Creature);
        creatureNode?.GetSpecialNode<NDecimillipedeSegmentDriver>("%Visuals/SegmentDriver")?.AttackShake();
    }
}
