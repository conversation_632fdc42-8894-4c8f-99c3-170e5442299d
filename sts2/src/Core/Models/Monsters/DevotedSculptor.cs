using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class DevotedSculptor : MonsterModel
{
    private static readonly LocString _forbiddenIncantationDialogue = new("monsters", "DEVOTED_SCULPTOR.moves.FORBIDDEN_INCANTATION.banter");

    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 151, 144);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 164, 162);

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Fur;

    private readonly int _ritualGain = 9;
    private int SavageDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 15, 12);

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState forbiddenIncantationState = new("FORBIDDEN_INCANTATION_MOVE", ForbiddenIncantationMove, new BuffIntent());
        MoveState savageState = new("SAVAGE_MOVE", SavageMove, new SingleAttackIntent(SavageDamage));

        forbiddenIncantationState.FollowUpState = savageState;
        savageState.FollowUpState = savageState;

        states.Add(forbiddenIncantationState);
        states.Add(savageState);

        return new MonsterMoveStateMachine(states, forbiddenIncantationState);
    }

    private async Task ForbiddenIncantationMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(CastSfx);
        TalkCmd.Play(_forbiddenIncantationDialogue, Creature);

        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.castTrigger, 0.75f);
        await PowerCmd.Apply<Ritual>(Creature, _ritualGain, null, null);
    }

    private async Task SavageMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(SavageDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.3f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();
    }
}
