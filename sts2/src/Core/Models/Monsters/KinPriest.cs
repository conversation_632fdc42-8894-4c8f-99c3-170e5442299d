using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.Nodes.Audio;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class KinPriest : MonsterModel
{
    public const string theKinCustomTrackName = "the_kin_progress";

    private static readonly LocString[] _ritualLines =
    [
        L10NMonsterLookup("KIN_PRIEST.moves.RITUAL.speakLine1"),
        L10NMonsterLookup("KIN_PRIEST.moves.RITUAL.speakLine2"),
        L10NMonsterLookup("KIN_PRIEST.moves.RITUAL.speakLine3")
    ];

    private const string _grenadeTrigger = "AttackGrenade";
    private const string _laserTrigger = "AttackLaser";
    private const string _rallyTrigger = "Rally";
    protected override string CastSfx => "event:/sfx/enemy/enemy_attacks/the_kin_priest/the_kin_priest_cast";

    private const string _soulBeamSfx = "event:/sfx/enemy/enemy_attacks/the_kin_priest/the_kin_priest_soul_beam";
    private const string _soulGrenadeSfx = "event:/sfx/enemy/enemy_attacks/the_kin_priest/the_kin_priest_soul_grenade";
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 199, 190);
    public override int MaxInitialHp => MinInitialHp;

    private int SoulGrenadeDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 4, 3);
    private int SoulBeamDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 16, 15);

    private IEnumerator<LocString>? _ritualLineEnumerator;

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Fur;

    private bool _orbDebuffWeak;

    private bool OrbDebuffWeak
    {
        get => _orbDebuffWeak;
        set
        {
            AssertMutable();
            _orbDebuffWeak = value;
        }
    }

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        Creature.Died += AfterDeath;
    }

    private void AfterDeath(Creature _)
    {
        Creature.Died -= AfterDeath;
        NClimbMusicController.Instance?.UpdateMusicParameter(theKinCustomTrackName, 5);
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState ritualState = new("RITUAL_MOVE", RitualMove, new BuffIntent());
        MoveState soulGrenadeState = new("SOUL_GRENADE_MOVE", SoulGrenadeMove, new SingleAttackIntent(SoulGrenadeDamage), new DebuffIntent());
        MoveState soulBeamState = new("SOUL_BEAM_MOVE", SoulBeamMove, new SingleAttackIntent(SoulBeamDamage));
        ConditionalBranchState checkSummonBranch = new("CHECK_SUMMON_BRANCH");
        MoveState summonState = new("SUMMON_MOVE", SummonMove, new SummonIntent());

        ritualState.FollowUpState = soulGrenadeState;
        soulGrenadeState.FollowUpState = soulBeamState;
        soulBeamState.FollowUpState = checkSummonBranch;
        // conditional logic, checks if we summon or soul beam based on how many followers are currently alive
        checkSummonBranch.AddState(summonState, (_, _) => CheckIfSummon());
        checkSummonBranch.AddState(ritualState, (_, _) => !CheckIfSummon());
        summonState.FollowUpState = ritualState;

        states.Add(ritualState);
        states.Add(soulGrenadeState);
        states.Add(soulBeamState);
        states.Add(summonState);

        states.Add(checkSummonBranch);

        return new MonsterMoveStateMachine(states, ritualState);
    }

    private async Task RitualMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(CastSfx);
        TalkCmd.Play(NextRitualLine(), Creature, 1f);
        await CreatureCmd.TriggerAnim(Creature, _rallyTrigger, 1f);

        foreach (Creature teammate in Creature.CombatState!.GetTeammatesOf(Creature))
        {
            await PowerCmd.Apply<Ritual>(teammate, 1, Creature, null);
        }
    }

    private LocString NextRitualLine()
    {
        SfxCmd.Play(CastSfx);
        AssertMutable();

        if (_ritualLineEnumerator == null || !_ritualLineEnumerator.MoveNext())
        {
            _ritualLineEnumerator = _ritualLines.ToList().StableShuffle(Rng).GetEnumerator();
            _ritualLineEnumerator.MoveNext();
        }

        return _ritualLineEnumerator.Current;
    }

    private async Task SoulGrenadeMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(_soulGrenadeSfx);
        NCreature creatureNode = NCombatRoom.Instance!.GetCreatureNode(Creature)!;
        SpineBoneNode? bone = creatureNode.GetSpecialNode<SpineBoneNode>("Visuals/SpineBoneNode");

        if (bone != null)
        {
            bone.Position = Vector2.Right * (NCombatRoom.Instance.GetCreatureNode(targets[0])!.GlobalPosition.X - creatureNode.GlobalPosition.X) * 3f;
        }

        await CreatureCmd.TriggerAnim(Creature, _grenadeTrigger, 0);
        await Cmd.Wait(1f); // manual wait here because it looks off if the vfx hits before the animation finishes

        NDebugAudioManager.Instance?.Play(TmpSfx.bluntAttack);

        NKinPriestGrenadeVfx? vfx = NKinPriestGrenadeVfx.Create(targets[0]);
        NCombatRoom.Instance.CombatVfxContainer.AddChildSafely(vfx);

        await CreatureCmd.Damage(targets, SoulGrenadeDamage, DamageProps.monsterMove, Creature, null);
        if (!OrbDebuffWeak)
        {
            await PowerCmd.Apply<Weak>(targets, 2, Creature, null);
        }
        else
        {
            await PowerCmd.Apply<Frail>(targets, 2, Creature, null);
        }

        OrbDebuffWeak = !OrbDebuffWeak;
    }

    private async Task SummonMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(CastSfx);
        await CreatureCmd.TriggerAnim(Creature, _rallyTrigger, 1f);

        List<string> lastSlots = CombatState.Encounter!.Slots.Where(s => CombatState.Enemies.All(c => c.SlotName != s)).TakeLast(2).ToList();

        KinFollower frontMonster = (KinFollower)ModelDb.Monster<KinFollower>().ToMutable();
        frontMonster.StartWithBoomerang = true;
        await CreatureCmd.Add(frontMonster, CombatState, CombatSide.Enemy, lastSlots[0]);
        await CreatureCmd.Add<KinFollower>(CombatState, lastSlots[1]);
    }

    private async Task SoulBeamMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(_soulBeamSfx);
        await CreatureCmd.TriggerAnim(Creature, _laserTrigger, 0.4f);

        NCreature creatureNode = NCombatRoom.Instance!.GetCreatureNode(Creature)!;
        creatureNode.GetSpecialNode<NKinPriestBeamVfx>("Visuals/Beam")?.Fire();

        LocString line = L10NMonsterLookup("KIN_PRIEST.moves.SOUL_BEAM.speakLine");
        TalkCmd.Play(line, Creature);

        SfxCmd.Play(AttackSfx);
        NDebugAudioManager.Instance?.Play(TmpSfx.bluntAttack);

        VfxCmd.PlayOnCreatureCenters(targets, VfxCmd.bluntPath);
        await CreatureCmd.Damage(targets, SoulBeamDamage, DamageProps.monsterMove, Creature, null);
    }

    private bool CheckIfSummon()
    {
        int aliveCount = Creature.CombatState!.GetTeammatesOf(Creature).Count(c => c.IsAlive && c != Creature);
        if (aliveCount <= 2) return true;

        return false;
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idleAnim = new(AnimState.idleAnim, true);
        AnimState rallyAnim = new("rally");
        AnimState grenadeAnim = new("attack_grenade");
        AnimState laserAnim = new("attack_laser");
        AnimState hurtAnim = new(AnimState.hurtAnim);
        AnimState deathAnim = new(AnimState.dieAnim);

        rallyAnim.NextState = idleAnim;
        grenadeAnim.NextState = idleAnim;
        laserAnim.NextState = idleAnim;
        hurtAnim.NextState = idleAnim;

        SpineAnimator animator = new(idleAnim, spineController);

        animator.AddAnyState(_rallyTrigger, rallyAnim);
        animator.AddAnyState(_grenadeTrigger, grenadeAnim);
        animator.AddAnyState(_laserTrigger, laserAnim);

        animator.AddAnyState(SpineAnimator.deathTrigger, deathAnim);
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtAnim);

        return animator;
    }
}
