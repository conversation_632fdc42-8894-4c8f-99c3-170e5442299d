using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class KnowledgeDemon : MonsterModel
{
    private static readonly LocString[] _curseOfKnowledgeLines =
    [
        L10NMonsterLookup("KNOWLEDGE_DEMON.moves.CURSE_OF_KNOWLEDGE.speakLine1"),
        L10NMonsterLookup("KNOWLEDGE_DEMON.moves.CURSE_OF_KNOWLEDGE.speakLine2"),
    ];

    private static readonly LocString[] _ponderLines =
    [
        L10NMonsterLookup("KNOWLEDGE_DEMON.moves.KNOWLEDGE_OVERWHELMING.speakLine1"),
        L10NMonsterLookup("KNOWLEDGE_DEMON.moves.KNOWLEDGE_OVERWHELMING.speakLine2"),
    ];

    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 379, 357);

    public override int MaxInitialHp => MinInitialHp;

    private int HaveATasteDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 15, 13);

    private int SlapDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 19, 17);
    private int Slap2Damage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 21, 19);

    private int KnowledgeOverwhelmingDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 9, 8);
    private const int _knowledgeOverwhelmingRepeat = 3;

    private const int _ponderHeal = 25;
    private static readonly int _ponderStrength = AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 4, 3);

    private bool _isBurnt;

    public bool IsBurnt
    {
        get => _isBurnt;
        set
        {
            AssertMutable();
            _isBurnt = value;
        }
    }

    private const string _mindRotTrigger = "MindRotTrigger";
    private const string _lightAttackTrigger = "LightAttackTrigger";
    private const string _mediumAttackTrigger = "MediumAttackTrigger";
    private const string _heavyAttackTrigger = "HeavyAttackTrigger";
    private const string _healTrigger = "HealTrigger";

    private IEnumerator<LocString>? _curseOfKnowledgeLineEnumerator;
    private IEnumerator<LocString>? _knowledgeOverwhelmingLineEnumerator;

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = new();

        MoveState curseOfKnowledgeMove = new("CURSE_OF_KNOWLEDGE_MOVE", CurseOfKnowledge, new DebuffIntent());
        MoveState haveATasteMove = new("HAVE_A_TASTE_MOVE", HaveATasteMove, new SingleAttackIntent(HaveATasteDamage), new DebuffIntent());
        MoveState slapMove = new("SLAP_MOVE", SlapMove, new SingleAttackIntent(SlapDamage));
        MoveState slap2Move = new("SLAP2_MOVE", Slap2Move, new SingleAttackIntent(Slap2Damage));
        MoveState knowledgeOverwhelmingMove = new("KNOWLEDGE_OVERWHELMING_MOVE", KnowledgeOverwhelmingMove, new MultiAttackIntent(KnowledgeOverwhelmingDamage, _knowledgeOverwhelmingRepeat));
        MoveState ponderMove = new("PONDER_MOVE", PonderMove, new HealIntent(), new BuffIntent());

        curseOfKnowledgeMove.FollowUpState = haveATasteMove;
        haveATasteMove.FollowUpState = slapMove;
        slapMove.FollowUpState = slap2Move;
        slap2Move.FollowUpState = knowledgeOverwhelmingMove;
        knowledgeOverwhelmingMove.FollowUpState = ponderMove;
        ponderMove.FollowUpState = haveATasteMove;

        states.Add(curseOfKnowledgeMove);
        states.Add(haveATasteMove);
        states.Add(slapMove);
        states.Add(slap2Move);
        states.Add(ponderMove);
        states.Add(knowledgeOverwhelmingMove);

        return new MonsterMoveStateMachine(states, curseOfKnowledgeMove);
    }

    private async Task CurseOfKnowledge(IReadOnlyList<Creature> targets)
    {
        // TODO when A10: make the curse hit every other card
        TalkCmd.Play(NextCurseOfKnowledgeLine(), Creature);
        await CreatureCmd.TriggerAnim(Creature, _mindRotTrigger, 0.8f);

        await PowerCmd.Apply<CurseOfKnowledge>(targets, 1, Creature, null);
    }

    private async Task SlapMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(SlapDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(_mediumAttackTrigger, 0.5f)
            .WithHitFx(VfxCmd.bluntPath, null, TmpSfx.bluntAttack)
            .Execute();
    }

    private async Task Slap2Move(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(Slap2Damage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(_mediumAttackTrigger, 0.5f)
            .WithHitFx(VfxCmd.bluntPath, null, TmpSfx.bluntAttack)
            .Execute();
    }

    private async Task HaveATasteMove(IReadOnlyList<Creature> targets)
    {
        LocString line = L10NMonsterLookup("KNOWLEDGE_DEMON.moves.HAVE_A_TASTE.speakLine");
        TalkCmd.Play(line, Creature);

        await DamageCmd.Attack(HaveATasteDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(_lightAttackTrigger, 0.5f)
            .WithHitFx(VfxCmd.bluntPath, null, TmpSfx.bluntAttack)
            .Execute();

        await PowerCmd.Apply<Weak>(targets, 2, Creature, null);
    }

    private async Task PonderMove(IReadOnlyList<Creature> targets)
    {
        TalkCmd.Play(NextPonderLine(), Creature);
        await CreatureCmd.TriggerAnim(Creature, _healTrigger, 1.8f);

        IsBurnt = false;
        await CreatureCmd.Heal(Creature, _ponderHeal * Creature.CombatState!.Players.Count);
        await PowerCmd.Apply<Strength>(Creature, _ponderStrength, Creature, null);
    }

    private async Task KnowledgeOverwhelmingMove(IReadOnlyList<Creature> targets)
    {
        IsBurnt = true;
        await DamageCmd.Attack(KnowledgeOverwhelmingDamage, _knowledgeOverwhelmingRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(_heavyAttackTrigger, 0.85f)
            .OnlyPlayAnimOnce()
            .WithHitFx(VfxCmd.bluntPath, null, TmpSfx.bluntAttack)
            .Execute();
    }

    private LocString NextCurseOfKnowledgeLine()
    {
        AssertMutable();

        if (_curseOfKnowledgeLineEnumerator == null || !_curseOfKnowledgeLineEnumerator.MoveNext())
        {
            _curseOfKnowledgeLineEnumerator = _curseOfKnowledgeLines.ToList().StableShuffle(Rng).GetEnumerator();
            _curseOfKnowledgeLineEnumerator.MoveNext();
        }

        return _curseOfKnowledgeLineEnumerator.Current;
    }

    public LocString NextPonderLine()
    {
        AssertMutable();

        if (_knowledgeOverwhelmingLineEnumerator == null || !_knowledgeOverwhelmingLineEnumerator.MoveNext())
        {
            _knowledgeOverwhelmingLineEnumerator = _ponderLines.ToList().StableShuffle(Rng).GetEnumerator();
            _knowledgeOverwhelmingLineEnumerator.MoveNext();
        }

        return _knowledgeOverwhelmingLineEnumerator.Current;
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idleAnim = new(AnimState.idleAnim, true);
        AnimState attackLightAnim = new("attack_light");
        AnimState attackMediumAnim = new("attack_medium");
        AnimState attackHeavyAnim = new("attack_heavy");
        AnimState brainRotAnim = new("brain_rot");
        AnimState healAnim = new("heal");
        AnimState burntIdleAnim = new("burnt_loop", true);

        AnimState hurtAnim = new(AnimState.hurtAnim);
        AnimState deathAnim = new(AnimState.dieAnim);
        AnimState hurtBurntAnim = new("hurt_burnt");
        AnimState deathBurntAnim = new("die_burnt");

        attackLightAnim.NextState = idleAnim;
        attackMediumAnim.NextState = idleAnim;
        attackHeavyAnim.NextState = burntIdleAnim;
        brainRotAnim.NextState = idleAnim;
        healAnim.NextState = idleAnim;

        hurtAnim.NextState = idleAnim;
        hurtBurntAnim.NextState = burntIdleAnim;

        SpineAnimator animator = new(idleAnim, spineController);

        animator.AddAnyState(_lightAttackTrigger, attackLightAnim);
        animator.AddAnyState(_mediumAttackTrigger, attackMediumAnim);
        animator.AddAnyState(_heavyAttackTrigger, attackHeavyAnim);
        animator.AddAnyState(_mindRotTrigger, brainRotAnim);
        animator.AddAnyState(_healTrigger, healAnim);

        animator.AddAnyState(SpineAnimator.deathTrigger, deathAnim, () => !_isBurnt);
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtAnim, () => !_isBurnt);
        animator.AddAnyState(SpineAnimator.deathTrigger, deathBurntAnim, () => _isBurnt);
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtBurntAnim, () => _isBurnt);
        return animator;
    }
}
