using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.MonsterMoves;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class LeafSlimeS : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 12, 11);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 16, 15);

    private int TackleDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 4, 3);
    private const int _goopAmount = 1;

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Slime;

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState tackleState = new("BUTT_MOVE", TackleMove, new SingleAttackIntent(TackleDamage));
        MoveState goopState = new("GOOP_MOVE", GoopMove, new StatusIntent(_goopAmount));
        RandomBranchState randState = new("RAND");

        tackleState.FollowUpState = randState;
        goopState.FollowUpState = randState;

        randState.AddBranch(tackleState, MoveRepeatType.CannotRepeat);
        randState.AddBranch(goopState, MoveRepeatType.CannotRepeat);

        states.Add(tackleState);
        states.Add(goopState);
        states.Add(randState);

        return new MonsterMoveStateMachine(states, randState);
    }

    private async Task TackleMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(TackleDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slimeImpactVfxPath)
            .Execute();
    }

    private async Task GoopMove(IReadOnlyList<Creature> targets)
    {
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.castTrigger, 0.5f);
        SfxCmd.Play(AttackSfx);

        VfxCmd.PlayOnCreatureCenters(targets, VfxCmd.slimeImpactVfxPath);
        await CardPileCmd.AddToCombatAndPreview<Slimed>(targets, PileType.Discard, 1, false);
    }
}
