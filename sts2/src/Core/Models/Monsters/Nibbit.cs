using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Nibbit : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 44, 42);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 48, 46);

    private int ButtDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 13, 12);
    private int SliceBlock => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 6, 6);
    private int SliceDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 6, 6);

    public override string DeathSfx => "event:/sfx/enemy/enemy_attacks/nibbit/nibbit_die";
    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Slime;

    private bool _isFront;
    private bool _isAlone;

    public bool IsFront
    {
        get => _isFront;
        set
        {
            AssertMutable();
            _isFront = value;
        }
    }

    public bool IsAlone
    {
        get => _isAlone;
        set
        {
            AssertMutable();
            _isAlone = value;
        }
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState buttState = new("BUTT_MOVE", ButtMove, new SingleAttackIntent(ButtDamage));
        MoveState sliceState = new("SLICE_MOVE", SliceMove, new SingleAttackIntent(SliceDamage), new DefendIntent());
        MoveState hissState = new("HISS_MOVE", HissMove, new BuffIntent());

        ConditionalBranchState initMoveState = new("INIT_MOVE");


        if (_isAlone)
        {
            // nibbit is alone so butt
            initMoveState.AddState(buttState, (_, o) => ((Nibbit)o.Monster!).IsAlone);
        }
        else
        {
            initMoveState.AddState(hissState, (_, o) => !((Nibbit)o.Monster!).IsFront);
            initMoveState.AddState(sliceState, (_, o) => ((Nibbit)o.Monster!).IsFront);
        }

        sliceState.FollowUpState = hissState;
        buttState.FollowUpState = sliceState;
        hissState.FollowUpState = buttState;

        states.Add(initMoveState);
        states.Add(buttState);
        states.Add(sliceState);
        states.Add(hissState);

        return new MonsterMoveStateMachine(states, initMoveState);
    }

    private async Task ButtMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(ButtDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }

    private async Task SliceMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(SliceDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();

        await CreatureCmd.GainBlock(Creature, SliceBlock, BlockProps.monsterMove, null);
    }

    private async Task HissMove(IReadOnlyList<Creature> targets)
    {
        await PowerCmd.Apply<Strength>(Creature, 2, Creature, null);
    }
}
