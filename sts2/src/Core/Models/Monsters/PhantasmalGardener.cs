using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Encounters;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.Nodes.Rooms;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class PhantasmalGardener : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 29, 28);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 33, 32);

    private int BiteDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 7, 6);
    private int LashDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 9, 8);
    private int FlailDamage => 1;
    private int FlailRepeat => 3;

    private int _enlargeTriggers;

    public int EnlargeTriggers
    {
        get => _enlargeTriggers;
        set
        {
            AssertMutable();
            _enlargeTriggers = value;
        }
    }

    private int _starterMoveIdx = -1;

    public int StarterMove
    {
        get => _starterMoveIdx;
        set
        {
            AssertMutable();
            _starterMoveIdx = value;
        }
    }

    public float CurrentScale { get; private set; } = 1.0f;

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Magic;

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        await PowerCmd.Apply<Skittish>(Creature, 4, Creature, null);
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];
        MoveState biteState = new("BITE_MOVE", BiteMove, new SingleAttackIntent(BiteDamage));
        MoveState lashState = new("LASH_MOVE", LashMove, new SingleAttackIntent(LashDamage));
        MoveState enlargeState = new("ENLARGE_MOVE", EnlargeMove, new BuffIntent(), new HealIntent());
        MoveState flailState = new("FLAIL_MOVE", FlailMove, new MultiAttackIntent(FlailDamage, FlailRepeat));

        RandomBranchState rand = new("rand");

        ConditionalBranchState initMoveState = new("INIT_MOVE");
        initMoveState.AddState(flailState, (_, o) => o.SlotName == PhantasmalGardenersElite.firstSlot);
        initMoveState.AddState(biteState, (_, o) => o.SlotName == PhantasmalGardenersElite.secondSlot);
        initMoveState.AddState(lashState, (_, o) => o.SlotName == PhantasmalGardenersElite.thirdSlot);
        initMoveState.AddState(enlargeState, (_, o) => o.SlotName == PhantasmalGardenersElite.fourthSlot);

        biteState.FollowUpState = rand;
        lashState.FollowUpState = rand;
        enlargeState.FollowUpState = rand;
        flailState.FollowUpState = rand;

        rand.AddBranch(biteState, MoveRepeatType.CannotRepeat);
        rand.AddBranch(lashState, MoveRepeatType.CannotRepeat);
        rand.AddBranch(enlargeState, MoveRepeatType.CannotRepeat);
        rand.AddBranch(flailState, MoveRepeatType.CannotRepeat);

        states.Add(biteState);
        states.Add(lashState);
        states.Add(enlargeState);
        states.Add(flailState);
        states.Add(rand);

        return new MonsterMoveStateMachine(states, initMoveState);
    }

    private async Task BiteMove(IReadOnlyList<Creature> targets)
    {
        NCombatRoom.Instance?.GetCreatureNode(Creature)?.SetDefaultScaleTo(CurrentScale, 0.75f);

        await DamageCmd.Attack(BiteDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();
    }

    private async Task LashMove(IReadOnlyList<Creature> targets)
    {
        NCombatRoom.Instance?.GetCreatureNode(Creature)?.SetDefaultScaleTo(CurrentScale, 0.75f);

        await DamageCmd.Attack(LashDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();
    }

    private async Task EnlargeMove(IReadOnlyList<Creature> targets)
    {
        await PowerCmd.Apply<Strength>(Creature, 1, Creature, null);

        IReadOnlyList<Player> players = CombatState.Players;
        await CreatureCmd.GainMaxHp(
            Creature,
            Creature.ScaleHpForMultiplayer(5, CombatState.Encounter, players.Count, players[0].ClimbState.CurrentActIndex)
        );

        EnlargeTriggers++;
        CurrentScale = 1 + 0.1f * (float)Math.Log(EnlargeTriggers + 1);

        NCombatRoom.Instance?.GetCreatureNode(Creature)?.SetDefaultScaleTo(CurrentScale, 0.75f);
    }

    private async Task FlailMove(IReadOnlyList<Creature> targets)
    {
        NCombatRoom.Instance?.GetCreatureNode(Creature)?.SetDefaultScaleTo(CurrentScale, 0.75f);

        await DamageCmd.Attack(FlailDamage, FlailRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();
    }
}
