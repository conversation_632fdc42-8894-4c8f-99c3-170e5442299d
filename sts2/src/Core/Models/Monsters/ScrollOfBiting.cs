using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class ScrollOfBiting : MonsterModel
{
    private static readonly string[] _skinOptions = ["skin1", "skin2"];

    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 32, 31);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 39, 38);

    private int ChompDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 16, 14);
    private int ChewDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 7, 6);
    private const int _chewRepeat = 2;
    private const int _buffAmt = 2;
    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Magic;
    public override bool HasDeathSfx => false;

    private int _starterMoveIdx;

    public int StarterMoveIdx
    {
        get => _starterMoveIdx;
        set
        {
            AssertMutable();
            _starterMoveIdx = value;
        }
    }

    private const string _attackDoubleTrigger = "ATTACK_DOUBLE";

    public override void SetupSkins()
    {
        NCreature node = NCombatRoom.Instance!.GetCreatureNode(Creature)!;
        SpineSkin compositeSkin = node.SpineController!.NewSkin("custom-skin");
        SpineSkeleton skeleton = node.SpineController.GetSkeleton();
        SpineSkeletonDataResource skeletonData = skeleton.GetData();

        compositeSkin.AddSkin(skeletonData.FindSkin(Random.Rng.Chaotic.NextItem(_skinOptions)));

        skeleton.SetSkin(compositeSkin);
        skeleton.SetSlotsToSetupPose();
    }

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        await PowerCmd.Apply<PaperCuts>(Creature, 2, Creature, null);
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState chompState = new("CHOMP", ChompMove, new SingleAttackIntent(ChompDamage));
        MoveState chewState = new("CHEW", ChewState, new MultiAttackIntent(ChewDamage, _chewRepeat));
        MoveState moreTeethState = new("MORE_TEETH", MoreTeethMove, new BuffIntent());

        RandomBranchState randState = new("rand");

        chompState.FollowUpState = randState;
        chewState.FollowUpState = randState;
        moreTeethState.FollowUpState = randState;

        randState.AddBranch(chompState, MoveRepeatType.CannotRepeat);
        randState.AddBranch(chewState, MoveRepeatType.CannotRepeat);
        randState.AddBranch(moreTeethState, MoveRepeatType.CannotRepeat);

        states.Add(chompState);
        states.Add(chewState);
        states.Add(moreTeethState);
        states.Add(randState);

        switch (StarterMoveIdx % 3)
        {
            case 0:
                return new MonsterMoveStateMachine(states, chompState);
            case 1:
                return new MonsterMoveStateMachine(states, chewState);
            default:
                return new MonsterMoveStateMachine(states, moreTeethState);
        }
    }

    private async Task ChompMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(ChompDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.2f)
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.bitePath)
            .Execute();
    }

    private async Task ChewState(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(ChewDamage, _chewRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(_attackDoubleTrigger, 0.2f)
            .OnlyPlayAnimOnce()
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.bitePath)
            .Execute();
    }

    private async Task MoreTeethMove(IReadOnlyList<Creature> targets)
    {
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.castTrigger, 0.8f);
        await PowerCmd.Apply<Strength>(Creature, _buffAmt, Creature, null);
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idleAnim = new(AnimState.idleAnim, true);
        AnimState castAnim = new("buff");
        AnimState attackAnim = new(AnimState.attackAnim);
        AnimState attackDoubleAnim = new("attack_double");
        AnimState hurtAnim = new(AnimState.hurtAnim);
        AnimState deathAnim = new(AnimState.dieAnim);

        castAnim.NextState = idleAnim;
        attackAnim.NextState = idleAnim;
        hurtAnim.NextState = idleAnim;
        attackDoubleAnim.NextState = idleAnim;

        SpineAnimator animator = new(idleAnim, spineController);

        animator.AddAnyState(SpineAnimator.castTrigger, castAnim);
        animator.AddAnyState(SpineAnimator.attackTrigger, attackAnim);
        animator.AddAnyState(SpineAnimator.deathTrigger, deathAnim);
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtAnim);
        animator.AddAnyState(_attackDoubleTrigger, attackDoubleAnim);

        return animator;
    }
}
