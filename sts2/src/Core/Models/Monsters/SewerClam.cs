using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SewerClam : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 63, 60);
    public override int MaxInitialHp => MinInitialHp;

    private int JetDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 11, 10);

    public override bool HasDeathSfx => false;
    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Stone;

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        await PowerCmd.Apply<Plating>(Creature, 8, Creature, null);
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState pressurizeState = new("PRESSURIZE_MOVE", PressurizeMove, new BuffIntent());
        MoveState jetState = new("JET_MOVE", JetMove, new SingleAttackIntent(JetDamage));

        pressurizeState.FollowUpState = jetState;
        jetState.FollowUpState = pressurizeState;

        states.Add(pressurizeState);
        states.Add(jetState);

        return new MonsterMoveStateMachine(states, jetState);
    }

    private async Task PressurizeMove(IReadOnlyList<Creature> targets)
    {
        await PowerCmd.Apply<Strength>(Creature, 4, Creature, null);
    }

    private async Task JetMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(JetDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(tmpSfx: TmpSfx.bluntAttack)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();
    }
}
