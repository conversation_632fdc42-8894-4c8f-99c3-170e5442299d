using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SpectralKnight : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 84, 81);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 87, 83);

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Armor;

    private int SoulSlashDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 15, 13);
    private int SoulFlameDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 4, 3);
    private const int _soulFlameRepeat = 3;

    private const string _attackFlameTrigger = "AttackFlame";
    private const string _attackSwordTrigger = "AttackSword";

    private const string _hexSfx = $"event:/sfx/enemy/enemy_attacks/spectral_knight/spectral_knight_hex";
    private const string  _soulFlameSfx = $"event:/sfx/enemy/enemy_attacks/spectral_knight/spectral_knight_soul_flame";
    private const string  _soulSlashSfx = $"event:/sfx/enemy/enemy_attacks/spectral_knight/spectral_knight_soul_slash";

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState hexState = new("HEX", HexMove, new DebuffIntent());
        MoveState soulSlashState = new("SOUL_SLASH", SoulSlashMove, new SingleAttackIntent(SoulSlashDamage));
        MoveState soulFlameState = new("SOUL_FLAME", SoulFlameMove, new MultiAttackIntent(SoulFlameDamage, _soulFlameRepeat));

        RandomBranchState randState = new("RAND");

        hexState.FollowUpState = randState;
        soulSlashState.FollowUpState = randState;
        soulFlameState.FollowUpState = randState;

        randState.AddBranch(soulSlashState, 2);
        randState.AddBranch(soulFlameState, MoveRepeatType.CannotRepeat);

        states.Add(hexState);
        states.Add(soulSlashState);
        states.Add(soulFlameState);
        states.Add(randState);

        return new MonsterMoveStateMachine(states, hexState);
    }

    private async Task HexMove(IReadOnlyList<Creature> targets)
    {
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.castTrigger, 0.3f);
        SfxCmd.Play(_hexSfx);

        foreach (Creature target in targets)
        {
            await PowerCmd.Apply<Hex>(target, 2, Creature, null);
        }
    }

    private async Task SoulSlashMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(SoulSlashDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(_attackSwordTrigger, 0.25f)
            .WithAttackerFx(sfx: _soulSlashSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }

    private async Task SoulFlameMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(SoulFlameDamage, _soulFlameRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(_attackFlameTrigger, 0.25f)
            .WithAttackerFx(sfx: _soulFlameSfx)
            .Execute();
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idleAnim = new(AnimState.idleAnim, true);
        AnimState castAnim = new("debuff");
        AnimState attackSwordAnim = new("attack_sword");
        AnimState attackFlameAnim = new("attack_flame");

        AnimState hurtAnim = new(AnimState.hurtAnim);
        AnimState deathAnim = new(AnimState.dieAnim);

        castAnim.NextState = idleAnim;
        attackSwordAnim.NextState = idleAnim;
        attackFlameAnim.NextState = idleAnim;
        hurtAnim.NextState = idleAnim;

        SpineAnimator animator = new(idleAnim, spineController);

        animator.AddAnyState(SpineAnimator.castTrigger, castAnim);
        animator.AddAnyState(_attackSwordTrigger, attackSwordAnim);
        animator.AddAnyState(_attackFlameTrigger, attackFlameAnim);
        animator.AddAnyState(SpineAnimator.deathTrigger, deathAnim);
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtAnim);

        return animator;
    }
}
