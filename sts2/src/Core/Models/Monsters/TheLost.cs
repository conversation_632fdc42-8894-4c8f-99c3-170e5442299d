using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TheLost : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 103, 101);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 109, 106);

    private int EyeLasersDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 5, 4);
    private const int _eyeLasersRepeat = 2;

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Stone;
    public override bool HasDeathSfx => false;

    public override async Task AfterAddedToRoom()
    {
        await PowerCmd.Apply<PossessPower>(Creature, 1, null, null);
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = new();

        MoveState debilitatingSmogState = new("DEBILITATING_SMOG", DebilitatingSmogMove, new DebuffIntent(), new BuffIntent());
        MoveState eyeLasersState = new("EYE_LASERS", EyeLasersMove, new MultiAttackIntent(EyeLasersDamage, _eyeLasersRepeat));

        debilitatingSmogState.FollowUpState = eyeLasersState;
        eyeLasersState.FollowUpState = debilitatingSmogState;

        states.Add(debilitatingSmogState);
        states.Add(eyeLasersState);
        return new MonsterMoveStateMachine(states, debilitatingSmogState);
    }

    private async Task DebilitatingSmogMove(IReadOnlyList<Creature> targets)
    {
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.castTrigger, 0.5f);
        await PowerCmd.Apply<Strength>(targets, -2, Creature, null);
        await PowerCmd.Apply<Strength>(Creature, 2, Creature, null);
    }

    private async Task EyeLasersMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(EyeLasersDamage, _eyeLasersRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .OnlyPlayAnimOnce()
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idleAnim = new(AnimState.idleAnim, true);
        AnimState castAnim = new("debuff");
        AnimState attackAnim = new(AnimState.attackAnim);
        AnimState hurtAnim = new(AnimState.hurtAnim);
        AnimState deathAnim = new(AnimState.dieAnim);

        castAnim.NextState = idleAnim;
        attackAnim.NextState = idleAnim;
        hurtAnim.NextState = idleAnim;

        SpineAnimator animator = new(idleAnim, spineController);

        animator.AddAnyState(SpineAnimator.castTrigger, castAnim);
        animator.AddAnyState(SpineAnimator.attackTrigger, attackAnim);
        animator.AddAnyState(SpineAnimator.deathTrigger, deathAnim);
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtAnim);

        return animator;
    }
}
