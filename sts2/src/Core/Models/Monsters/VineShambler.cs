using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class VineShambler : MonsterModel
{
    private const string _vineShamblerVfxPath = "vfx/monsters/vine_shambler_vines/vine_shambler_vines_vfx";

    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 63, 60);
    public override int MaxInitialHp => MinInitialHp;

    private int GraspingVinesDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 9, 8);
    private int ThrashingVinesDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 8, 7);
    private const int _thrashRepeat = 2;
    private int ChompDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 18, 17);

    private const string _swipeTrigger = "Swipe";
    private const string _vinesTrigger = "Vines";
    private const string _chompTrigger = "Chomp";

    private const string _chomp = "event:/sfx/enemy/enemy_attacks/vine_shambler/vine_shambler_chomp";
    private const string _defensiveSwipe = "event:/sfx/enemy/enemy_attacks/vine_shambler/vine_shambler_defensive_swipe";
    private const string _graspingVines = "event:/sfx/enemy/enemy_attacks/vine_shambler/vine_shambler_grasping_vines";

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState graspingVinesState = new("GRASPING_VINES_MOVE", GraspingVinesMove, new SingleAttackIntent(GraspingVinesDamage), new CardDebuffIntent());
        MoveState thrashingVinesState = new("THRASHING_VINES_MOVE", ThrashingVinesMove, new MultiAttackIntent(ThrashingVinesDamage, _thrashRepeat));
        MoveState chompState = new("CHOMP_MOVE", ChompMove, new SingleAttackIntent(ChompDamage));

        RandomBranchState randState = new("RAND");

        graspingVinesState.FollowUpState = randState;
        thrashingVinesState.FollowUpState = randState;
        chompState.FollowUpState = randState;

        randState.AddBranch(graspingVinesState, MoveRepeatType.CannotRepeat, 1);
        randState.AddBranch(thrashingVinesState, MoveRepeatType.CannotRepeat, 1);
        randState.AddBranch(chompState, MoveRepeatType.CannotRepeat, 1);

        states.Add(graspingVinesState);
        states.Add(thrashingVinesState);
        states.Add(chompState);
        states.Add(randState);

        return new MonsterMoveStateMachine(states, thrashingVinesState);
    }

    private async Task GraspingVinesMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(GraspingVinesDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(_vinesTrigger, 0.5f)
            .WithAttackerFx(sfx: _graspingVines)
            .WithHitFx(_vineShamblerVfxPath)
            .Execute();

        await PowerCmd.Apply<Tangled>(targets, 1, Creature, null);
    }

    private async Task ThrashingVinesMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(ThrashingVinesDamage, _thrashRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .OnlyPlayAnimOnce()
            .WithAttackerAnim(_swipeTrigger, 0.4f)
            .WithAttackerFx(sfx: _defensiveSwipe)
            .WithHitFx(VfxCmd.scratchPath)
            .Execute();
    }

    private async Task ChompMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(ChompDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(_chompTrigger, 0.4f)
            .WithAttackerFx(sfx: _chomp)
            .WithHitFx(VfxCmd.bitePath)
            .Execute();
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idleAnim = new(AnimState.idleAnim, true);
        AnimState castAnim = new(AnimState.castAnim);
        AnimState attackChompAnim = new("attack_chomp");
        AnimState attackSwipeAnim = new("attack_swipe");
        AnimState attackVineAnim = new("attack_vines");

        AnimState hurtAnim = new(AnimState.hurtAnim);
        AnimState deathAnim = new(AnimState.dieAnim);

        castAnim.NextState = idleAnim;

        attackChompAnim.NextState = idleAnim;
        attackSwipeAnim.NextState = idleAnim;
        attackVineAnim.NextState = idleAnim;

        hurtAnim.NextState = idleAnim;

        SpineAnimator animator = new(idleAnim, spineController);

        animator.AddAnyState(SpineAnimator.deathTrigger, deathAnim);
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtAnim);
        animator.AddAnyState(SpineAnimator.castTrigger, castAnim);
        animator.AddAnyState(_chompTrigger, attackChompAnim);
        animator.AddAnyState(_swipeTrigger, attackSwipeAnim);
        animator.AddAnyState(_vinesTrigger, attackVineAnim);

        return animator;
    }
}
