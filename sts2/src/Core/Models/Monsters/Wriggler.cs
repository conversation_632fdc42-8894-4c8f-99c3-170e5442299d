using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Wriggler : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 18, 17);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 22, 21);

    private const string _spawnedMoveId = "SPAWNED_MOVE";
    private const string _initMoveId = "INIT_MOVE";

    private int BiteDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 7, 6);
    private const int _wriggleStrength = 2;

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Plant;

    private bool _startStunned;

    public bool StartStunned
    {
        get => _startStunned;
        set
        {
            AssertMutable();
            _startStunned = value;
        }
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState biteState = new("NASTY_BITE_MOVE", BiteMove, new SingleAttackIntent(BiteDamage));
        MoveState wriggleState = new("WRIGGLE_MOVE", WriggleMove, new BuffIntent(), new StatusIntent(1));
        MoveState spawnedState = new(_spawnedMoveId, SpawnedMove, new StunIntent());

        ConditionalBranchState initMoveState = new(_initMoveId);

        initMoveState.AddState(biteState, (_, o) => o.SlotName == "wriggler1");
        initMoveState.AddState(wriggleState, (_, o) => o.SlotName == "wriggler2");
        initMoveState.AddState(biteState, (_, o) => o.SlotName == "wriggler3");
        initMoveState.AddState(wriggleState, (_, o) => o.SlotName == "wriggler4");

        spawnedState.FollowUpState = initMoveState;
        biteState.FollowUpState = wriggleState;
        wriggleState.FollowUpState = biteState;

        states.Add(spawnedState);
        states.Add(biteState);
        states.Add(wriggleState);
        states.Add(initMoveState);

        MonsterState initialState = StartStunned ? spawnedState : initMoveState;
        return new MonsterMoveStateMachine(states, initialState);
    }

    private Task SpawnedMove(IReadOnlyList<Creature> targets)
    {
        // It's stunned, just look cute.
        return Task.CompletedTask;
    }

    private async Task BiteMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(BiteDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.75f)
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.bitePath)
            .Execute();
    }

    private async Task WriggleMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(AttackSfx);
        await CreatureCmd.TriggerAnim(Creature,SpineAnimator.attackTrigger, 0.75f);
        await CardPileCmd.AddToCombatAndPreview<Infection>(targets, PileType.Discard, 1, false);
        await PowerCmd.Apply<Strength>(Creature, _wriggleStrength, Creature, null);
    }
}
