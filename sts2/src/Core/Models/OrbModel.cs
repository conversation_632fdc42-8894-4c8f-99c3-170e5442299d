using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Orbs;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Models;

public abstract class OrbModel : AbstractModel
{
    public const string locTable = "orbs";

    public static OrbModel GetRandomOrb(Rng rng)
    {
        return ModelDb.GetById<OrbModel>(rng.NextItem(_validOrbs)!);
    }

    private static readonly ModelId[] _validOrbs =
    [
        ModelDb.GetId<LightningOrb>(),
        ModelDb.GetId<FrostOrb>(),
        ModelDb.GetId<DarkOrb>(),
        ModelDb.GetId<PlasmaOrb>(),
        ModelDb.GetId<ScrapOrb>(),
    ];

    #region Text

    public LocString Title => new(locTable, $"{Id.Entry}.title");
    public LocString Description => new(locTable, $"{Id.Entry}.description");
    public bool HasSmartDescription => LocString.Exists(locTable, SmartDescriptionLocKey);

    private string SmartDescriptionLocKey => $"{Id.Entry}.smartDescription";

    public LocString SmartDescription => HasSmartDescription ? new LocString(locTable, $"{Id.Entry}.smartDescription") : Description;

    #endregion

    #region Audio

    // TODO: replace these with fmod event paths once we get some from Clark.
    protected string PassiveSfx => $"{Id.Entry.ToLower()}_passive.ogg";
    protected string EvokeSfx => $"{Id.Entry.ToLower()}_evoke.ogg";
    public string ChannelSfx => $"{Id.Entry.ToLower()}_channel.ogg";

    #endregion

    #region HoverTips

    public static HoverTip EmptySlotHoverTipHoverTip => new(
        new LocString(locTable, "EMPTY_SLOT.title"),
        new LocString(locTable, "EMPTY_SLOT.description")
    );

    public HoverTip DumbHoverTip => new(this, Description);

    protected virtual IEnumerable<IHoverTip> ExtraHoverTips => [];

    public IEnumerable<IHoverTip> HoverTips
    {
        get
        {
            List<IHoverTip> tips = ExtraHoverTips.ToList();

            if (HasSmartDescription && IsMutable)
            {
                LocString description = SmartDescription;

                description.Add("energyPrefix", Owner.Character.CardPool.Title);

                DynamicVarSet dynamicVars = DynamicVars.Clone();
                dynamicVars.ClearPreview();
                UpdateDynamicVarPreview(dynamicVars);

                dynamicVars.AddTo(description);
                tips.Add(new HoverTip(this, description));
            }
            else
            {
                tips.Add(DumbHoverTip);
            }

            return tips;
        }
    }

    #endregion

    #region Visuals

    private string IconPath => ImageHelper.GetImagePath($"orbs/{Id.Entry.ToLower()}.png");

    private string SpritePath => SceneHelper.GetScenePath($"orbs/orb_visuals/{Id.Entry.ToLower()}");

    public CompressedTexture2D Icon => PreloadManager.Cache.GetCompressedTexture2D(IconPath);

    public Node2D CreateSprite()
    {
        Node2D ret = PreloadManager.Cache.GetScene(SpritePath).Instantiate<Node2D>();
        ret.GetNode<SpineSprite>("SpineSkeleton").GetAnimationState().SetAnimation("idle_loop");
        return ret;
    }

    #endregion

    #region Model stuff

    private OrbModel _canonicalInstance = default!;

    private OrbModel CanonicalInstance
    {
        get => IsMutable ? _canonicalInstance : this;
        set
        {
            AssertMutable();
            _canonicalInstance = value;
        }
    }

    public OrbModel ToMutable(int initialAmount = 0)
    {
        AssertCanonical();
        OrbModel clone = (OrbModel)MutableClone();
        clone.CanonicalInstance = this;
        return clone;
    }

    #endregion

    #region Gameplay

    private Player? _owner;

    public Player Owner
    {
        get
        {
            AssertMutable();
            return _owner!;
        }
        set
        {
            AssertMutable();

            // we check if value != owner here in the case that we rechannel an orb/copy of a
            // previously set orb (ie Recursion)
            if (_owner != null && value != null && value != _owner) throw new InvalidOperationException($"Card {Id.Entry} already has an owner.");

            _owner = value;
        }
    }

    /// <summary>
    /// Get the CombatState of the orb's owner.
    /// Will never be null, since orbs are combat-only.
    /// </summary>
    public CombatState CombatState => Owner.Creature.CombatState!;

    private DynamicVarSet? _dynamicVars;
    public DynamicVarSet DynamicVars => _dynamicVars ??= new DynamicVarSet(CanonicalVars);
    protected virtual IEnumerable<DynamicVar> CanonicalVars => [];

    public void Trigger() => Triggered?.Invoke();
    public event Action? Triggered;

    public virtual Task BeforeTurnEndOrbTrigger() => Task.CompletedTask;

    public virtual Task AfterTurnStartOrbTrigger() => Task.CompletedTask;

    public virtual Task Passive(Creature? target) => Task.CompletedTask;

    public virtual Task<IEnumerable<Creature>> Evoke(PlayerChoiceContext playerChoiceContext) => Task.FromResult<IEnumerable<Creature>>([]);

    protected decimal ModifyOrbValue(decimal result)
    {
        return Hook.ModifyOrbValue(Owner.Creature.CombatState!, Owner, result);
    }

    #endregion

    /// <summary>
    /// Updates the dynamic variables of this orb based on hooks (i.e. damage, block, and powers).
    /// This is so powers and relic modifications to these values can be reflected in orb descriptions.
    /// </summary>
    /// <param name="dynamicVarSet">The dynamic variables for this orb.</param>
    public void UpdateDynamicVarPreview(DynamicVarSet dynamicVarSet)
    {
        foreach (OrbVar orbVar in dynamicVarSet.Values.OfType<OrbVar>())
        {
            orbVar.PreviewValue = Hook.ModifyOrbValue(CombatState, Owner, orbVar.PreviewValue);
        }
    }

    protected override void AfterCloned()
    {
        base.AfterCloned();

        Triggered = null;
    }

    public override bool ShouldReceiveCombatHooks => true;

    public IEnumerable<string> AssetPaths =>
    [
        IconPath,
        SpritePath
    ];
}
