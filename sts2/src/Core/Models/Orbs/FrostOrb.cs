using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Orbs;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class FrostOrb : OrbModel
{
    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new OrbVar(2, OrbVar.OrbVarType.Passive),
        new OrbVar(5, OrbVar.OrbVarType.Evoke)
    ];

    public override async Task BeforeTurnEndOrbTrigger()
    {
        await Passive(null);
    }

    public override async Task Passive(Creature? target)
    {
        if (target != null)
        {
            throw new InvalidOperationException("Frost orbs cannot target creatures.");
        }

        Trigger();
        NDebugAudioManager.Instance?.Play(PassiveSfx);
        decimal block = ModifyOrbValue(DynamicVars.PassiveOrb.IntValue);
        await CreatureCmd.GainBlock(Owner.Creature, block, BlockProps.nonCardUnpowered, null);
    }

    public override async Task<IEnumerable<Creature>> Evoke(PlayerChoiceContext playerChoiceContext)
    {
        decimal block = ModifyOrbValue(DynamicVars.EvokeOrb.IntValue);
        NDebugAudioManager.Instance?.Play(EvokeSfx);
        await CreatureCmd.GainBlock(Owner.Creature, block, BlockProps.nonCardUnpowered, null);
        return [Owner.Creature];
    }
}
