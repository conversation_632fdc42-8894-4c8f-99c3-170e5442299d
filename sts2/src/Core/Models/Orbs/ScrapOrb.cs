using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;

namespace MegaCrit.Sts2.Core.Models.Orbs;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class ScrapOrb : OrbModel
{
    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new OrbVar(1, OrbVar.OrbVarType.Passive),
        new OrbVar(1, OrbVar.OrbVarType.Evoke)
    ];

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromKeyword(CardKeyword.Retain)];

    public override async Task BeforeTurnEndOrbTrigger()
    {
        await Passive(null);
    }

    public override Task Passive(Creature? target)
    {
        if (target != null)
        {
            throw new InvalidOperationException("Scrap orbs cannot target creatures.");
        }

        Trigger();
        decimal value = ModifyOrbValue(DynamicVars.PassiveOrb.BaseValue);
        List<CardModel> cards = PileType.Hand.GetPile(Owner).Cards
            .Where(RetainFilter)
            .ToList()
            .StableShuffle(Owner.ClimbState.Rng.CombatCardSelection)
            .Take((int)value).ToList();

        foreach (CardModel card in cards)
        {
            card.GiveSingleTurnRetain();
        }
        return Task.CompletedTask;
    }

    public override async Task<IEnumerable<Creature>> Evoke(PlayerChoiceContext playerChoiceContext)
    {
        decimal value = ModifyOrbValue(DynamicVars.EvokeOrb.BaseValue);
        await CardPileCmd.Draw(playerChoiceContext, value, Owner);

        return [Owner.Creature];
    }

    // If a card is already being retained, don't include it in the selection.
    private bool RetainFilter(CardModel card) => !card.ShouldRetainThisTurn;
}
