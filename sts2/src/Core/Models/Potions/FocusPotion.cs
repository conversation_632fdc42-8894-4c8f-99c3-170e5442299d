using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Potions;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Powers;

namespace MegaCrit.Sts2.Core.Models.Potions;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class FocusPotion : PotionModel
{
    private const string _focusKey = "Focus";

    public override PotionRarity Rarity => PotionRarity.Common;
    public override PotionUsage Usage => PotionUsage.CombatOnly;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_focusKey, 2)
    ];

    public override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromPower<Focus>()];

    protected override async Task OnUse(PlayerChoiceContext choiceContext, Creature? target)
    {
        await PowerCmd.Apply<Focus>(Owner.Creature, DynamicVars[_focusKey].BaseValue, Owner.Creature, null);
    }
}
