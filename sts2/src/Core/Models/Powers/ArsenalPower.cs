using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class ArsenalPower : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;
    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromPower<Strength>()];

    public override async Task AfterCardPlayed(PlayerChoiceContext context, CardModel card, Creature? target, int playCount)
    {
        if (card.Owner != Owner.Player) return;
        if (!card.Pool.IsColorless) return;

        Flash();
        await PowerCmd.Apply<Strength>(Owner, Amount, Owner, null);
    }
}
