using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class AutomationPower : PowerModel
{
    private const int _baseCardsLeft = 10;
    private const string _baseCardsKey = "BaseCards";

    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;
    public override int DisplayAmount => GetInternalData<Data>().cardsLeft;

    public override bool IsInstanced => true;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_baseCardsKey, _baseCardsLeft)
    ];

    private class Data
    {
        public int cardsLeft = _baseCardsLeft;
    }

    protected override object InitInternalData() => new Data();

    public override async Task AfterCardDrawn(PlayerChoiceContext choiceContext, CardModel card, bool fromHandDraw)
    {
        if (card.Owner != Owner.Player) return;
        Data data = GetInternalData<Data>();

        data.cardsLeft--;
        InvokeDisplayAmountChanged();

        if (data.cardsLeft <= 0)
        {
            Flash();
            await Cmd.Wait(0.5f);
            await PlayerCmd.GainEnergy(Amount, Owner.Player!);
            data.cardsLeft = _baseCardsLeft;
            InvokeDisplayAmountChanged();
        }
    }
}