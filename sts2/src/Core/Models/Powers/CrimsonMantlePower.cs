using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class CrimsonMantlePower : PowerModel
{
    private const string _selfDamageKey = "SelfDamage";

    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.Static(StaticHoverTip.Block)];

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        // This is cardHpLoss because it's triggered by the Combust card and we want it to trigger Rupture.
        new DamageVar(_selfDamageKey, 0, DamageProps.cardHpLoss)
    ];

    public override async Task AfterSideTurnStart(CombatSide side, CombatState combatState)
    {
        if (side != CombatSide.Player) return;

        Flash();

        DamageVar selfDamageVar = (DamageVar)DynamicVars[_selfDamageKey];
        await CreatureCmd.Damage(Owner, selfDamageVar.BaseValue, selfDamageVar.Props, Owner, null);
        await CreatureCmd.GainBlock(Owner, Amount, BlockProps.nonCardUnpowered, null);
    }

    public void IncrementSelfDamage()
    {
        AssertMutable();
        DynamicVars[_selfDamageKey].BaseValue++;
    }
}
