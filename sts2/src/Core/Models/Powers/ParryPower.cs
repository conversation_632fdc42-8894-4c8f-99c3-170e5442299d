using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

public class ParryPower : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;
    
    // This is called directly by Sovereign blade. If there are other cards that end up needing this.
    // then this should become a proper
    public async Task AfterSovereignBladePlayed(Creature? dealer, IEnumerable<DamageResult> damageResults)
    {
        if (dealer == null) return;
        if (dealer != Owner) return;

        Flash();
        await CreatureCmd.GainBlock(dealer, Amount, BlockProps.cardUnpowered, null);
    }
}