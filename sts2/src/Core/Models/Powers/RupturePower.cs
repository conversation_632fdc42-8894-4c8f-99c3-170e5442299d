using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class RupturePower : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromPower<Strength>()];

    private class Data
    {
        public readonly Dictionary<CardModel, int> playedCards = [];
    }

    protected override object InitInternalData() => new Data();

    public override Task BeforeCardPlayed(CardModel card, Creature? target, int playCount, PileType resultPile)
    {
        if (card.Owner.Creature != Owner) return Task.CompletedTask;
        GetInternalData<Data>().playedCards.Add(card, 0);
        return Task.CompletedTask;
    }

    public override async Task AfterDamageReceived(Creature target, DamageResult result, ValueProp props, Creature? dealer, CardModel? cardSource)
    {
        if (target != Owner) return;
        if (props != DamageProps.cardHpLoss && cardSource == null) return;
        if (result.UnblockedDamage <= 0) return;

        if (cardSource == null || !GetInternalData<Data>().playedCards.ContainsKey(cardSource))
        {
            // if damage isn't coming from a card OR card is not being played then just apply the strength directly
            await PowerCmd.Apply<Strength>(Owner, Amount, Owner, null);
        }
        else
        {
            // wait till card is finished before applying the strength
            GetInternalData<Data>().playedCards[cardSource] += Amount;
        }
    }

    public override async Task AfterCardPlayed(PlayerChoiceContext context, CardModel card, Creature? target, int playCount)
    {
        if (card.Owner.Creature != Owner) return;
        if (!GetInternalData<Data>().playedCards.Remove(card, out int strength)) return;

        await PowerCmd.Apply<Strength>(Owner, strength, Owner, null);
    }
}
