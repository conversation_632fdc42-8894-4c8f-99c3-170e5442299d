using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Slumber : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;

    public override async Task AfterDamageReceived(Creature target, DamageResult result, ValueProp props, Creature? dealer, CardModel? cardSource)
    {
        if (target != Owner) return;
        if (result.UnblockedDamage == 0) return;

        await PowerCmd.Decrement(this);

        if (Amount <= 0)
        {
            await CreatureCmd.Stun(Owner, WakeUpMove, SlumberingBeetle.rolloutMoveId);
        }
    }

    private async Task WakeUpMove(IReadOnlyList<Creature> _)
    {
        // SfxCmd.StopLoop(SlumberingBeetle.sleepLoop);
        SfxCmd.Play(SlumberingBeetle.wakeUp);
        ((SlumberingBeetle)Owner.Monster!).IsAwake = true;
        await CreatureCmd.TriggerAnim(Owner, SlumberingBeetle.wakeUpTrigger, 0.6f);
    }

    public override Task AfterRemoved(Creature oldOwner)
    {
        // SfxCmd.StopLoop(SlumberingBeetle.sleepLoop);
        return Task.CompletedTask;
    }

    public override async Task AfterTurnEnd(PlayerChoiceContext choiceContext, CombatSide side)
    {
        if (side != Owner.Side) return;
        await PowerCmd.Decrement(this);

        if (Amount <= 0)
        {
            await WakeUpMove([]);
        }
    }
}
