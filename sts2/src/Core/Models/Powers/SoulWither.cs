using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SoulWither : PowerModel
{
    public const int threshold = 7;

    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;
    public override bool IsInstanced => true;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new DamageVar(999, DamageProps.nonCardUnpowered)
    ];

    public override async Task AfterDamageGiven(Creature? dealer, DamageResult result, ValueProp props, Creature target, CardModel? cardSource)
    {
        if (dealer != Owner) return;
        if (target != Target) return;
        if (!props.IsPoweredAttack()) return;

        // Don't count blocked damage.
        if (result.UnblockedDamage <= 0) return;

        // Don't count damage that hits a non-Player creature (such as Osty).
        if (!target.IsPlayer) return;

        if (Amount > 1)
        {
            await PowerCmd.Decrement(this);
        }
        else
        {
            await Cmd.CustomScaledWait(0.5f, 0.7f);
            await CreatureCmd.Damage(Target, DynamicVars.Damage, Owner, null);
            await PowerCmd.ModifyAmount(this, threshold - Amount, Applier, null);
        }
    }
}
