using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.CardSelection;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class StratagemPower : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;

    public override async Task AfterShuffle(PlayerChoiceContext choiceContext, Player player)
    {
        if (player != Owner.Player) return;

        Flash();

        IEnumerable<CardModel> cards = await CardSelectCmd.FromSimpleGrid(
            choiceContext,
            PileType.Draw.GetPile(Owner.Player).Cards.OrderBy(c => c.Rarity).ThenBy(c => c.Id).ToList(),
            Owner.Player,
            new CardSelectorPrefs(SelectionScreenPrompt, Amount)
        );

        foreach (CardModel card in cards)
        {
            await CardPileCmd.Add(card, PileType.Hand);
        }
    }
}
