using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models.Encounters;
using MegaCrit.Sts2.Core.Models.Monsters;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Surprise : PowerModel
{
    private static readonly LocString _banterLine = new("monsters", "FAT_GREMLIN.moves.FLEE.banter");

    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Single;

    public override async Task AfterDeath(PlayerChoiceContext choiceContext, Creature target, bool wasRemovalPrevented, float deathAnimLength)
    {
        if (wasRemovalPrevented) return;
        if (Owner != target) return;

        await CreatureCmd.Add<SneakyGremlin>(CombatState, GremlinMercNormal.sneakySlot);
        Creature fatGremlin = await CreatureCmd.Add<FatGremlin>(CombatState, GremlinMercNormal.fatSlot);

        // Transfer thievery value to heist
        foreach (Thievery thievery in Owner.GetPowerInstances<Thievery>())
        {
            Heist heist = (Heist)ModelDb.Power<Heist>().ToMutable();
            heist.Target = thievery.Target;
            await PowerCmd.Apply(heist, fatGremlin, thievery.DynamicVars.Gold.IntValue, Owner, null);
        }

        // Allow some time for the Fat Gremlin to spawn before he banters.
        await Cmd.Wait(0.75f);
        TalkCmd.Play(_banterLine, fatGremlin);
    }

    public override bool ShouldStopCombatFromEnding() => true;
}
