using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Surrounded : PowerModel
{
    // The direction that a player is facing.
    public enum Direction { Right, Left }

    public override PowerType Type => PowerType.Debuff;
    public override PowerStackType StackType => PowerStackType.Single;

    public override bool IsNegatable => false;

    private Direction _facing;

    public Direction Facing
    {
        get => _facing;
        private set
        {
            AssertMutable();
            _facing = value;
        }
    }

    public override decimal ModifyDamageReceived(Creature target, decimal amount, ValueProp props, Creature? dealer, CardModel? cardSource)
    {
        if (dealer == null) return amount;
        if (target != Owner) return amount;

        return Facing switch
        {
            // When facing right, only take extra damage if the creature is attacking from the left.
            Direction.Right when !dealer.HasPower<BackAttackLeft>() => amount,

            // When facing left, only take extra damage if the creature is attacking from the right.
            Direction.Left when !dealer.HasPower<BackAttackRight>() => amount,

            _ => amount * 1.5m
        };
    }

    public override async Task BeforeCardPlayed(CardModel card, Creature? target, int playCount, PileType resultPile)
    {
        if (target == null) return;
        if (card.Owner != Owner.Player) return;

        await UpdateDirection(target);
    }

    public override async Task BeforePotionUsed(PotionModel potion, Creature? target)
    {
        if (!CombatManager.Instance.IsInProgress) return;
        if (target == null) return;
        if (potion.Owner != Owner.Player) return;

        await UpdateDirection(target);
    }

    public override async Task AfterDeath(PlayerChoiceContext choiceContext, Creature creature, bool wasRemovalPrevented, float deathAnimLength)
    {
        if (wasRemovalPrevented) return;
        if (creature.Side == Owner.Side) return;

        IReadOnlyList<Creature> remainingEnemies = Owner.CombatState!.HittableEnemies;
        if (remainingEnemies.Count == 0) return;

        // If all the remaining enemies are on the same side, automatically face that side.
        if (remainingEnemies.All(e => e.HasPower<BackAttackLeft>()) ||
            remainingEnemies.All(e => e.HasPower<BackAttackRight>()))
        {
            await UpdateDirection(remainingEnemies[0]);
        }
    }

    private async Task UpdateDirection(Creature target)
    {
        switch (Facing)
        {
            case Direction.Right:
                // If we're facing right and the target is on the left, flip directions.
                if (target.HasPower<BackAttackLeft>())
                {
                    await FaceDirection(Direction.Left);
                }

                break;
            case Direction.Left:
                // If we're facing left and the target is on the right, flip directions.
                if (target.HasPower<BackAttackRight>())
                {
                    await FaceDirection(Direction.Right);
                }

                break;
        }
    }

    private Task FaceDirection(Direction direction)
    {
        Facing = direction;

        Node2D? body = NCombatRoom.Instance?.GetCreatureNode(Owner)?.Body;

        // TODO: Animate scale flip
        if (body != null)
        {
            float scaleX = body.Scale.X;

            // If the creature is facing the wrong way, flip the X scale.
            if ((Facing == Direction.Right && scaleX < 0) || (Facing == Direction.Left && scaleX > 0))
            {
                body.Scale *= new Vector2(-1f, 1f);
            }
        }

        return Task.CompletedTask;
    }
}
