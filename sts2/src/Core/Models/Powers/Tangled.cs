using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Afflictions;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Tangled : PowerModel
{
    public override PowerType Type => PowerType.Debuff;
    public override PowerStackType StackType => PowerStackType.Counter;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new EnergyVar(1) // amount to increase the energy by
    ];

    public override async Task AfterApplied()
    {
        IEnumerable<CardModel> attackCards = Owner.Player!.PlayerCombatState!.AllCards.Where(c => c.Type == CardType.Attack);
        foreach (CardModel attackCard in attackCards)
        {
            await CardCmd.Afflict<Entangled>(attackCard, 1);
        }
    }

    public override async Task AfterCardEnteredCombat(CardModel card)
    {
        if (card.Owner != Owner.Player) return;
        if (card.Affliction != null) return;
        if (card.Type != CardType.Attack) return;

        await CardCmd.Afflict<Entangled>(card, 1);
    }

    public override async Task AfterTurnEnd(PlayerChoiceContext choiceContext, CombatSide side)
    {
        if (side != Owner.Side) return;

        Flash();
        await PowerCmd.Remove(this);
    }

    public override Task AfterRemoved(Creature oldOwner)
    {
        IEnumerable<CardModel> attackCards = oldOwner.Player!.PlayerCombatState!.AllCards.Where(c => c.Affliction is Entangled);
        foreach (CardModel attackCard in attackCards)
        {
            CardCmd.ClearAffliction(attackCard);
        }
        return Task.CompletedTask;
    }
}
