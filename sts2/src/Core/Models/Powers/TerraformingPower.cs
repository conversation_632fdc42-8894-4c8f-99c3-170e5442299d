using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TerraformingPower : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;

    private class Data
    {
        public readonly Dictionary<CardModel, int> damageIncreasePerCard = new();
    }

    protected override object InitInternalData() => new Data();

    public override decimal ModifyDamageGiven(Creature? dealer, decimal amount, ValueProp props, Creature? _, CardModel? card)
    {
        if (Owner != dealer) return amount;
        if (!props.IsPoweredAttack()) return amount;
        if (card == null) return amount;
        if (!GetInternalData<Data>().damageIncreasePerCard.ContainsKey(card)) return amount;

        return amount + GetInternalData<Data>().damageIncreasePerCard[card];
    }

    public override Task AfterTurnEnd(PlayerChoiceContext choiceContext, CombatSide side)
    {
        if (side != Owner.Side) return Task.CompletedTask;

        IEnumerable<CardModel> attacks = CardPile.GetCards(Owner.Player!, PileType.Draw);
        foreach (CardModel card in attacks)
        {
            GetInternalData<Data>().damageIncreasePerCard.TryAdd(card, 0);
            GetInternalData<Data>().damageIncreasePerCard[card] += Amount;
        }

        Flash();
        return Task.CompletedTask;
    }
}
