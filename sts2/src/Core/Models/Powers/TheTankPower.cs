using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TheTankPower : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;

    public override async Task AfterApplied()
    {
        IEnumerable<Creature> teammates = CombatState.GetTeammatesOf(Owner)
            .Where(c => c is { IsAlive: true, IsPlayer: true } && !Equals(c.Player!.Creature, Owner));

        foreach (Creature c in teammates)
        {
            await PowerCmd.Apply<Guarded>(c, Amount, Owner, null);
        }
    }

    public override decimal ModifyDamageReceived(Creature target, decimal amount, ValueProp props, Creature? dealer, CardModel? cardSource)
    {
        if (target != Owner) return amount;
        if (!props.IsPoweredAttack()) return amount;

        return amount * Amount;
    }
}
