using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Models.Orbs;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class ThunderPower : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;

    protected override IEnumerable<IHoverTip> ExtraHoverTips =>
    [
        HoverTipFactory.Static(StaticHoverTip.Evoke),
        HoverTipFactory.FromOrb<LightningOrb>()
    ];

    public override async Task AfterOrbEvoked(OrbModel orb, IEnumerable<Creature> targets)
    {
        if (orb.Owner != Owner.Player) return;
        if (orb is not LightningOrb) return;

        Flash();

        SfxCmd.Play(TmpSfx.slashAttack);
        VfxCmd.PlayOnCreatureCenters(targets.Where(c => c.IsAlive), VfxCmd.slashPath);
        await CreatureCmd.TriggerAnim(orb.Owner.Creature, SpineAnimator.attackTrigger, Owner.Player.Character.AttackAnimDelay);

        await CreatureCmd.Damage(targets.Where(c => c.IsAlive), Amount, DamageProps.nonCardUnpowered, Owner, null);
    }
}
