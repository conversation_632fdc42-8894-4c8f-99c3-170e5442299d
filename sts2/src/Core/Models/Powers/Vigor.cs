using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Vigor : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;

    private class Data
    {
        public CardModel? cardToModify;
        public int amountWhenCardPlayStarted;
    }

    protected override object InitInternalData() => new Data();

    public override decimal ModifyDamageGiven(Creature? dealer, decimal amount, ValueProp props, Creature? _, CardModel? __)
    {
        if (Owner != dealer) return amount;
        if (!props.IsPoweredAttack()) return amount;
        return amount + Amount;
    }

    public override async Task AfterDamageGiven(Creature? dealer, DamageResult _, ValueProp props, Creature target, CardModel? cardSource)
    {
        if (dealer != Owner) return;
        if (!props.IsPoweredAttack()) return;

        // if vigor is coming from a card, make sure the card finishes playing before clearing vigor
        if (cardSource != null)
        {
            if (cardSource is not { Type: CardType.Attack }) return;

            Data data = GetInternalData<Data>();

            // "Capture" the card whose damage we want to modify right when it starts being played. This handles a
            // couple tricky situations:
            //
            // 1. Avoids immediate removal when an attack gives you Vigor (like Drain Life).
            // 2. Avoids applying to multiple attacks when one attack triggers another.
            //
            // This solution is kind of brittle, because we're using multiple hooks to mimic the idea of a power
            // "attaching itself" to a card. If we run into any tricky bugs with this in the future, we should probably
            // add attached powers as a first-class concept to CardModel.
            data.cardToModify ??= cardSource;

            // Also capture the amount of Vigor that will be applied to the card. This way, if the card itself applies
            // more Vigor (like if you play Drain Life while you already have Vigor), the new Vigor won't be removed
            // along with the old Vigor.
            data.amountWhenCardPlayStarted = Amount;
        }
        else
        {
            // if the vigor is attached to a monster, we just remove it right away.
            // NOTE: I think this ends up breaking for a monster multi-attack, but so far we don't have a case
            // for that. We will have to revisit this though if we do end up with a monster with that design.
            await PowerCmd.Remove(this);
        }
    }

    public override async Task AfterCardPlayed(PlayerChoiceContext context, CardModel card, Creature? target, int playCount)
    {
        Data data = GetInternalData<Data>();
        if (card != data.cardToModify) return;

        await PowerCmd.ModifyAmount(this, -data.amountWhenCardPlayStarted, null, null);
    }
}
