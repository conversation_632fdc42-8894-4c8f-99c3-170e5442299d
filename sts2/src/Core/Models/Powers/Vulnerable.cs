using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Vulnerable : PowerModel
{
    private const string _damageIncrease = "DamageIncrease";

    public override PowerType Type => PowerType.Debuff;
    public override PowerStackType StackType => PowerStackType.Counter;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_damageIncrease, 1.5m)
    ];

    public override bool ShouldPreviewWhenSharedByAllEnemies => true;

    public override decimal ModifyDamageReceived(Creature target, decimal amount, ValueProp props, Creature? dealer, CardModel? cardSource)
    {
        if (target != Owner) return amount;
        if (!props.IsPoweredAttack()) return amount;

        decimal vulnIncrease = DynamicVars[_damageIncrease].BaseValue;

        if (dealer != null)
        {
            if (dealer.Player?.GetRelic<PaperPhrog>() != null)
            {
                vulnIncrease = dealer.Player!.GetRelic<PaperPhrog>()!.ModifyVulnerableMultiplier(target, vulnIncrease, props, dealer, cardSource);
            }

            if (dealer.HasPower<CrueltyPower>())
            {
                vulnIncrease = dealer.GetPower<CrueltyPower>()!.ModifyVulnerableMultiplier(target, vulnIncrease, props, dealer, cardSource);
            }
        }

        if (target.HasPower<EnfeeblePower>())
        {
            vulnIncrease = target.GetPower<EnfeeblePower>()!.ModifyVulnerableMultiplier(target, vulnIncrease, props, dealer, cardSource);
        }

        return amount * vulnIncrease;
    }

    public override async Task AfterTurnEnd(PlayerChoiceContext choiceContext, CombatSide side)
    {
        // Vulnerable always ticks down at the end of the *enemy* turn, regardless of which side has it.
        // This gives the player the chance to hit Vulnerable enemies, but prevents the player from keeping their own
        // Vulnerable for longer than they should.
        if (side != CombatSide.Enemy) return;

        await PowerCmd.TickDownDuration(this);
    }
}
