using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Weak : PowerModel
{
    private const string _damageDecrease = "DamageDecrease";

    public override PowerType Type => PowerType.Debuff;
    public override PowerStackType StackType => PowerStackType.Counter;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_damageDecrease, 0.75m)
    ];

    public override decimal ModifyDamageGivenLate(Creature? dealer, decimal amount, ValueProp props, Creature? target, CardModel? cardSource)
    {
        if (dealer != Owner) return amount;
        if (!props.IsPoweredAttack()) return amount;
        decimal weakDecrease = DynamicVars[_damageDecrease].BaseValue;

        if (target?.Player?.GetRelic<PaperKrane>() != null)
        {
            weakDecrease = target.Player!.GetRelic<PaperKrane>()!.ModifyWeakMultiplier(target, weakDecrease, props, dealer, cardSource);
        }

        if (dealer.HasPower<EnfeeblePower>())
        {
            weakDecrease = dealer.GetPower<EnfeeblePower>()!.ModifyWeakMultiplier(dealer, weakDecrease, props, dealer, cardSource);
        }

        return amount * weakDecrease;
    }

    public override async Task AfterTurnEnd(PlayerChoiceContext choiceContext, CombatSide side)
    {
        // Weak always ticks down at the end of the *enemy* turn, regardless of which side has it.
        // This gives the player the chance to receive reduced-damage hits from Weak enemies, but prevents the player
        // from keeping their own Weak for longer than they should.
        if (side != CombatSide.Enemy) return;

        await PowerCmd.TickDownDuration(this);
    }
}
