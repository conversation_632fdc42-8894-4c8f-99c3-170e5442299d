using MegaCrit.Sts2.Core.Models.CardPools;
using MegaCrit.Sts2.Core.Models.Relics;

namespace MegaCrit.Sts2.Core.Models.RelicPools;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class FallbackRelicPool : RelicPoolModel
{
    public override string EnergyColorName => ColorlessCardPool.energyColorName;

    protected override RelicModel[] GenerateRelics() =>
    [
        ModelDb.Relic<Circlet>()
    ];
}
