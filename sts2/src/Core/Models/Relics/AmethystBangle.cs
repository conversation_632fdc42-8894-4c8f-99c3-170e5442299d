using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Models.CardPools;

namespace MegaCrit.Sts2.Core.Models.Relics;

public sealed class AmethystBangle : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Shop;

    public override IEnumerable<CardModel> ModifyCardRewardCardPool(Player player, IEnumerable<CardModel> options, CardCreationSource source)
    {
        if (Owner != player) return options;
        if (source == CardCreationSource.Custom) return options;

        List<CardModel> newOptions = options.ToList();
        CardPoolModel colorlessCardPool = ModelDb.CardPool<ColorlessCardPool>();

        foreach (CardModel cardModel in colorlessCardPool.Cards)
        {
            // make sure we don't double add colorless cards if they are there already for some reason
            if (!newOptions.Contains(cardModel))
            {
                newOptions.Add(cardModel);
            }
        }

        return newOptions;
    }
}
