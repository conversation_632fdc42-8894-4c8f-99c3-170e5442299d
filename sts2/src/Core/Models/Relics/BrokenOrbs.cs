using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class BrokenOrbs : RelicModel
{
    private const string _usesKey = "Uses";
    public override RelicRarity Rarity => RelicRarity.Event;

    public override bool IsUsedUp => TimesUsed >= DynamicVars[_usesKey].IntValue;

    public override bool ShowCounter => true;
    public override int DisplayAmount => DynamicVars[_usesKey].IntValue - TimesUsed;
    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new EnergyVar(1),
        new (_usesKey, 3)
    ];

    private int _timesUsed;

    [SavedProperty]
    public int TimesUsed
    {
        get => _timesUsed;
        set
        {
            AssertMutable();
            _timesUsed = value;
            InvokeDisplayAmountChanged();

            if (IsUsedUp)
            {
                Status = RelicStatus.Disabled;
            }
        }
    }

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.ForEnergy(this)];

    public override async Task AfterSideTurnStart(CombatSide side, CombatState combatState)
    {
        if (side != Owner.Creature.Side) return;
        if (combatState.RoundNumber > 1) return;
        if (IsUsedUp) return;

        Flash();
        TimesUsed++;
        await PlayerCmd.GainEnergy(DynamicVars.Energy.BaseValue, Owner);
    }
}
