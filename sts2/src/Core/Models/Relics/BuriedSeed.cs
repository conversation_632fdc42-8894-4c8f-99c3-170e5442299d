using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class BuriedSeed : RelicModel
{
    private const string _combatsKey = "Combats";
    private const int _maxCombats = 8;

    public override RelicRarity Rarity => RelicRarity.Event;

    public override bool IsUsedUp => CombatsEntered >= _maxCombats;
    public override bool ShowCounter => true;
    public override int DisplayAmount => _maxCombats - CombatsEntered;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new EnergyVar(1),
        new(_combatsKey, _maxCombats)
    ];

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.ForEnergy(this)];

    private int _combatsEntered;

    [SavedProperty]
    public int CombatsEntered
    {
        get => _combatsEntered;
        set
        {
            AssertMutable();
            _combatsEntered = value;
            InvokeDisplayAmountChanged();

            if (IsUsedUp)
            {
                Status = RelicStatus.Disabled;
            }
        }
    }

    public override async Task AfterSideTurnStart(CombatSide side, CombatState combatState)
    {
        if (IsUsedUp) return;
        if (side != Owner.Creature.Side) return;

        // Only trigger on round 1.
        if (combatState.RoundNumber > 1) return;

        await PlayerCmd.GainEnergy(DynamicVars.Energy.BaseValue, Owner);

        CombatsEntered++;
        Flash();
    }
}
