using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Climbs.History;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Factories;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.CardPools;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class ClericsHeadpiece : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Ancient;

    public override async Task AfterObtained()
    {
        List<CardModel> options =
            CardFactory.CreateForReward(Owner, ModelDb.CardPool<MultiplayerCardPool>(), CardCreationSource.Custom, 3, OddsRollMethod.RegularEncounter)
                .Select(r => r.Card).ToList();

        CardModel? chosenCard = await CardSelectCmd.FromChooseACardScreen(new NullPlayerChoiceContext(), options, Owner, true);

        if (chosenCard != null)
        {
            CardPileAddResult result = await CardPileCmd.Add(chosenCard, PileType.Deck);
            CardCmd.PreviewCardPileAdd(result);
        }

        foreach (CardModel option in options)
        {
            if (option != chosenCard)
            {
                Owner.ClimbState.CurrentMapPointHistoryEntry?.GetEntry(Owner.NetId).CardChoices.Add(
                    new ModelChoiceHistoryEntry(option.Id, false)
                );
            }
        }
    }
}
