using MegaCrit.Sts2.Core.Entities.Relics;

namespace MegaCrit.Sts2.Core.Models.Relics;

/// <summary>
/// Represents a relic that has been removed from the game. Mostly used for the climb history.
/// </summary>
// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class DeprecatedRelic : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Event;

    public override bool IsStackable => true;
}
