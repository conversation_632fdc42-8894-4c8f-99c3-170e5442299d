using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.CardPools;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class DustyTome : RelicModel
{
    private const string _ancientCardKey = "AncientCard";

    public override RelicRarity Rarity => RelicRarity.Ancient;

    private ModelId? _ancientCard;

    [SavedProperty]
    public ModelId? AncientCard
    {
        get => _ancientCard;
        set
        {
            AssertMutable();
            _ancientCard = value;

            if (_ancientCard != null)
            {
                CardModel card = ModelDb.GetById<CardModel>(_ancientCard);
                _extraHoverTips = card.HoverTips.Concat([HoverTipFactory.FromCard(card)]);
                ((StringVar)DynamicVars[_ancientCardKey]).StringValue = card.Title;
            }
        }
    }

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new StringVar(_ancientCardKey)
    ];

    private IEnumerable<IHoverTip> _extraHoverTips = [];

    protected override IEnumerable<IHoverTip> ExtraHoverTips => _extraHoverTips;

    public void SetupForPlayer(Player player)
    {
        // don't include cards that are from vibrant halo on this list
        IEnumerable<CardModel> ancientCards = player.Character.CardPool.Cards
            .Concat(ModelDb.CardPool<ColorlessCardPool>().Cards)
            .Where(c => c.Rarity == CardRarity.Ancient && !VibrantHalo.TranscendenceCards.Contains(c));

        AncientCard = player.PlayerRng.Rewards.NextItem(ancientCards)!.Id;
    }

    public override async Task AfterObtained()
    {
        CardModel ancientCard = Owner.ClimbState.CreateCard(ModelDb.GetById<CardModel>(AncientCard!), Owner);
        CardPileAddResult result = await CardPileCmd.Add(ancientCard, PileType.Deck);
        CardCmd.PreviewCardPileAdd(result, 2f);
    }
}
