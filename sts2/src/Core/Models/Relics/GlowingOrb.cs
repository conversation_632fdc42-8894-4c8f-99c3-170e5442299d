using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class GlowingOrb : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Event;

    public override bool IsUsedUp => WasUsed;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new EnergyVar(3),
    ];

    private bool _wasUsed;

    [SavedProperty]
    public bool WasUsed
    {
        get => _wasUsed;
        set
        {
            AssertMutable();
            _wasUsed = value;

            if (IsUsedUp)
            {
                Status = RelicStatus.Disabled;
            }
        }
    }

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.ForEnergy(this)];

    public override async Task AfterSideTurnStart(CombatSide side, CombatState combatState)
    {
        if (combatState.Encounter?.RoomType != RoomType.Boss) return;
        if (side != Owner.Creature.Side) return;
        if (combatState.RoundNumber > 1) return;
        if (IsUsedUp) return;

        Flash();
        WasUsed = true;
        await PlayerCmd.GainEnergy(DynamicVars.Energy.BaseValue, Owner);
    }
}
