using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class GoldenCompass : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Ancient;

    public override bool HasUponPickupEffect => true;

    private int _goldenPathAct = -1;

    [SavedProperty]
    public int GoldenPathAct
    {
        get => _goldenPathAct;
        set
        {
            AssertMutable();
            _goldenPathAct = value;
        }
    }

    public override async Task AfterObtained()
    {
        GoldenPathAct = Owner.ClimbState.CurrentActIndex;
        await ClimbManager.Instance.GenerateMap();
    }

    public override ActMap ModifyGeneratedMap(IClimbState climbState, ActMap map, int actIndex)
    {
        if (GoldenPathAct != actIndex) return map;

        return new GoldenPathActMap(climbState);
    }

    public override IReadOnlySet<RoomType> ModifyUnknownMapPointRoomTypes(IReadOnlySet<RoomType> roomTypes)
    {
        if (GoldenPathAct != Owner.ClimbState.CurrentActIndex) return roomTypes;

        HashSet<RoomType> result = [RoomType.Event];
        return result;
    }
}
