using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class HeartPiece : RelicModel
{
    private const string _maxHpLossKey = "MaxHpLoss";
    public override RelicRarity Rarity => RelicRarity.Rare;

    private decimal _damageReceivedThisTurn;
    private decimal DamageReceivedThisTurn
    {
        get => _damageReceivedThisTurn;
        set
        {
            AssertMutable();
            _damageReceivedThisTurn = value;
        }
    }

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_maxHpLoss<PERSON>ey, 20)
    ];

    public override decimal ModifyHpLost(Creature target, decimal amount, ValueProp props, Creature? dealer, CardModel? cardSource)
    {
        if (target != Owner.Creature) return amount;

        return Math.Min(amount, DynamicVars[_maxHpLossKey].BaseValue - _damageReceivedThisTurn);
    }

    public override Task AfterModifyingHpLost()
    {
        Flash();
        return Task.CompletedTask;
    }

    public override Task AfterDamageReceived(Creature target, DamageResult result, ValueProp props, Creature? dealer, CardModel? cardSource)
    {
        if (target != Owner.Creature) return Task.CompletedTask;
        DamageReceivedThisTurn += result.UnblockedDamage;
        return Task.CompletedTask;
    }

    public override Task BeforeSideTurnStart(CombatSide side, CombatState combatState)
    {
        if (side != CombatSide.Player) return Task.CompletedTask;
        _damageReceivedThisTurn = 0;
        return Task.CompletedTask;
    }
}
