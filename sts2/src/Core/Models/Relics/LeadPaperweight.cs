using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs.History;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.CardPools;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class LeadPaperweight : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Ancient;

    public override async Task AfterObtained()
    {
        List<CardModel> options = ModelDb.CardPool<ColorlessCardPool>().Cards
            .TakeRandom(3, Owner.PlayerRng.Rewards)
            .ToList();

        CardModel? chosenCard = await CardSelectCmd.FromChooseACardScreen(new NullPlayerChoiceContext(), options, Owner, true);

        if (chosenCard != null)
        {
            CardModel mutableCard = Owner.ClimbState.CreateCard(chosenCard, Owner);
            CardPileAddResult result = await CardPileCmd.Add(mutableCard, PileType.Deck);
            CardCmd.PreviewCardPileAdd(result);
        }

        foreach (CardModel option in options)
        {
            if (option != chosenCard)
            {
                Owner.ClimbState.CurrentMapPointHistoryEntry?.GetEntry(Owner.NetId).CardChoices.Add(
                    new ModelChoiceHistoryEntry(option.Id, false)
                );
            }
        }
    }
}
