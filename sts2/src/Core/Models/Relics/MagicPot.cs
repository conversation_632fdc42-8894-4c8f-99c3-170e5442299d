using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.CardSelection;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class MagicPot : RelicModel
{
    public const int cardsCount = 5;
    private const string _cardTitlesKey = "CardTitles";

    public override RelicRarity Rarity => RelicRarity.Ancient;
    public override bool ShowCounter => IsMutable && _serializableCards.Count > 0;
    public override int DisplayAmount => IsMutable ? _serializableCards.Count : 0;
    public override bool HasUponPickupEffect => true;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new CardsVar(cardsCount),
        new StringVar(_cardTitlesKey)
    ];

    private List<SerializableCard> _serializableCards = [];

    [SavedProperty]
    public List<SerializableCard> SerializableCards
    {
        get => _serializableCards;

        [SuppressMessage("ReSharper", "UnusedMember.Local", Justification = "Used by serialization.")]
        private set
        {
            AssertMutable();

            _serializableCards.Clear();
            _serializableCards.AddRange(value);

            UpdateCardList();
        }
    }

    protected override void AfterCloned()
    {
        base.AfterCloned();
        _serializableCards = [];
    }

    public override async Task AfterObtained()
    {
        CardSelectorPrefs prefs = new(CardSelectorPrefs.RemoveSelectionPrompt, DynamicVars.Cards.IntValue);
        IEnumerable<CardModel> cards = (await CardSelectCmd.FromDeckForRemoval(Owner, prefs)).OrderBy(c => c.Id.Entry);

        foreach (CardModel card in cards)
        {
            CardModel copy = (CardModel)card.MutableClone();
            SerializableCards.Add(copy.ToSerializable());
            await CardPileCmd.RemoveFromDeck(card);
        }

        UpdateCardList();
    }

    public override async Task AfterCombatEnd(CombatRoom room)
    {
        if (Owner.Creature.IsDead) return;
        if (SerializableCards.Count == 0) return;

        Flash();
        await Cmd.CustomScaledWait(0.1f, 1f); // Without this, the card appears the moment we strike the killing blow.

        SerializableCard serializableCard = Owner.PlayerRng.Rewards.NextItem(SerializableCards)!;
        CardModel card = CardModel.FromSerializable(serializableCard);

        if (!Owner.ClimbState.ContainsCard(card))
        {
            Owner.ClimbState.AddCard(card, Owner);
        }

        if (card.IsUpgradable)
        {
            CardCmd.Upgrade(card, CardPreviewStyle.MessyLayout);
        }

        CardPileAddResult result = await CardPileCmd.Add(card, PileType.Deck);
        CardCmd.PreviewCardPileAdd(result);

        Status = SerializableCards.Count > 0 ? RelicStatus.Normal : RelicStatus.Disabled;
        SerializableCards.Remove(serializableCard);
        UpdateCardList();
    }

    private void UpdateCardList()
    {
        Status = SerializableCards.Count > 0 ? RelicStatus.Normal : RelicStatus.Disabled;
        StringVar titles = ((StringVar)DynamicVars[_cardTitlesKey]);

        if (SerializableCards.Count == 0)
        {
            titles.StringValue = string.Empty;
        }
        else
        {
            titles.StringValue = string.Join('\n', SerializableCards.Select(c => $"- {ModelDb.GetById<CardModel>(c.Id!).Title}"));
        }

        InvokeDisplayAmountChanged();
    }

    /// <summary>
    /// DO NOT USE, this is only public for tests.
    /// </summary>
    public void DebugAddCard(SerializableCard card)
    {
        SerializableCards.Add(card);
    }
}
