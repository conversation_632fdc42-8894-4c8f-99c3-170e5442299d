using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Merchant;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class MawBank : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Event;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new GoldVar(12)
    ];

    // we don't want this flashing on the player because in multiplayer
    // its a bit too intense
    public override bool ShouldFlashOnPlayer => false;

    public override bool IsUsedUp => HasItemBeenBought;

    private bool _hasItemBeenBought;

    [SavedProperty]
    public bool HasItemBeenBought
    {
        get => _hasItemBeenBought;
        set
        {
            AssertMutable();
            _hasItemBeenBought = value;

            if (IsUsedUp)
            {
                Status = RelicStatus.Disabled;
            }
        }
    }

    public override async Task AfterRoomEntered(AbstractRoom room)
    {
        if (Owner.ClimbState.BaseRoom != room) return;
        if (HasItemBeenBought) return;

        Flash();
        await PlayerCmd.GainGold(DynamicVars.Gold.BaseValue, Owner);
    }

    public override Task AfterItemPurchased(Player player, MerchantEntry itemPurchased, int cost)
    {
        if (player != Owner) return Task.CompletedTask;
        if (HasItemBeenBought) return Task.CompletedTask;

        Flash();
        HasItemBeenBought = true;
        return Task.CompletedTask;
    }
}
