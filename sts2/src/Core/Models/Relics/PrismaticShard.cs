using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.Event
public sealed class PrismaticShard : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Event;

    public override IEnumerable<CardModel> ModifyCardRewardCardPool(Player player, IEnumerable<CardModel> options, CardCreationSource source)
    {
        if (Owner != player) return options;
        if (source == CardCreationSource.Custom) return options;

        List<CardModel> newOptions = [];

        List<CardPoolModel> validPools = ModelDb.UnlockedCharacterCardPools.ToList();

        newOptions.AddRange(validPools.SelectMany(pool => pool.Cards));
        return newOptions;
    }
}
