using System.Collections.Generic;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Localization.DynamicVars;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class RingOfTheDrake : RelicModel
{
    private const string _turnsKey = "Turns";
    public override RelicRarity Rarity => RelicRarity.Ancient;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new CardsVar(2),
        new(_turnsKey, 2)
    ];

    public override decimal ModifyHandDraw(Player player, decimal count)
    {
        if (player != Owner) return count;
        if (player.Creature.CombatState!.RoundNumber > DynamicVars[_turnsKey].BaseValue) return count;

        return count + DynamicVars.Cards.BaseValue;
    }
}
