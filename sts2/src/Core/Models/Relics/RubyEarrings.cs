using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Factories;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class RubyEarrings : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Ancient;

    public override bool TryModifyCardRewardOptions(Player player, List<CardCreationResult> options, CardCreationSource source)
    {
        if (player != Owner) return false;
        if (source == CardCreationSource.Custom) return false;

        CardModel card1 = options[0].Card;

        if (card1.Rarity != CardRarity.Common)
        {
            List<CardModel> commonCards = card1.Pool.Cards.Where(c => c.Rarity == CardRarity.Common).ToList();

            // if the current card pool doesn't have the common cards, fallback to the players card pool (i.e. the colorless card po0l)
            if (commonCards.Count <= 0)
            {
                commonCards = player.Character.CardPool.Cards.Where(c => c.Rarity == CardRarity.Common).ToList();
            }

            CardModel replacement = CardFactory.CreateForRewardWithoutModifications(player, commonCards, 1).First();
            options[0].ModifyCard(replacement, this);
        }

        CardModel card2 = options[1].Card;
        if (card2.Rarity != CardRarity.Uncommon)
        {
            List<CardModel> uncommonCards = card2.Pool.Cards.Where(c => c.Rarity == CardRarity.Uncommon).ToList();

            // if the current card pool doesn't have the common cards, fallback to the players card pool
            if (uncommonCards.Count <= 0)
            {
                uncommonCards = player.Character.CardPool.Cards.Where(c => c.Rarity == CardRarity.Uncommon).ToList();
            }

            CardModel replacement = CardFactory.CreateForRewardWithoutModifications(player, uncommonCards, 1).First();
            options[1].ModifyCard(replacement, this);
        }

        CardModel card3 = options[2].Card;
        if (card3.Rarity != CardRarity.Rare)
        {
            List<CardModel> rareCards = card3.Pool.Cards.Where(c => c.Rarity == CardRarity.Rare).ToList();

            // if the current card pool doesn't have the common cards, fallback to the players card pool
            if (rareCards.Count <= 0)
            {
                rareCards = player.Character.CardPool.Cards.Where(c => c.Rarity == CardRarity.Rare).ToList();
            }

            CardModel replacement = CardFactory.CreateForRewardWithoutModifications(player, rareCards, 1).First();
            options[2].ModifyCard(replacement, this);
        }

        return true;
    }

    public override Task AfterModifyingRewards()
    {
        Flash();
        return Task.CompletedTask;
    }
}
