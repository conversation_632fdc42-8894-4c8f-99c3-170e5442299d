using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Powers;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class ScreamingFlagon : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Shop;

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromPower<Strength>()];

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new PowerVar<Strength>(2)
    ];

    // This needs to trigger before BeforeFlush, because that's when <PERSON> and other cards discard themselves, but we
    // want to count them as in hand at the end of turn
    public override async Task BeforeTurnEnd(PlayerChoiceContext choiceContext, CombatSide side)
    {
        if (side != CombatSide.Player) return;
        if (!PileType.Hand.GetPile(Owner).IsEmpty) return;

        Flash();
        await PowerCmd.Apply<Strength>(Owner.Creature, DynamicVars.Strength.BaseValue, Owner.Creature, null);
    }
}
