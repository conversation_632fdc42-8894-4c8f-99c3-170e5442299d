using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Shoehorn : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Uncommon;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new BlockVar(15, BlockProps.nonCardUnpowered),
        new DamageVar(6, DamageProps.nonCardUnpowered)
    ];

    public override async Task AfterTurnEnd(PlayerChoiceContext choiceContext, CombatSide side)
    {
        if (side != CombatSide.Player) return;
        if (Owner.Creature.Block < DynamicVars.Block.BaseValue) return;

        Creature? enemy = Owner.ClimbState.Rng.CombatTargets.NextItem(Owner.Creature.CombatState!.HittableEnemies);

        if (enemy != null)
        {
            VfxCmd.PlayOnCreatureCenter(enemy, VfxCmd.bluntPath);
            await CreatureCmd.Damage(enemy, DynamicVars.Damage, Owner.Creature);
        }
    }
}
