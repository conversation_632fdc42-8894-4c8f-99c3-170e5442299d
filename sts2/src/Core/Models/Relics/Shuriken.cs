using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Shuriken : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Rare;

    public override bool ShowCounter => CombatManager.Instance.IsInProgress;
    public override int DisplayAmount => IsActivating ? DynamicVars.Cards.IntValue : AttacksPlayedThisTurn % DynamicVars.Cards.IntValue;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new CardsVar(3),
        new PowerVar<Strength>(1)
    ];

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromPower<Strength>()];

    // Used to display the threshold value briefly while the relic is granting strength
    private bool _isActivating;
    private bool IsActivating
    {
        get => _isActivating;
        set
        {
            AssertMutable();
            _isActivating = value;
            UpdateDisplay();
        }
    }

    private int _attacksPlayedThisTurn;
    private int AttacksPlayedThisTurn
    {
        get => _attacksPlayedThisTurn;
        set
        {
            AssertMutable();
            _attacksPlayedThisTurn = value;
            UpdateDisplay();
        }
    }

    private void UpdateDisplay()
    {
        if (IsActivating)
        {
            Status = RelicStatus.Normal;
        }
        else
        {
            int threshold = DynamicVars.Cards.IntValue;
            Status = AttacksPlayedThisTurn % threshold == threshold - 1 ? RelicStatus.Active : RelicStatus.Normal;
        }

        InvokeDisplayAmountChanged();
    }

    // Use BeforeTurnStart instead of AfterTurnStart so that effects like Hellraiser can proc it
    public override Task BeforeSideTurnStart(CombatSide side, CombatState combatState)
    {
        if (side != Owner.Creature.Side) return Task.CompletedTask;

        AttacksPlayedThisTurn = 0;
        Status = RelicStatus.Normal;
        return Task.CompletedTask;
    }

    public override async Task AfterCardPlayed(PlayerChoiceContext context, CardModel card, Creature? target, int playCount)
    {
        if (card.Owner != Owner) return;
        if (!CombatManager.Instance.IsInProgress) return;
        if (card.Type != CardType.Attack) return;

        AttacksPlayedThisTurn++;

        int threshold = DynamicVars.Cards.IntValue;

        if (AttacksPlayedThisTurn % threshold == 0)
        {
            _ = TaskHelper.RunSafely(DoActivateVisuals());
            await PowerCmd.Apply<Strength>(Owner.Creature, DynamicVars.Strength.BaseValue, Owner.Creature, null);
        }
    }

    private async Task DoActivateVisuals()
    {
        IsActivating = true;
        Flash();
        await Cmd.Wait(NRelicFlashVfx.activationDuration);
        IsActivating = false;
    }

    public override Task AfterCombatEnd(CombatRoom _)
    {
        Status = RelicStatus.Normal;
        AttacksPlayedThisTurn = 0;
        IsActivating = false;
        return Task.CompletedTask;
    }
}
