using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SilverCrucible : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Ancient;

    public override bool IsUsedUp => TimesUsed >= DynamicVars.Cards.IntValue;
    public override bool ShowCounter => DynamicVars.Cards.IntValue > 0;
    public override int DisplayAmount => DynamicVars.Cards.IntValue - TimesUsed;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new CardsVar(2)
    ];

    private int _timesUsed;

    [SavedProperty]
    public int TimesUsed
    {
        get => _timesUsed;
        set
        {
            AssertMutable();
            _timesUsed = value;
            InvokeDisplayAmountChanged();

            if (IsUsedUp)
            {
                Status = RelicStatus.Disabled;
            }
        }
    }

    public override bool TryModifyCardRewardOptionsLate(Player player, List<CardCreationResult> options, CardCreationSource source)
    {
        if (player != Owner) return false;
        if (IsUsedUp) return false;

        foreach (CardCreationResult entry in options)
        {
            CardModel card = entry.Card;
            if (!card.IsUpgradable) continue;

            CardModel upgraded = Owner.ClimbState.CloneCard(card);
            CardCmd.Upgrade(upgraded);

            entry.ModifyCard(upgraded, this);
        }

        return true;
    }

    public override Task AfterModifyingCardRewardOptions()
    {
        if (IsUsedUp) return Task.CompletedTask;

        TimesUsed++;
        return Task.CompletedTask;
    }
}
