using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SwordOfStone : RelicModel
{
    private const string _elitesKey = "Elites";

    public override RelicRarity Rarity => RelicRarity.Event;

    public override bool ShowCounter => true;
    public override int DisplayAmount => ElitesDefeated;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_elitesKey, 5)
    ];

    private int _elitesDefeated;

    [SavedProperty]
    public int ElitesDefeated
    {
        get => _elitesDefeated;
        set
        {
            AssertMutable();

            _elitesDefeated = value;
            InvokeDisplayAmountChanged();
        }
    }

    public override async Task AfterCombatVictory(CombatRoom room)
    {
        // Only trigger on elites.
        if (room.RoomType != RoomType.Elite) return;

        ElitesDefeated++;
        Flash();

        if (ElitesDefeated >= DynamicVars[_elitesKey].BaseValue)
        {
            await RelicCmd.Replace(this, ModelDb.Relic<SwordOfJade>().ToMutable());
        }
    }
}
