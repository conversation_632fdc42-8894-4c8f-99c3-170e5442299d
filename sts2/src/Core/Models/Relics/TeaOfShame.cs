using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TeaOfShame : RelicModel
{
    private const string _combatsKey = "Combats";
    private const string _dazedCountKey = "DazedCount";

    public override RelicRarity Rarity => RelicRarity.Event;

    public override bool IsUsedUp => CombatsLeft <= 0;
    public override bool ShowCounter => false;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_combatsKey, CombatsLeft),
        new(_dazedCountKey, 3)
    ];

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromCard<Dazed>()];

    private int _combatsLeft = 1;

    [SavedProperty]
    public int CombatsLeft
    {
        get => _combatsLeft;
        set
        {
            AssertMutable();

            _combatsLeft = value;
            DynamicVars[_combatsKey].BaseValue = _combatsLeft;
            InvokeDisplayAmountChanged();

            if (IsUsedUp)
            {
                Status = RelicStatus.Disabled;
            }
        }
    }

    public override async Task BeforeCombatStart()
    {
        if (CombatsLeft <= 0) return;

        await CardPileCmd.AddToCombatAndPreview<Dazed>(Owner.Creature, PileType.Draw, DynamicVars[_dazedCountKey].IntValue, true, CardPilePosition.Random);
        CombatsLeft--;
        Flash();
    }
}
