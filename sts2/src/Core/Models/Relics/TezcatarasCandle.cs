using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TezcatarasCandle : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Ancient;

    public override bool HasUponPickupEffect => true;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new EnergyVar(1)
    ];

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.ForEnergy(this)];

    private int _activeAct = -1;

    [SavedProperty]
    public int ActiveAct
    {
        get => _activeAct;
        set
        {
            AssertMutable();
            _activeAct = value;
        }
    }

    public override Task AfterObtained()
    {
        ActiveAct = Owner.ClimbState.CurrentActIndex;
        return Task.CompletedTask;
    }

    public override decimal ModifyMaxEnergy(Player player, decimal amount)
    {
        if (player != Owner) return amount;
        if (ActiveAct != Owner.ClimbState.CurrentActIndex) return amount;

        return amount + DynamicVars.Energy.IntValue;
    }

    public override Task AfterRoomEntered(AbstractRoom _)
    {
        Status = ActiveAct == Owner.ClimbState.CurrentActIndex ? RelicStatus.Normal : RelicStatus.Disabled;
        return Task.CompletedTask;
    }
}
