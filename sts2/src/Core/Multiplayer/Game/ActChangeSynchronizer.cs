using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Nodes.Screens;
using MegaCrit.Sts2.Core.Nodes.Screens.Overlays;

namespace MegaCrit.Sts2.Core.Multiplayer.Game;

public class ActChangeSynchronizer
{
    private readonly ClimbState _climbState;

    // This list is indexed by player slot ID (i.e. same order as _players)
    private readonly List<bool> _readyPlayers = [];

    private readonly Logger _logger = new("ActChangeSynchronizer", LogType.GameSync);

    public ActChangeSynchronizer(ClimbState climbState)
    {
        _climbState = climbState;

        for (int i = 0; i < climbState.Players.Count; i++)
        {
            _readyPlayers.Add(false);
        }
    }

    public void SetLocalPlayerReady()
    {
        _logger.Info("Local player ready to move to next act");
        ClimbManager.Instance.ActionQueueSynchronizer.RequestEnqueue(
            new VoteToMoveToNextActAction(LocalContext.GetMe(_climbState)!)
        );
    }

    public bool IsWaitingForOtherPlayers()
    {
        int localPlayerIndex = _climbState.GetPlayerSlotIndex(LocalContext.NetId!.Value);

        for (int i = 0; i < _readyPlayers.Count; i++)
        {
            if (!_readyPlayers[i] && i != localPlayerIndex) return true;
        }

        return false;
    }

    public void OnPlayerReady(Player player)
    {
        _logger.Debug($"Player {player.NetId} ready to move to next act");
        int index = _climbState.GetPlayerSlotIndex(player);
        _readyPlayers[index] = true;

        if (_readyPlayers.All(x => x))
        {
            MoveToNextAct();
        }
    }

    private void MoveToNextAct()
    {
        for (int i = 0; i < _readyPlayers.Count; i++)
        {
            _readyPlayers[i] = false;
        }

        _logger.Info("All players ready to move to next act, beginning transition");
        _climbState.ActFloor++;
        TaskHelper.RunSafely(ClimbManager.Instance.EnterNextAct());

        if (NOverlayStack.Instance?.Peek() is NRewardsScreen rewardsScreen)
        {
            rewardsScreen.HideWaitingForPlayersScreen();
        }
    }
}
