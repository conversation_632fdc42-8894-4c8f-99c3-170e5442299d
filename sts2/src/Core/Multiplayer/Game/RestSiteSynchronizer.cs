using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.RestSite;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Game.Flavor;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Game.Sync;

namespace MegaCrit.Sts2.Core.Multiplayer.Game;

public class RestSiteSynchronizer : IDisposable
{
    // The minimum millseconds between updates.
    public const int minHoverMessageMsec = 50;

    private readonly INetGameService _netService;
    private readonly ClimbLocationTargetedMessageBuffer _messageBuffer;
    private readonly IPlayerCollection _playerCollection;
    private readonly ulong _localPlayerId;
    private readonly List<PlayerRestSite> _restSites = [];

    private readonly Logger _logger = new(nameof(RestSiteSynchronizer), LogType.GameSync);

    private Player LocalPlayer => _playerCollection.GetPlayer(_localPlayerId)!;

    // Last time we sent a hover message to peers.
    private ulong _lastHoverMessageMsec;

    // The next hover message to send to peers.
    private RestSiteOptionHoveredMessage? _hoveredMessage;

    // Task which will send _updateToSend to peers.
    private Task? _hoverMessageTask;

    public event Action<ulong>? PlayerHoverChanged;
    public event Action<RestSiteOption, ulong>? BeforePlayerOptionChosen;
    public event Action<RestSiteOption, bool, ulong>? AfterPlayerOptionChosen;

    private class PlayerRestSite
    {
        public List<RestSiteOption> options = default!;
        public uint? lastChosenOptionIndex; // If players can choose more than one option, move this to a list
        public uint? hoveredOptionIndex;
    }

    public RestSiteSynchronizer(ClimbLocationTargetedMessageBuffer messageBuffer, INetGameService netService, IPlayerCollection playerCollection, ulong localPlayerId)
    {
        _netService = netService;
        _messageBuffer = messageBuffer;
        _playerCollection = playerCollection;
        _localPlayerId = localPlayerId;

        _messageBuffer.RegisterMessageHandler<OptionIndexChosenMessage>(HandleRestSiteOptionChosenMessage);
        _messageBuffer.RegisterMessageHandler<RestSiteOptionHoveredMessage>(HandleRestSiteOptionHoveredMessage);
    }

    public void Dispose()
    {
        _messageBuffer.UnregisterMessageHandler<OptionIndexChosenMessage>(HandleRestSiteOptionChosenMessage);
        _messageBuffer.UnregisterMessageHandler<RestSiteOptionHoveredMessage>(HandleRestSiteOptionHoveredMessage);
        _hoverMessageTask?.Dispose();
        _hoverMessageTask = null;
    }

    public void BeginRestSite()
    {
        _logger.Debug("Beginning rest site");

        _restSites.Clear();

        foreach (Player player in _playerCollection.Players)
        {
            List<RestSiteOption> options = RestSiteOption.Generate(player);
            _restSites.Add(new PlayerRestSite { options = options });

            _logger.VeryDebug($"Rest site began for player {player.NetId} with options: {string.Join(",", options)}");
        }
    }

    private void HandleRestSiteOptionChosenMessage(OptionIndexChosenMessage message, ulong senderId)
    {
        if (message.type != OptionIndexType.RestSite) return;
        _logger.Debug($"Player {senderId} chose rest site option index {message.optionIndex}");

        Player? player = _playerCollection.GetPlayer(senderId);

        if (player == null) throw new InvalidOperationException($"Received EventOptionChosenMessage for player {senderId} that doesn't exist!");

        TaskHelper.RunSafely(ChooseOption(player, (int)message.optionIndex));
    }

    private void HandleRestSiteOptionHoveredMessage(RestSiteOptionHoveredMessage message, ulong senderId)
    {
        int slotIndex = _playerCollection.GetPlayerSlotIndex(_playerCollection.GetPlayer(senderId)!);
        _restSites[slotIndex].hoveredOptionIndex = message.optionIndex;
        PlayerHoverChanged?.Invoke(senderId);
    }

    public Task<bool> ChooseLocalOption(int index)
    {
        _logger.Debug($"Local player chose rest site option index {index}");

        OptionIndexChosenMessage message = default;
        message.type = OptionIndexType.RestSite;
        message.optionIndex = (uint)index;
        message.location = _messageBuffer.CurrentLocation;

        _netService.SendMessage(message);

        return ChooseOption(LocalPlayer, index);
    }

    private async Task<bool> ChooseOption(Player player, int optionIndex)
    {
        int playerSlotIndex = _playerCollection.GetPlayerSlotIndex(player);
        PlayerRestSite restSite = _restSites[playerSlotIndex];

        if (optionIndex >= restSite.options.Count)
        {
            throw new InvalidOperationException($"Player {player.NetId} attempted to choose rest site option index {optionIndex}, but there were only {restSite.options.Count} options available!");
        }

        RestSiteOption option = restSite.options[optionIndex];

        BeforePlayerOptionChosen?.Invoke(option, player.NetId);
        bool success = await option.OnSelect();

        _logger.Debug($"Rest site option index {optionIndex} chosen for player {player.NetId} with success {success}. Option: {restSite.options[optionIndex].OptionId}");

        restSite.lastChosenOptionIndex = (uint)optionIndex;
        AfterPlayerOptionChosen?.Invoke(option, success, player.NetId);

        if (!success) return false;

        player.ClimbState.CurrentMapPointHistoryEntry?.GetEntry(player.NetId).RestSiteChoices.Add(option.OptionId);

        if (Hook.ShouldDisableRemainingRestSiteOptions(player.ClimbState, player))
        {
            _logger.Debug($"Clearing all remaining rest site options for player {player.NetId}");
            restSite.options.Clear();
        }
        else
        {
            _logger.Debug($"Leaving remaining rest site options enabled for player {player.NetId}");
            restSite.options.RemoveAt(optionIndex);
        }

        return true;
    }

    public void LocalOptionHovered(RestSiteOption? option)
    {
        int slotIndex = _playerCollection.GetPlayerSlotIndex(LocalContext.GetMe(_playerCollection)!);
        PlayerRestSite restSite = _restSites[slotIndex];

        if (option != null)
        {
            restSite.hoveredOptionIndex = (uint)restSite.options.IndexOf(option);
        }
        else
        {
            restSite.hoveredOptionIndex = null;
        }

        _hoveredMessage ??= new RestSiteOptionHoveredMessage
        {
            Location = _messageBuffer.CurrentLocation,
            optionIndex = restSite.hoveredOptionIndex
        };

        TrySendHoverMessage();
        PlayerHoverChanged?.Invoke(LocalContext.NetId!.Value);
    }

    public int? GetHoveredOptionIndex(ulong playerId)
    {
        int slotIndex = _playerCollection.GetPlayerSlotIndex(_playerCollection.GetPlayer(playerId)!);
        return (int?)_restSites[slotIndex].hoveredOptionIndex;
    }

    public int? GetChosenOptionIndex(ulong playerId)
    {
        int slotIndex = _playerCollection.GetPlayerSlotIndex(_playerCollection.GetPlayer(playerId)!);
        return (int?)_restSites[slotIndex].lastChosenOptionIndex;
    }

    public IReadOnlyList<RestSiteOption> GetLocalOptions()
    {
        return GetOptionsForPlayer(LocalPlayer);
    }

    public IReadOnlyList<RestSiteOption> GetOptionsForPlayer(ulong playerId)
    {
        return GetOptionsForPlayer(_playerCollection.GetPlayer(playerId)!);
    }

    public IReadOnlyList<RestSiteOption> GetOptionsForPlayer(Player player)
    {
        int playerSlotIndex = _playerCollection.GetPlayerSlotIndex(player);
        return _restSites[playerSlotIndex].options;
    }

    /// <summary>
    /// Sends a hover message if enough time has passed since the last one, or buffers it to be sent otherwise.
    /// </summary>
    private void TrySendHoverMessage()
    {
        // If we already have a message queued up, do nothing
        if (_hoverMessageTask != null) return;

        int delay = (int)(_lastHoverMessageMsec + minHoverMessageMsec - Time.GetTicksMsec());

        // If enough time has passed since the last message, send it immediately
        if (delay <= 0)
        {
            _hoverMessageTask = TaskHelper.RunSafely(SendHoverMessageAfterSmallDelay());
        }
        else
        {
            // Otherwise, schedule the send for the future
            _hoverMessageTask = TaskHelper.RunSafely(QueueHoverMessage(delay));
        }
    }

    private async Task QueueHoverMessage(int delayMsec)
    {
        await Task.Delay(delayMsec);
        SendHoverMessage();
    }

    private async Task SendHoverMessageAfterSmallDelay()
    {
        await Task.Yield();
        SendHoverMessage();
    }

    private void SendHoverMessage()
    {
        // Since sends can be delayed, need to check if we are still connected
        if (!_netService.IsConnected) return;

        _netService.SendMessage(_hoveredMessage!.Value);
        _lastHoverMessageMsec = Time.GetTicksMsec();
        _hoveredMessage = null;
        _hoverMessageTask = null;
    }
}
