using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.CardSelection;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Climbs.History;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Game;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Game.Sync;
using MegaCrit.Sts2.Core.Rewards;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Multiplayer.Game;

/// <summary>
/// Tells other players about actions directly affecting our local player, either from reward screens or the merchant.
/// For responsiveness, this does not use GameAction. When the player clicks a reward button, we want the reward to occur
/// immediately, without having to wait for a roundtrip to the host.
/// Since these messages do not use GameAction, and are not sequenced with other combat actions, this may not be used
/// during combat. Handling these messages in combat could result in a state divergence depending on timing of GameAction
/// resolution.
/// Despite this restriction, it is possible for reward messages to be received while we are still in combat, e.g. if we
/// are in slow mode but our peers are in fast mode. Reward messages are buffered until combat ends, and will be handled
/// then.
/// </summary>
public class RewardSynchronizer : IDisposable
{
    private readonly ClimbLocationTargetedMessageBuffer _messageBuffer;
    private readonly INetGameService _gameService;
    private readonly IPlayerCollection _playerCollection;
    private readonly ulong _localPlayerId;

    private readonly List<BufferedMessage> _bufferedMessages = [];

    private Player LocalPlayer => _playerCollection.GetPlayer(_localPlayerId)!;

    private struct BufferedMessage
    {
        public ulong senderId;
        public RewardObtainedMessage? rewardMessage;
        public GoldLostMessage? goldLostMessage;
        public CardRemovedMessage? cardRemovedMessage;
    }

    public RewardSynchronizer(ClimbLocationTargetedMessageBuffer messageBuffer, INetGameService gameService, IPlayerCollection playerCollection, ulong localPlayerId)
    {
        _gameService = gameService;
        _playerCollection = playerCollection;
        _localPlayerId = localPlayerId;

        _messageBuffer = messageBuffer;
        _messageBuffer.RegisterMessageHandler<RewardObtainedMessage>(HandleRewardObtainedMessage);
        _messageBuffer.RegisterMessageHandler<GoldLostMessage>(HandleGoldLostMessage);
        _messageBuffer.RegisterMessageHandler<CardRemovedMessage>(HandleCardRemovedMessage);

        CombatManager.Instance.CombatEnded += OnCombatEnded;
    }

    public void Dispose()
    {
        _messageBuffer.UnregisterMessageHandler<RewardObtainedMessage>(HandleRewardObtainedMessage);
        _messageBuffer.UnregisterMessageHandler<GoldLostMessage>(HandleGoldLostMessage);
        _messageBuffer.UnregisterMessageHandler<CardRemovedMessage>(HandleCardRemovedMessage);

        CombatManager.Instance.CombatEnded -= OnCombatEnded;
    }

    public void SyncLocalObtainedCard(CardModel card)
    {
        SyncLocalCardEvent(card, false);
    }

    public void SyncLocalSkippedCard(CardModel card)
    {
        SyncLocalCardEvent(card, true);
    }

    private void SyncLocalCardEvent(CardModel card, bool skipped)
    {
        if (!ClimbManager.Instance.IsSinglePlayerOrFakeMultiplayer && CombatManager.Instance.IsInProgress)
        {
            throw new InvalidOperationException($"Tried to sync card event {card} during combat! This is not allowed");
        }

        RewardObtainedMessage message = new() { rewardType = RewardType.Card, cardModel = card, wasSkipped = skipped, location = _messageBuffer.CurrentLocation };
        _gameService.SendMessage(message);
    }

    public void SyncLocalObtainedRelic(RelicModel relic)
    {
        SyncLocalRelicEvent(relic, false);
    }

    public void SyncLocalSkippedRelic(RelicModel relic)
    {
        SyncLocalRelicEvent(relic, true);
    }

    private void SyncLocalRelicEvent(RelicModel relic, bool skipped)
    {
        if (!ClimbManager.Instance.IsSinglePlayerOrFakeMultiplayer && CombatManager.Instance.IsInProgress)
        {
            throw new InvalidOperationException(
                $"Tried to sync relic event {relic} during combat! This is not allowed"
            );
        }

        RewardObtainedMessage message = new() { rewardType = RewardType.Relic, relicModel = relic, wasSkipped = skipped, location = _messageBuffer.CurrentLocation };
        _gameService.SendMessage(message);
    }

    public void SyncLocalObtainedPotion(PotionModel potion)
    {
        SyncLocalPotionEvent(potion, false);
    }

    public void SyncLocalSkippedPotion(PotionModel potion)
    {
        SyncLocalPotionEvent(potion, true);
    }

    private void SyncLocalPotionEvent(PotionModel potion, bool skipped)
    {
        if (!ClimbManager.Instance.IsSinglePlayerOrFakeMultiplayer && CombatManager.Instance.IsInProgress)
        {
            throw new InvalidOperationException(
                $"Tried to sync potion event {potion} during combat! This is not allowed"
            );
        }

        RewardObtainedMessage message = new() { rewardType = RewardType.Potion, potionModel = potion, wasSkipped = skipped, location = _messageBuffer.CurrentLocation };
        _gameService.SendMessage(message);
    }

    public void SyncLocalObtainedGold(int goldAmount)
    {
        if (!ClimbManager.Instance.IsSinglePlayerOrFakeMultiplayer && CombatManager.Instance.IsInProgress)
        {
            throw new InvalidOperationException(
                $"Tried to sync obtaining {goldAmount} gold during combat! This is not allowed"
            );
        }

        RewardObtainedMessage message = new() { rewardType = RewardType.Gold, goldAmount = goldAmount, wasSkipped = false, location = _messageBuffer.CurrentLocation };
        _gameService.SendMessage(message);
    }

    public void SyncLocalGoldLost(int goldLost)
    {
        if (!ClimbManager.Instance.IsSinglePlayerOrFakeMultiplayer && CombatManager.Instance.IsInProgress)
        {
            throw new InvalidOperationException(
                $"Tried to sync losing {goldLost} gold during combat! This is not allowed"
            );
        }

        GoldLostMessage message = new() { goldLost = goldLost, location = _messageBuffer.CurrentLocation };
        _gameService.SendMessage(message);
    }

    public async Task<bool> DoLocalCardRemoval()
    {
        CardRemovedMessage message = new() { Location = _messageBuffer.CurrentLocation };
        _gameService.SendMessage(message);

        return await DoCardRemoval(LocalPlayer);
    }

    private void HandleRewardObtainedMessage(RewardObtainedMessage message, ulong senderId)
    {
        // We cannot handle reward messages in combat. Buffer this until the end of combat
        if (CombatManager.Instance.IsInProgress)
        {
            BufferedMessage buffered = new()
            {
                senderId = senderId,
                rewardMessage = message
            };

            _bufferedMessages.Add(buffered);
            return;
        }

        Player player = _playerCollection.GetPlayer(senderId)!;
        MapPointHistoryEntry? pointHistory = player.ClimbState.GetHistoryEntryFor(message.location);
        PlayerMapPointHistoryEntry? entry = null;

        if (pointHistory != null)
        {
            entry = pointHistory.GetEntry(player.NetId);
        }

        switch (message.rewardType)
        {
            case RewardType.Card:
                if (!message.wasSkipped)
                {
                    CardModel card = message.cardModel!;
                    player.ClimbState.AddCard(card, player);
                    TaskHelper.RunSafely(CardPileCmd.Add(card, PileType.Deck));
                }

                // No else here because CardPileCmd.Add records to CardsGained, not CardChoices
                entry?.CardChoices.Add(new ModelChoiceHistoryEntry(message.cardModel!.Id, !message.wasSkipped));
                break;
            case RewardType.Gold:
                if (message.wasSkipped) throw new NotImplementedException("Cannot handle skip gold reward message!");
                TaskHelper.RunSafely(PlayerCmd.GainGold(message.goldAmount!.Value, player));

                break;
            case RewardType.Potion:
                if (!message.wasSkipped)
                {
                    TaskHelper.RunSafely(PotionCmd.TryToProcure(message.potionModel!.ToMutable(), player));
                }
                else
                {
                    entry?.PotionChoices.Add(new ModelChoiceHistoryEntry(message.potionModel!.Id, !message.wasSkipped));
                }

                break;
            case RewardType.Relic:
                if (!message.wasSkipped)
                {
                    TaskHelper.RunSafely(RelicCmd.Obtain(message.relicModel!, player));
                }
                else
                {
                    entry?.RelicChoices.Add(new ModelChoiceHistoryEntry(message.relicModel!.Id, !message.wasSkipped));
                }

                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(message.rewardType), message.rewardType, null);
        }
    }

    private void HandleGoldLostMessage(GoldLostMessage message, ulong senderId)
    {
        // We cannot handle messages in combat. Buffer this until the end of combat
        if (CombatManager.Instance.IsInProgress)
        {
            BufferedMessage buffered = new()
            {
                senderId = senderId,
                goldLostMessage = message
            };

            _bufferedMessages.Add(buffered);
            return;
        }

        Player player = _playerCollection.GetPlayer(senderId)!;
        TaskHelper.RunSafely(PlayerCmd.LoseGold(message.goldLost, player));
    }

    private void HandleCardRemovedMessage(CardRemovedMessage message, ulong senderId)
    {
        // We cannot handle messages in combat. Buffer this until the end of combat
        if (CombatManager.Instance.IsInProgress)
        {
            BufferedMessage buffered = new()
            {
                senderId = senderId,
                cardRemovedMessage = message
            };

            _bufferedMessages.Add(buffered);
            return;
        }

        Player player = _playerCollection.GetPlayer(senderId)!;

        if (player == LocalPlayer)
        {
            throw new InvalidOperationException(
                "CardRemovedMessage should not be sent to the player removing the card!"
            );
        }

        TaskHelper.RunSafely(DoCardRemoval(player));
    }

    private void OnCombatEnded(CombatRoom _)
    {
        foreach (BufferedMessage buffered in _bufferedMessages)
        {
            if (buffered.rewardMessage != null)
            {
                HandleRewardObtainedMessage(buffered.rewardMessage.Value, buffered.senderId);
            }
            else if (buffered.goldLostMessage != null)
            {
                HandleGoldLostMessage(buffered.goldLostMessage.Value, buffered.senderId);
            }
            else if (buffered.cardRemovedMessage != null)
            {
                HandleCardRemovedMessage(buffered.cardRemovedMessage, buffered.senderId);
            }
        }

        _bufferedMessages.Clear();
    }

    private async Task<bool> DoCardRemoval(Player player)
    {
        CardSelectorPrefs prefs = new(new LocString("gameplay_ui", "COMBAT_REWARD_CARD_REMOVAL.selectionScreenPrompt"), 1) { Cancelable = true, RequireManualConfirmation = true };

        CardModel? card = (await CardSelectCmd.FromDeckForRemoval(player, prefs)).FirstOrDefault();

        if (card != null)
        {
            await CardPileCmd.RemoveFromDeck(card);
            return true;
        }

        return false;
    }
}
