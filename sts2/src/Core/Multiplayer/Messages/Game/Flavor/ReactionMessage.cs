using Godot;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;
using MegaCrit.Sts2.Core.Multiplayer.Transport;

namespace MegaCrit.Sts2.Core.Multiplayer.Messages.Game.Flavor;

/// <summary>
/// Sent when a player uses the reaction wheel to send a reaction.
/// </summary>
public struct ReactionMessage : INetMessage
{
    private static readonly QuantizeParams _quantizeParams = new(-3f, 3f, 16);

    public bool ShouldBroadcast => true;
    public NetTransferMode Mode => NetTransferMode.Unreliable;
    public LogLevel LogLevel => LogLevel.VeryDebug;

    public ReactionType type;
    public Vector2 normalizedPosition;

    public void Serialize(PacketWriter writer)
    {
        writer.WriteEnum(type);
        writer.WriteVector2(normalizedPosition, _quantizeParams, _quantizeParams);
    }

    public void Deserialize(PacketReader reader)
    {
        type = reader.ReadEnum<ReactionType>();
        normalizedPosition = reader.ReadVector2(_quantizeParams, _quantizeParams);
    }
}
