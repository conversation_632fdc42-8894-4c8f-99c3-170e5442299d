using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;
using MegaCrit.Sts2.Core.Multiplayer.Transport;

namespace MegaCrit.Sts2.Core.Multiplayer.Messages.Game.Sync;

/// <summary>
/// Sent by the host to all clients when votes are received by all players during a shared event. This message specifies
/// the option that all players should execute.
/// </summary>
public struct SharedEventOptionChosenMessage : INetMessage, IClimbLocationTargetedMessage
{
    public bool ShouldBroadcast => false;
    public NetTransferMode Mode => NetTransferMode.Reliable;
    public LogLevel LogLevel => LogLevel.VeryDebug;
    public ClimbLocation Location => location;

    public uint optionIndex;
    public uint pageIndex;
    public ClimbLocation location;

    public void Serialize(PacketWriter writer)
    {
        writer.WriteUInt(optionIndex, 4);
        writer.WriteUInt(pageIndex, 4);
        writer.Write(location);
    }

    public void Deserialize(PacketReader reader)
    {
        optionIndex = reader.ReadUInt(4);
        pageIndex = reader.ReadUInt(4);
        location = reader.Read<ClimbLocation>();
    }

    public override string ToString() => $"{nameof(SharedEventOptionChosenMessage)} index {optionIndex} page {pageIndex}";
}
