using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Debug;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;
using MegaCrit.Sts2.Core.Multiplayer.Transport;

namespace MegaCrit.Sts2.Core.Multiplayer.Messages.Lobby;

/// <summary>
/// Sent from the host to the client as the first message after the client connects.
/// </summary>
public struct InitialGameInfoMessage : INetMessage
{
    public bool ShouldBroadcast => false;
    public NetTransferMode Mode => NetTransferMode.Reliable;
    public LogLevel LogLevel => LogLevel.Info;

    /// <summary>
    /// The version of the game the host is running.
    /// </summary>
    public string version;

    /// <summary>
    /// A hash of all the IDs in the model database.
    /// </summary>
    public uint idDatabaseHash;

    /// <summary>
    /// What kind of climb this is (standard, daily, custom).
    /// </summary>
    public GameMode gameMode;

    /// <summary>
    /// What state the climb is currently in.
    /// </summary>
    public ClimbSessionState sessionState;

    /// <summary>
    /// If the host is about to disconnect the client, why.
    /// </summary>
    public ConnectionFailureReason? connectionFailureReason;

    /// <summary>
    /// Returns an InitialGameInfoMessage with the version and idDatabaseHash filled in.
    /// </summary>
    public static InitialGameInfoMessage Basic()
    {
        InitialGameInfoMessage message = default;
        message.version = ReleaseInfoManager.Instance.ReleaseInfo?.Version ?? GitHelper.ShortCommitId ?? "UNKNOWN";
        message.idDatabaseHash = ModelIdSerializationCache.Hash;

        return message;
    }

    public void Serialize(PacketWriter writer)
    {
        writer.WriteString(version);
        writer.WriteUInt(idDatabaseHash);
        writer.WriteEnum(gameMode);
        writer.WriteEnum(sessionState);

        writer.WriteBool(connectionFailureReason != null);
        if (connectionFailureReason != null)
        {
            writer.WriteEnum(connectionFailureReason.Value);
        }
    }

    public void Deserialize(PacketReader reader)
    {
        version = reader.ReadString();
        idDatabaseHash = reader.ReadUInt();
        gameMode = reader.ReadEnum<GameMode>();
        sessionState = reader.ReadEnum<ClimbSessionState>();

        if (reader.ReadBool())
        {
            connectionFailureReason = reader.ReadEnum<ConnectionFailureReason>();
        }
    }
}
