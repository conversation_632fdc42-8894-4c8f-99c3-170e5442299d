using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;
using MegaCrit.Sts2.Core.Multiplayer.Transport;

namespace MegaCrit.Sts2.Core.Multiplayer.Messages.Lobby;

/// <summary>
/// Sent when the LoadLobby closes and the climb begins.
/// Used only in LoadLobby. Lobby uses LobbyBeginClimbMessage.
/// </summary>
public struct LobbyBeginLoadedClimbMessage : INetMessage
{
    public bool ShouldBroadcast => true;
    public NetTransferMode Mode => NetTransferMode.Reliable;
    public LogLevel LogLevel => LogLevel.VeryDebug;

    public void Serialize(PacketWriter writer) { }

    public void Deserialize(PacketReader reader) { }
}
