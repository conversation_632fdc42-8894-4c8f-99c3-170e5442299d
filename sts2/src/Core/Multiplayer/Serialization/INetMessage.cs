using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Multiplayer.Transport;

namespace MegaCrit.Sts2.Core.Multiplayer.Serialization;

public interface INetMessage : IPacketSerializable
{
    /// <summary>
    /// If set, when this message is sent to the host, it will be echoed to all clients.
    /// </summary>
    public bool ShouldBroadcast { get; }

    /// <summary>
    /// Whether this message is transferred reliably or unreliably.
    /// </summary>
    public NetTransferMode Mode { get; }

    /// <summary>
    /// What log level to use when logging info about this message.
    /// Almost all messages should be VeryDebug - only set this to Info for messages that are logged very infrequently.
    /// </summary>
    public LogLevel LogLevel { get; }
}
