using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace MegaCrit.Sts2.Core.Multiplayer.Serialization;

/// <summary>
/// Bidirectional map of types to unique integer IDs, for use in serialization.
/// </summary>
/// <typeparam name="TBase">All classes which implement this type will automatically be mapped by this class.</typeparam>
public class NetTypeCache<TBase>
{
    private readonly Dictionary<Type, int> _typeToId = [];
    private readonly List<Type> _idToType;

    public NetTypeCache()
    {
        // Note: this can change fairly easily
        if (!typeof(TBase).IsInterface) throw new InvalidOperationException($"Type {typeof(TBase)} is not an interface! TypeCache only supports interfaces");

        Assembly assembly = Assembly.GetAssembly(typeof(TBase))!;
        List<Type> netMessageTypes = assembly.GetTypes().Where(t => t.GetInterfaces().Contains(typeof(TBase))).ToList();
        netMessageTypes.Sort((t1, t2) => string.CompareOrdinal(t1.Name, t2.Name));

        _idToType = netMessageTypes;

        for (int i = 0; i < netMessageTypes.Count; i++)
        {
            _typeToId[netMessageTypes[i]] = i;
        }
    }

    /// <summary>
    /// Obtain the ID for a given type.
    /// </summary>
    /// <typeparam name="T">The type to map.</typeparam>
    /// <returns>The integer ID for the type T.</returns>
    /// <exception cref="InvalidOperationException">Thrown if T does not implement TBase.</exception>
    public int TypeToId<T>() where T: TBase
    {
        return TypeToId(typeof(T));
    }

    /// <summary>
    /// Obtain the ID for a given type.
    /// </summary>
    /// <param name="type">The type to map.</param>
    /// <returns>The integer ID for the given type.</returns>
    /// <exception cref="InvalidOperationException">Thrown if type does not implement TBase.</exception>
    public int TypeToId(Type type)
    {
        if (!type.GetInterfaces().Contains(typeof(TBase))) throw new InvalidOperationException($"Tried to get type ID for type {type}, but it does not implement interface {typeof(TBase)}!");
        return _typeToId[type];
    }

    /// <summary>
    /// Obtain the type for a given integer ID.
    /// </summary>
    /// <param name="id">The ID to map to a type.</param>
    /// <param name="type">The type for the given ID.</param>
    /// <returns>True if a type was found for the ID, false otherwise.</returns>
    public bool TryGetTypeFromId(int id, out Type? type)
    {
        if (id < 0 || id >= _idToType.Count)
        {
            type = null;
            return false;
        }

        type = _idToType[id];
        return true;
    }
}
