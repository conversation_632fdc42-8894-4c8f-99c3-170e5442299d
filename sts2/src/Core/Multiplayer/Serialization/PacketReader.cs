using System;
using System.Buffers.Binary;
using System.Collections.Generic;
using System.Text;
using Godot;

namespace MegaCrit.Sts2.Core.Multiplayer.Serialization;

public class PacketReader
{
    public int BitPosition { get; private set; }
    public byte[] Buffer { get; private set; } = default!;

    private readonly byte[] _tempBuffer = new byte[16];
    private byte[] _stringBuffer = new byte[64];

    public void Reset(byte[] buffer)
    {
        BitPosition = 0;
        Buffer = buffer;
    }

    public bool ReadBool()
    {
        Array.Clear(_tempBuffer);
        BitSerializationUtil.ReadBits(Buffer, BitPosition, _tempBuffer, 1);
        BitPosition += 1;
        return _tempBuffer[0] != 0;
    }

    public byte ReadByte(int bits = 8)
    {
        Array.Clear(_tempBuffer);
        BitSerializationUtil.ReadBits(Buffer, BitPosition, _tempBuffer, bits);
        BitPosition += bits;
        return _tempBuffer[0];
    }

    public void ReadBytes(byte[] destinationBuffer, int byteCount)
    {
        BitSerializationUtil.ReadBits(Buffer, BitPosition, destinationBuffer, byteCount * 8);
        BitPosition += byteCount * 8;
    }

    public short ReadShort(int bits = 16)
    {
        Array.Clear(_tempBuffer);
        BitSerializationUtil.ReadBits(Buffer, BitPosition, _tempBuffer, bits);
        BitPosition += bits;
        short val = BinaryPrimitives.ReadInt16LittleEndian(_tempBuffer.AsSpan());
        return val;
    }

    public ushort ReadUShort(int bits = 16)
    {
        Array.Clear(_tempBuffer);
        BitSerializationUtil.ReadBits(Buffer, BitPosition, _tempBuffer, bits);
        BitPosition += bits;
        ushort val = BinaryPrimitives.ReadUInt16LittleEndian(_tempBuffer.AsSpan());
        return val;
    }

    public T ReadEnum<T>() where T : struct, Enum
    {
        if (!typeof(int).IsAssignableFrom(Enum.GetUnderlyingType(typeof(T))))
        {
            // This can probably be overcome by writing the appropriate type
            throw new InvalidOperationException($"Trying to write enum type {typeof(T)} that is not assignable to int!");
        }

        int bits = Mathf.CeilToInt(Math.Log2(MaxEnumValueCache.Get<T>()) + 1);
        int valueAsInt = ReadInt(bits);
        return (T)Enum.ToObject(typeof(T), valueAsInt);
    }

    public int ReadInt(int bits = 32)
    {
        Array.Clear(_tempBuffer);
        BitSerializationUtil.ReadBits(Buffer, BitPosition, _tempBuffer, bits);
        BitPosition += bits;
        int val = BinaryPrimitives.ReadInt32LittleEndian(_tempBuffer.AsSpan());
        return val;
    }

    public uint ReadUInt(int bits = 32)
    {
        Array.Clear(_tempBuffer);
        BitSerializationUtil.ReadBits(Buffer, BitPosition, _tempBuffer, bits);
        BitPosition += bits;
        uint val = BinaryPrimitives.ReadUInt32LittleEndian(_tempBuffer.AsSpan());
        return val;
    }

    public float ReadFloat(QuantizeParams? quantizeParams = null)
    {
        Array.Clear(_tempBuffer);

        if (quantizeParams != null)
        {
            uint quantized = ReadUInt(quantizeParams.Value.bits);
            return Unquantize(quantized, quantizeParams.Value.min, quantizeParams.Value.max, quantizeParams.Value.bits);
        }
        else
        {
            BitSerializationUtil.ReadBits(Buffer, BitPosition, _tempBuffer, 32);
            BitPosition += 32;
            return BinaryPrimitives.ReadSingleLittleEndian(_tempBuffer.AsSpan());
        }
    }

    public long ReadLong(int bits = 64)
    {
        Array.Clear(_tempBuffer);
        BitSerializationUtil.ReadBits(Buffer, BitPosition, _tempBuffer, bits);
        BitPosition += bits;
        long val = BinaryPrimitives.ReadInt64LittleEndian(_tempBuffer.AsSpan());
        return val;
    }

    public ulong ReadULong(int bits = 64)
    {
        Array.Clear(_tempBuffer);
        BitSerializationUtil.ReadBits(Buffer, BitPosition, _tempBuffer, bits);
        BitPosition += bits;
        ulong val = BinaryPrimitives.ReadUInt64LittleEndian(_tempBuffer.AsSpan());
        return val;
    }

    public double ReadDouble()
    {
        Array.Clear(_tempBuffer);
        BitSerializationUtil.ReadBits(Buffer, BitPosition, _tempBuffer, 64);
        BitPosition += 64;
        double val = BinaryPrimitives.ReadDoubleLittleEndian(_tempBuffer.AsSpan());
        return val;
    }

    public Vector2 ReadVector2(QuantizeParams? quantizeParamsX = null, QuantizeParams? quantizeParamsY = null)
    {
        float x = ReadFloat(quantizeParamsX);
        float y = ReadFloat(quantizeParamsY);
        return new Vector2(x, y);
    }

    public List<T> ReadList<T>(int lengthBits = 32) where T: IPacketSerializable, new()
    {
        List<T> list = [];
        int length = ReadInt(lengthBits);
        for (int i = 0; i < length; i++)
        {
            list.Add(Read<T>());
        }

        return list;
    }

    public string ReadString()
    {
        int byteLength = ReadInt();

        if (_stringBuffer.Length < byteLength)
        {
            int nearestPowerOfTwo = (int)Math.Pow(2, Math.Ceiling(Math.Log2(byteLength)));
            _stringBuffer = new byte[nearestPowerOfTwo];
        }

        ReadBytes(_stringBuffer, byteLength);
        return Encoding.UTF8.GetString(_stringBuffer, 0, byteLength);
    }

    public T Read<T>() where T: IPacketSerializable, new()
    {
        T serializable = new();
        serializable.Deserialize(this);
        return serializable;
    }

    // Converts a quantized uint value back into a float.
    public static float Unquantize(uint value, float min, float max, int bitLength)
    {
        return (float)((value / Math.Pow(2, bitLength)) * (max - min) + min);
    }
}
