using System;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Platform.Steam;
using Steamworks;

namespace MegaCrit.Sts2.Core.Multiplayer.Transport.Steam;

public class SteamClient : NetClient
{
    public override bool IsConnected => _isConnected;
    public override ulong NetId => SteamUser.GetSteamID().m_SteamID;

    private CSteamID? _lobbyId;
    private HSteamNetConnection? _conn;
    private bool _isConnected;
    private TaskCompletionSource<ConnectionResult>? _connectingTaskCompletionSource;
    private Callback<SteamNetConnectionStatusChangedCallback_t>? _netStatusChangedCallback;
    private readonly Logger _logger = new(nameof(SteamClient), LogType.Network);

    public SteamClient(INetClientHandler handler) : base(handler) { }

    private struct ConnectionResult
    {
        public HSteamNetConnection? connection;
        public SteamDisconnectionReason? disconnectionReason;
        public string? debugReason;
    }

    public Task<NetErrorInfo?> ConnectToLobbyOwnedByFriend(ulong steamPlayerId)
    {
        _logger.Info($"Initializing Steam client. Our player id: {NetId}");
        _logger.Debug($"Attempting to connect to lobby of player {steamPlayerId}");

        bool success = SteamFriends.GetFriendGamePlayed(new CSteamID(steamPlayerId), out FriendGameInfo_t friendGameInfo);
        if (!success) throw new InvalidOperationException($"Tried to join game of {steamPlayerId}, but they are not playing a game!");

        if (friendGameInfo.m_gameID != new CGameID(SteamInitializer.steamAppId) ||
            friendGameInfo.m_steamIDLobby.m_SteamID == 0)
        {
            return Task.FromResult<NetErrorInfo?>(new NetErrorInfo(NetError.InvalidJoin, false));
        }

        return ConnectToLobby(friendGameInfo.m_steamIDLobby.m_SteamID);
    }

    public async Task<NetErrorInfo?> ConnectToLobby(ulong lobbyId)
    {
        SteamAPICall_t handle = SteamMatchmaking.JoinLobby(new CSteamID(lobbyId));
        using SteamCallResult<LobbyEnter_t> callResult = new(handle);

        _logger.Debug($"Attempting to enter lobby {lobbyId}");
        LobbyEnter_t lobbyEnterResult = await callResult.Task;

        if (lobbyEnterResult.m_ulSteamIDLobby != lobbyId)
        {
            _logger.Error("Joined incorrect lobby?");
            return new NetErrorInfo(NetError.InternalError, false);
        }

        EChatRoomEnterResponse lobbyEnterResponse = (EChatRoomEnterResponse)lobbyEnterResult.m_EChatRoomEnterResponse;

        if (lobbyEnterResponse != EChatRoomEnterResponse.k_EChatRoomEnterResponseSuccess)
        {
            _logger.Error($"Failed to enter lobby, response: {lobbyEnterResponse}");
            return new NetErrorInfo(lobbyEnterResponse);
        }

        _lobbyId = new CSteamID(lobbyEnterResult.m_ulSteamIDLobby);

        // Even though we may have a steamPlayerId if we called into ConnectToLobbyOwnedByFriend, the lobby owner may
        // not be that player (e.g. in friend-of-friend case)
        CSteamID lobbyOwnerId = SteamMatchmaking.GetLobbyOwner(_lobbyId!.Value);

        SteamNetworkingIdentity netId = lobbyOwnerId.ToNetId();

        _netStatusChangedCallback = new Callback<SteamNetConnectionStatusChangedCallback_t>(OnNetStatusChanged);
        _connectingTaskCompletionSource = new TaskCompletionSource<ConnectionResult>();
        _conn = SteamNetworkingSockets.ConnectP2P(ref netId, 0, 0, null);

        _logger.Debug($"Connecting to user {lobbyOwnerId.m_SteamID}");
        ConnectionResult result = await _connectingTaskCompletionSource.Task;

        if (result.disconnectionReason != null)
        {
            SteamMatchmaking.LeaveLobby(_lobbyId!.Value);
            _netStatusChangedCallback.Dispose();
            _netStatusChangedCallback = null;
            return new NetErrorInfo(result.disconnectionReason.Value, result.debugReason!, false);
        }

        if (_conn.Value.m_HSteamNetConnection != result.connection!.Value.m_HSteamNetConnection)
        {
            _logger.Error("Got different connection back from OnNetStatusChanged than we expected!");
            DisconnectFromHostInternal(SteamDisconnectionReason.AppInternalError, "Invalid OnNetStatusChanged hConn", true, false);
            return new NetErrorInfo(NetError.InternalError, false);
        }

        _connectingTaskCompletionSource = null;
        _isConnected = true;
        _handler.OnConnectedToHost();
        _logger.Debug($"Successfully connected to host {lobbyOwnerId.m_SteamID}");

        return null;
    }

    private void OnNetStatusChanged(SteamNetConnectionStatusChangedCallback_t data)
    {
        _logger.Debug($"Connection status changed: {data.m_eOldState} -> {data.m_info.m_eState}");

        if (data.m_info.m_eState == ESteamNetworkingConnectionState.k_ESteamNetworkingConnectionState_Connected)
        {
            _logger.Debug("Steam connection accepted.");

            if (_connectingTaskCompletionSource == null)
            {
                _logger.Error("Connection was accepted while we were not waiting for it!");
                DisconnectFromHostInternal(SteamDisconnectionReason.InternalError, "Not Connecting", true, false);
            }
            else
            {
                _connectingTaskCompletionSource.SetResult(new ConnectionResult { connection = data.m_hConn });
            }
        }
        else if (data.m_info.m_eState == ESteamNetworkingConnectionState.k_ESteamNetworkingConnectionState_ProblemDetectedLocally)
        {
            _logger.Info($"Steam connection closed because of problem. Reason: {data.m_info.m_eEndReason}, {data.m_info.m_szEndDebug}");

            SteamDisconnectionReason reason = (SteamDisconnectionReason)data.m_info.m_eEndReason;
            string debugReason = data.m_info.m_szEndDebug;
            _connectingTaskCompletionSource?.SetResult(new ConnectionResult { disconnectionReason = reason, debugReason = debugReason });
            DisconnectFromHostInternal(reason, debugReason, true, false);
        }
        else if (data.m_info.m_eState == ESteamNetworkingConnectionState.k_ESteamNetworkingConnectionState_ClosedByPeer)
        {
            _logger.Info($"Steam connection closed by host. Reason: {data.m_info.m_eEndReason}, {data.m_info.m_szEndDebug}");

            SteamDisconnectionReason reason = (SteamDisconnectionReason)data.m_info.m_eEndReason;
            string debugReason = data.m_info.m_szEndDebug;
            _connectingTaskCompletionSource?.SetResult(new ConnectionResult { disconnectionReason = reason, debugReason = debugReason });
            DisconnectFromHostInternal((SteamDisconnectionReason)data.m_info.m_eEndReason, data.m_info.m_szEndDebug, true, false);
        }
    }

    public override void Update()
    {
        SteamUtil.ProcessMessages(_conn!.Value, _handler, _logger);
    }

    public override void SendMessageToHost(byte[] bytes, int length, NetTransferMode mode, int channel = 0)
    {
        _logger.VeryDebug($"Sending {length} bytes to host");

        GCHandle pinnedArray = GCHandle.Alloc(bytes, GCHandleType.Pinned);
        EResult result = SteamNetworkingSockets.SendMessageToConnection(_conn!.Value, pinnedArray.AddrOfPinnedObject(), (uint)length, SteamUtil.FlagsFromMode(mode), out _);
        pinnedArray.Free();

        if (result != EResult.k_EResultOK)
        {
            _logger.Warn($"Failed to send message length {length}: {result}");
        }
    }

    public override void DisconnectFromHost(NetError reason, bool now = false)
    {
        DisconnectFromHostInternal(reason.ToSteam(), string.Empty, now, true);
    }

    private void DisconnectFromHostInternal(SteamDisconnectionReason reason, string? debugReason, bool now, bool selfInitiated)
    {
        _logger.Debug($"Disconnecting from host (now: {now} reason: {reason} debug: {debugReason})");
        SteamNetworkingSockets.CloseConnection(_conn!.Value, (int)reason, debugReason, !now);
        SteamMatchmaking.LeaveLobby(_lobbyId!.Value);

        _conn = null;
        _connectingTaskCompletionSource = null;
        _netStatusChangedCallback?.Dispose();
        _netStatusChangedCallback = null;
        _isConnected = false;

        _handler.OnDisconnectedFromHost(new NetErrorInfo(reason, debugReason, selfInitiated));
    }
}
