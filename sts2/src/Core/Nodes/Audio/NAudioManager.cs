using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Audio;

public partial class NAudioManager : Node
{
    public static NAudioManager? Instance => NGame.Instance?.AudioManager;

    private Node _audioNode = default!;

    public override void _Ready()
    {
        _audioNode = GetNode<Node>("Proxy");
    }

    public void PlayLoop(string path)
    {
        if (TestMode.IsOn) return;

        _audioNode.Call("play_loop", path);
    }

    public void StopLoop(string path)
    {
        if (TestMode.IsOn) return;

        _audioNode.Call("stop_loop", path);
    }

    public void SetParam(string path, string param, float value)
    {
        if (TestMode.IsOn) return;

        _audioNode.Call("set_param", path, param, value);
    }

    public void StopAllLoops()
    {
        if (TestMode.IsOn) return;

        _audioNode.Call("stop_all_loops");
    }

    public void PlayOneShot(string path, Dictionary<string, int> parameters)
    {
        if (TestMode.IsOn) return;

        // Godot Dictionaries are different from C# ones, so I have to make this conversion
        // for it to successfully pass the param information to godot
        Godot.Collections.Dictionary gdDict = new();

        foreach (KeyValuePair<string, int> kvp in parameters)
        {
            gdDict.Add(kvp.Key, kvp.Value);
        }

        _audioNode.Call("play_one_shot", path, gdDict);
    }

    public void PlayOneShot(string path)
    {
        if (TestMode.IsOn) return;

        PlayOneShot(path, new Dictionary<string, int>());
    }

    public void PlayMusic(string music)
    {
        if (TestMode.IsOn) return;

        _audioNode.Call("play_music", music);
    }

    public void StopMusic()
    {
        if (TestMode.IsOn) return;

        _audioNode.Call("stop_music");
    }

    public void SetMasterVol(float volume)
    {
        if (TestMode.IsOn) return;

        _audioNode.Call("set_master_volume", Mathf.Pow(volume, 2f));
    }

    public void SetSfxVol(float volume)
    {
        if (TestMode.IsOn) return;

        _audioNode.Call("set_sfx_volume", Mathf.Pow(volume, 2f));
    }

    public void SetAmbienceVol(float volume)
    {
        if (TestMode.IsOn) return;

        _audioNode.Call("set_ambience_volume", Mathf.Pow(volume, 2f));
    }

    public void SetBgmVol(float volume)
    {
        if (TestMode.IsOn) return;

        _audioNode.Call("set_bgm_volume", Mathf.Pow(volume, 2f));
    }
}
