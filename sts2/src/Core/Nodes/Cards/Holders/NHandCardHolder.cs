using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.Nodes.Cards.Holders;

public partial class NHandCardHolder : NCardHolder
{
    private Control _flash = default!;
    private Tween? _flashTween;

    // we have a separate focused and unfocused event for hand card holders so that they get received
    // by both mouse over and input map focusing... probably could use a different name though
    [Signal]
    public delegate void HolderFocusedEventHandler(NHandCardHolder cardHolder);

    [Signal]
    public delegate void HolderUnfocusedEventHandler(NHandCardHolder cardHolder);

    // we have unique events from mouse dragged and mouse released because Pressed typically fires
    // on mouse released.
    [Signal]
    public delegate void HolderMouseClickedEventHandler(NCardHolder cardHolder);

    private Label _handIndexLabel = default!;

    private const float _rotateSpeed = 10f;
    private const float _angleSnapThreshold = 0.1f;
    private const float _scaleSpeed = 8f;
    private const float _scaleSnapThreshold = 0.002f;
    private const float _moveSpeed = 7f;
    private const float _positionSnapThreshold = 1f;

    // For creating smooth non-time based lerping behavior for cards.
    // Will collide with Tweens. Call StopAnimations() to well, stop animating.
    private Vector2 _targetPosition;
    private float _targetAngle;
    private Vector2 _targetScale;
    private CancellationTokenSource? _angleCancelToken;
    private CancellationTokenSource? _positionCancelToken;
    private CancellationTokenSource? _scaleCancelToken;

    private NPlayerHand _hand = default!;

    public bool InSelectMode { get; set; }
    public Vector2 TargetPosition => _targetPosition;
    public float TargetAngle => _targetAngle;

    private static string ScenePath => SceneHelper.GetScenePath("cards/holders/hand_card_holder");
    public static IEnumerable<string> AssetPaths => [ScenePath];

    public static NHandCardHolder Create(NCard card, NPlayerHand hand)
    {
        NHandCardHolder holder = PreloadManager.Cache.GetScene(ScenePath).Instantiate<NHandCardHolder>();
        holder.Name = $"{holder.GetType().Name}-{card.Model!.Id}";
        holder.SetCard(card);
        holder._hand = hand;
        return holder;
    }

    public override void _Ready()
    {
        ConnectSignals();
        _flash = GetNode<Control>("Flash");
        _flash.Modulate = new Color(_flash.Modulate.R, _flash.Modulate.G, _flash.Modulate.B, 0f);
        _handIndexLabel = GetNode<Label>("%HandIndex");
        UpdateCard();

        // DO NOT subscribe to events in _EnterTree or _Ready! We've already subscribed in SetCard via Create
    }

    public override void _ExitTree()
    {
        base._ExitTree();
        UnsubscribeFromEvents(CardNode?.Model);
        StopAnimations();
    }

    public override void Clear()
    {
        UnsubscribeFromEvents(CardNode?.Model);
        base.Clear();
        StopAnimations();
    }

    protected override void OnFocus()
    {
        EmitSignal(SignalName.HolderFocused, this);
        base.OnFocus();
    }

    protected override void OnUnfocus()
    {
        EmitSignal(SignalName.HolderUnfocused, this);
        base.OnUnfocus();
    }

    protected override void OnMousePressed(InputEvent inputEvent)
    {
        if (inputEvent is not InputEventMouseButton mouseAction) return;

        if (mouseAction.ButtonIndex == MouseButton.Left)
        {
            EmitSignal(SignalName.HolderMouseClicked, this);
        }
    }

    protected override void OnMouseReleased(InputEvent inputEvent)
    {
        if (CardNode == null) return;
        if (!_isHovered) return;
    }

    protected override void DoCardHoverEffects(bool isHovered)
    {
        ZIndex = isHovered ? 1 : 0;

        if (isHovered)
        {
            CreateHoverTips();
        }
        else
        {
            ClearHoverTips();
        }
    }

    public void SetIndexLabel(int i)
    {
        _handIndexLabel.Text = i.ToString();
        _handIndexLabel.Visible = i > 0 && SaveManager.Instance.SettingsSave.ShowCardIndices;
    }

    /// <summary>
    /// Sets the angle we want this card to be (in degrees).
    /// Used when fanning out cards in the hand. Lacks features that normal tweens have.
    /// </summary>
    /// <param name="angle"></param>
    public void SetTargetAngle(float angle)
    {
        _targetAngle = angle;
        _angleCancelToken?.Cancel();
        _angleCancelToken = new CancellationTokenSource();
        TaskHelper.RunSafely(AnimAngle(_angleCancelToken));
    }

    /// <summary>
    /// Sets the target global position we want this card to be at.
    /// Used when we want this card to navigate from one position to another without a fixed time frame (non-tween).
    /// </summary>
    public void SetTargetPosition(Vector2 position)
    {
        _targetPosition = position;
        _positionCancelToken?.Cancel();
        _positionCancelToken = new CancellationTokenSource();
        TaskHelper.RunSafely(AnimPosition(_positionCancelToken));
    }

    /// <summary>
    /// Sets the target scale we want this card to be at, it will smoothly get there.
    /// Used when we want to lerp this card scale in various screens but lacks features that normal tweens have.
    /// </summary>
    /// <param name="scale"></param>
    public void SetTargetScale(Vector2 scale)
    {
        _targetScale = scale;
        _scaleCancelToken?.Cancel();
        _scaleCancelToken = new CancellationTokenSource();
        TaskHelper.RunSafely(AnimScale(_scaleCancelToken));
    }

    /// <summary>
    /// Cancels tweens and sets the Rotation of this CardHolder
    /// </summary>
    /// <param name="setAngle"></param>
    public void SetAngleInstantly(float setAngle)
    {
        _angleCancelToken?.Cancel();
        RotationDegrees = setAngle;
    }

    /// <summary>
    /// Cancels tweens and sets the Scale of this CardHolder
    /// </summary>
    /// <param name="setScale"></param>
    public void SetScaleInstantly(Vector2 setScale)
    {
        _scaleCancelToken?.Cancel();
        Scale = setScale;
    }

    /// <summary>
    /// Call if we want to stop animating this CardHolder for any reason (like using separate animators/tweens)
    /// </summary>
    private void StopAnimations()
    {
        _angleCancelToken?.Cancel();
        _positionCancelToken?.Cancel();
        _scaleCancelToken?.Cancel();
    }

    /// <summary>
    /// Animates our card to rotate to targetAngle.
    /// NOTE: This is NOT a timer-based tween.
    /// </summary>
    /// <param name="cancelToken"></param>
    private async Task AnimAngle(CancellationTokenSource cancelToken)
    {
        // Runs until we reach the target angle (via Snapping) or we cancel it by calling another AnimAngle.
        while (!cancelToken.IsCancellationRequested)
        {
            RotationDegrees = Mathf.Lerp(RotationDegrees, _targetAngle, (float)GetProcessDeltaTime() * _rotateSpeed); // CONST: rotate speed

            // Angle snapping logic. Otherwise, we lerp forever.
            if (Mathf.Abs(RotationDegrees - _targetAngle) < _angleSnapThreshold)
            {
                RotationDegrees = _targetAngle;
                return;
            }

            await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
        }
    }

    /// <summary>
    /// Animates our card to scale to targetSize.
    /// NOTE: This is NOT a timer-based tween.
    /// </summary>
    /// <param name="cancelToken"></param>
    private async Task AnimScale(CancellationTokenSource cancelToken)
    {
        // Runs until we reach the target size (via Snapping) or we cancel it by calling another AnimScale.
        while (!cancelToken.IsCancellationRequested)
        {
            Scale = Scale.Lerp(_targetScale, (float)GetProcessDeltaTime() * _scaleSpeed);

            // Scale snapping logic. Otherwise, we lerp forever.
            if (Mathf.Abs(_targetScale.X - Scale.X) < _scaleSnapThreshold)
            {
                Scale = _targetScale;
                return;
            }

            await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
        }
    }

    /// <summary>
    /// Animates our card to move to a specified position.
    /// NOTE: This is NOT a timer-based tween.
    /// </summary>
    /// <param name="cancelToken"></param>
    private async Task AnimPosition(CancellationTokenSource cancelToken)
    {
        // Runs until we reach the target size (via Snapping) or we cancel it by calling another AnimPosition.
        while (!cancelToken.IsCancellationRequested)
        {
            // TODO: Can be improved by using a velocity based transition. Hard to test at the moment.
            Position = Position.Lerp(_targetPosition, (float)GetProcessDeltaTime() * _moveSpeed);

            // Position snapping logic. Otherwise, we lerp forever.
            if (Position.DistanceSquaredTo(_targetPosition) < _positionSnapThreshold) // TODO: Const
            {
                Position = _targetPosition;
                return;
            }

            await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
        }
    }

    protected override void SetCard(NCard node)
    {
        if (CardNode != null)
        {
            CardNode.ModelChanged -= OnModelChanged;
        }

        UnsubscribeFromEvents(CardNode?.Model);
        base.SetCard(node);

        UpdateCard();
        SubscribeToEvents(CardNode?.Model);

        if (CardNode != null)
        {
            CardNode.ModelChanged += OnModelChanged;
        }

        // If we received a card with a non-one scale from elsewhere, then tween it back to one scale
        if (node.Scale != Vector2.One)
        {
            node.CreateTween().TweenProperty(node, "scale", Vector2.One, 0.25f);
        }
    }

    public void UpdateCard()
    {
        if (!IsNodeReady()) return;
        if (CardNode == null) return;

        CardNode.UpdateVisuals(PileType.Hand);

        if (CardNode.Model!.CanPlay() || ShouldGlowRed || ShouldGlowGold)
        {
            CardNode.CardHighlight.AnimShow();
            CardNode.CardHighlight.Modulate = NCardHighlight.playableColor;

            if (ShouldGlowRed)
            {
                CardNode.CardHighlight.Modulate = NCardHighlight.red;
            }
            else if (ShouldGlowGold)
            {
                CardNode.CardHighlight.Modulate = NCardHighlight.gold;
            }
        }
        else
        {
            CardNode.CardHighlight.AnimHide();
        }
    }

    public void BeginDrag()
    {
        SetAngleInstantly(0f);
        SetScaleInstantly(HoverScale);
    }

    public void CancelDrag()
    {
        ZIndex = 0;
        SetAngleInstantly(0f);
        SetScaleInstantly(Vector2.One);
    }

    public void SetDefaultTargets()
    {
        ZIndex = 0;

        IReadOnlyList<NHandCardHolder> holders = _hand.ActiveHolders;
        int index = holders.IndexOf(this);
        int handSize = holders.Count;

        // If this is not in the hand, then don't do anything
        if (index < 0) return;

        SetTargetPosition(HandPosHelper.GetPosition(handSize, index));
        SetTargetAngle(HandPosHelper.GetAngle(handSize, index));
        SetTargetScale(HandPosHelper.GetScale(handSize));
    }

    public void Flash()
    {
        _flash.Scale = Vector2.One;
        _flash.Modulate = NCardHighlight.playableColor;

        if (ShouldGlowGold)
        {
            _flash.Modulate = NCardHighlight.gold;
        }
        else if (ShouldGlowRed)
        {
            _flash.Modulate = NCardHighlight.red;
        }

        _flashTween?.Kill();
        _flashTween = CreateTween();
        _flashTween.TweenProperty(_flash, "modulate:a", 0.6, 0.15);
        _flashTween.TweenProperty(_flash, "modulate:a", 0, 0.3);
    }

    private bool ShouldGlowGold
    {
        get
        {
            CardModel? cardModel = CardNode?.Model;
            if (cardModel == null) return false;

            if (_hand.SelectModeGoldGlowOverride != null)
            {
                return _hand.SelectModeGoldGlowOverride.Invoke(cardModel);
            }
            else
            {
                return cardModel.CanPlay() && cardModel.ShouldGlowGold;
            }
        }
    }

    private bool ShouldGlowRed => CardNode?.Model?.ShouldGlowRed ?? false;

    private void SubscribeToEvents(CardModel? card)
    {
        if (card == null || !IsInsideTree()) return;

        card.Upgraded += Flash;
        card.KeywordsChanged += Flash;
        card.ReplayCountChanged += Flash;
        card.AfflictionChanged += Flash;
        card.EnergyCostChanged += Flash;
        card.StarCostChanged += Flash;
    }

    private void UnsubscribeFromEvents(CardModel? card)
    {
        if (card == null) return;

        card.Upgraded -= Flash;
        card.KeywordsChanged -= Flash;
        card.ReplayCountChanged -= Flash;
        card.AfflictionChanged -= Flash;
        card.EnergyCostChanged -= Flash;
        card.StarCostChanged -= Flash;
    }

    private void OnModelChanged(CardModel? oldModel)
    {
        UnsubscribeFromEvents(oldModel);
        SubscribeToEvents(CardNode?.Model);
    }
}
