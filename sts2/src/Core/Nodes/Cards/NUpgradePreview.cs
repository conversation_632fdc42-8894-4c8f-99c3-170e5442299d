using Godot;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Cards.Holders;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;

namespace MegaCrit.Sts2.Core.Nodes.Cards;

/// <summary>
/// Used in Rest Site, Armaments, etc.
/// This is only for upgrades. Not used for previewing enchantments.
/// </summary>
public partial class NUpgradePreview : Control
{
    private Control _before = default!;
    private Control _after = default!;
    private Control _arrows = default!;
    private CardModel? _card;

    public Vector2 SelectedCardPosition => _before.GlobalPosition;

    public CardModel? Card
    {
        get => _card;
        set
        {
            _card = value;
            Reload();
        }
    }

    public override void _Ready()
    {
        _before = GetNode<Control>("%Before");
        _after = GetNode<Control>("%After");
        _arrows = GetNode<Control>("Arrows");
    }

    private void Reload()
    {
        RemoveExistingCards();

        _arrows.Visible = Card != null;
        if (Card == null) return;

        NPlayerHand? hand = NCombatRoom.Instance?.Ui.Hand;
        NPreviewCardHolder beforeCardHolder = NPreviewCardHolder.Create(NCard.Create(Card!)!, hand == null, hand != null)!;
        _before.AddChildSafely(beforeCardHolder);
        beforeCardHolder.FocusMode = FocusModeEnum.All;
        beforeCardHolder.CardNode!.UpdateVisuals(Card.Pile!.Type);

        if (hand != null)
        {
            beforeCardHolder.Connect(NCardHolder.SignalName.Pressed, Callable.From<NCardHolder>(ReturnCard));
        }

        CardModel upgradedCard = Card.CardScope!.CloneCard(Card);
        upgradedCard.UpgradeInternal();
        upgradedCard.UpgradePreviewType = Card.Pile!.IsCombatPile ? CardUpgradePreviewType.Combat : CardUpgradePreviewType.Deck;

        NPreviewCardHolder afterCardHolder = NPreviewCardHolder.Create(NCard.Create(upgradedCard)!, true, false)!;
        afterCardHolder.FocusMode = FocusModeEnum.None;
        _after.AddChildSafely(afterCardHolder);
        afterCardHolder.CardNode!.ShowUpgradePreview();
    }

    /// <summary>
    /// Helper function to remove any existing preview cards
    /// </summary>
    private void RemoveExistingCards()
    {
        foreach (Node child in _before.GetChildren())
        {
            child.QueueFreeSafely();
        }

        foreach (Node child in _after.GetChildren())
        {
            child.QueueFreeSafely();
        }
    }

    private void ReturnCard(NCardHolder holder)
    {
        holder.Pressed -= ReturnCard;
        NCombatRoom.Instance?.Ui.Hand.DeselectCard(holder.CardNode!);
        Card = null;
    }
}
