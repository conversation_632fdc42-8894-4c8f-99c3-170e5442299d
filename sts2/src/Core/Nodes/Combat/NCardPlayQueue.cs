using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Actions;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.GameActions;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Nodes.Cards;
using MegaCrit.Sts2.Core.Nodes.Cards.Holders;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Multiplayer;
using MegaCrit.Sts2.Core.Nodes.Rooms;

namespace MegaCrit.Sts2.Core.Nodes.Combat;

/// <summary>
/// Takes control of card nodes that are about to be played and arranges them in the order of play.
/// </summary>
public partial class NCardPlayQueue : Control
{
    public static NCardPlayQueue? Instance => NCombatRoom.Instance?.Ui.PlayQueue;

    private List<QueueItem> _playQueue = [];

    private class QueueItem
    {
        public required NCard card;
        public int? localHandIndex;
        public PlayCardAction? remoteAction;
        public Tween? currentTween;
    }

    public override void _Ready()
    {
        ClimbManager.Instance.ActionQueueSet.ActionEnqueued += OnActionEnqueued;
    }

    public override void _ExitTree()
    {
        ClimbManager.Instance.ActionQueueSet.ActionEnqueued -= OnActionEnqueued;

        _playQueue.Clear();
    }

    /// <summary>
    /// Called after a card is played by the local player.
    /// If the card is not yet in the play pile, it tweens the card into its queue position.
    /// </summary>
    /// <param name="holder">The card holder that was played.</param>
    /// <param name="handIndex">The index of the card in the hand to return it to, if the card play is canceled.</param>
    public void AfterLocalCardPlayed(NCardHolder holder, int handIndex)
    {
        TaskHelper.RunSafely(MoveLocalCardToQueueAfterSmallDelay(holder, handIndex));
    }

    private async Task MoveLocalCardToQueueAfterSmallDelay(NCardHolder holder, int handIndex)
    {
        // Save the holder's card in case the holder is released during the delay.
        NCard card = holder.CardNode!;

        // If we are a client player, wait for a small amount of time.
        // If we're a remote player, and there's no other cards in the queue, then there's a small delay before the card
        // gets added to the play pile because of the GameAction message roundtrip time. If we immediately start tweening
        // the card, there's a weird scale flicker and a curve as the card starts going to the queue, then gets redirected
        // to the play pile. Instead, add a small delay that is mostly imperceptible.
        if (ClimbManager.Instance.NetService.Type == NetGameType.Client)
        {
            await Cmd.Wait(0.15f);
        }

        // If the card is already in the play pile, then we do not take ownership of it.
        // This could happen because it was moved to the play pile during the delay, or (if we didn't delay) because the
        // play pile was empty and it got moved there immediately after the mouse released it.
        if (card.Model?.Pile?.Type != PileType.Hand) return;

        QueueItem item = new()
        {
            card = card,
            localHandIndex = handIndex
        };

        card.Reparent(this);
        MoveChild(card, 0);

        if (holder.IsValid())
        {
            holder.Clear();
        }

        _playQueue.Add(item);
        TweenCardToQueuePosition(item, _playQueue.Count - 1);
    }

    /// <summary>
    /// Called when any action is enqueued onto the action queue.
    /// Handles cases in which a card is played by a remote player and we want to display the card in the queue.
    /// </summary>
    /// <param name="action">The action that was enqueued.</param>
    private void OnActionEnqueued(GameAction action)
    {
        if (action is not PlayCardAction playCardAction) return;
        if (LocalContext.IsMe(playCardAction.Player)) return; // Handled by AfterLocalCardPlayed

        // Card has been played by a remote player.
        // If the action is a play card action, spawn a card above the player's head where the intent was, and
        // tween it to its position in the queue.
        NCreature creature = NClimb.Instance!.CombatRoom!.GetCreatureNode(playCardAction.Player.Creature)!;
        NMultiplayerPlayerIntentHandler intentHandler = creature.PlayerIntentHandler!;

        // In most cases, the card is "real" at this point. However, sometimes the card is generated by another action
        // (like Blade Dance) and the action is still awaiting execution. In those cases, the card does not exist yet,
        // so ToCardModelOrNull returns null. Fall back to displaying the canonical card in those instances - we'll
        // update the card to the "real" card when the card hits the front of the queue.
        CardModel model = playCardAction.NetCombatCard.ToCardModelOrNull() ?? ModelDb.GetById<CardModel>(playCardAction.CardModelId);
        NCard card = NCard.Create(model)!;

        Vector2 cardPosition = intentHandler.CardIntent.GlobalPosition + intentHandler.CardIntent.Size * 0.5f;
        card.GlobalPosition = cardPosition;

        // Set the card scale and not the holder so that when it gets grabbed by CardPileCmd the tween starts from
        // the small scale
        card.Scale = Vector2.One * 0.25f;

        this.AddChildSafely(card);
        MoveChild(card, 0);

        QueueItem item = new()
        {
            card = card,
            remoteAction = playCardAction
        };

        _playQueue.Add(item);
        UpdateCardVisuals(item);
        TweenCardToQueuePosition(item, _playQueue.Count - 1);
    }

    /// <summary>
    /// After remote player choice is complete, this re-adds the card back into the queue if necessary.
    /// Note that this is not called for local player choice - that just sits in the center of the screen.
    /// </summary>
    /// <param name="card">The card that finished player choice.</param>
    /// <param name="action">The action that ran the player choice.</param>
    public void ReAddCardAfterPlayerChoice(NCard card, PlayCardAction action)
    {
        // If the action is already being executed, do not take ownership of the card. Animate it straight to the play pile.
        if (action.State == GameActionState.Executing)
        {
            card.Reparent(NCombatRoom.Instance!.Ui.PlayContainer);
            card.AnimCardToPlayPile();
            return;
        }

        QueueItem item = new()
        {
            card = card,
            remoteAction = action
        };

        card.Reparent(this);
        MoveChild(card, 0);

        _playQueue.Add(item);
        TweenCardToQueuePosition(item, _playQueue.Count - 1);

        action.BeforeResumedAfterPlayerChoice += BeforeRemoteCardPlayResumedAfterPlayerChoice;
    }

    /// <summary>
    /// This is a bit of a hack. When a card resumes execution after player choice, there's nothing around to tell the
    /// card that it needs to animate back to the play pile. We do it here.
    /// </summary>
    /// <param name="action">Action that finished player choice.</param>
    private void BeforeRemoteCardPlayResumedAfterPlayerChoice(GameAction action)
    {
        action.BeforeResumedAfterPlayerChoice -= BeforeRemoteCardPlayResumedAfterPlayerChoice;

        int index = _playQueue.FindIndex(i => i.remoteAction == action);
        if (index < 0) return;

        QueueItem item = _playQueue[index];
        RemoveCardFromQueue(index);

        item.card.Reparent(NCombatRoom.Instance!.Ui.PlayContainer);
        item.card.AnimCardToPlayPile();
    }

    /// <summary>
    /// Called when a card play is canceled.
    /// When a card play is canceled (e.g. because it's now targeting something invalid), this removes the card from the
    /// queue. If the card was a local card, then it is returned to the hand.
    /// </summary>
    /// <param name="card">The card that was canceled.</param>
    public void RemoveCardFromQueueForCancellation(CardModel card)
    {
        int index = _playQueue.FindIndex(i => i.card.Model == card);
        if (index < 0) return;

        QueueItem item = _playQueue[index];
        RemoveCardFromQueue(index);

        if (item.localHandIndex != null)
        {
            // If this was from the local player hand, then it should take the card back
            if (card.Pile?.Type == PileType.Hand)
            {
                NPlayerHand.Instance!.Add(item.card, item.localHandIndex.Value);
            }
        }
        else
        {
            // Otherwise, this is a remote card, and we can just remove it
            item.card.QueueFreeSafely();
        }
    }

    /// <summary>
    /// Called when a card begins execution.
    /// The play pile (specifically CardPileCmd.Add) takes control of the NCard when it begins executing. This method
    /// relinquishes control of the NCard, but does nothing with the node afterward - it is left where it is.
    /// </summary>
    /// <param name="card">The card that was canceled.</param>
    public void RemoveCardFromQueueForExecution(CardModel card)
    {
        int index = _playQueue.FindIndex(i => i.card.Model == card);
        if (index < 0) throw new InvalidOperationException();

        QueueItem item = _playQueue[index];

        if (item.remoteAction != null)
        {
            // This gets called just before the action gets executed. If the card was dynamically created in combat but not
            // yet created on our peer, then it was created with a placeholder card. By this time, it should be available.
            item.card.Model = item.remoteAction.NetCombatCard.ToCardModel();
            UpdateCardVisuals(item);
        }

        // Remove the item from the queue - the CardPileCmd handles it from here.
        RemoveCardFromQueue(index);
    }

    private void UpdateCardVisuals(QueueItem item)
    {
        if (item.remoteAction != null)
        {
            item.card.SetPreviewTarget(item.remoteAction.Target);
        }

        item.card.UpdateVisuals(item.card.Model!.Pile?.Type ?? PileType.None);
    }

    private void RemoveCardFromQueue(NCard card)
    {
        int index = _playQueue.FindIndex(i => i.card == card);
        if (index < 0) return;
        RemoveCardFromQueue(index);
    }

    private void RemoveCardFromQueue(int index)
    {
        QueueItem item = _playQueue[index];

        item.currentTween?.Kill();
        _playQueue.RemoveAt(index);
        TweenAllToQueuePosition();
    }

    private void TweenAllToQueuePosition()
    {
        for (int i = 0; i < _playQueue.Count; i++)
        {
            TweenCardToQueuePosition(_playQueue[i], i);
        }
    }

    /// <summary>
    /// Returns the NCard representing the card if it is in the queue.
    /// Remember that:
    ///  - Cards that are being dragged are in the NPlayerHand
    ///  - Cards that are being executed are in NCombatUi.PlayContainer
    /// This only returns cards that are enqueued, but not yet executing.
    /// </summary>
    /// <param name="card">The card whose node we will return.</param>
    /// <returns>The node for the card, if it is in the play queue.</returns>
    public NCard? GetCardNode(CardModel card)
    {
        return _playQueue.FirstOrDefault(i => i.card.Model == card)?.card;
    }

    private void TweenCardToQueuePosition(QueueItem item, int queueIndex)
    {
        // Always kill the previous tween.
        item.currentTween?.Kill();

        item.currentTween = CreateTween().SetParallel();
        item.currentTween.TweenProperty(item.card, "position", GetPositionForQueueIndex(item.card, queueIndex), 0.35f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic);
        item.currentTween.TweenProperty(item.card, "scale", GetScaleForQueueIndex(queueIndex), 0.35f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic);
        item.currentTween.TweenProperty(item.card, "modulate:a", 1f, 0.35f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic);
        item.currentTween.Play();
    }

    private Vector2 GetScaleForQueueIndex(int index)
    {
        // Shift index by one; the one-scaled card is the one in the play pile
        index++;

        // Produces a function which starts at 1 and approaches zero
        float interpolation = 1 - (float)index / (index + 1);
        return interpolation * Vector2.One * 0.8f;
    }

    private Vector2 GetPositionForQueueIndex(NCard card, int index)
    {
        const float leftmostPosition = 300f;

        // Shift index by one; the centered card is the one in the play pile
        index++;

        // Produces a function which starts at 0 and approaches one
        float interpolation = (float)index / (index + 2);
        return PileType.Play.GetTargetPosition(card) +  Vector2.Left * leftmostPosition * interpolation;
    }
}
