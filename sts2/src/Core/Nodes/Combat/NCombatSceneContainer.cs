using Godot;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Settings;

namespace MegaCrit.Sts2.Core.Nodes.Combat;

/// <summary>
/// Resizes BgContainer of the combat room so it fits well in all aspect ratios.
/// TODO: Expand to adjust for enemy placement.
/// </summary>
public partial class NCombatSceneContainer : Control
{
    private const float _sixteenByNine = 16f / 9f;
    private const float _maxNarrowRatio = 4f / 3f;
    private Window _window = default!;
    private Control _bgContainer = default!;

    public override void _Ready()
    {
        _bgContainer = GetNode<Control>("%BgContainer");
        _window = GetTree().Root;
        _window.Connect(Viewport.SignalName.SizeChanged, Callable.From(OnWindowChange));
    }

    private void OnWindowChange()
    {
        float ratio = (float)_window.Size.X / _window.Size.Y;

        // Scale up the Bg if the aspect ratio is narrower than 16:9
        if (ratio < _sixteenByNine && SaveManager.Instance.SettingsSave.AspectRatioSetting == AspectRatioSetting.Auto)
        {
            _bgContainer.Scale = Vector2.One * Mathf.Max(MathHelper.Remap(ratio, _maxNarrowRatio, _sixteenByNine, 1.08f, 0.9f), 1.08f);
        }
        else
        {
            _bgContainer.Scale = Vector2.One * 0.9f;
        }
    }
}
