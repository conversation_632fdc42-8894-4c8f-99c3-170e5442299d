using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.HoverTips;
using MegaCrit.Sts2.Core.Nodes.Multiplayer;
using MegaCrit.Sts2.Core.Nodes.Orbs;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Settings;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Combat;

public partial class NCreature : Node2D
{
    private static readonly string _scenePath = SceneHelper.GetScenePath("combat/creature");
    public static IEnumerable<string> AssetPaths => [_scenePath];

    private NCreatureVisuals _visuals = default!;
    private NCreatureStateDisplay _stateDisplay = default!;
    private Control _intentContainer = default!;

    private Tween? _intentFadeTween;
    private Tween? _shakeTween;

    private SpineAnimator? _spineAnimator;
    private bool _isFocused;
    private bool _isRemotePlayerOrPet;

    private float _tempScale = 1;
    private Tween? _scaleTween;

    public Task? DeathAnimationTask { get; set; }
    public CancellationTokenSource DeathAnimCancelToken { get; } = new();

    public Control Hitbox { get; private set; } = default!;
    public NOrbManager? OrbManager { get; private set; }
    public NSelectionReticle SelectionReticle { get; private set; } = default!;
    public bool IsInteractable { get; private set; } = true;
    public Creature Entity { get; private set; } = default!;

    public Vector2 VfxSpawnPosition => _visuals.VfxSpawnPosition.GlobalPosition;
    public NCreatureVisuals Visuals => _visuals;
    public Node2D Body => _visuals.Body;
    public Control IntentContainer => _intentContainer;
    public bool IsPlayingDeathAnimation => DeathAnimationTask != null;
    public bool HasSpineAnimation => _visuals.HasSpineAnimation;
    public SpineSprite? SpineController => _visuals.SpineBody;
    public bool IsFocused => _isFocused;
    public NMultiplayerPlayerIntentHandler? PlayerIntentHandler { get; private set; }

    // TODO: Used to get nodes specific to this creature visual
    // ex: position markers for specific effects
    public T? GetSpecialNode<T>(string name) where T : Node => _visuals.GetNode<T>(name);

    public static NCreature? Create(Creature entity)
    {
        if (TestMode.IsOn) return null;

        NCreature node = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NCreature>();
        node.Entity = entity;
        node._visuals = entity.CreateVisuals()!;

        return node;
    }

    public override void _Ready()
    {
        _stateDisplay = GetNode<NCreatureStateDisplay>("%HealthBar");
        _intentContainer = GetNode<Control>("%Intents");
        Hitbox = GetNode<Control>("%Hitbox");
        SelectionReticle = GetNode<NSelectionReticle>("%SelectionReticle");

        Hitbox.Connect(Control.SignalName.FocusEntered, Callable.From(OnFocus));
        Hitbox.Connect(Control.SignalName.FocusExited, Callable.From(OnUnfocus));

        Hitbox.Connect(Control.SignalName.MouseEntered, Callable.From(OnFocus));
        Hitbox.Connect(Control.SignalName.MouseExited, Callable.From(OnUnfocus));

        if (Entity.IsPlayer)
        {
            OrbManager = NOrbManager.Create(Entity.Player!, LocalContext.IsMe(Entity));
            this.AddChildSafely(OrbManager);
        }

        bool isMultiplayerPlayer = Entity.IsPlayer && Entity.CombatState?.ClimbState.Players.Count > 1;

        if (isMultiplayerPlayer)
        {
            PlayerIntentHandler = NMultiplayerPlayerIntentHandler.Create(Entity.Player!);
            if (PlayerIntentHandler != null)
            {
                _intentContainer.AddChildSafely(PlayerIntentHandler);
                _intentContainer.Modulate = Colors.White;
            }
        }

        this.AddChildSafely(_visuals);
        MoveChild(_visuals, 0);

        _stateDisplay.SetCreature(Entity);
        _stateDisplay.SetCreatureBounds(_visuals.Bounds);

        // On the player side, we only display the local player's HP bar and the local player's pets' HP bars
        bool isNonLocalPlayerPet = Entity.PetOwner != null && !LocalContext.IsMe(Entity.PetOwner);
        bool isNonLocalPlayer = Entity.IsPlayer && !LocalContext.IsMe(Entity);
        _isRemotePlayerOrPet = isNonLocalPlayer || isNonLocalPlayerPet;

        if (_isRemotePlayerOrPet)
        {
            _stateDisplay.HideImmediately();
        }
        else
        {
            _stateDisplay.AnimateIn(true);
        }

        if (HasSpineAnimation)
        {
            if (Entity.Player != null)
            {
                _spineAnimator = Entity.Player.Character.GenerateSpineAnimator(SpineController!);
            }
            else
            {
                _spineAnimator = Entity.Monster!.GenerateSpineAnimator(SpineController!);
                _visuals.SetUpSkin(Entity.Monster);
            }

            _spineAnimator.BoundsUpdated += UpdateBounds;

            // If the creature entered combat dead, then immediately show as dead
            if (Entity.IsDead)
            {
                SetAnimationTrigger(SpineAnimator.deathTrigger);
                SpineTrackEntry trackEntry = Visuals.SpineBody!.GetAnimationState().GetCurrent(0);
                trackEntry.SetTrackTime(trackEntry.GetAnimationEnd());
            }
        }

        SetOrbManagerPosition();

        if (Entity.Monster != null)
        {
            ToggleIsInteractable(Entity.Monster.IsHealthBarVisible);
        }

        UpdateBounds(_visuals);
    }

    public override void _EnterTree()
    {
        base._EnterTree();

        CombatManager.Instance.CombatEnded += OnCombatEnded;
        Entity.PowerApplied += OnPowerApplied;
        Entity.PowerRemoved += OnPowerRemoved;
        Entity.PowerIncreased += OnPowerIncreased;

        foreach (PowerModel power in Entity.Powers)
        {
            SubscribeToPower(power);
        }

        // This subscription already exists in _Ready, but we duplicate it here in case the node gets reparented.
        if (_spineAnimator != null)
        {
            _spineAnimator.BoundsUpdated += UpdateBounds;
        }
    }

    public override void _ExitTree()
    {
        base._ExitTree();

        DeathAnimCancelToken.Cancel();

        CombatManager.Instance.CombatEnded -= OnCombatEnded;
        Entity.PowerApplied -= OnPowerApplied;
        Entity.PowerRemoved -= OnPowerRemoved;
        Entity.PowerIncreased -= OnPowerIncreased;

        foreach (PowerModel power in Entity.Powers)
        {
            UnsubscribeFromPower(power);
        }

        if (_spineAnimator != null)
        {
            _spineAnimator.BoundsUpdated -= UpdateBounds;
        }

        // If the creature dies while focused, OnUnfocused never gets called and this doesn't get unsubscribed
        CombatManager.Instance.StateTracker.CombatStateChanged -= ShowCreatureHoverTips;
    }

    private void UpdateBounds(string boundsNodeName)
    {
        UpdateBounds(_visuals.GetNode<Control>(boundsNodeName));
    }

    private void UpdateBounds(Node boundsContainer)
    {
        Control boundsNode = boundsContainer.GetNode<Control>("Bounds");
        Hitbox.Size = boundsNode.Size * boundsNode.GetGlobalTransform().Scale;
        Hitbox.GlobalPosition = boundsNode.GlobalPosition;
        SelectionReticle.Size = boundsNode.Size * boundsNode.GetGlobalTransform().Scale;
        SelectionReticle.GlobalPosition = boundsNode.GlobalPosition;

        _intentContainer.Position = boundsContainer.GetNode<Marker2D>("IntentPos").Position - _intentContainer.Size / 2f;
        _stateDisplay.SetCreatureBounds(boundsNode);
    }

    public Task UpdateIntent(IEnumerable<Creature> targets)
    {
        if (Entity.Monster == null) throw new InvalidOperationException("Only valid on monsters.");

        // When we update intents, we reuse the ones we can and delete the ones we can't.
        // We used to re-create them every time, which was simpler, but made sudden intent changes cause visual jitter,
        // as it reset the intent positions and animations every time.
        IReadOnlyList<AbstractIntent> intents = Entity.Monster.NextMove.Intents;

        int i = 0;

        // Step 1: Update the intent nodes that exist
        while (i < intents.Count && i < _intentContainer.GetChildCount())
        {
            NIntent intent = _intentContainer.GetChild<NIntent>(i);
            intent.SetFrozen(false);
            intent.UpdateIntent(intents[i], targets, Entity);
            i++;
        }


        // Step 2: Create any new Intent nodes if we need them.

        // Randomizes the animation on each intent group based on its hash
        // that way it is still varied between monsters, but the consistently  per creature.
        float randomStartOffset = GetHashCode() / 100f;

        while (i < intents.Count)
        {
            NIntent node = NIntent.Create(randomStartOffset + i * 0.3f);
            _intentContainer.AddChildSafely(node);
            node.UpdateIntent(intents[i], targets, Entity);
            i++;
        }

        // Step 3: Delete any intents nodes we no longer need.
        // We store the nodes in a temporary list just in case something externally affects the nodes in the container.
        List<Node> unusedIntents = _intentContainer.GetChildren().TakeLast(_intentContainer.GetChildCount() - i).ToList();
        foreach (Node unusedIntent in unusedIntents)
        {
            unusedIntent.QueueFreeSafely();
        }

        return Task.CompletedTask;
    }

    public async Task PerformIntent()
    {
        foreach (NIntent child in _intentContainer.GetChildren().OfType<NIntent>())
        {
            child.PlayPerform();

            // Don't allow the intent to update while the creature is performing the move. It can cause unpleasant pops
            // when the enemy is e.g. both attacking and gaining strength.
            child.SetFrozen(true);
        }

        if (SaveManager.Instance.SettingsSave.FastMode == FastModeType.Instant)
        {
            _intentContainer.Modulate = new Color(_intentContainer.Modulate.R, _intentContainer.Modulate.G, _intentContainer.Modulate.B, 0f);
            return;
        }

        AnimHideIntent(0.4f);

        await Cmd.CustomScaledWait(0.25f, 0.4f, 0.6f);
    }

    public async Task RefreshIntents()
    {
        await UpdateIntent(Entity.CombatState!.Players.Select(p => p.Creature));
        await RevealIntents();
    }

    /// <summary>
    /// This makes the intents visible again!
    /// So let's fade them in.
    /// </summary>
    private Task RevealIntents()
    {
        _intentContainer.Modulate = Colors.Transparent;

        _intentFadeTween?.Kill();
        _intentFadeTween = CreateTween().SetParallel();
        _intentFadeTween
            .TweenProperty(_intentContainer, "modulate:a", 1f, 1f)
            .SetDelay(Rng.Chaotic.NextFloat(0f, 0.3f));

        return Task.CompletedTask;
    }

    private void OnFocus()
    {
        if (_isFocused) return;

        _isFocused = true;

        if (_isRemotePlayerOrPet)
        {
            _stateDisplay.AnimateIn(false);
            _stateDisplay.ZIndex = 1;
            Player? localPlayer = LocalContext.GetMe(Entity.CombatState);
            NCombatRoom.Instance?.GetCreatureNode(localPlayer?.Creature)?.SetRemotePlayerFocused(true);
        }
        else
        {
            // If we're a remote player, then our nameplate at the top-left will get highlighted in addition
            _stateDisplay.ShowNameplate();
        }

        NClimb.Instance!.GlobalUi.MultiplayerPlayerContainer.HighlightPlayer(Entity.Player!);

        if (NTargetManager.Instance.IsInSelection)
        {
            NTargetManager.Instance.OnNodeHovered(this);
        }
        else
        {
            if (NControllerManager.Instance!.IsUsingController)
            {
                SelectionReticle.OnSelect();
            }

            ShowHoverTips(Entity.HoverTips);
            CombatManager.Instance.StateTracker.CombatStateChanged += ShowCreatureHoverTips;
        }
    }

    private void OnUnfocus()
    {
        _isFocused = false;

        if (NControllerManager.Instance!.IsUsingController)
        {
            SelectionReticle.OnDeselect();
        }

        if (_isRemotePlayerOrPet)
        {
            _stateDisplay.AnimateOut();
            Player? localPlayer = LocalContext.GetMe(Entity.CombatState);
            NCombatRoom.Instance?.GetCreatureNode(localPlayer?.Creature)?.SetRemotePlayerFocused(false);
        }
        else
        {
            _stateDisplay.HideNameplate();
        }

        NClimb.Instance!.GlobalUi.MultiplayerPlayerContainer.UnhighlightPlayer(Entity.Player!);
        NTargetManager.Instance.OnNodeUnhovered(this);
        CombatManager.Instance.StateTracker.CombatStateChanged -= ShowCreatureHoverTips;
        HideHoverTips();
    }

    // Called when NTargetManager begins targeting during combat. Targets the creature if it is already focused.
    public void OnTargetingStarted()
    {
        if (!IsFocused) return;

        NTargetManager.Instance.OnNodeHovered(this);
        CombatManager.Instance.StateTracker.CombatStateChanged -= ShowCreatureHoverTips;
        HideHoverTips();
    }

    private void ShowCreatureHoverTips(CombatState _)
    {
        ShowHoverTips(Entity.HoverTips);
    }

    public void ShowHoverTips(IEnumerable<IHoverTip> hoverTips)
    {
        if (NCombatRoom.Instance!.Ui.Hand.InCardPlay) return;

        HideHoverTips();
        NHoverTipSet.CreateAndShow(Hitbox, hoverTips, HoverTip.GetHoverTipAlignment(this, 0.5f));
    }

    public void SetRemotePlayerFocused(bool remotePlayerFocused)
    {
        if (!LocalContext.IsMe(Entity))
        {
            throw new InvalidOperationException("This should only be called on the local player's creature node!");
        }

        // Animate our display away when the remote player is focused
        if (remotePlayerFocused)
        {
            _stateDisplay.AnimateOut();
        }
        // If the remote player is unfocused, animate our display back in, but only if we're still alive
        else if (Entity.IsAlive)
        {
            _stateDisplay.AnimateIn(false);
        }
    }

    public void HideHoverTips()
    {
        NHoverTipSet.Remove(Hitbox);
    }

    private void SubscribeToPower(PowerModel power)
    {
        power.Flashed += OnPowerFlashed;
    }

    private void UnsubscribeFromPower(PowerModel power)
    {
        power.Flashed -= OnPowerFlashed;
    }

    private void OnPowerApplied(PowerModel power)
    {
        SubscribeToPower(power);
    }

    private void OnPowerIncreased(PowerModel power, int amount)
    {
        // assumes this means the power applied was one that the monster initially starts with,
        // so we don't want to play any animations here.
        if (!CombatManager.Instance.IsInProgress) return;

        NCombatRoom.Instance?.CombatVfxContainer.AddChildSafely(NPowerAppliedVfx.Create(power, amount));
        NDebugAudioManager.Instance?.Play(power.GetTypeForAmount(amount) == PowerType.Buff ? TmpSfx.buff : TmpSfx.debuff);

        if (power.ShouldVisiblyDebuff &&
            power.GetTypeForAmount(power.Amount) == PowerType.Debuff)
        {
            AnimShake();
        }
    }

    private void OnPowerRemoved(PowerModel power)
    {
        NCombatRoom.Instance?.CombatVfxContainer.AddChildSafely(NPowerRemovedVfx.Create(power));
        UnsubscribeFromPower(power);
    }

    private void OnPowerFlashed(PowerModel power)
    {
        NCombatRoom.Instance?.CombatVfxContainer.AddChildSafely(NPowerFlashVfx.Create(power));
    }

    private void OnCombatEnded(CombatRoom _)
    {
        AnimHideIntent();
    }

    public void SetAnimationTrigger(string trigger)
    {
        _spineAnimator?.SetTrigger(trigger);
    }

    public float GetCurrentAnimationLength()
    {
        return SpineController!.GetAnimationState().GetCurrent(0).GetAnimation().GetDuration();
    }

    public float GetCurrentAnimationTimeRemaining()
    {
        SpineTrackEntry entry = SpineController!.GetAnimationState().GetCurrent(0);
        return entry.GetTrackComplete() - entry.GetTrackTime();
    }

    // This is a way to make a creature un-interactable.
    // Example: Decimillipede Segments that were killed, but that are going to reattach.
    public void ToggleIsInteractable(bool on)
    {
        IsInteractable = on;
        _stateDisplay.Visible = !NCombatUi.IsDebugHidingHpBar && on;
        Hitbox.MouseFilter = on ? Control.MouseFilterEnum.Stop : Control.MouseFilterEnum.Ignore;
    }

    public Tween AnimDisableUi()
    {
        if (!IsNodeReady()) return CreateTween();

        Tween hideUiTween = CreateTween();
        hideUiTween.TweenProperty(_stateDisplay, "modulate:a", 0f, 0.5f)
            .SetDelay(0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);

        return hideUiTween;
    }

    public Tween AnimEnableUi()
    {
        Tween showUiTween = CreateTween();
        showUiTween.TweenProperty(_stateDisplay, "modulate:a", 1f, 0.5f)
            .SetDelay(0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);

        return showUiTween;
    }

    public float StartDeathAnim(bool shouldRemove)
    {
        if (DeathAnimationTask is { IsCompleted: false }) return 0;

        float deathLength = 0;

        if (_spineAnimator != null)
        {
            if (Entity.Monster?.HasDeathSfx ?? false)
            {
                SfxCmd.PlayDeath(Entity.Monster!);
            }

            if (Entity.Player != null)
            {
                SfxCmd.PlayDeath(Entity.Player!);
            }

            SetAnimationTrigger(SpineAnimator.deathTrigger);
            deathLength = GetCurrentAnimationLength();
        }

        DeathAnimationTask = AnimDie(shouldRemove, DeathAnimCancelToken.Token);
        TaskHelper.RunSafely(DeathAnimationTask);

        return Mathf.Min(deathLength, 30f);
    }

    public void StartReviveAnim()
    {
        SetAnimationTrigger(SpineAnimator.reviveTrigger);

        // On the player side, we only display the local player's HP bar and the local player's pets' HP bars
        if (!_isRemotePlayerOrPet)
        {
            AnimEnableUi();
        }

        Hitbox.MouseFilter = Control.MouseFilterEnum.Stop;
    }

    private async Task AnimDie(bool shouldRemove, CancellationToken cancelToken)
    {
        Tween disableUiTween = AnimDisableUi();

        Hitbox.MouseFilter = Control.MouseFilterEnum.Ignore;

        if (!ClimbManager.Instance.IsSinglePlayerOrFakeMultiplayer)
        {
            OrbManager?.ClearOrbs();
        }

        if (shouldRemove)
        {
            AnimHideIntent();
        }

        if (_spineAnimator != null)
        {
            float waitTime = Math.Min(GetCurrentAnimationTimeRemaining() + 0.5f, 20f);
            await Cmd.Wait(waitTime, cancelToken, true);
            if (cancelToken.IsCancellationRequested) return;
        }

        if (shouldRemove)
        {
            Task? fadeVfx = null;
            if (Entity.Monster is { ShouldFadeAfterDeath: true } && Body.IsVisibleInTree())
            {
                NMonsterDeathVfx? vfx = NMonsterDeathVfx.Create(this, cancelToken);
                NCombatRoom.Instance?.CombatVfxContainer.AddChildSafely(vfx);
                fadeVfx = vfx?.PlayVfx();
            }

            if (disableUiTween.IsValid() && disableUiTween.IsRunning())
            {
                await ToSignal(disableUiTween, Tween.SignalName.Finished);
            }

            foreach (IDeathDelayer delayer in this.GetChildrenRecursive<IDeathDelayer>())
            {
                await delayer.GetDelayTask();
            }

            if (fadeVfx != null)
            {
                await fadeVfx;
            }

            this.QueueFreeSafely();
        }

        if (Entity.Monster is Osty)
        {
            OstyScaleToSize(0, 0.75f);
        }
    }

    public void AnimHideIntent(float delay = 0f)
    {
        _intentFadeTween?.Kill();
        _intentFadeTween = CreateTween().SetParallel();
        PropertyTweener prop = _intentFadeTween.TweenProperty(_intentContainer, "modulate:a", 0f, 0.5f);

        if (delay > 0f)
        {
            prop.SetDelay(delay);
        }
    }

    public void SetScaleAndHue(float scale, float hue)
    {
        _visuals.SetScaleAndHue(scale, hue);
        UpdateBounds(_visuals);
    }

    public void ScaleTo(float size, float duration)
    {
        if (Entity.IsMonster && !Entity.Monster!.CanChangeScale) return;

        _tempScale = size;
        _scaleTween?.Kill();
        _scaleTween = CreateTween();
        _scaleTween.TweenMethod(Callable.From<Vector2>(DoScaleTween), _visuals.Scale, Vector2.One * _tempScale * _visuals.DefaultScale, duration)
            .SetEase(Tween.EaseType.InOut)
            .SetTrans(Tween.TransitionType.Sine);
    }

    public void SetDefaultScaleTo(float size, float duration)
    {
        if (Entity.IsMonster && !Entity.Monster!.CanChangeScale) return;

        _visuals.DefaultScale = size;
        ScaleTo(_tempScale, duration);
    }

    public void OstyScaleToSize(float ostyHealth, float duration)
    {
        float ostySize = Mathf.Lerp(Osty.ScaleRange.X, Osty.ScaleRange.Y, Mathf.Clamp(ostyHealth / 150.0f, 0, 1));
        Vector2 ostyPosition = Osty.MinOffset.Lerp(Osty.MaxOffset, Mathf.Clamp(ostyHealth / 150.0f, 0, 1));

        NCreature owner = NCombatRoom.Instance?.GetCreatureNode(Entity.PetOwner!.Creature)!;
        _scaleTween = CreateTween();
        _scaleTween.TweenProperty(_visuals, "scale", Vector2.One * ostySize * _visuals.DefaultScale, duration)
            .SetEase(Tween.EaseType.InOut)
            .SetTrans(Tween.TransitionType.Sine);

        // only scoot osty forward if you are the local player. this is so it doesn't modify the offset for remote Osty's.
        // It still looks a little jank (full size osty blocks necro), so we will need to revisit this in the future.
        if (LocalContext.IsMe(Entity.PetOwner))
        {
            _scaleTween.Parallel().TweenProperty(this, "position", owner.Position + Vector2.Right * owner.Hitbox.Size.X * 0.5f + ostyPosition, duration);
        }

        _scaleTween.TweenCallback(Callable.From(() => UpdateBounds(_visuals)));
    }

    public void AnimShake()
    {
        if (_shakeTween != null && _shakeTween.IsRunning()) return;
        if (_visuals.IsPlayingHurtAnimation()) return;

        _visuals.Position = Vector2.Zero;
        _shakeTween = CreateTween();
        _shakeTween.TweenMethod(Callable.From<float>(t => { _visuals.Position = Vector2.Right * 10f * Mathf.Sin(t * 4) * Mathf.Sin(t / 2f); }), 0f, 2.0f * Mathf.Pi, 1f)
            .SetEase(Tween.EaseType.Out).SetTrans(Tween.TransitionType.Cubic);
    }

    private void DoScaleTween(Vector2 scale)
    {
        _visuals.Scale = scale;
        SetOrbManagerPosition();
    }

    private void SetOrbManagerPosition()
    {
        if (OrbManager == null) return;

        // If visual scale is greater than one, don't make the orbs super huge
        // If we're shrunken, don't shrink the orbs to an unmanageable degree
        OrbManager.Scale = _visuals.Scale.X > 1f ? Vector2.One : _visuals.Scale.Lerp(Vector2.One, 0.5f);

        OrbManager.Position = _visuals.OrbPosition.Position * _visuals.Scale;
        if (!OrbManager.IsLocal)
        {
            OrbManager.Position += Vector2.Up * 50;
        }
    }

    /// <summary>
    /// Helper function to get the top of this creature's hitbox position.
    /// Used for spawning vfx or aligning UI elements to a creature's hitbox.
    /// </summary>
    /// <returns></returns>
    public Vector2 GetTopOfHitbox()
    {
        return Hitbox.GlobalPosition + new Vector2(Hitbox.Size.X * 0.5f, 0f);
    }

    /// <summary>
    /// Helper function to get the top of this creature's hitbox position.
    /// Used for spawning vfx or aligning UI elements to a creature's hitbox.
    /// </summary>
    /// <returns></returns>
    public Vector2 GetBottomOfHitbox()
    {
        return Hitbox.GlobalPosition + new Vector2(Hitbox.Size.X * 0.5f, Hitbox.Size.Y);
    }

    /// <summary>
    /// Track the block status of another creature.
    /// Used for pets who want to show extra UI when their owner has block.
    /// For example, Osty tracks Necrobinder's block.
    /// </summary>
    /// <param name="creature">Extra creature (different from _creature) whose block status we want to track in this UI.</param>
    public void TrackBlockStatus(Creature creature) => _stateDisplay.TrackBlockStatus(creature);
}
