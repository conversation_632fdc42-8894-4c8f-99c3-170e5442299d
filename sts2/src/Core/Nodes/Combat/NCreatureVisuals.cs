using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Models;

namespace MegaCrit.Sts2.Core.Nodes.Combat;

public partial class NCreatureVisuals : Node2D
{
    public Node2D Body { get; private set; } = default!;
    public Control Bounds { get; private set; } = default!;
    public Marker2D IntentPosition { get; private set; } = default!;
    public Marker2D OrbPosition { get; private set; } = default!;

    public Marker2D? TalkPosition { get; private set; }

    public bool HasSpineAnimation => Body is SpineSprite;

    // Can be null if we don't have a spine animation for the creature yet
    public SpineSprite? SpineBody { get; private set; }

    // Position we spawn things like hit vfx. Sometimes the center of the creatures isn't always the center of the
    // visual.
    public Marker2D VfxSpawnPosition { get; private set; } = default!;

    public float DefaultScale { get; set; } = 1f;

    public override void _Ready()
    {
        Body = GetNode<Node2D>("%Visuals");
        Bounds = GetNode<Control>("%Bounds");
        IntentPosition = GetNode<Marker2D>("%IntentPos");
        VfxSpawnPosition = GetNode<Marker2D>("%CenterPos");
        OrbPosition = HasNode("%OrbPos") ? GetNode<Marker2D>("%OrbPos") : IntentPosition;
        TalkPosition = HasNode("%TalkPos") ? GetNode<Marker2D>("%TalkPos") : null;
        if (HasSpineAnimation)
        {
            SpineBody = GetNode<SpineSprite>("%Visuals");
        }
    }

    public void SetUpSkin(MonsterModel model)
    {
        if (SpineBody?.GetSkeleton() == null) return;

        model.SetupSkins();
    }

    public void SetScaleAndHue(float scale, float hue)
    {
        DefaultScale = scale;
        Scale = Vector2.One * scale;

        if (!Mathf.IsEqualApprox(hue, 0f))
        {
            if (SpineBody != null)
            {
                ShaderMaterial hsvMat;

                if (SpineBody.NormalMaterial == null)
                {
                    Material cachedMat = (ShaderMaterial)PreloadManager.Cache.GetMaterial("res://materials/vfx/hsv.tres");
                    hsvMat = (ShaderMaterial)cachedMat.Duplicate();
                    SpineBody.NormalMaterial = hsvMat;
                }
                else
                {
                    hsvMat = (ShaderMaterial)SpineBody.NormalMaterial;
                }

                hsvMat.SetShaderParameter("h", hue);
            }
        }
    }

    public bool IsPlayingHurtAnimation()
    {
        if (SpineBody?.GetSkeleton() != null)
        {
            return SpineBody.GetAnimationState().GetCurrent(0).GetAnimation().GetName().Equals("hurt");
        }

        return false;
    }
}
