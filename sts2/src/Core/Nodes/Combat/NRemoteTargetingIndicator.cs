using Godot;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;

namespace MegaCrit.Sts2.Core.Nodes.Combat;

public partial class NRemoteTargetingIndicator : Node2D
{
    private const int _segmentCount = 100;
    private const float _defaultAlpha = 0.5f;
    private const float _targetingAlpha = 1f;

    private Player _player = default!;

    private Vector2 _fromPosition;
    private Vector2 _toPosition;

    private Line2D _line = default!;
    private Line2D _lineBack = default!;

    private Tween? _tween;
    private bool _isTargetingCreature;

    public override void _Ready()
    {
        _line = GetNode<Line2D>("Line");
        _lineBack = GetNode<Line2D>("LineBack");

        for (int i = 0; i < _segmentCount + 1; i++)
        {
            _line.AddPoint(Vector2.Zero);
            _lineBack.AddPoint(Vector2.Zero);
        }

        StopDrawing();
    }

    public void Initialize(Player player)
    {
        _player = player;

        CharacterModel character = player.Character;
        _line.DefaultColor = character.RemoteTargetingLineColor;
        _lineBack.DefaultColor = character.RemoteTargetingLineOutline;

        Gradient gradient = _line.GetGradient();

        if (gradient != null)
        {
            for (int i = 0; i < gradient.GetPointCount(); i++)
            {
                gradient.SetColor(i, gradient.GetColor(i) * character.RemoteTargetingLineColor);
            }

            _line.SetGradient(gradient);
        }

        Gradient gradientBack = _lineBack.GetGradient();

        if (gradientBack != null)
        {
            for (int i = 0; i < gradientBack.GetPointCount(); i++)
            {
                gradientBack.SetColor(i, gradientBack.GetColor(i) * character.RemoteTargetingLineOutline);
            }

            _lineBack.SetGradient(gradient);
        }
    }

    public override void _Process(double delta)
    {
        Vector2 controlPoint = Vector2.Zero;
        controlPoint.X = _fromPosition.X + (_toPosition.X - _fromPosition.X) * 0.5f;
        controlPoint.Y = _fromPosition.Y - (_toPosition.Y - _fromPosition.Y) * 0.5f;

        for (int i = 0; i < _segmentCount; i++)
        {
            Vector2 pointPos = MathHelper.BezierCurve(_fromPosition, _toPosition, controlPoint, i / (_segmentCount + 1f));
            _line.SetPointPosition(i, pointPos);
            _lineBack.SetPointPosition(i, pointPos);
        }

        _line.SetPointPosition(_segmentCount, _toPosition);
        _lineBack.SetPointPosition(_segmentCount, _toPosition);

        bool isTargetingCreature = false;

        foreach (Creature creature in _player.Creature.CombatState?.Enemies ?? [])
        {
            NCreature? creatureNode = NClimb.Instance!.CombatRoom!.GetCreatureNode(creature);

            if (creatureNode != null && creatureNode.Hitbox.GetGlobalRect().HasPoint(GlobalPosition + _toPosition))
            {
                isTargetingCreature = true;
                break;
            }
        }

        DoTargetingCreatureTween(isTargetingCreature);
    }

    public void StartDrawingFrom(Vector2 from)
    {
        _fromPosition = from;
        Visible = !NCombatUi.IsDebugHideTargetingUi;
        ProcessMode = Visible ? ProcessModeEnum.Inherit : ProcessModeEnum.Disabled;
    }

    public void StopDrawing()
    {
        Visible = false;
        ProcessMode = ProcessModeEnum.Disabled;
        Modulate = Modulate with { A = _defaultAlpha };
    }

    public void UpdateDrawingTo(Vector2 position)
    {
        _toPosition = position;
    }

    private void DoTargetingCreatureTween(bool isTargetingCreature)
    {
        if (isTargetingCreature == _isTargetingCreature) return;

        _tween?.Kill();
        _tween = CreateTween();

        if (isTargetingCreature)
        {
            _tween.TweenProperty(this, "modulate:a", _targetingAlpha, 0.1f);
        }
        else
        {
            _tween.TweenProperty(this, "modulate:a", _defaultAlpha, 0.25f);
        }

        _isTargetingCreature = isTargetingCreature;
    }
}
