using System.Collections.Generic;
using System.Linq;
using Godot;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Cards;
using MegaCrit.Sts2.Core.Nodes.Cards.Holders;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;

namespace MegaCrit.Sts2.Core.Nodes.Combat;

public partial class NSelectedHandCardContainer : Control
{
    public NPlayerHand? Hand { get; set; }
    public List<NSelectedHandCardHolder> Holders => GetChildren().OfType<NSelectedHandCardHolder>().ToList();

    public override void _Ready()
    {
        Connect(Control.SignalName.FocusEntered, Callable.From(OnFocus));
    }

    public NSelectedHandCardHolder Add(NHandCardHolder originalHolder)
    {
        NCard node = originalHolder.CardNode!;
        Vector2 originalPos = node.GlobalPosition;
        NSelectedHandCardHolder holder = NSelectedHandCardHolder.Create(originalHolder);
        holder.Connect(NCardHolder.SignalName.Pressed, Callable.From<NCardHolder>(DeselectHolder), (uint)ConnectFlags.OneShot);
        this.AddChildSafely(holder);
        RefreshHolderPositions();

        node.GlobalPosition = originalPos;
        return holder;
    }

    private void RefreshHolderPositions()
    {
        int holderCount = Holders.Count;
        FocusMode = holderCount > 0 ? FocusModeEnum.All : FocusModeEnum.None;

        if (holderCount == 0) return;

        float holderWidth = Holders.First().Size.X;
        float holderXOffset = -holderWidth * (holderCount - 1) / 2f;

        for (int i = 0; i < holderCount; i++)
        {
            Holders[i].Position = new Vector2(holderXOffset, 0f);
            holderXOffset += holderWidth;

            Holders[i].FocusNeighborLeft = i > 0 ? Holders[i - 1].GetPath() : Holders[Holders.Count - 1].GetPath();
            Holders[i].FocusNeighborRight = i < Holders.Count - 1 ? Holders[i + 1].GetPath() : Holders[0].GetPath();
        }
    }

    private void DeselectHolder(NCardHolder holder)
    {
        NSelectedHandCardHolder selectedHolder = (NSelectedHandCardHolder)holder;
        NCard node = selectedHolder.CardNode!;
        Hand!.DeselectCard(node);

        this.RemoveChildSafely(selectedHolder);
        selectedHolder.QueueFreeSafely();
        RefreshHolderPositions();
    }

    public void DeselectCard(CardModel card)
    {
        NSelectedHandCardHolder holder = Holders.First(child => child.CardNode!.Model == card);
        DeselectHolder(holder);
    }

    private void OnFocus()
    {
        Holders.First().TryGrabFocus();
    }
}
