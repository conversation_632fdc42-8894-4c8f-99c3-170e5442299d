using System.Threading;
using Godot;

namespace MegaCrit.Sts2.Core.Nodes.Combat;

public partial class NSelectionReticle : Control
{
    private Tween? _currentTween;
    private readonly CancellationTokenSource _cancelToken = new();

    public bool IsSelected { get; private set; }

    public override void _Ready()
    {
        Modulate = Colors.Transparent;
        PivotOffset = Size * 0.5f;
    }

    public override void _ExitTree()
    {
        base._ExitTree();
        _cancelToken.Cancel();
    }

    public void OnSelect()
    {
        if (NCombatUi.IsDebugHideTargetingUi) return;

        _currentTween?.Kill();

        _currentTween = CreateTween().SetParallel();
        _currentTween.TweenProperty(this, "modulate:a", 1f, 0.2f);
        _currentTween.TweenProperty(this, "scale", Vector2.One, 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo)
            .From(Vector2.One * 0.9f);

        Modulate = Colors.White;
        Scale = Vector2.One;
        IsSelected = true;
    }

    public void OnDeselect()
    {
        if (_cancelToken.IsCancellationRequested) return;
        _currentTween?.Kill();

        // We might be deselecting as part of being removed from the tree. Exit early if so.
        if (!IsInsideTree()) return;

        _currentTween = CreateTween().SetParallel();
        _currentTween.TweenProperty(this, "modulate:a", 0f, 0.2f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Sine);
        _currentTween.TweenProperty(this, "scale", Vector2.One * 1.05f, 0.2f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Sine);

        IsSelected = false;
    }
}
