using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Localization.Formatters;
using MegaCrit.Sts2.Core.Nodes.HoverTips;

namespace MegaCrit.Sts2.Core.Nodes.Combat;

public partial class NStarCounter : Control
{
    private static readonly string _starGainVfxPath = SceneHelper.GetScenePath("vfx/star_gain_vfx");

    public static IEnumerable<string> AssetPaths => [_starGainVfxPath];

    // Will be null until initialized.
    private Player? _player;

    private MegaRichTextLabel _label = default!;
    private Control _rotationLayers = default!;
    private Control _icon = default!;
    private ShaderMaterial _hsv = default!;

    private float _lerpingStarCount;
    private float _velocity;
    private int _displayedStarCount;
    private Tween? _hsvTween;
    private bool _isListeningToCombatState;

    private HoverTip _hoverTip;

    public override void _Ready()
    {
        _label = GetNode<MegaRichTextLabel>("%CountLabel");
        _rotationLayers = GetNode<Control>("%RotationLayers");
        _icon = GetNode<Control>("Icon");
        _hsv = (ShaderMaterial)_icon.Material;

        LocString description = new("static_hover_tips", "STAR_COUNT.description");
        description.Add("singleStarIcon", StarIconsFormatter.starIconSprite);

        _hoverTip = new HoverTip(
            new LocString("static_hover_tips", "STAR_COUNT.title"),
            description
        );

        Connect(Control.SignalName.MouseEntered, Callable.From(OnHovered));
        Connect(Control.SignalName.MouseExited, Callable.From(OnUnhovered));

        Visible = false;
    }

    public override void _EnterTree()
    {
        base._EnterTree();

        if (_player != null && !_isListeningToCombatState)
        {
            _player.PlayerCombatState!.StarsChanged += OnStarsChanged;
            _isListeningToCombatState = true;
        }
    }

    public override void _ExitTree()
    {
        base._ExitTree();

        if (_player != null && _isListeningToCombatState)
        {
            _player.PlayerCombatState!.StarsChanged -= OnStarsChanged;
            _isListeningToCombatState = false;
        }
    }

    public void Initialize(Player player)
    {
        _player = player;

        if (!_isListeningToCombatState)
        {
            _player.PlayerCombatState!.StarsChanged += OnStarsChanged;
            _isListeningToCombatState = true;
        }

        RefreshVisibility();
    }

    private void OnHovered()
    {
        NHoverTipSet tip = NHoverTipSet.CreateAndShow(this, _hoverTip);
        tip.GlobalPosition = GlobalPosition + new Vector2(-34f, -300f);
    }

    private void OnUnhovered()
    {
        NHoverTipSet.Remove(this);
    }

    private void OnStarsChanged(int oldStars, int newStars)
    {
        UpdateStarCount(oldStars, newStars);
        RefreshVisibility();
    }

    public override void _Process(double delta)
    {
        if (_player == null) return;

        float rotSpeed = _player.PlayerCombatState!.Stars == 0 ? 5f : 30f;

        for (int i = 0; i < _rotationLayers.GetChildCount(); i++)
        {
            _rotationLayers.GetChild<Control>(i).RotationDegrees += (float)delta * rotSpeed * (i + 1);
        }

        _lerpingStarCount = MathHelper.SmoothDamp(_lerpingStarCount, _player.PlayerCombatState!.Stars, ref _velocity, 0.1f, (float)delta);
        SetStarCountText(Mathf.RoundToInt(_lerpingStarCount));
    }

    private void UpdateStarCount(int oldCount, int newCount)
    {
        // If losing stars, stop gain VFX and immediately set count
        if (newCount < oldCount)
        {
            _hsvTween?.Kill();
            _hsv.SetShaderParameter("v", 1f);
            _lerpingStarCount = newCount;
            SetStarCountText(newCount);
        }
        else if (newCount > oldCount)
        {
            _hsvTween?.Kill();
            _hsvTween = CreateTween();
            _hsvTween.TweenMethod(Callable.From<float>(UpdateShaderV), 2f, 1f, 0.2f);

            Node2D vfx = PreloadManager.Cache.GetAsset<PackedScene>(_starGainVfxPath).Instantiate<Node2D>();
            this.AddChildSafely(vfx);
            MoveChild(vfx, 0);
            vfx.Position = Size / 2f;
        }
    }

    private void SetStarCountText(int stars)
    {
        if (_displayedStarCount != stars)
        {
            _displayedStarCount = stars;
            _label.AddThemeColorOverride("font_color", stars == 0 ? StsColors.red : StsColors.cream);
            _label.Text = $"[center]{stars}[/center]";

            if (stars == 0)
            {
                _hsv.SetShaderParameter("s", 0.5f);
                _hsv.SetShaderParameter("v", 0.85f);
            }
            else
            {
                _hsv.SetShaderParameter("s", 1f);
                _hsv.SetShaderParameter("v", 1f);
            }
        }
    }

    private void UpdateShaderV(float value)
    {
        _hsv.SetShaderParameter("v", value);
    }

    private void RefreshVisibility()
    {
        if (_player == null)
        {
            Visible = false;
            return;
        }

        int stars = _player.PlayerCombatState!.Stars;

        // If I am already visible, don't try to make me invisible.
        Visible = Visible || _player.Character.ShouldAlwaysShowStarCounter || stars > 0;
    }
}
