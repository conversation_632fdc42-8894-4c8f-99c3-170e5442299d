using Godot;

namespace MegaCrit.Sts2.Core.Nodes.CommonUi;

public partial class NCardPreviewContainer : Control
{
    public override void _Ready()
    {
        Connect(Node.SignalName.ChildEnteredTree, Callable.From<Node>(ReformatElements));
    }

    private void ReformatElements(Node _)
    {
        Vector2 cardPreviewContainerCenter = Size / 2f + Vector2.Down * 50f;
        const int xPadding = 325;

        for (int i = 0; i < GetChildCount(); i++)
        {
            float startX = -(GetChildCount() - 1) * xPadding / 2f;

            // We offset this slightly so that cards generated in combat don't block played cards.
            Vector2 centerPos = cardPreviewContainerCenter;
            Vector2 position = centerPos + Vector2.Right * (startX + xPadding * i);

            Node? child = GetChild(i);
            if (child is Node2D node)
            {
                node.Position = position;
            }
            else
            {
                ((Control)child).Position = position;
            }
        }
    }
}
