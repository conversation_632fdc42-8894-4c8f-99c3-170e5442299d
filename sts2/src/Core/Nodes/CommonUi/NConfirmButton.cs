using System.Threading;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.ControllerInput;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;

namespace MegaCrit.Sts2.Core.Nodes.CommonUi;

/// <summary>
/// Confirm <PERSON><PERSON> used in various places to confirm choices, embark on a climb, etc.
/// Very useful but not as cool as Back Button but that's life.
/// Use the ButtonReleased Action to handle events.
/// </summary>
public partial class NConfirmButton : NButton
{
    private Control _outline = default!;
    private Control _buttonImage = default!;

    private Color _defaultOutlineColor = StsColors.cream;
    private Color _hoveredOutlineColor = StsColors.gold;
    private Color _downColor = Colors.Gray;
    private Color _outlineColor = new("F0B400");
    private Color _outlineTransparentColor = new("00FFFF00"); // NOTE: It's transparent cyan because it does a cool double-color fade out
    private Viewport _viewport = default!;

    protected override string[] Hotkeys => [MegaInput.accept];

    // f0b400c0 ADDITIVE OUTLINE

    private static readonly Vector2 _hoverScale = new(1.05f, 1.05f);
    private static readonly Vector2 _downScale = new(0.95f, 0.95f);
    private const float _pressDownDur = 0.25f;
    private const float _unhoverAnimDur = 0.5f;
    private const float _animInOutDur = 0.35f; // How long it takes for this button to animate in or out (same amount of time)

    private Vector2 _posOffset;
    private Vector2 _showPos;
    private Vector2 _hidePos;
    private static readonly Vector2 _hideOffset = new(180f, 0f);

    private Tween? _moveTween;
    private CancellationTokenSource? _pressDownCancelToken;
    private CancellationTokenSource? _unhoverAnimCancelToken;

    public override void _Ready()
    {
       ConnectSignals();

        _isEnabled = false;
        _outline = GetNode<Control>("Outline");
        _buttonImage = GetNode<Control>("Image");
        _viewport = GetViewport();
        _posOffset = new Vector2(OffsetRight + 120f, -OffsetBottom + 110f);
        GetTree().Root.Connect(Viewport.SignalName.SizeChanged, Callable.From(OnWindowChange));
        OnWindowChange();
        OnDisable();
    }

    public override void _ExitTree()
    {
        base._ExitTree();

        _pressDownCancelToken?.Cancel();
        _unhoverAnimCancelToken?.Cancel();
    }

    private void OnWindowChange()
    {
        _showPos = NGame.Instance!.Size - _posOffset;
        _hidePos = _showPos + _hideOffset;
        Position = _isEnabled ? _showPos : _hidePos;
    }

    /// <summary>
    /// Call when we want this button to animate in.
    /// </summary>
    protected override void OnEnable()
    {
        base.OnEnable();
        _isEnabled = true;

        _outline.Modulate = Colors.Transparent;
        _buttonImage.Modulate = Colors.White;

        _moveTween?.Kill();
        _moveTween = CreateTween();
        _moveTween.TweenProperty(this, "position", _showPos, _animInOutDur)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Back)
            .FromCurrent();
    }

    /// <summary>
    /// Call when we want this button to hide this button (Disables clickability/hotkeys)
    /// </summary>
    protected override void OnDisable()
    {
        base.OnDisable();
        _isEnabled = false;

        _moveTween?.Kill();
        _moveTween = CreateTween();
        _moveTween.TweenProperty(this, "position", _hidePos, _animInOutDur)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo)
            .FromCurrent();
    }

    protected override void OnFocus()
    {
        base.OnFocus();
        _unhoverAnimCancelToken?.Cancel();
        Scale = _hoverScale;
        _outline.Modulate = _outlineColor;
        _buttonImage.Modulate = Colors.White;
    }

    protected override void OnUnfocus()
    {
        base.OnUnfocus();
        _pressDownCancelToken?.Cancel();
        _unhoverAnimCancelToken = new CancellationTokenSource();
        TaskHelper.RunSafely(AnimUnhover(_unhoverAnimCancelToken));
    }

    private async Task AnimUnhover(CancellationTokenSource cancelToken)
    {
        float timer = 0f;
        Vector2 startScale = Scale;
        Color startButtonColor = _buttonImage.Modulate;
        Color startColor = _outline.Modulate;

        while (timer < _unhoverAnimDur)
        {
            if (cancelToken.IsCancellationRequested) return;

            Scale = startScale.Lerp(Vector2.One, Ease.ExpoOut(timer / _unhoverAnimDur));
            _outline.Modulate = startColor.Lerp(_outlineTransparentColor, Ease.ExpoOut(timer / _unhoverAnimDur));
            _buttonImage.Modulate = startButtonColor.Lerp(Colors.White, Ease.ExpoOut(timer / _unhoverAnimDur));

            await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
            timer += (float)GetProcessDeltaTime();
        }

        Scale = Vector2.One;
        _outline.Modulate = _outlineTransparentColor;
        _buttonImage.Modulate = Colors.White;
    }

    protected override void OnPressDown()
    {
        base.OnPressDown();
        _pressDownCancelToken = new CancellationTokenSource();
        TaskHelper.RunSafely(AnimPressDown(_pressDownCancelToken));
    }

    private async Task AnimPressDown(CancellationTokenSource cancelToken)
    {
        float timer = 0f;
        _buttonImage.Modulate = Colors.White;
        _outline.Modulate = _outlineColor;
        Scale = _hoverScale;

        while (timer < _pressDownDur)
        {
            if (cancelToken.IsCancellationRequested) return;

            Scale = _hoverScale.Lerp(_downScale, Ease.CubicOut(timer / _pressDownDur));
            _buttonImage.Modulate = Colors.White.Lerp(_downColor, Ease.CubicOut(timer / _pressDownDur));
            _outline.Modulate = _outlineColor.Lerp(_outlineTransparentColor, Ease.CubicOut(timer / _pressDownDur));

            await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
            timer += (float)GetProcessDeltaTime();
        }

        Scale = _downScale;
        _buttonImage.Modulate = _downColor;
        _outline.Modulate = _outlineTransparentColor;
    }
}
