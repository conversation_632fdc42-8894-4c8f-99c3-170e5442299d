using Godot;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;

namespace MegaCrit.Sts2.Core.Nodes.CommonUi;

/// <summary>
/// Represents a single item within a DropdownList.
/// Must be extended, see NLanguageDropdownItem for an example.
/// </summary>
public partial class NDropdownItem : NButton
{
    [Signal]
    public delegate void SelectedEventHandler(NDropdownItem cardHolder);

    private ColorRect _highlight = default!;
    protected Label _label = default!;

    public string Text
    {
        get => _label.Text;
        set => _label.Text = value;
    }

    public override void _Ready()
    {
        ConnectSignals();
        _highlight = GetNode<ColorRect>("Highlight");
        _label = GetNode<Label>("Label");
    }

    protected override void OnFocus()
    {
        _highlight.Visible = true;
    }

    protected override void OnUnfocus()
    {
        _highlight.Visible = false;
    }

    protected override void OnPressDown()
    {
        _highlight.Visible = false;
    }

    protected sealed override void OnRelease()
    {
        _highlight.Visible = true;
        EmitSignal(SignalName.Selected, this);
    }

    /// <summary>
    /// Helper class to access protected method OnUnhover()
    /// </summary>
    public void UnhoverSelection()
    {
        OnUnfocus();
    }
}
