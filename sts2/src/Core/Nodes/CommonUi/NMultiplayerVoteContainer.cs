using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;

namespace MegaCrit.Sts2.Core.Nodes.CommonUi;

public partial class NMultiplayerVoteContainer : Control
{
    public delegate bool PlayerVotedDelegate(Player player);

    private const string _voteIconPath = "ui/multiplayer_vote_icon";
    public static IEnumerable<string> AssetPaths => [SceneHelper.GetScenePath(_voteIconPath)];

    private readonly List<EndTurnIcon> _votes = [];
    private PlayerVotedDelegate _playerVotedDelegate = default!;
    private readonly List<Player> _players = [];

    private readonly List<EndTurnIcon> _iconsAnimatingOut = [];

    private record class EndTurnIcon
    {
        public required Player player;
        public required TextureRect node;
        public Tween? tween;
    }

    public void Initialize(PlayerVotedDelegate del, IReadOnlyList<Player> players)
    {
        _playerVotedDelegate = del;
        _players.AddRange(players);
    }

    public void RefreshPlayerVotes()
    {
        // In singleplayer, display and do absolutely nothing
        if (_players.Count == 1) return;

        // Check if votes are still present
        for (int i = 0; i < _votes.Count; i++)
        {
            EndTurnIcon vote = _votes[i];

            if (_playerVotedDelegate(vote.player)) continue;

            // Remove the vote indicator
            AnimVoteOut(vote);
            _votes.RemoveAt(i);
            i--;
        }

        // Add votes that are not present
        foreach (Player player in _players)
        {
            if (!_playerVotedDelegate(player)) continue;

            int index = _votes.FindIndex(p => p.player == player);

            // Ignore votes that are already accounted for
            if (index >= 0) continue;

            // Make new indicator for votes that are not accounted for
            EndTurnIcon vote = new()
            {
                player = player,
                node = SceneHelper.Instantiate<TextureRect>(_voteIconPath)
            };

            vote.node.Texture = player.Character.IconTexture;
            _votes.Add(vote);
            this.AddChildSafely(vote.node);
            AnimVoteIn(vote);
        }
    }

    private void AnimVoteIn(EndTurnIcon vote)
    {
        int animatingOutIndex = _iconsAnimatingOut.FindIndex(i => i.player == vote.player);
        if (animatingOutIndex > 0)
        {
            _iconsAnimatingOut[animatingOutIndex].tween?.Kill();
            _iconsAnimatingOut.RemoveAt(animatingOutIndex);
        }

        vote.tween?.Kill();
        vote.tween = CreateTween().SetParallel();
        vote.tween.TweenProperty(vote.node, "modulate:a", 1f, 0.2f).From(0f);
        vote.tween.TweenProperty(vote.node, "position:y", 0f, 0.3f).From(20f).SetTrans(Tween.TransitionType.Back).SetEase(Tween.EaseType.Out);
    }

    private void AnimVoteOut(EndTurnIcon vote)
    {
        _iconsAnimatingOut.Add(vote);

        vote.tween?.Kill();
        vote.tween = CreateTween().SetParallel();
        vote.tween.TweenProperty(vote.node, "modulate:a", 0f, 0.1f).SetDelay(0.15f);
        vote.tween.TweenProperty(vote.node, "position:y", 20f, 0.25f).SetTrans(Tween.TransitionType.Expo).SetEase(Tween.EaseType.In);
        vote.tween.Chain().TweenCallback(Callable.From(() =>
        {
            _iconsAnimatingOut.Remove(vote);
            vote.node.QueueFreeSafely();
        }));
    }
}
