using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.ControllerInput;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;

namespace MegaCrit.Sts2.Core.Nodes.CommonUi;

public partial class NPopupYesNoButton : NButton
{
    // Nodes and Tween
    private Control _visuals = default!;
    private Control _outline = default!;
    private Control _image = default!;
    private MegaLabel _label = default!;
    private Tween? _tween;

    // Image hsv
    private float _baseS;
    private float _baseV;
    private ShaderMaterial _hsv = default!;

    // Outline
    private CanvasItemMaterial _outlineMaterial = default!;
    private bool _isFocused;
    private static readonly Color _goldOutline = new("f0b400");

    private bool _isYes;

    /// <summary>
    /// Whether this is the Yes or No option of the Pop up
    /// </summary>
    public bool IsYes
    {
        get => _isYes;
        set
        {
            if (_isYes == value) return;

            // make sure to update the hotkey for this button
            foreach (string hotkey in Hotkeys)
            {
                NHotkeyManager.Instance!.RemoveHotkey(hotkey, OnReleaseHandler);
            }

            _isYes = value;
            foreach (string hotkey in Hotkeys)
            {
                NHotkeyManager.Instance!.PushHotkey(hotkey, OnReleaseHandler);
            }
        }
    }

    protected override string[] Hotkeys => [IsYes ? MegaInput.select : MegaInput.cancel];

    public override void _Ready()
    {
        ConnectSignals();

        _visuals = GetNode<Control>("%Visuals");
        _outline = GetNode<Control>("%Outline");
        _image = GetNode<Control>("%Image");
        _label = GetNode<MegaLabel>("%Label");

        _hsv = (ShaderMaterial)_image.GetMaterial();
        _outlineMaterial = (CanvasItemMaterial)_outline.GetMaterial();

        _baseS = (float)_hsv.GetShaderParameter("s");
        _baseV = (float)_hsv.GetShaderParameter("v");
    }

    public void SetText(string text)
    {
        _label.Text = text;
    }

    protected override void OnFocus()
    {
        base.OnFocus();

        _isFocused = true;
        _outlineMaterial.BlendMode = CanvasItemMaterial.BlendModeEnum.Add; // Additive
        _outline.Modulate = Colors.White;
        _outline.SelfModulate = _goldOutline;

        _tween?.Kill();
        _tween = CreateTween().SetParallel();
        _tween.TweenProperty(_visuals, "scale", Vector2.One * 1.025f, 0.05);
        _tween.TweenMethod(Callable.From<float>(UpdateShaderS), _hsv.GetShaderParameter("s"), _baseS + 0.25f, 0.05)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _tween.TweenMethod(Callable.From<float>(UpdateShaderV), _hsv.GetShaderParameter("v"), _baseV + 0.25f, 0.05)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    protected override void OnUnfocus()
    {
        base.OnUnfocus();

        _isFocused = false;
        _outlineMaterial.BlendMode = CanvasItemMaterial.BlendModeEnum.Mix;
        _outline.Modulate = StsColors.halfTransparentWhite;
        _outline.SelfModulate = Colors.Black;

        _tween?.Kill();
        _tween = CreateTween().SetParallel();
        _tween.TweenProperty(_visuals, "scale", Vector2.One, 0.5)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _tween.TweenMethod(Callable.From<float>(UpdateShaderS), _hsv.GetShaderParameter("s"), _baseS, 0.5)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _tween.TweenMethod(Callable.From<float>(UpdateShaderV), _hsv.GetShaderParameter("v"), _baseV, 0.5)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    protected override void OnPressDown()
    {
        base.OnPressDown();

        _tween?.Kill();
        _tween = CreateTween().SetParallel();
        _tween.TweenProperty(_visuals, "scale", Vector2.One * 0.975f, 0.5)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _tween.TweenMethod(Callable.From<float>(UpdateShaderS), _hsv.GetShaderParameter("s"), _baseS - 0.1f, 0.5)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _tween.TweenMethod(Callable.From<float>(UpdateShaderV), _hsv.GetShaderParameter("v"), _baseV - 0.1f, 0.5)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    protected override void OnRelease()
    {
        _isFocused = false;
        _outlineMaterial.BlendMode = CanvasItemMaterial.BlendModeEnum.Mix;
        _outline.Modulate = StsColors.halfTransparentWhite;
        _outline.SelfModulate = Colors.Black;

        _tween?.Kill();
        _tween = CreateTween().SetParallel();
        _tween.TweenProperty(_visuals, "scale", Vector2.One, 0.05);
        _tween.TweenMethod(Callable.From<float>(UpdateShaderS), _hsv.GetShaderParameter("s"), _baseS, 0.05);
        _tween.TweenMethod(Callable.From<float>(UpdateShaderV), _hsv.GetShaderParameter("v"), _baseV, 0.05);
    }

    private void UpdateShaderS(float value)
    {
        _hsv.SetShaderParameter("s", value);
    }

    private void UpdateShaderV(float value)
    {
        _hsv.SetShaderParameter("v", value);
    }
}
