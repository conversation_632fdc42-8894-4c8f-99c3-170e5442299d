using Godot;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.Nodes.CommonUi;

public partial class NSaveIndicator : Control
{
    private Tween? _tween;

    public override void _Ready()
    {
        Modulate = Colors.Transparent;
    }

    private void SavedGame()
    {
        if (NCombatUi.IsDebugHideTextVfx) return;

        _tween?.Kill();
        _tween = CreateTween();
        _tween.TweenInterval(0.5);
        _tween.TweenProperty(this, "modulate", Colors.White, 1.0);
        _tween.TweenInterval(0.5);
        _tween.TweenProperty(this, "modulate", Colors.Transparent, 1.0);
    }

    public override void _EnterTree()
    {
        SaveManager.Instance.Saved += SavedGame;
    }

    public override void _ExitTree()
    {
        SaveManager.Instance.Saved -= SavedGame;
    }
}
