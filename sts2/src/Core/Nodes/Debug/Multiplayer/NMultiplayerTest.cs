using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Debug;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Multiplayer;
using MegaCrit.Sts2.Core.Multiplayer.Connection;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Multiplayer.Game.Lobby;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Game;
using MegaCrit.Sts2.Core.Multiplayer.Replay;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;
using MegaCrit.Sts2.Core.Nodes.Audio;
using MegaCrit.Sts2.Core.Platform;
using MegaCrit.Sts2.Core.Platform.Steam;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Climbs;
using FileAccess = Godot.FileAccess;

namespace MegaCrit.Sts2.Core.Nodes.Debug.Multiplayer;

public partial class NMultiplayerTest : Control, IStartClimbLobbyListener
{
    private const ushort _port = 33771;

    private TextEdit _ipField = default!;
    private TextEdit _idField = default!;
    private Button _readyButton = default!;
    private Control _readyIndicator = default!;
    private Control _loadingPanel = default!;

    private NMultiplayerTestCharacterPaginator _characterPaginator = default!;
    private readonly List<CharacterContainer> _characterContainers = [];

    private NGame _game = default!;
    private StartClimbLobby? _lobby;

    private readonly Dictionary<ulong, SerializablePlayer> _readyData = [];
    private readonly SerializablePlayer _localPlayerData = new();

    private IBootstrapSettings? _settings;
    private bool _ignoreReplayModelIdHash;
    private bool _beginningClimb;

    private struct CharacterContainer
    {
        public TextureRect characterImage;
        public Label playerName;
    }

    public override void _Ready()
    {
        _ipField = GetNode<TextEdit>("IpField");
        _idField = GetNode<TextEdit>("NameField");
        _characterPaginator = GetNode<NMultiplayerTestCharacterPaginator>("CharacterChooser");
        Button hostButton = GetNode<Button>("HostButton");
        Button steamHostButton = GetNode<Button>("SteamHostButton");
        Button joinButton = GetNode<Button>("JoinButton");
        _readyButton = GetNode<Button>("ReadyButton");
        _readyIndicator = GetNode<Control>("ReadyButton/ReadyIndicator");
        Button replayButton = GetNode<Button>("ReplayButton");
        _loadingPanel = GetNode<Control>("LoadingPanel");

        Control characters = GetNode<Control>("Characters");

        foreach (Node node in characters.GetChildren())
        {
            _characterContainers.Add(new CharacterContainer
            {
                characterImage = node.GetNode<TextureRect>("Image"),
                playerName = node.GetNode<Label>("Name")
            });
        }

        hostButton.Connect(BaseButton.SignalName.ButtonUp, Callable.From(HostButtonPressed));
        steamHostButton.Connect(BaseButton.SignalName.ButtonUp, Callable.From(SteamHostButtonPressed));
        joinButton.Connect(BaseButton.SignalName.ButtonUp, Callable.From(JoinButtonPressed));
        _readyButton.Connect(BaseButton.SignalName.ButtonUp, Callable.From(ReadyButtonPressed));
        replayButton.Connect(BaseButton.SignalName.ButtonUp, Callable.From(ChooseReplayToLoad));

        _characterPaginator.Visible = false;
        _characterPaginator.CharacterChanged += OnCharacterChanged;

        _readyButton.Visible = false;

        _game = GetTree().Root.GetNodeOrNull<NGame>("Game");

        if (_game == null)
        {
            _game = SceneHelper.Instantiate<NGame>("game");
            _game.StartOnMainMenu = false;

            Callable.From(AddGame).CallDeferred();
        }

        Assembly assembly = Assembly.GetAssembly(typeof(NSceneBootstrapper))!;
        Type? settingsType = assembly.GetTypes().FirstOrDefault(t => t.GetInterfaces().Contains(typeof(IBootstrapSettings)));

        if (settingsType != null)
        {
            _settings = (IBootstrapSettings)Activator.CreateInstance(settingsType)!;
            PreloadManager.Enabled = _settings.DoPreloading;

            if (_settings is { BootstrapInMultiplayer: true, Seed: not null })
            {
                _game.DebugSeedOverride = _settings.Seed;
            }
        }

        if (!SteamInitializer.Initialized)
        {
            SteamInitializer.Initialize(_game, true);
        }

        Logger.logLevelTypeMap[LogType.Network] = LogLevel.Debug;
        Logger.logLevelTypeMap[LogType.Actions] = LogLevel.VeryDebug;
        Logger.logLevelTypeMap[LogType.GameSync] = LogLevel.VeryDebug;
    }

    public override void _ExitTree()
    {
        if (!_beginningClimb)
        {
            _game.RemoteCursorContainer.Deinitialize();
            _game.ReactionContainer.DeinitializeNetworking();

            _lobby?.NetService.UnregisterMessageHandler<SyncPlayerDataMessage>(OnSyncPlayerData);
            _lobby?.CleanUp(true);
        }
    }

    private void AddGame()
    {
        Node rootNode = GetTree().Root;
        rootNode.AddChildSafely(_game);
        _game.RootSceneContainer.SetCurrentScene(this);
        TaskHelper.RunSafely(_game.Transition.FadeIn());
    }

    private void HostButtonPressed()
    {
        TaskHelper.RunSafely(StartHost(false));
    }

    private void SteamHostButtonPressed()
    {
        TaskHelper.RunSafely(StartHost(true));
    }

    private void JoinButtonPressed()
    {
        ulong netId = _idField.Text != string.Empty ? ulong.Parse(_idField.Text) : 1000;
        string ip = _ipField.Text != string.Empty ? _ipField.Text : "127.0.0.1";
        ENetClientConnectionInitializer connectionInitializer = new(netId, ip, _port);
        TaskHelper.RunSafely(JoinToHost(connectionInitializer));
    }

    private void ReadyButtonPressed()
    {
        _localPlayerData.Deck = _characterPaginator.Character.StartingDeck.Select(c => c.ToMutable().ToSerializable()).ToList();
        _localPlayerData.Relics = _characterPaginator.Character.StartingRelics.Select(r => r.ToMutable().ToSerializable()).ToList();
        _localPlayerData.Potions = [];

        // We don't need these fields but bad things happen if we don't assign them
        _localPlayerData.Rng = new SerializablePlayerRngSet();
        _localPlayerData.Odds = new SerializablePlayerOddsSet();
        _localPlayerData.RelicGrabBag = new SerializableRelicGrabBag();

        Log.Info("Sending player data message");

        SyncPlayerDataMessage message = default;
        message.player = _localPlayerData;
        _localPlayerData.NetId = _lobby!.NetService.NetId;

        _lobby!.NetService.SendMessage(message);
        OnSyncPlayerData(message, _lobby!.NetService.NetId);

        _lobby!.SetReady(true);
        _readyIndicator.Visible = true;
    }

    private void OnSyncPlayerData(SyncPlayerDataMessage message, ulong senderId)
    {
        Log.Info($"Received ready data for player {senderId}");
        _readyData[senderId] = message.player;
    }

    public void BeginClimb(string seed, IReadOnlyList<ModifierModel> __, string _)
    {
        _loadingPanel.Visible = true;

        // Need to unregister before starting climb as CombatStateSynchronizer also uses this message
        _lobby?.NetService.UnregisterMessageHandler<SyncPlayerDataMessage>(OnSyncPlayerData);

        TaskHelper.RunSafely(BeginClimbAsync(seed));
    }

    private async Task BeginClimbAsyncWrapper(string seed)
    {
        try
        {
            await BeginClimbAsync(seed);
        }
        finally
        {
            _loadingPanel.Visible = false;
        }
    }

    private async Task BeginClimbAsync(string seed)
    {
        _beginningClimb = true;
        Node rootNode = GetTree().Root;

        List<ActModel> acts = ActModel.GetDefaultList().ToList();

        if (_settings is { BootstrapInMultiplayer: true })
        {
            acts[0] = _settings.Act;
        }

        ClimbState climbState = await _game.StartNewMultiplayerClimb(
            _lobby!,
            _settings?.SaveClimbHistory ?? true,
            acts,
            _settings?.Modifiers ?? [],
            seed,
            0
        );

        _lobby!.CleanUp(false);

        foreach (KeyValuePair<ulong, SerializablePlayer> pair in _readyData)
        {
            Player? player = climbState.GetPlayer(pair.Key);
            player!.SyncWithSerializedPlayer(pair.Value);

            // These things shouldn't run when synced with net data, but we want them to in this specific instance to
            // pretend as if they were obtained
            foreach (RelicModel relic in player.Relics)
            {
                await relic.AfterObtained();
            }
        }

        if (_settings?.BootstrapInMultiplayer ?? false)
        {
            climbState.AppendToMapPointHistory(_settings.MapPointType, _settings.RoomType);

            await _settings.Setup(LocalContext.GetMe(climbState)!);

            switch (_settings.RoomType)
            {
                case RoomType.RestSite:
                case RoomType.Treasure:
                case RoomType.Shop:
                    await ClimbManager.Instance.EnterRoomDebug(_settings.RoomType, showTransition: false);
                    ClimbManager.Instance.ActionExecutor.Unpause();
                    break;
                case RoomType.Event:
                    await ClimbManager.Instance.EnterRoomDebug(_settings.RoomType, model: _settings.Event, showTransition: false);
                    break;
                default:
                    await ClimbManager.Instance.EnterRoomDebug(
                        _settings.RoomType,
                        model: _settings.Encounter.ToMutable(),
                        showTransition: false
                    );
                    break;
            }
        }
        else
        {
            await ClimbManager.Instance.EnterAct(0);
        }
    }

    private void Disconnect(NetError reason)
    {
        _lobby?.NetService.Disconnect(reason);
        _lobby?.CleanUp(true);
        _lobby = null;
    }

    private async Task<bool> StartHost(bool steam)
    {
        Disconnect(NetError.Quit);
        NetHostGameService netService = new();

        NetErrorInfo? info;

        if (steam)
        {
            info = await netService.StartSteamHost(4);
        }
        else
        {
            info = netService.StartENetHost(_port, 4);
        }

        if (info == null)
        {
            _lobby = new StartClimbLobby(GameMode.Standard, netService, this, 4);
            _lobby.AddLocalHostPlayer(SaveManager.Instance.ProgressSave);
            AfterMultiplayerStarted();
            Log.Info($"Successful host");
        }
        else
        {
            _lobby = null;
            Log.Info($"Failed host: {info}");
        }

        return info == null;
    }

    public async Task JoinToHost(IClientConnectionInitializer initializer)
    {
        Disconnect(NetError.Quit);

        JoinFlow joinFlow = new();

        try
        {
            JoinResult result = await joinFlow.Begin(initializer, GetTree());

            if (result.sessionState == ClimbSessionState.InLobby)
            {
                Log.Info("Successfully joined lobby");
                _lobby = new StartClimbLobby(result.gameMode, joinFlow.NetService!, this, -1);
                _lobby.InitializeFromMessage(result.joinResponse!.Value);
                AfterMultiplayerStarted();
            }
            else if (result.sessionState == ClimbSessionState.Running)
            {
                Log.Info("Successfully joined climb in-progress. Initializing climb");
                throw new NotImplementedException("Climb re-joining has yet to be implemented!");
            }
        }
        catch (Exception e)
        {
            joinFlow.NetService!.Disconnect(NetError.ClimbInProgress);
            _lobby = null;
            Log.Info($"Failed join: {e}");
        }
    }

    private void AfterMultiplayerStarted()
    {
        foreach (LobbyPlayer player in _lobby!.Players)
        {
            _characterContainers[player.slotId].characterImage.Texture = player.character.IconTexture;
            _characterContainers[player.slotId].playerName.Text = PlatformUtil.GetPlayerName(_lobby!.NetService.Platform, player.id);
        }

        _readyButton.Visible = true;
        _characterPaginator.Visible = true;
        _game.RemoteCursorContainer.Initialize(_lobby!.InputSynchronizer, _lobby.Players.Select(p => p.id));
        _game.ReactionContainer.InitializeNetworking(_lobby!.NetService);
        OnCharacterChanged(_lobby!.LocalPlayer.character);

        _lobby.NetService.RegisterMessageHandler<SyncPlayerDataMessage>(OnSyncPlayerData);
    }

    private void ChooseReplayToLoad()
    {
        FileDialog fileDialog = new FileDialog();
        fileDialog.Filters = ["*.mcr"];
        fileDialog.UseNativeDialog = true;
        fileDialog.Title = "Choose Replay";
        fileDialog.Access = FileDialog.AccessEnum.Filesystem;
        fileDialog.FileMode = FileDialog.FileModeEnum.OpenFile;
        fileDialog.Connect(FileDialog.SignalName.FileSelected, Callable.From<string>(LoadReplay));
        fileDialog.Show();
    }

    private void LoadReplay(string path)
    {
        using MemoryStream memoryStream = new();
        using (FileAccessStream stream = new(path, FileAccess.ModeFlags.Read))
        {
            stream.CopyTo(memoryStream);
        }

        PacketReader reader = new();
        reader.Reset(memoryStream.ToArray());
        CombatReplay replay = reader.Read<CombatReplay>();

        TaskHelper.RunSafely(RunReplay(replay));
    }

    private async Task RunReplay(CombatReplay replay)
    {
        Log.Info($"Loaded replay. Game version: {replay.version} Commit: {replay.gitCommit} Model ID hash: {replay.modelIdHash}");

        if (replay.modelIdHash != ModelIdSerializationCache.Hash)
        {
            if (!_ignoreReplayModelIdHash)
            {
                Log.Error($"Attempting to load replay with Model ID hash {replay.modelIdHash} that does not match ours ({ModelIdSerializationCache.Hash})! The replay will mismatch. If you want to continue anyway, try running the replay again.");
                _ignoreReplayModelIdHash = true;
                return;
            }
            else
            {
                Log.Warn("Ignoring model ID hash mismatch in replay.");
            }
        }

        string? gitCommit = ReleaseInfoManager.Instance.ReleaseInfo?.Commit ?? GitHelper.ShortCommitId;

        if (replay.gitCommit != gitCommit)
        {
            Log.Warn($"Git commit in replay {replay.gitCommit} does not match ours ({gitCommit}). The replay has a chance of mismatching!");
        }

        ClimbState climbState = ClimbState.FromSerializable(replay.serializableClimb);
        ClimbManager.Instance.SetUpReplay(climbState, replay);

        ClimbManager.Instance.CombatStateSynchronizer.IsDisabled = true;

        await PreloadManager.LoadClimbAssets(climbState.Players.Select(p => p.Character));
        await PreloadManager.LoadActAssets(climbState.Act);

        ClimbManager.Instance.Launch();
        NAudioManager.Instance?.StopMusic();

        await ClimbManager.Instance.GenerateMap();
        NGame.Instance!.RootSceneContainer.SetCurrentScene(NClimb.Create(climbState));

        ClimbManager.Instance.ActionQueueSet.FastForwardNextActionId(replay.nextActionId);
        ClimbManager.Instance.ActionQueueSynchronizer.FastForwardHookId(replay.nextHookId);
        ClimbManager.Instance.ChecksumTracker.LoadReplayChecksums(replay.checksumData, replay.nextChecksumId);
        ClimbManager.Instance.PlayerChoiceSynchronizer.FastForwardChoiceIds(replay.choiceIds);

        await ClimbManager.Instance.LoadIntoLatestMapCoord(AbstractRoom.FromSerializable(replay.serializableClimb.PreFinishedRoom, climbState));

        while (ClimbManager.Instance.ActionExecutor.IsPaused)
        {
            await Engine.GetMainLoop().ToSignal(Engine.GetMainLoop(), SceneTree.SignalName.ProcessFrame);
        }

        foreach (CombatReplayEvent replayEvent in replay.events)
        {
            switch (replayEvent.eventType)
            {
                case CombatReplayEventType.GameAction:
                {
                    while (CombatManager.Instance.EndingPlayerTurnPhaseOne ||
                        CombatManager.Instance.EndingPlayerTurnPhaseTwo ||
                        CombatManager.Instance.DebugOnlyGetState()!.CurrentSide == CombatSide.Enemy)
                    {
                        await new SignalAwaiter(Engine.GetMainLoop(), SceneTree.SignalName.ProcessFrame, Engine.GetMainLoop());
                    }

                    Player player = climbState.GetPlayer(replayEvent.playerId!.Value)!;
                    GameAction action = replayEvent.action!.ToGameAction(player);
                    ClimbManager.Instance.ActionQueueSet.EnqueueWithoutSynchronizing(action);
                    break;
                }
                case CombatReplayEventType.HookAction:
                {
                    uint hookId = replayEvent.hookId!.Value;
                    ClimbManager.Instance.ActionQueueSet.EnqueueWithoutSynchronizing(ClimbManager.Instance.ActionQueueSynchronizer.GetHookActionForId(hookId, replayEvent.playerId!.Value));
                    break;
                }
                case CombatReplayEventType.ResumeAction:
                    ClimbManager.Instance.ActionQueueSet.ResumeActionWithoutSynchronizing(replayEvent.actionId!.Value);
                    break;
                case CombatReplayEventType.PlayerChoice:
                {
                    Player player = climbState.GetPlayer(replayEvent.playerId!.Value)!;
                    ClimbManager.Instance.PlayerChoiceSynchronizer.ReceiveReplayChoice(player, replayEvent.choiceId!.Value, replayEvent.playerChoiceResult!.Value);
                    break;
                }
                default:
                    throw new InvalidEnumArgumentException();
            }

            await ClimbManager.Instance.ActionExecutor.FinishedExecutingActions();

            while (CombatManager.Instance.EndingPlayerTurnPhaseTwo ||
                CombatManager.Instance.DebugOnlyGetState()!.CurrentSide == CombatSide.Enemy)
            {
                await new SignalAwaiter(Engine.GetMainLoop(), SceneTree.SignalName.ProcessFrame, Engine.GetMainLoop());
            }

            await ClimbManager.Instance.ActionExecutor.FinishedExecutingActions();
        }
    }

    private void OnCharacterChanged(CharacterModel model)
    {
        _lobby!.SetLocalCharacter(model);
        _localPlayerData.CharacterId = model.Id;
        _localPlayerData.CurrentHp = model.StartingHp;
        _localPlayerData.MaxHp = model.StartingHp;
        _localPlayerData.MaxEnergy = model.MaxEnergy;
        _localPlayerData.MaxPotionSlotCount = 3;
        _localPlayerData.Gold = model.StartingGold;
    }

    public override void _Process(double delta)
    {
        _lobby?.NetService.Update();
    }

    public void PlayerConnected(LobbyPlayer player)
    {
        _characterContainers[player.slotId].characterImage.Texture = player.character.IconTexture;
        _characterContainers[player.slotId].playerName.Text = PlatformUtil.GetPlayerName(_lobby!.NetService.Platform, player.id);
    }

    public void PlayerChanged(LobbyPlayer player)
    {
        _characterContainers[player.slotId].characterImage.Texture = player.character.IconTexture;
        _characterContainers[player.slotId].playerName.Text = PlatformUtil.GetPlayerName(_lobby!.NetService.Platform, player.id);
    }

    public void AscensionChanged() { }
    public void SeedChanged() { }
    public void ModifiersChanged() { }
    public void MaxAscensionChanged() { }

    public void RemotePlayerDisconnected(LobbyPlayer player)
    {
        _characterContainers[player.slotId].characterImage.Texture = null;
        _characterContainers[player.slotId].playerName.Text = "?";
    }

    public void LocalPlayerDisconnected(NetErrorInfo info)
    {
        _lobby = null;
        _characterPaginator.Visible = false;
        _readyButton.Visible = false;
        _characterPaginator.SetIndex(0);

        _game.RemoteCursorContainer.Deinitialize();
        _game.ReactionContainer.DeinitializeNetworking();
    }
}
