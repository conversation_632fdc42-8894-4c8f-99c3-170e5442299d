using Godot;
using MegaCrit.Sts2.Core.Helpers;

namespace MegaCrit.Sts2.Core.Nodes.Debug;

public partial class NDebugAspectRatio : Control
{
    private Window _window = default!;
    private Label _infoLabel = default!;
    private TextureRect _bg = default!;

    // Min/max aspect ratios allowed for this game
    private const float _maxNarrowRatio = 1024f / 768f;
    private const float _maxWideRatio = 3440f / 1440f;

    // Handles scaling the bg asset to fit when the aspect ratio is narrower than 15:10
    private static readonly Vector2 _defaultBgScale = Vector2.One * 1.01f; // Slightly off because of <PERSON>'s past mistakes
    private const float _bgScaleRatioThreshold = 1.5f;

    public override void _Ready()
    {
        _bg = GetNode<TextureRect>("EventBg");
        _infoLabel = GetNode<Label>("Anchors/Label");
        _window = GetTree().Root;
        _window.Connect(Viewport.SignalName.SizeChanged, Callable.From(OnWindowChange));
    }

    private void OnWindowChange()
    {
        float ratio = (float)_window.Size.X / _window.Size.Y;
        string ratioString = ratio.ToString("0.000");

        ScaleBgIfNarrow(ratio);

        // Too wide
        if (ratio > _maxWideRatio)
        {
            _window.ContentScaleAspect = Window.ContentScaleAspectEnum.KeepWidth;
            _window.ContentScaleSize = new Vector2I(2580, 1080);
            _infoLabel.Text = $"{ratioString}: {_window.Size}";
            _infoLabel.Modulate = StsColors.red;
        }
        // Too narrow
        else if (ratio < _maxNarrowRatio)
        {
            _window.ContentScaleAspect = Window.ContentScaleAspectEnum.KeepHeight;
            _window.ContentScaleSize = new Vector2I(1680, 1260);
            _infoLabel.Text = $"{ratioString}: {_window.Size}";
            _infoLabel.Modulate = StsColors.red;
        }
        // Default case (window is between 4:3 and 21:9 aspect ratios)
        else
        {
            _window.ContentScaleAspect = Window.ContentScaleAspectEnum.Expand;
            _window.ContentScaleSize = new Vector2I(1680, 1080);
            _infoLabel.Text = $"{ratioString}: {_window.Size}";
            _infoLabel.Modulate = StsColors.cream;
        }
    }

    private void ScaleBgIfNarrow(float ratio)
    {
        // If we're starting to get a bit narrow, we need to scale up the bg asset.
        if (ratio < _bgScaleRatioThreshold)
        {
            _bg.Scale = Vector2.One * 1.05f;
        }
        else
        {
            // The screen getting wider is handled by Godot's default behavior
            _bg.Scale = _defaultBgScale;
        }
    }
}