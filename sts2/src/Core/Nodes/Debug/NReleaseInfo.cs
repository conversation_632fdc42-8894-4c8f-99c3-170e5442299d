using System;
using Godot;
using MegaCrit.Sts2.Core.Debug;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.Vfx;

namespace MegaCrit.Sts2.Core.Nodes.Debug;

public partial class NReleaseInfo : Control
{
    public override void _Ready()
    {
        Label date = GetNode<Label>("%Date");
        Label version = GetNode<Label>("%Version");
        Label commitId = GetNode<Label>("%CommitId");
        ReleaseInfo? releaseInfo = ReleaseInfoManager.Instance.ReleaseInfo;

        // NOTE: Can't this all be one label? StringBuilder here?
        if (releaseInfo == null)
        {
            date.Text = DateTime.Now.ToString("yyyy-MM-dd");
            version.Text = "Unreleased";
            // use git commit id if release info is not available and use "????????" if git commit id is not available
            commitId.Text = GitHelper.ShortCommitId ?? "????????";
            return;
        }

        date.Text = $"{releaseInfo.Date:yyyy-MM-dd}";
        version.Text = releaseInfo.Version;
        commitId.Text = releaseInfo.Commit;
    }

    public override void _Input(InputEvent inputEvent)
    {
        if (inputEvent.IsActionReleased(DebugHotkey.hideVersionInfo))
        {
            Visible = !Visible;
            NGame.Instance!.AddChildSafely(NFullscreenTextVfx.Create(Visible ? "Show Version Info" : "Hide Version Info")!);
        }
    }
}
