using Godot;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Settings;

namespace MegaCrit.Sts2.Core.Nodes.Events;

public partial class NAncientEventLayout : NEventLayout
{
    protected override void AnimateIn()
    {
        _description.Modulate = Colors.Transparent;

        _descriptionTween?.Kill();
        _descriptionTween = CreateTween().SetParallel();

        if (SaveManager.Instance.SettingsSave.FastMode == FastModeType.Fast)
        {
            _descriptionTween.TweenInterval(0.2);
        }
        else
        {
            _descriptionTween.TweenInterval(0.5);
        }

        _descriptionTween.Chain();
        _descriptionTween.TweenProperty(_description, "modulate", Colors.White, 1.0);
    }
}
