using System;
using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Nodes.Cards;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.HoverTips;
using MegaCrit.Sts2.Core.Nodes.Potions;
using MegaCrit.Sts2.Core.Nodes.Relics;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Settings;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Multiplayer;

/// <summary>
/// Handles the following:
///  - Displaying "intents" for remote players when they hover over potions, cards, relics, and powers
///  - Pulling cards that are executing player choice from the play pile and displaying them above the player's head
///    with some thinky dots, then returning those cards to the play queue
///  - Displaying relics, potions, and powers that are executing player choice and displaying them above the player's head
///    with thinky dots
///
/// Note that none of these are executed in singleplayer.
/// For the local player in multiplayer, the first responsibility is excluded (we know what we're hovering). However,
/// the last two points still apply.
/// </summary>
public partial class NMultiplayerPlayerIntentHandler : Control
{
    private const string _scenePath = "combat/multiplayer_player_intent";

    private NMultiplayerCardIntent _cardIntent = default!;
    private NRelic _relicIntent = default!;
    private NPotion _potionIntent = default!;
    private NPower _powerIntent = default!;
    private Control _hitbox = default!;
    private NRemoteTargetingIndicator _targetingIndicator = default!;

    private MegaRichTextLabel _cardThinkyDots = default!;
    private MegaRichTextLabel _relicThinkyDots = default!;
    private MegaRichTextLabel _potionThinkyDots = default!;
    private MegaRichTextLabel _powerThinkyDots = default!;

    private bool _shouldShowHoverTip;

    private Player _player = default!;
    private AbstractModel? _displayedModel;
    private NHoverTipSet? _hoverTips;

    // If the remote player is making a card choice (like in a Survivor play), then _isInPlayerChoice is set to true and
    // _cardInPlayAwaitingPlayerChoice is set to the card node we stole from the play pile to use as an intent.
    private bool _isInPlayerChoice;
    private NCard? _cardInPlayAwaitingPlayerChoice;

    // REMEMBER TO USE GetTree().CreateTween() in this class because we mess with ProcessMode!
    private Tween? _tween;

    public NMultiplayerCardIntent CardIntent => _cardIntent;

    public static NMultiplayerPlayerIntentHandler? Create(Player player)
    {
        if (TestMode.IsOn) return null;

        // Don't show in bootstrap and replays
        if (ClimbManager.Instance.NetService.Type is NetGameType.Singleplayer or NetGameType.Replay) return null;

        NMultiplayerPlayerIntentHandler handler = PreloadManager.Cache.GetScene(SceneHelper.GetScenePath(_scenePath)).Instantiate<NMultiplayerPlayerIntentHandler>();
        handler._player = player;

        return handler;
    }

    public override void _Ready()
    {
        _cardIntent = GetNode<NMultiplayerCardIntent>("%CardIntent");
        _relicIntent = GetNode<NRelic>("%RelicIntent");
        _potionIntent = GetNode<NPotion>("%PotionIntent");
        _powerIntent = GetNode<NPower>("%PowerIntent");
        _hitbox = GetNode<Control>("%Hitbox");
        _targetingIndicator = GetNode<NRemoteTargetingIndicator>("%TargetingIndicator");

        _cardThinkyDots = _cardIntent.GetNode<MegaRichTextLabel>("ThinkyDots");
        _relicThinkyDots = _relicIntent.GetNode<MegaRichTextLabel>("ThinkyDots");
        _potionThinkyDots = _potionIntent.GetNode<MegaRichTextLabel>("ThinkyDots");
        _powerThinkyDots = _powerIntent.GetNode<MegaRichTextLabel>("ThinkyDots");

        _targetingIndicator.Initialize(_player);

        _cardIntent.Visible = false;
        _relicIntent.Visible = false;
        _potionIntent.Visible = false;
        _powerIntent.Visible = false;

        HideThinkyDots();

        _hitbox.Connect(Control.SignalName.FocusEntered, Callable.From(OnHitboxEntered));
        _hitbox.Connect(Control.SignalName.FocusExited, Callable.From(OnHitboxExited));
        _hitbox.Connect(Control.SignalName.MouseEntered, Callable.From(OnHitboxEntered));
        _hitbox.Connect(Control.SignalName.MouseExited, Callable.From(OnHitboxExited));

        ClimbManager.Instance.ActionQueueSet.ActionEnqueued += OnActionEnqueued;

        if (!LocalContext.IsMe(_player))
        {
            // This stuff only happens for remote players
            ClimbManager.Instance.HoveredModelTracker.HoverChanged += OnHoverChanged;
            ClimbManager.Instance.InputSynchronizer.StateChanged += OnPeerInputStateChanged;
            ClimbManager.Instance.InputSynchronizer.StateRemoved += OnPeerInputStateRemoved;
        }

        ProcessMode = ProcessModeEnum.Disabled;
    }

    public override void _ExitTree()
    {
        ClimbManager.Instance.ActionQueueSet.ActionEnqueued -= OnActionEnqueued;

        if (!LocalContext.IsMe(_player))
        {
            // This stuff only happens for remote players
            ClimbManager.Instance.HoveredModelTracker.HoverChanged -= OnHoverChanged;
            ClimbManager.Instance.InputSynchronizer.StateChanged -= OnPeerInputStateChanged;
            ClimbManager.Instance.InputSynchronizer.StateRemoved -= OnPeerInputStateRemoved;
        }
    }

    private void OnHitboxEntered()
    {
        _shouldShowHoverTip = true;
        RefreshHoverTips();
    }

    private void OnHitboxExited()
    {
        _shouldShowHoverTip = false;
        RefreshHoverTips();
    }

    private void OnHoverChanged(ulong playerId)
    {
        if (_player.NetId != playerId) return;
        RefreshHoverDisplay();
    }

    private void RefreshHoverDisplay()
    {
        if (_isInPlayerChoice) return;

        // Clean up state from player choice, if any
        _tween?.Kill();
        HideThinkyDots();

        AbstractModel? hoveredModel = ClimbManager.Instance.HoveredModelTracker.GetHoveredModel(_player.NetId);
        if (_displayedModel == hoveredModel) return;

        Modulate = StsColors.halfTransparentWhite;
        _cardIntent.Visible = false;
        _relicIntent.Visible = false;
        _potionIntent.Visible = false;
        _powerIntent.Visible = false;
        _hitbox.Visible = hoveredModel != null;

        if (hoveredModel != null)
        {
            switch (hoveredModel)
            {
                case CardModel cardModel:
                    _cardIntent.Visible = true;
                    _cardIntent.Card = cardModel;
                    _hitbox.Position = _cardIntent.Position;
                    _hitbox.Size = _cardIntent.Size;
                    break;
                case PotionModel potionModel:
                    _potionIntent.Visible = true;
                    _potionIntent.Model = potionModel;
                    _hitbox.Position = _potionIntent.Position;
                    _hitbox.Size = _potionIntent.Size;
                    break;
                case RelicModel relicModel:
                    _relicIntent.Visible = true;
                    _relicIntent.Model = relicModel;
                    _hitbox.Position = _relicIntent.Position;
                    _hitbox.Size = _relicIntent.Size;
                    break;
                case PowerModel powerModel:
                    _powerIntent.Visible = true;
                    _powerIntent.Model = powerModel;
                    _hitbox.Position = _powerIntent.Position;
                    _hitbox.Size = _powerIntent.Size;
                    break;
                default:
                    throw new InvalidOperationException($"Player {_player.NetId} hovering unsupported model {hoveredModel}");
            }
        }

        RefreshHoverTips();

        _displayedModel = hoveredModel;
    }

    private void OnPeerInputStateChanged(ulong playerId)
    {
        if (playerId != _player.NetId) return;

        bool isTargeting = ClimbManager.Instance.InputSynchronizer.GetIsTargeting(_player.NetId);

        if (isTargeting && !_targetingIndicator.Visible)
        {
            _targetingIndicator.StartDrawingFrom(Vector2.Zero);
            ProcessMode = ProcessModeEnum.Inherit;
        }
        else if (!isTargeting && _targetingIndicator.Visible)
        {
            _targetingIndicator.StopDrawing();
            ProcessMode = ProcessModeEnum.Disabled;
        }
    }

    private void OnPeerInputStateRemoved(ulong playerId)
    {
        if (playerId != _player.NetId) return;

        if (_targetingIndicator.Visible)
        {
            _targetingIndicator.StopDrawing();
            ProcessMode = ProcessModeEnum.Disabled;
        }
    }

    // We do this in _Process and not OnStateChanged to take advantage of NRemoteMouseCursor's position interpolation
    public override void _Process(double delta)
    {
        Vector2 globalMousePos = NGame.Instance!.RemoteCursorContainer.GetCursorPosition(_player.NetId);

        // Usually we'd be able to use GetTransform().Inverse(), but since we're nested in some Node2Ds it screws up the
        // transform hierarchy
        _targetingIndicator.UpdateDrawingTo(globalMousePos - _targetingIndicator.GlobalPosition);
    }

    private void OnActionEnqueued(GameAction action)
    {
        if (action.OwnerId != _player.NetId) return;
        action.BeforeExecuted += BeforeActionExecuted;
    }

    private void BeforeActionExecuted(GameAction action)
    {
        action.BeforeExecuted -= BeforeActionExecuted;

        // Hook into a bunch of action events to prepare for the possibility that this is an action that will require
        // player choice.
        action.BeforePausedForPlayerChoice += BeforeActionPausedForPlayerChoice;
        action.BeforeReadyToResumeAfterPlayerChoice += BeforeActionReadyToResumeAfterPlayerChoice;
        action.AfterFinished += UnsubscribeFromAction;
        action.BeforeCancelled += UnsubscribeFromAction;
    }

    private void UnsubscribeFromAction(GameAction action)
    {
        action.BeforePausedForPlayerChoice -= BeforeActionPausedForPlayerChoice;
        action.BeforeReadyToResumeAfterPlayerChoice -= BeforeActionReadyToResumeAfterPlayerChoice;
        action.AfterFinished -= UnsubscribeFromAction;
        action.BeforeCancelled -= UnsubscribeFromAction;
    }

    private void BeforeActionPausedForPlayerChoice(GameAction action)
    {
        AbstractModel? model = null;

        if (action is PlayCardAction playCardAction)
        {
            model = playCardAction.PlayerChoiceContext?.LastInvolvedModel;
        }
        else if (action is UsePotionAction usePotionAction)
        {
            model = usePotionAction.PlayerChoiceContext?.LastInvolvedModel;
        }
        else if (action is GenericHookGameAction hookGameAction)
        {
            model = hookGameAction.ChoiceContext?.LastInvolvedModel;
        }

        if (model == null) return;

        _isInPlayerChoice = true;

        _cardIntent.Visible = false;
        _relicIntent.Visible = false;
        _potionIntent.Visible = false;
        _powerIntent.Visible = false;
        _hitbox.Visible = false;
        _cardInPlayAwaitingPlayerChoice = null;

        if (model is CardModel cardModel)
        {
            // If card is on table, use that instead of the card intent
            NCard? card = NCard.FindOnTable(cardModel);

            _cardThinkyDots.Visible = true;
            _cardThinkyDots.ProcessMode = ProcessModeEnum.Always;

            if (card != null)
            {
                Tween tween = card.CreateTween();
                tween.Parallel().TweenProperty(card, "position", _cardIntent.GlobalPosition + _cardIntent.Size / 2f, SaveManager.Instance.SettingsSave.FastMode == FastModeType.Fast ? 0.2f : 0.3f);
                tween.Parallel().TweenProperty(card, "scale", Vector2.One * 0.25f, SaveManager.Instance.SettingsSave.FastMode == FastModeType.Fast ? 0.2f : 0.3f);
                _cardInPlayAwaitingPlayerChoice = card;

                // When we're using an NCard on the table, we want the thinky dots to show up above it, so we need to reparent
                _cardThinkyDots.Reparent(card.GetParent());
                _hitbox.Reparent(card.GetParent());
            }
            else
            {
                _cardIntent.Card = cardModel;
                _cardIntent.Visible = true;
            }

            _hitbox.Visible = true;
            _hitbox.GlobalPosition = _cardIntent.GlobalPosition;
            _hitbox.Size = _cardIntent.Size;
        }
        else if (model is RelicModel relicModel)
        {
            _relicIntent.Model = relicModel;
            _relicIntent.Visible = true;
            _relicThinkyDots.Visible = true;
            _relicThinkyDots.ProcessMode = ProcessModeEnum.Always;

            _hitbox.Visible = true;
            _hitbox.Position = _relicIntent.Position;
            _hitbox.Size = _relicIntent.Size;
        }
        else if (model is PotionModel potionModel)
        {
            _potionIntent.Model = potionModel;
            _potionIntent.Visible = true;
            _potionThinkyDots.Visible = true;
            _potionThinkyDots.ProcessMode = ProcessModeEnum.Always;

            _hitbox.Visible = true;
            _hitbox.Position = _potionIntent.Position;
            _hitbox.Size = _potionIntent.Size;
        }
        else if (model is PowerModel powerModel)
        {
            _powerIntent.Model = powerModel;
            _powerIntent.Visible = true;
            _powerThinkyDots.Visible = true;
            _powerThinkyDots.ProcessMode = ProcessModeEnum.Always;

            _hitbox.Visible = true;
            _hitbox.Position = _powerIntent.Position;
            _hitbox.Size = _powerIntent.Size;
        }

        RefreshHoverTips();

        Modulate = StsColors.transparentWhite;
        _tween?.Kill();
        _tween = GetTree().CreateTween();
        _tween.TweenProperty(this, "modulate", Colors.White, 0.25f);
    }

    private void BeforeActionReadyToResumeAfterPlayerChoice(GameAction action)
    {
        _tween?.Kill();
        _tween = GetTree().CreateTween();
        _tween.TweenProperty(this, "modulate", StsColors.transparentWhite, 0.15f);
        _tween.TweenCallback(Callable.From(HideThinkyDots));

        _isInPlayerChoice = false;

        if (_cardInPlayAwaitingPlayerChoice != null)
        {
            // Reparent thinky dots back to where they used to be
            _cardThinkyDots.Reparent(_cardIntent);
            _hitbox.Reparent(this);

            // Since the card is ready to resume, but not yet resumed, re-insert the card back into the play queue
            NCardPlayQueue.Instance!.ReAddCardAfterPlayerChoice(_cardInPlayAwaitingPlayerChoice, (PlayCardAction)action);

            _cardInPlayAwaitingPlayerChoice = null;
        }
    }

    private void HideThinkyDots()
    {
        _cardThinkyDots.Visible = false;
        _relicThinkyDots.Visible = false;
        _potionThinkyDots.Visible = false;
        _powerThinkyDots.Visible = false;

        _cardThinkyDots.ProcessMode = ProcessModeEnum.Disabled;
        _relicThinkyDots.ProcessMode = ProcessModeEnum.Disabled;
        _potionThinkyDots.ProcessMode = ProcessModeEnum.Disabled;
        _powerThinkyDots.ProcessMode = ProcessModeEnum.Disabled;
    }

    private void RefreshHoverTips()
    {
        NHoverTipSet.Remove(this);

        // Sometimes, when the hitbox gets resized, the mouse enter/exit events do not fire
        if (_hitbox.GetGlobalRect().HasPoint(GetGlobalMousePosition()))
        {
            _shouldShowHoverTip = true;
        }

        if (!_hitbox.Visible)
        {
            _shouldShowHoverTip = false;
        }

        if (!_shouldShowHoverTip) return;

        List<IHoverTip> hoverTips = [];

        if (_cardInPlayAwaitingPlayerChoice != null)
        {
            hoverTips.Add(HoverTipFactory.FromCard(_cardInPlayAwaitingPlayerChoice.Model!, false));
            hoverTips.AddRange(_cardInPlayAwaitingPlayerChoice.Model!.HoverTips);
        }
        else if (_cardIntent.Visible)
        {
            hoverTips.Add(HoverTipFactory.FromCard(_cardIntent.Card!, false));
            hoverTips.AddRange(_cardIntent.Card!.HoverTips);
        }
        else if (_relicIntent.Visible)
        {
            hoverTips.AddRange(_relicIntent.Model.HoverTips);
        }
        else if (_potionIntent.Visible)
        {
            hoverTips.Add(HoverTipFactory.FromPotion(_potionIntent.Model));
            hoverTips.AddRange(_potionIntent.Model.HoverTips);
        }

        if (hoverTips.Count > 0)
        {
            NHoverTipSet.CreateAndShow(this, hoverTips, HoverTipAlignment.Right);
        }
    }
}
