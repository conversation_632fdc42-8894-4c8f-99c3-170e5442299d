using System.Collections.Concurrent;
using Godot;
using MegaCrit.Sts2.Core.Assets;

namespace MegaCrit.Sts2.Core.Nodes;

public partial class NAssetLoader : Node
{
    private static NAssetLoader? _instance;
    public static NAssetLoader Instance => _instance ?? new NAssetLoader();
    private readonly ConcurrentQueue<AssetLoadingSession?> _sessions = new();
    private AssetLoadingSession? _currentSession;

    public override void _Ready()
    {
        _instance ??= this;
    }

    public void LoadInTheBackground(AssetLoadingSession session)
    {
        _sessions.Enqueue(session);

        SetProcess(true); // enable the loading processing
    }

    public override void _Process(double delta)
    {
        if (_currentSession == null || _currentSession.IsCompleted)
        {
            if (_sessions.TryDequeue(out AssetLoadingSession? session))
            {
                _currentSession = session;
            }
            else
            {
                // Disable the loader once complete
                SetProcess(false);
            }
        }
        else
        {
            _currentSession.Process();
            // _currentSession.PrintStatus();
        }
    }
}
