using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Potions;

public partial class NPotion : Control
{
    private const float _newlyAcquiredPopDuration = 0.35f;
    private const float _newlyAcquiredFadeInDuration = 0.1f;
    private const float _newlyAcquiredPopDistance = 40f;

    private PotionModel? _model;

    private Control _container = default!;
    public TextureRect Image { get; private set; } = default!;
    public TextureRect Outline { get; private set; } = default!;

    // private TextureRect _bottle = default!;
    // private TextureRect _overlay = default!;
    // private TextureRect _juice = default!;

    private Tween? _bounceTween;
    private Tween? _obtainedTween;
    private CancellationTokenSource? _cancellationTokenSource;

    private static string ScenePath => SceneHelper.GetScenePath("/potions/potion");
    public static IEnumerable<string> AssetPaths => [ScenePath, NPotionFlashVfx.ScenePath];

    public PotionModel Model
    {
        get => _model ?? throw new InvalidOperationException("Model was accessed before it was set.");
        set
        {
            value.AssertMutable();

            _model = value;
            Reload();
        }
    }

    /// <summary>
    /// Create an instance of the node.
    /// Null if we're in test mode.
    /// </summary>
    public static NPotion? Create(PotionModel potion)
    {
        if (TestMode.IsOn) return null;

        NPotion node = PreloadManager.Cache.GetScene(ScenePath).Instantiate<NPotion>();
        node.Model = potion;

        return node;
    }

    public override void _Ready()
    {
        Image = GetNode<TextureRect>("%Image");
        Outline = GetNode<TextureRect>("%Outline");
        // _bottle = GetNode<TextureRect>("%Bottle");
        // _overlay = GetNode<TextureRect>("%Overlay");
        // _juice = GetNode<TextureRect>("%Juice");
        _container = GetNode<Control>("Container");
        Reload();
    }

    private void Reload()
    {
        if (!IsNodeReady() || _model == null) return;

        Image.Texture = _model.Image;
        Outline.Texture = _model.Outline;

        // _bottle.Texture = _model.BodyTexture;
        // _overlay.Texture = _model.OverlayTexture;
        // _juice.Texture = _model.JuiceTexture;
        // _juice.Modulate = _model.JuiceTint;
        // _overlay.Modulate = _model.OverlayTint;
    }

    public async Task PlayNewlyAcquiredAnimation(Vector2? startLocation)
    {
        if (_cancellationTokenSource != null)
        {
            await _cancellationTokenSource.CancelAsync();
        }

        CancellationTokenSource cancelTokenSource = new();
        _cancellationTokenSource = cancelTokenSource;

        // NPotion seems to not be placed in the correct position within the NPotionContainer until one frame after it's
        // added, which is a problem for the global -> local transform needed when startLocation is specified
        await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);

        if (cancelTokenSource.IsCancellationRequested)
        {
            return;
        }

        _obtainedTween?.Kill();

        if (startLocation == null)
        {
            _container.Position = _container.Position with { Y = _newlyAcquiredPopDistance };
            _container.Modulate = _container.Modulate with { A = 0f };

            _obtainedTween = GetTree().CreateTween();

            _obtainedTween.TweenProperty(_container, "modulate:a", 1f, _newlyAcquiredFadeInDuration);
            _obtainedTween.Parallel();

            _obtainedTween.SetEase(Tween.EaseType.Out);
            _obtainedTween.SetTrans(Tween.TransitionType.Back);

            _obtainedTween.TweenProperty(_container, "position:y", 0f, _newlyAcquiredPopDuration);
            _obtainedTween.TweenCallback(Callable.From(DoFlash));
        }
        else
        {
            _container.GlobalPosition = startLocation.Value;
            _container.Modulate = _container.Modulate with { A = 1f };

            _obtainedTween = GetTree().CreateTween();
            _obtainedTween.SetEase(Tween.EaseType.Out);
            _obtainedTween.SetTrans(Tween.TransitionType.Quad);

            _obtainedTween.TweenProperty(_container, "position", Vector2.Zero, _newlyAcquiredPopDuration);
            _obtainedTween.TweenCallback(Callable.From(DoFlash));
        }
    }

    private void DoFlash()
    {
        this.AddChildSafely(NPotionFlashVfx.Create(this)!);
    }

    public void DoBounce()
    {
        _bounceTween?.Kill();
        _bounceTween = CreateTween();
        _bounceTween.TweenProperty(_container, "position:y", Position.Y - 12f, 0.125)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Sine);
        _bounceTween.TweenProperty(_container, "position:y", 0f, 0.125)
            .SetEase(Tween.EaseType.In)
            .SetTrans(Tween.TransitionType.Sine);
    }
}
