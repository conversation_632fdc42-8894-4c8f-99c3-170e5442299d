using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.Ftue;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.Nodes.Potions;

public partial class NPotionContainer : Control
{
    // Will be null until initialized.
    private Player? _player;

    private readonly List<NPotionHolder> _holders = [];
    private Control _potionHolders = default!;
    private Control _potionErrorBg = default!;
    private NButton _potionShortcutButton = default!;

    private Tween? _potionsFullTween;
    private Vector2 _potionHolderInitPos;

    private NPotionHolder? _focusedHolder;

    public override void _EnterTree()
    {
        _potionHolders = GetNode<Control>("MarginContainer/PotionHolders");
        _potionErrorBg = GetNode<Control>("PotionErrorBg");
        _potionShortcutButton = GetNode<NButton>("PotionShortcutButton");
        _potionErrorBg.Modulate = Colors.Transparent;

        _potionShortcutButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(_ => { _potionHolders.GetChild<Control>(0).TryGrabFocus(); }));

        CombatManager.Instance.CombatSetUp += OnCombatSetUp;
        ConnectPlayerEvents();
    }

    public override void _ExitTree()
    {
        DisconnectPlayerEvents();
        _player = null;
        CombatManager.Instance.CombatSetUp -= OnCombatSetUp;
    }

    public void Initialize(IClimbState climbState)
    {
        DisconnectPlayerEvents();
        _player = LocalContext.GetMe(climbState)!;
        ConnectPlayerEvents();

        GrowPotionHolders(_player.MaxPotionCount);

        foreach (PotionModel potion in _player.Potions)
        {
            Add(potion);
        }
    }

    private void ConnectPlayerEvents()
    {
        if (_player == null) return;

        _player.AddPotionFailed += PlayAddFailedAnim;
        _player.PotionProcured += OnPotionProcured;
        _player.UsedPotionRemoved += OnUsedPotionRemoved;
        _player.PotionDiscarded += Discard;
        _player.MaxPotionCountChanged += GrowPotionHolders;
        _player.RelicObtained += OnRelicsUpdated;
        _player.RelicRemoved += OnRelicsUpdated;
    }

    private void DisconnectPlayerEvents()
    {
        if (_player == null) return;

        _player.AddPotionFailed -= PlayAddFailedAnim;
        _player.PotionProcured -= OnPotionProcured;
        _player.UsedPotionRemoved -= OnUsedPotionRemoved;
        _player.PotionDiscarded -= Discard;
        _player.MaxPotionCountChanged -= GrowPotionHolders;
        _player.RelicObtained -= OnRelicsUpdated;
        _player.RelicRemoved -= OnRelicsUpdated;
    }

    private void GrowPotionHolders(int newMaxPotionSlots)
    {
        for (int i = _holders.Count; i < newMaxPotionSlots; i++)
        {
            NPotionHolder node = NPotionHolder.Create(true);
            _holders.Add(node);
            _potionHolders.AddChildSafely(node);

            node.Connect(Control.SignalName.FocusEntered, Callable.From(() => OnPotionHolderFocused(node)));
            node.Connect(Control.SignalName.FocusExited, Callable.From(() => OnPotionHolderUnfocused(node)));
            node.Connect(Control.SignalName.MouseEntered, Callable.From(() => OnPotionHolderFocused(node)));
            node.Connect(Control.SignalName.MouseExited, Callable.From(() => OnPotionHolderUnfocused(node)));
        }

        UpdateNavigation();
    }

    // We have to update the navigation just in case the original first relic was removed.
    private void OnRelicsUpdated(RelicModel _)
    {
        // We defer the call to make sure the relic node gets added to the relic bar before
        // trying to update the navigation
        Callable.From(UpdateNavigation).CallDeferred();
    }

    private void UpdateNavigation()
    {
        Control? relicNode = NClimb.Instance!.GlobalUi.RelicInventory.RelicNodes.FirstOrDefault();
        if (relicNode == null) return;

        for (int i = 0; i < _holders.Count; i++)
        {
            _holders[i].FocusNeighborBottom = relicNode.GetPath();
        }
    }

    private void Add(PotionModel potion)
    {
        if (_holders.All(h => h.HasPotion)) return;

        PotionFtueCheck();

        NPotion node = NPotion.Create(potion)!;
        node.Position = new Vector2(-30f, -30f);
        _holders.First(holder => !holder.HasPotion).AddPotion(node);
    }

    public void AnimatePotion(PotionModel potion, Vector2? startPosition = null)
    {
        if (!LocalContext.IsMine(potion)) return;

        NPotionHolder potionHolder = _holders.First(n => n.Potion!.Model == potion);
        TaskHelper.RunSafely(potionHolder.Potion!.PlayNewlyAcquiredAnimation(startPosition));
    }

    public void OnPotionUseCanceled(PotionModel potion)
    {
        NPotionHolder? potionHolder = _holders.FirstOrDefault(n => n.Potion!.Model == potion);

        if (potionHolder != null)
        {
            potionHolder.CancelPotionUse();
        }
        else
        {
            Log.Error($"Tried to cancel potion use for potion {potion} but a holder for it does not exist in the player's belt!");
        }
    }

    private void PotionFtueCheck()
    {
        if (!SaveManager.Instance.SeenFtue(NObtainPotionFtue.id))
        {
            NModalContainer.Instance!.Add(NObtainPotionFtue.Create()!);
            SaveManager.Instance.MarkFtueAsComplete(NObtainPotionFtue.id);
        }
    }

    private void PlayAddFailedAnim()
    {
        // I'm not a fan of having to do this, but there is no reliable way of resetting the position if I end the tween
        // early. (I miss DoTween.DoShakePosition)
        if (_potionsFullTween != null && _potionsFullTween.IsRunning())
        {
            _potionsFullTween?.Kill();
            _potionHolders.Position = _potionHolderInitPos;
        }

        _potionsFullTween = CreateTween().SetParallel();
        _potionHolderInitPos = _potionHolders.Position;
        _potionsFullTween.TweenMethod(Callable.From<float>
                (t => { _potionHolders.Position = _potionHolderInitPos + Vector2.Right * 3f * Mathf.Sin(t * 5f) * Mathf.Sin(t * 0.5f); }),
            0f, 2f * Mathf.Pi, 0.5f);

        _potionsFullTween.TweenProperty(_potionErrorBg, "modulate", Colors.White, 0.15);
        _potionsFullTween.TweenProperty(_potionErrorBg, "modulate", Colors.Transparent, 0.5).SetDelay(0.35);
    }

    private void Discard(PotionModel potion)
    {
        NPotionHolder node = _holders.First(n => n.Potion != null && n.Potion.Model == potion);
        OnPotionHolderUnfocused(node);
        node.DiscardPotion();
    }

    private void RemoveUsed(PotionModel potion)
    {
        NPotionHolder node = _holders.First(n => n.Potion != null && n.Potion.Model == potion);
        OnPotionHolderUnfocused(node);
        node.RemoveUsedPotion();
    }

    private void OnPotionProcured(PotionModel potion) => Add(potion);
    private void OnUsedPotionRemoved(PotionModel potion) => RemoveUsed(potion);

    private void OnPotionHolderFocused(NPotionHolder holder)
    {
        if (_focusedHolder == holder) return;
        if (holder.Potion == null) return;
        ClimbManager.Instance.HoveredModelTracker.OnLocalPotionHovered(holder.Potion!.Model);
        _focusedHolder = holder;
    }

    private void OnPotionHolderUnfocused(NPotionHolder holder)
    {
        if (_focusedHolder != holder) return;
        ClimbManager.Instance.HoveredModelTracker.OnLocalPotionUnhovered();
        _focusedHolder = null;
    }

    private void OnCombatSetUp(CombatState _)
    {
        TaskHelper.RunSafely(ShinePotions());
    }

    private async Task ShinePotions()
    {
        await Cmd.Wait(1f);

        foreach (NPotionHolder potionHolder in _holders)
        {
            await TaskHelper.RunSafely(potionHolder.ShineOnStartOfCombat());
        }
    }
}
