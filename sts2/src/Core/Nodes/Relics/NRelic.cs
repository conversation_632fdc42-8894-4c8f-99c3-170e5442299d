using System;
using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Relics;

/// <summary>
/// Only contains the visuals (the image) of a relic.
/// Does NOT handle input, that is to be done by a RelicHolder.
/// </summary>
public partial class NRelic : Control
{
    public static IEnumerable<string> AssetPaths => [relicMatPath, _scenePath];
    public const string relicMatPath = "res://materials/ui/relic_mat.tres";

    private static readonly string _scenePath = SceneHelper.GetScenePath("relics/relic");

    public TextureRect Icon { get; private set; } = default!;
    private TextureRect Outline { get; set; } = default!;
    private RelicModel? _model;

    // First argument is the previous model, second argument is new model
    public event Action<RelicModel?, RelicModel?>? ModelChanged;

    public RelicModel Model
    {
        get => _model ?? throw new InvalidOperationException("Model was accessed before it was set.");
        set
        {
            if (_model != value)
            {
                RelicModel? oldModel = _model;
                _model = value;
                ModelChanged?.Invoke(oldModel, _model);
            }

            Reload();
        }
    }

    public static NRelic? Create(RelicModel relic)
    {
        if (TestMode.IsOn) return null;

        NRelic node = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NRelic>();
        node.Name = $"NRelic-{relic.Id}";
        node.Model = relic;

        return node;
    }

    public override void _Ready()
    {
        Icon = GetNode<TextureRect>("%Icon");
        Outline = GetNode<TextureRect>("%Outline");
        Reload();
    }

    private void Reload()
    {
        if (!IsNodeReady()) return;

        if (_model != null)
        {
            Model.UpdateTexture(Icon);
            Icon.Texture = Model.Icon;
            Outline.Texture = Model.IconOutline;
        }
    }
}
