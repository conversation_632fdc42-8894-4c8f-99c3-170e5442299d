using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.HoverTips;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Relics;

/// <summary>
/// Shows the relic in a relic container. The full relic experience; shows the amount, can flash, and can animate in
/// from an arbitrary position.
/// This is used in the relic display under the top bar during a climb.
/// </summary>
public partial class NRelicInventoryHolder : NButton
{
    private static readonly string _scenePath = SceneHelper.GetScenePath("relics/relic_inventory_holder");
    private static readonly string _flashPath = SceneHelper.GetScenePath("vfx/relic_inventory_flash_vfx");

    private const float _newlyAcquiredPopDuration = 0.35f;
    private const float _newlyAcquiredFadeInDuration = 0.1f;
    private const float _newlyAcquiredPopDistance = 40f;

    private NRelic _relic = default!;
    private RelicModel? _subscribedRelic;
    private Label _amountLabel = default!;

    private Tween? _hoverTween;
    private Tween? _obtainedTween;
    private CancellationTokenSource? _cancellationTokenSource;

    private Vector2 _originalIconPosition;
    private RelicModel _model = default!;

    public NRelic Relic => _relic;
    public NRelicInventory Inventory { get; set; } = default!;

    public static NRelicInventoryHolder? Create(RelicModel relic)
    {
        if (TestMode.IsOn) return null;

        NRelicInventoryHolder node = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NRelicInventoryHolder>();
        node.Name = $"NRelicContainerHolder-{relic.Id}";
        node._model = relic;

        return node;
    }

    public override void _Ready()
    {
        ConnectSignals();

        _relic = GetNode<NRelic>("%Relic");
        _amountLabel = GetNode<Label>("%AmountLabel");

        _originalIconPosition = _relic.Icon.Position;
        _relic.ModelChanged += OnModelChanged;
        _relic.Model = _model;
    }

    public override void _ExitTree()
    {
        _hoverTween?.Kill();

        if (_subscribedRelic != null)
        {
            _subscribedRelic.DisplayAmountChanged += OnDisplayAmountChanged;
            _subscribedRelic.StatusChanged += OnStatusChanged;
            _subscribedRelic.Flashed += OnRelicFlashed;
        }

        _subscribedRelic = null;
        _relic.ModelChanged -= OnModelChanged;
    }

    private void OnModelChanged(RelicModel? oldModel, RelicModel? newModel)
    {
        if (oldModel != null)
        {
            oldModel.DisplayAmountChanged -= OnDisplayAmountChanged;
            oldModel.StatusChanged -= OnStatusChanged;
            oldModel.Flashed -= OnRelicFlashed;
        }

        if (newModel != null)
        {
            newModel.DisplayAmountChanged += OnDisplayAmountChanged;
            newModel.StatusChanged += OnStatusChanged;
            newModel.Flashed += OnRelicFlashed;
        }

        RefreshAmount();
        RefreshStatus();

        _subscribedRelic = newModel;
    }

    private void RefreshAmount()
    {
        if (_relic.Model.ShowCounter && ClimbManager.Instance.IsInProgress)
        {
            _amountLabel.Visible = true;
            _amountLabel.Text = _relic.Model.DisplayAmount.ToString();
        }
        else
        {
            _amountLabel.Visible = false;
        }
    }

    private void RefreshStatus()
    {
        if (!ClimbManager.Instance.IsInProgress)
        {
            _relic.Icon.Modulate = Colors.White;
            return;
        }

        _relic.Model.UpdateTexture(_relic.Icon);
        switch (_relic.Model.Status)
        {
            case RelicStatus.Normal:
            case RelicStatus.Active:
                _relic.Icon.Modulate = Colors.White;
                break;
            case RelicStatus.Disabled:
                _relic.Icon.Modulate = new Color("#808080"); // dark grey
                break;
            default:
                throw new ArgumentOutOfRangeException();
        }
    }

    public async Task PlayNewlyAcquiredAnimation(Vector2? startLocation, Vector2? startScale)
    {
        if (_cancellationTokenSource != null)
        {
            await _cancellationTokenSource.CancelAsync();
        }

        CancellationTokenSource cancelTokenSource = new();
        _cancellationTokenSource = cancelTokenSource;

        // NRelic seems to not be placed in the correct position within the NRelicContainer until one frame after it's
        // added, which is a problem for the global -> local transform needed when startLocation is specified
        await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);

        if (cancelTokenSource.IsCancellationRequested)
        {
            return;
        }

        _obtainedTween?.Kill();

        if (startLocation == null)
        {
            _relic.Icon.Position = _relic.Icon.Position with { Y = _relic.Icon.Position.Y + _newlyAcquiredPopDistance };
            _relic.Icon.Modulate = _relic.Icon.Modulate with { A = 0f };

            _obtainedTween = GetTree().CreateTween();

            _obtainedTween.TweenProperty(_relic.Icon, "modulate:a", 1f, _newlyAcquiredFadeInDuration);
            _obtainedTween.Parallel();

            _obtainedTween.SetEase(Tween.EaseType.Out);
            _obtainedTween.SetTrans(Tween.TransitionType.Back);

            _obtainedTween.TweenProperty(_relic.Icon, "position:y", _originalIconPosition.Y, _newlyAcquiredPopDuration);
            _obtainedTween.TweenCallback(Callable.From(DoFlash));
        }
        else
        {
            _relic.Icon.GlobalPosition = startLocation.Value;
            _relic.Icon.Scale = startScale ?? Vector2.One;
            _relic.Icon.Modulate = _relic.Icon.Modulate with { A = 1f };

            _obtainedTween = GetTree().CreateTween();
            _obtainedTween.SetEase(Tween.EaseType.Out);
            _obtainedTween.SetTrans(Tween.TransitionType.Sine);

            _obtainedTween.TweenProperty(_relic.Icon, "position", _originalIconPosition, _newlyAcquiredPopDuration);
            _obtainedTween.Parallel().TweenProperty(_relic.Icon, "scale", Vector2.One, _newlyAcquiredPopDuration);
            _obtainedTween.TweenCallback(Callable.From(DoFlash));
        }
    }

    protected override void OnFocus()
    {
        // Casey: Shouldn't OnFocus and other Button-y behaviors be moved to Relic Holders?
        // See: https://linear.app/megacrit/issue/PRG-2392

        _hoverTween?.Kill();
        _hoverTween = CreateTween();
        _hoverTween.TweenProperty(_relic.Icon, "scale", Vector2.One * 1.25f, 0.05);

        NHoverTipSet tip = NHoverTipSet.CreateAndShow(this, _relic.Model.HoverTips);
        tip.SetAlignmentForRelic(_relic);
    }

    protected override void OnUnfocus()
    {
        _hoverTween?.Kill();
        _hoverTween = CreateTween();
        _hoverTween.TweenProperty(_relic.Icon, "scale", Vector2.One, 1.0)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);

        NHoverTipSet.Remove(this);
    }

    private void OnRelicFlashed(RelicModel _, IEnumerable<Creature> __)
    {
        DoFlash();
    }

    private void DoFlash()
    {
        // Note that we spawn this rather than embedding the flash into the scene because we want the flash to show
        // over the top bar
        Node2D flash = PreloadManager.Cache.GetScene(_flashPath).Instantiate<Node2D>();
        Node vfxContainer = NClimb.Instance!.GlobalUi.AboveTopBarVfxContainer;
        flash.GetNode<GpuParticles2D>("Particles").Texture = _relic.Model.Icon;
        flash.GlobalPosition = GlobalPosition + Size * 0.5f;
        vfxContainer.AddChildSafely(flash);
    }

    private void OnDisplayAmountChanged() => RefreshAmount();
    private void OnStatusChanged() => RefreshStatus();
}
