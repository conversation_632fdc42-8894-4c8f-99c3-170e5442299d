using Godot;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.HoverTips;

namespace MegaCrit.Sts2.Core.Nodes.Screens.CardLibrary;

/// <summary>
/// The current pool the player is viewing in the Card Library.
/// Unlike the other tickboxes, this one is a radio-buttion style.
/// Clicking on a pool filter will deselect the others. If this filter
/// is already active, then you can't click on it.
/// </summary>
public partial class NCardPoolFilter : NButton
{
    public LocString? Loc { get; set; }
    private bool _isSelected;

    [Signal]
    public delegate void ToggledEventHandler(NCardPoolFilter filter);

    private Control _image = default!;
    private ShaderMaterial _hsv = default!;
    private NSelectionReticle _controllerSelectionReticle = default!;
    private Tween? _tween;

    private const float _focusedMultiplier = 1.2f;
    private const float _pressDownMultiplier = 0.8f;
    private static readonly Vector2 _enabledScale = Vector2.One * 1.1f;
    private static readonly Vector2 _disabledScale = Vector2.One * 0.95f;

    public bool IsSelected
    {
        get => _isSelected;
        set
        {
            _isSelected = value;
            OnToggle();
        }
    }

    public override void _Ready()
    {
        ConnectSignals();
        _image = GetNode<Control>("Image");
        _controllerSelectionReticle = GetNode<NSelectionReticle>("%SelectionReticle");
        _hsv = (ShaderMaterial)_image.GetMaterial();
    }

    private void OnToggle()
    {
        _tween?.Kill();
        _hsv.SetShaderParameter("s", _isSelected ? 1f : 0.3f);
        _hsv.SetShaderParameter("v", _isSelected ? 1f : 0.55f);

        if (!_isSelected)
        {
            _tween = CreateTween().SetParallel();
            _tween.TweenProperty(_image, "scale",
                    _disabledScale, 0.3)
                .SetEase(Tween.EaseType.Out)
                .SetTrans(Tween.TransitionType.Expo);
        }
        else
        {
            _tween = CreateTween().SetParallel();
            _tween.TweenProperty(_image, "scale",
                    _enabledScale, 0.2)
                .SetEase(Tween.EaseType.Out)
                .SetTrans(Tween.TransitionType.Back);
        }
    }

    protected override void OnRelease()
    {
        if (_isSelected) return;

        base.OnRelease();

        IsSelected = !IsSelected;
        EmitSignal(SignalName.Toggled, this);
    }

    protected override void OnFocus()
    {
        // Only do hover tween if we aren't already selected (hovertip should be displayed anyway though)
        if (!_isSelected)
        {
            // TODO: Replace SFX with custom sfx per character.
            base.OnFocus();
            _tween?.Kill();
            _tween = CreateTween().SetParallel();
            _tween.TweenProperty(_image, "scale",
                (_isSelected ? _enabledScale : _disabledScale) * _focusedMultiplier, 0.05);
        }

        if (NControllerManager.Instance!.IsUsingController)
        {
            _controllerSelectionReticle.OnSelect();
        }

        if (Loc != null)
        {
            NHoverTipSet tip = NHoverTipSet.CreateAndShow(this, new HoverTip(Loc));
            tip.GlobalPosition = new Vector2(310f, GlobalPosition.Y);
        }
    }

    protected override void OnUnfocus()
    {
        base.OnUnfocus();

        _tween?.Kill();
        _tween = CreateTween().SetParallel();
        _tween.TweenProperty(_image, "scale",
            _isSelected ? _enabledScale : _disabledScale, 0.3);
        _controllerSelectionReticle.OnDeselect();

        if (Loc != null)
        {
            NHoverTipSet.Remove(this);
        }
    }

    protected override void OnPressDown()
    {
        if (_isSelected) return;

        base.OnPressDown();

        _tween?.Kill();
        _tween = CreateTween().SetParallel();
        _tween.TweenProperty(_image, "scale",
                (_isSelected ? _enabledScale : _disabledScale) * _pressDownMultiplier, 0.3)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }
}
