using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.ControllerInput;
using MegaCrit.Sts2.Core.Entities.CardRewardAlternatives;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Entities.Rewards;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Cards;
using MegaCrit.Sts2.Core.Nodes.Cards.Holders;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.Ftue;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Screens.Overlays;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Screens.CardSelection;

/// <summary>
/// Selection screen for card rewards after winning combat.
/// NOTE: The card reward selection screen is only set up to look good with exactly 3 cards right now. Any more or less
/// than that and things may start to look wonky. We'll need do some UI work to make it look nicer if that's required.
/// </summary>
public partial class NCardRewardSelectionScreen : Control, IOverlayScreen, IFocusableScreen
{
    private static string ScenePath => SceneHelper.GetScenePath("screens/card_selection/card_reward_selection_screen");

    // Amount of milliseconds after the selection screen is opened before the player is allowed to select a card.
    private const ulong _noSelectionTimeMsec = 350;

    public static IEnumerable<string> AssetPaths
    {
        get
        {
            return new[]
            {
                ScenePath
            }.Concat(NCardRewardAlternativeButton.AssetPaths);
        }
    }

    private Control _ui = default!;
    private NCommonBanner _banner = default!;
    private Control _cardRow = default!;
    private IReadOnlyList<CardCreationResult> _options = default!;
    private IReadOnlyList<CardRewardAlternative> _extraOptions = default!;
    private Control _rewardAlternativesContainer = default!;
    private TaskCompletionSource<Tuple<IEnumerable<NCardHolder>, bool>>? _completionSource;
    private ulong _openedTicks;

    private Tween? _cardTween;
    private Tween? _buttonTween;
    private const float _cardXOffset = 350f;

    private static readonly Vector2 _bannerAnimPosOffset = new(0f, 50f);

    public NetScreenType ScreenType => NetScreenType.CardSelection;

    public override void _Ready()
    {
        _ui = GetNode<Control>("UI");
        _cardRow = GetNode<Control>("UI/CardRow");

        _banner = GetNode<NCommonBanner>("UI/Banner");
        _banner.label.SetTextAutoSize(new LocString("gameplay_ui", "CHOOSE_CARD_HEADER").GetRawText());
        _banner.AnimateIn();

        _rewardAlternativesContainer = GetNode<Control>("UI/RewardAlternatives");

        RefreshOptions(_options, _extraOptions);
    }

    /// <summary>
    /// Called both in _Ready and if someone re-rolls the options of the associated CardReward.
    /// </summary>
    public void RefreshOptions(IReadOnlyList<CardCreationResult> options, IReadOnlyList<CardRewardAlternative> extraOptions)
    {
        _options = options;
        _extraOptions = extraOptions;

        Vector2 startPos = Vector2.Left * (_options.Count - 1) * _cardXOffset * 0.5f;

        // Clear out old options if there were any
        foreach (NGridCardHolder holder in _cardRow.GetChildren().OfType<NGridCardHolder>())
        {
            holder.QueueFreeSafely();
        }

        foreach (NCardRewardAlternativeButton altButton in _rewardAlternativesContainer.GetChildren().OfType<NCardRewardAlternativeButton>())
        {
            altButton.QueueFreeSafely();
        }

        // Add new card options and tween them in
        _cardTween = CreateTween().SetParallel();

        for (int i = 0; i < _options.Count; i++)
        {
            CardCreationResult option = _options[i];
            NCard node = NCard.Create(option.Card)!;
            NGridCardHolder holder = NGridCardHolder.Create(node)!;

            _cardRow.AddChildSafely(holder);
            holder.Connect(NCardHolder.SignalName.Pressed, Callable.From<NCardHolder>(SelectCard));
            holder.Connect(NCardHolder.SignalName.AltPressed, Callable.From<NCardHolder>(InspectCard));

            node.UpdateVisuals(PileType.None);
            holder.Scale = holder.SmallScale;

            _cardTween.TweenProperty(holder, "position", holder.Position + startPos + Vector2.Right * _cardXOffset * i, 0.5)
                .SetEase(Tween.EaseType.Out)
                .SetTrans(Tween.TransitionType.Expo);

            _cardTween.TweenProperty(holder, "modulate", Colors.White, 1.0)
                .SetEase(Tween.EaseType.Out)
                .SetTrans(Tween.TransitionType.Cubic)
                .From(Colors.Black);

            node.ActivateRewardScreenGlow();

            foreach (RelicModel model in option.ModifyingRelics)
            {
                model.Flash();

                NRelicFlashVfx flashVfx = NRelicFlashVfx.Create(model)!;
                node.Body.AddChildSafely(flashVfx);
                flashVfx.Scale = Vector2.One * 2f;
                flashVfx.Position = node.Size * 0.5f;
            }
        }

        // Add new alternate options
        foreach (CardRewardAlternative rewardOption in _extraOptions)
        {
            NCardRewardAlternativeButton altButton = NCardRewardAlternativeButton.Create(rewardOption.Title.GetFormattedText(), rewardOption.Hotkey);
            _rewardAlternativesContainer.AddChildSafely(altButton);
            altButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(_ =>
            {
                OnAlternateRewardSelected(rewardOption.AfterSelected);
                TaskHelper.RunSafely(rewardOption.OnSelect.Invoke());
            }));
        }

        // setup controller navigation
        List<NGridCardHolder> holders = _cardRow.GetChildren().OfType<NGridCardHolder>().ToList();
        NGridCardHolder centerHolder = _cardRow.GetChildren().OfType<NGridCardHolder>().ToList()[holders.Count / 2];
        for (int i = 0; i < _rewardAlternativesContainer.GetChildCount(); i++)
        {
            Control button = _rewardAlternativesContainer.GetChild<Control>(i);
            button.FocusNeighborTop = centerHolder.GetPath();
            button.FocusNeighborBottom = button.GetPath();
            button.FocusNeighborLeft = i > 0 ? _rewardAlternativesContainer.GetChild(i - 1).GetPath() : button.GetPath();
            button.FocusNeighborRight = i < _rewardAlternativesContainer.GetChildCount() - 1 ? _rewardAlternativesContainer.GetChild(i + 1).GetPath() : button.GetPath();
        }

        for (int i = 0; i < _cardRow.GetChildCount(); i++)
        {
            Control card = _cardRow.GetChild<Control>(i);
            card.FocusNeighborBottom = _rewardAlternativesContainer.GetChild(0).GetPath();
            card.FocusNeighborTop = card.GetPath();
            card.FocusNeighborLeft = i > 0 ? _cardRow.GetChild(i - 1).GetPath() : _cardRow.GetChild(_cardRow.GetChildCount() - 1).GetPath();
            card.FocusNeighborRight = i < _cardRow.GetChildCount() - 1 ? _cardRow.GetChild(i + 1).GetPath() : _cardRow.GetChild(0).GetPath();
        }
    }

    public override void _ExitTree()
    {
        if (_completionSource is { Task.IsCompleted: false })
        {
            _completionSource.SetResult(new Tuple<IEnumerable<NCardHolder>, bool>([], false));
        }

        NControllerManager.Instance!.RemoveScreen(this);
    }

    private void OnAlternateRewardSelected(PostAlternateCardRewardAction afterSelected)
    {
        if (afterSelected is PostAlternateCardRewardAction.None or PostAlternateCardRewardAction.DoNothing) return;
        _completionSource?.SetResult(new Tuple<IEnumerable<NCardHolder>, bool>([], afterSelected == PostAlternateCardRewardAction.DismissScreenAndRemoveReward));
    }

    public static NCardRewardSelectionScreen? ShowScreen(IReadOnlyList<CardCreationResult> options, IReadOnlyList<CardRewardAlternative> extraOptions)
    {
        if (TestMode.IsOn) return null;

        NCardRewardSelectionScreen node = PreloadManager.Cache.GetScene(ScenePath).Instantiate<NCardRewardSelectionScreen>();
        node.Name = nameof(NCardRewardSelectionScreen);
        node._options = options;
        node._extraOptions = extraOptions;

        NOverlayStack.Instance!.Push(node);

        return node;
    }

    private void SelectCard(NCardHolder cardHolder)
    {
        if (_completionSource == null) throw new InvalidOperationException("CardsSelected must be awaited before a card is selected!");
        if (Time.GetTicksMsec() - _openedTicks <= _noSelectionTimeMsec) return;

        _completionSource.SetResult(new Tuple<IEnumerable<NCardHolder>, bool>([cardHolder], true));
    }

    private void InspectCard(NCardHolder cardHolder)
    {
        NGame.Instance!.InspectCardScreen.Open([cardHolder.CardNode!.Model!], 0);
    }

    /// <returns>A tuple of the selected cards (if there are any) and if we should remove the card reward.</returns>
    public async Task<Tuple<IEnumerable<NCardHolder>, bool>> CardsSelected()
    {
        _completionSource = new TaskCompletionSource<Tuple<IEnumerable<NCardHolder>, bool>>();
        return await _completionSource.Task;
    }

    public void AfterOverlayOpened()
    {
        PowerCardFtueCheck();
        _banner.AnimateIn();
        _buttonTween = CreateTween();
        _buttonTween.SetParallel();

        _buttonTween.TweenProperty(_rewardAlternativesContainer, "position", _rewardAlternativesContainer.Position, 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Back)
            .From(_rewardAlternativesContainer.Position - _bannerAnimPosOffset);

        _openedTicks = Time.GetTicksMsec();
    }

    private void PowerCardFtueCheck()
    {
        if (SaveManager.Instance.SeenFtue(NPowerCardFtue.id)) return;

        IEnumerable<NGridCardHolder> cardHolders = _cardRow.GetChildren().OfType<NGridCardHolder>();
        NGridCardHolder? powerCardHolder = cardHolders.FirstOrDefault(h => h.CardModel.Type == CardType.Power);

        if (powerCardHolder != null)
        {
            NModalContainer.Instance!.Add(NPowerCardFtue.Create(powerCardHolder)!);
            SaveManager.Instance.MarkFtueAsComplete(NPowerCardFtue.id);
        }
    }

    public void AfterOverlayClosed()
    {
        this.QueueFreeSafely();
    }

    public void AfterOverlayShown()
    {
        Visible = true;
    }

    public void AfterOverlayHidden()
    {
        Visible = false;
    }

    public bool UseSharedBackstop => true;

    public void OnFocusScreen()
    {
        List<NGridCardHolder> holders = _cardRow.GetChildren().OfType<NGridCardHolder>().ToList();
        holders[holders.Count / 2].TryGrabFocus();
    }

    public void OnUnfocusScreen() { }
}
