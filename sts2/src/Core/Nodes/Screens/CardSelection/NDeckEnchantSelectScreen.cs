using System;
using System.Collections.Generic;
using System.Linq;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.CardSelection;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Cards;
using MegaCrit.Sts2.Core.Nodes.Cards.Holders;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Screens.Overlays;

namespace MegaCrit.Sts2.Core.Nodes.Screens.CardSelection;

/// <summary>
/// NCardSelectionScreen specifically for enchanting cards in your deck.
/// Managing logic for the extra confirmation screen.
/// </summary>
public sealed partial class NDeckEnchantSelectScreen : NCardGridSelectionScreen
{
    private static string ScenePath => SceneHelper.GetScenePath("screens/card_selection/deck_enchant_select_screen");
    public static IEnumerable<string> AssetPaths => [ScenePath];

    private readonly HashSet<CardModel> _selectedCards = [];
    private CardSelectorPrefs _prefs;
    private bool UseSingleSelection => _prefs.MaxSelect == 1;
    private EnchantmentModel _enchantment = default!;
    private int _enchantmentAmount;

    private Control _enchantSinglePreviewContainer = default!;
    private NEnchantPreview _singlePreview = default!;
    private NBackButton _singlePreviewCancelButton = default!;
    private NConfirmButton _singlePreviewConfirmButton = default!;

    private Control _enchantMultiPreviewContainer = default!;
    private Control _multiPreview = default!;
    private NBackButton _multiPreviewCancelButton = default!;
    private NConfirmButton _multiPreviewConfirmButton = default!;

    private Control _enchantmentDescriptionContainer = default!;
    private MegaLabel _enchantmentTitle = default!;
    private MegaRichTextLabel _enchantmentDescription = default!;
    private TextureRect _enchantmentIcon = default!;

    private NBackButton _closeButton = default!;

    public override void _Ready()
    {
        ConnectSignalsAndInitGrid();
        _enchantSinglePreviewContainer = GetNode<Control>("%EnchantSinglePreviewContainer");
        _singlePreview = _enchantSinglePreviewContainer.GetNode<NEnchantPreview>("EnchantPreview");
        _singlePreviewCancelButton = _enchantSinglePreviewContainer.GetNode<NBackButton>("Cancel");
        _singlePreviewConfirmButton = _enchantSinglePreviewContainer.GetNode<NConfirmButton>("Confirm");

        _enchantMultiPreviewContainer = GetNode<Control>("%EnchantMultiPreviewContainer");
        _multiPreview = _enchantMultiPreviewContainer.GetNode<Control>("Cards");
        _multiPreviewCancelButton = _enchantMultiPreviewContainer.GetNode<NBackButton>("Cancel");
        _multiPreviewConfirmButton = _enchantMultiPreviewContainer.GetNode<NConfirmButton>("Confirm");

        _enchantmentDescriptionContainer = GetNode<Control>("%EnchantmentDescriptionContainer");
        _enchantmentIcon = _enchantmentDescriptionContainer.GetNode<TextureRect>("%EnchantmentIcon");
        _enchantmentTitle = _enchantmentDescriptionContainer.GetNode<MegaLabel>("%EnchantmentTitle");
        _enchantmentDescription = _enchantmentDescriptionContainer.GetNode<MegaRichTextLabel>("%EnchantmentDescription");

        _closeButton = GetNode<NBackButton>("%Close");

        _singlePreviewCancelButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(CancelSelection));
        _singlePreviewConfirmButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(ConfirmSelection));
        _multiPreviewCancelButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(CancelSelection));
        _multiPreviewConfirmButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(ConfirmSelection));
        _closeButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(CloseSelection));

        if (_prefs.Cancelable)
        {
            _closeButton.Enable();
        }
        else
        {
            _closeButton.Disable();
        }

        EnchantmentModel enchantPreview = _enchantment.ToMutable();
        enchantPreview.Amount = _enchantmentAmount;
        enchantPreview.RecalculateValues();

        _enchantmentTitle.Text = enchantPreview.Title.GetFormattedText();
        _enchantmentDescription.Text = enchantPreview.DynamicDescription.GetFormattedText();
        _enchantmentIcon.Texture = enchantPreview.Icon;

        _enchantSinglePreviewContainer.Visible = false;
        _enchantSinglePreviewContainer.MouseFilter = MouseFilterEnum.Ignore;

        _enchantMultiPreviewContainer.Visible = false;
        _enchantMultiPreviewContainer.MouseFilter = MouseFilterEnum.Ignore;
    }

    public static NDeckEnchantSelectScreen ShowScreen(IReadOnlyList<CardModel> cards, EnchantmentModel enchantment, int amount, CardSelectorPrefs prefs)
    {
        NDeckEnchantSelectScreen node = PreloadManager.Cache.GetScene(ScenePath).Instantiate<NDeckEnchantSelectScreen>();
        node.Name = nameof(NDeckEnchantSelectScreen);
        node._cards = cards;
        node._prefs = prefs;
        node._enchantment = enchantment;
        node._enchantmentAmount = amount;
        NOverlayStack.Instance!.Push(node);
        return node;
    }

    protected override IEnumerable<Control> PeekButtonTargets =>
    [
        _enchantSinglePreviewContainer,
        _enchantMultiPreviewContainer,
        _enchantmentDescriptionContainer,
        _closeButton
    ];

    protected override void OnCardClicked(CardModel card)
    {
        if (_selectedCards.Add(card))
        {
            _grid.HighlightCard(card);

            if (UseSingleSelection)
            {
                _grid.SetCanScroll(false);
                _closeButton.Disable();

                GetViewport().GuiReleaseFocus();
                _enchantSinglePreviewContainer.Visible = true;
                _enchantSinglePreviewContainer.MouseFilter = MouseFilterEnum.Stop;
                _singlePreview.Init(card, _enchantment, _enchantmentAmount);

                _singlePreviewCancelButton.Enable();
                _singlePreviewConfirmButton.Enable();
            }
            else if (_prefs.MaxSelect == _selectedCards.Count)
            {
                _grid.SetCanScroll(false);
                _closeButton.Disable();

                GetViewport().GuiReleaseFocus();
                _enchantMultiPreviewContainer.Visible = true;
                _enchantMultiPreviewContainer.MouseFilter = MouseFilterEnum.Stop;

                _multiPreviewCancelButton.Enable();
                _multiPreviewConfirmButton.Enable();

                foreach (CardModel selectedCard in _selectedCards)
                {
                    NCard node = NCard.Create(selectedCard)!;
                    _multiPreview.AddChildSafely(NPreviewCardHolder.Create(node, true, false));
                    node.UpdateVisuals(selectedCard.Pile!.Type);
                }
            }
        }
        else
        {
            _selectedCards.Remove(card);
            _grid.UnhighlightCard(card);
        }
    }

    private void CloseSelection(NButton _)
    {
        _completionSource.SetResult(Array.Empty<CardModel>());
        NOverlayStack.Instance!.Remove(this);
    }

    private void CancelSelection(NButton _)
    {
        if (UseSingleSelection)
        {
            _singlePreviewCancelButton.Disable();
            _singlePreviewConfirmButton.Disable();

            _enchantSinglePreviewContainer.Visible = false;
            _enchantSinglePreviewContainer.MouseFilter = MouseFilterEnum.Ignore;
        }
        else
        {
            _multiPreviewCancelButton.Disable();
            _multiPreviewConfirmButton.Disable();

            _enchantMultiPreviewContainer.Visible = false;
            _enchantMultiPreviewContainer.MouseFilter = MouseFilterEnum.Ignore;
            for (int i = 0; i < _multiPreview.GetChildCount(); i++)
            {
                _multiPreview.GetChild(i).QueueFreeSafely();
            }
        }

        _grid.SetCanScroll(true);
        OnFocusScreen();

        if (_prefs.Cancelable)
        {
            _closeButton.Enable();
        }

        foreach (CardModel card in _selectedCards)
        {
            _grid.UnhighlightCard(card);
        }

        // not a huge fan of having to do a different pattern here, but we have to do this because the preview container
        // isn't its own screen
        _grid.GetCardHolder(_selectedCards.Last())?.TryGrabFocus();
        _selectedCards.Clear();
    }

    private void ConfirmSelection(NButton inputEvent)
    {
        if (_selectedCards.Count == 0) return;

        CheckIfSelectionComplete();
    }

    private void CheckIfSelectionComplete()
    {
        _singlePreviewCancelButton.Enable();
        _singlePreviewConfirmButton.Enable();

        // TODO: Handle multi-select with confirm
        if (_selectedCards.Count >= _prefs.MaxSelect)
        {
            _completionSource.SetResult(_selectedCards);
            NOverlayStack.Instance!.Remove(this);
        }
    }

    public override void OnFocusScreen()
    {
        // not a huge fan of having to do a different pattern here, but we have to do this because the preview container
        // isn't its own screen
        if (!_enchantSinglePreviewContainer.Visible && !_enchantMultiPreviewContainer.Visible)
        {
            _grid.OnFocus();
        }
    }
}
