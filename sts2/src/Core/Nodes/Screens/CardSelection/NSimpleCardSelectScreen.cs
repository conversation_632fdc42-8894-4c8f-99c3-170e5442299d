using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.CardSelection;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Cards.Holders;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Screens.Overlays;
using MegaCrit.Sts2.Core.Nodes.Vfx;

namespace MegaCrit.Sts2.Core.Nodes.Screens.CardSelection;

public sealed partial class NSimpleCardSelectScreen : NCardGridSelectionScreen
{
    private Control _bottomTextContainer = default!;
    private MegaRichTextLabel _infoLabel = default!;
    private readonly HashSet<CardModel> _selectedCards = [];
    private CardSelectorPrefs _prefs;

    private NConfirmButton _confirmButton = default!;
    private List<CardCreationResult>? _cardResults;
    private CombatStateTracker? _stateTracker;

    private static string ScenePath => SceneHelper.GetScenePath("screens/card_selection/simple_card_select_screen");
    public static IEnumerable<string> AssetPaths => [ScenePath];

    public static NSimpleCardSelectScreen Create(IReadOnlyList<CardModel> cards, CardSelectorPrefs prefs)
    {
        NSimpleCardSelectScreen node = PreloadManager.Cache.GetScene(ScenePath).Instantiate<NSimpleCardSelectScreen>();
        node.Name = nameof(NSimpleCardSelectScreen);
        node._cards = cards.ToList();
        node._cardResults = null;
        node._prefs = prefs;

        return node;
    }

    public static NSimpleCardSelectScreen Create(IReadOnlyList<CardCreationResult> cards, CardSelectorPrefs prefs)
    {
        NSimpleCardSelectScreen node = PreloadManager.Cache.GetScene(ScenePath).Instantiate<NSimpleCardSelectScreen>();
        node.Name = nameof(NSimpleCardSelectScreen);
        node._cards = cards.Select(r => r.Card).ToList();
        node._cardResults = cards.ToList();
        node._prefs = prefs;

        return node;
    }

    public override void _Ready()
    {
        RecalculateCardValues();

        ConnectSignalsAndInitGrid();
        _confirmButton = GetNode<NConfirmButton>("%Confirm");

        _bottomTextContainer = GetNode<Control>("%BottomText");
        _infoLabel = _bottomTextContainer.GetNode<MegaRichTextLabel>("%BottomLabel");
        _infoLabel.Text = $"[center]{_prefs.Prompt.GetFormattedText()}[/center]";

        if (CombatManager.Instance.IsInProgress)
        {
            _stateTracker = CombatManager.Instance.StateTracker;
            _stateTracker.CombatStateChanged += OnCombatStateChanged;
        }

        if (_prefs.MinSelect == 0)
        {
            _confirmButton.Enable();
        }
        else
        {
            _confirmButton.Disable();
        }

        _confirmButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(_ => CompleteSelection()));
    }

    public override void _ExitTree()
    {
        if (_stateTracker != null)
        {
            _stateTracker.CombatStateChanged -= OnCombatStateChanged;
        }
    }

    private void OnCombatStateChanged(CombatState _) => RecalculateCardValues();

    private void RecalculateCardValues()
    {
        // Only recalc the card values if we are in the middle of a combat
        if (!CombatManager.Instance.IsInProgress) return;

        foreach (CardModel card in _cards)
        {
            if (card.IsMutable)
            {
                card.RecalculateValues();
            }
        }
    }

    protected override IEnumerable<Control> PeekButtonTargets => [_bottomTextContainer];

    public override void AfterOverlayOpened()
    {
        base.AfterOverlayOpened();
        TaskHelper.RunSafely(FlashRelicsOnModifiedCards());
    }

    private async Task FlashRelicsOnModifiedCards()
    {
        if (_cardResults == null) return;

        // This needs to be delayed by one frame for the cards to correctly initialize size and position
        await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);

        foreach (CardCreationResult result in _cardResults)
        {
            NGridCardHolder? holder = _grid.CurrentlyDisplayedCardHolders.FirstOrDefault(h => h.CardModel == result.Card);

            if (holder == null) continue;
            if (!result.HasBeenModified) continue;

            foreach (RelicModel model in result.ModifyingRelics)
            {
                model.Flash();

                NRelicFlashVfx flashVfx = NRelicFlashVfx.Create(model)!;
                holder.AddChildSafely(flashVfx);
                flashVfx.Scale = Vector2.One * 2f;
                flashVfx.Position = holder.Size * 0.5f;
            }
        }
    }

    protected override void OnCardClicked(CardModel card)
    {
        if (_selectedCards.Contains(card))
        {
            _grid.UnhighlightCard(card);

            _selectedCards.Remove(card);
        }
        else
        {
            if (_selectedCards.Count < _prefs.MaxSelect)
            {
                _grid.HighlightCard(card);
                _selectedCards.Add(card);
            }

            if (!_prefs.RequireManualConfirmation)
            {
                CheckIfSelectionComplete();
            }
        }

        if (_selectedCards.Count >= _prefs.MinSelect && _prefs.RequireManualConfirmation)
        {
            _confirmButton.Enable();
        }
        else
        {
            _confirmButton.Disable();
        }
    }

    private void CheckIfSelectionComplete()
    {
        // TODO: Handle multi-select with confirm
        if (_selectedCards.Count >= _prefs.MaxSelect)
        {
            CompleteSelection();
        }
    }

    private void CompleteSelection()
    {
        _completionSource.SetResult(_selectedCards);
        NOverlayStack.Instance!.Remove(this);
    }
}
