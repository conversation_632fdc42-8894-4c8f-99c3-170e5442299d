using System.Linq;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Screens.CustomClimb;

public partial class NClimbModifierTickbox : NTickbox
{
    public const string scenePath = "res://scenes/screens/custom_climb/modifier_tickbox.tscn";
    private static readonly LocString _descriptionLoc = new("main_menu_ui", "CUSTOM_CLIMB_SCREEN.MODIFIER_LABEL");
    private MegaRichTextLabel _label = default!;

    public ModifierModel? Modifier { get; private set; }

    public static NClimbModifierTickbox? Create(ModifierModel model)
    {
        if (TestMode.IsOn) return null;
        NClimbModifierTickbox tickbox = PreloadManager.Cache.GetScene(scenePath).Instantiate<NClimbModifierTickbox>();
        tickbox.Modifier = model;
        return tickbox;
    }

    protected override void OnEnable()
    {
        base.OnEnable();
        Modulate = Colors.White;
    }

    protected override void OnDisable()
    {
        base.OnDisable();
        Modulate = Colors.Gray;
    }

    public override void _Ready()
    {
        ConnectSignals();

        _label = GetNode<MegaRichTextLabel>("Description");

        if (Modifier != null)
        {
            string color =
                ModelDb.GoodModifiers.FirstOrDefault(m => m.GetType() ==  Modifier.GetType()) != null ? "green" :
                ModelDb.BadModifiers.FirstOrDefault(m => m.GetType() ==  Modifier.GetType()) != null ? "red" :
                "blue";

            _descriptionLoc.Add("color", color);
            _descriptionLoc.Add("modifier_title", Modifier.Title.GetFormattedText());
            _descriptionLoc.Add("modifier_description", Modifier.Description.GetFormattedText());
            _label.Text = _descriptionLoc.GetFormattedText();
        }

        IsTicked = false;
    }
}
