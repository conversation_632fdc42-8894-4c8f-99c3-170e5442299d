using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Entities.UI;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Multiplayer.Game.Lobby;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Lobby;
using MegaCrit.Sts2.Core.Nodes.Audio;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Multiplayer;
using MegaCrit.Sts2.Core.Nodes.Screens.CharacterSelect;
using MegaCrit.Sts2.Core.Nodes.Screens.MainMenu;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Climbs;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Screens.CustomClimb;

/// <summary>
/// Intermediate screen when loading a custom climb in multiplayer. Allows players to join back into the session
/// before it is resumed.
/// </summary>
public partial class NCustomClimbLoadScreen : NSubmenu, ILoadClimbLobbyListener
{
    private NConfirmButton _confirmButton = default!;
    private NAscensionPanel _ascensionPanel = default!;
    private Control _readyAndWaitingContainer = default!;
    private LineEdit _seedInput = default!;
    private NRemoteLoadLobbyPlayerContainer _remotePlayerContainer = default!;
    private NCustomClimbModifiersList _modifiersList = default!;

    private LoadClimbLobby _lobby = default!;

    public static IEnumerable<string> AssetPaths => [NClimbModifierTickbox.scenePath];

    public override void _Ready()
    {
        ConnectSignals();
        _ascensionPanel = GetNode<NAscensionPanel>("%AscensionPanel");
        _remotePlayerContainer = GetNode<NRemoteLoadLobbyPlayerContainer>("LeftContainer/RemotePlayerLoadContainer");
        _readyAndWaitingContainer = GetNode<Control>("%ReadyAndWaitingPanel");
        _modifiersList = GetNode<NCustomClimbModifiersList>("%ModifiersList");
        _seedInput = GetNode<LineEdit>("%SeedInput");
        _confirmButton = GetNode<NConfirmButton>("ConfirmButton");

        _confirmButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OnEmbarkPressed));

        ProcessMode = ProcessModeEnum.Disabled;

        GetNode<Label>("%CustomModeTitle").Text = new LocString("main_menu_ui", "CUSTOM_CLIMB_SCREEN.CUSTOM_MODE_TITLE").GetFormattedText();
        GetNode<Label>("%ModifiersTitle").Text = new LocString("main_menu_ui", "CUSTOM_CLIMB_SCREEN.MODIFIERS_TITLE").GetFormattedText();
        GetNode<Label>("%SeedLabel").Text = new LocString("main_menu_ui", "CUSTOM_CLIMB_SCREEN.SEED_LABEL").GetFormattedText();
        _seedInput.PlaceholderText = new LocString("main_menu_ui", "CUSTOM_CLIMB_SCREEN.SEED_RANDOM_PLACEHOLDER").GetFormattedText();
    }

    public void InitializeAsHost(INetGameService gameService, SerializableClimb climb)
    {
        if (gameService.Type != NetGameType.Host) throw new InvalidOperationException($"Initialized custom climb screen with NetService of type {gameService.Type} when hosting!");
        _lobby = new LoadClimbLobby(gameService, this, climb);

        try
        {
            _lobby.AddLocalHostPlayer();
            AfterInitialized();
        }
        catch
        {
            // On any exception, stop hosting
            CleanUpLobby(true);
            throw;
        }
    }

    public void InitializeAsClient(INetGameService gameService, ClientLoadJoinResponseMessage message)
    {
        if (gameService.Type != NetGameType.Client) throw new InvalidOperationException($"Initialized character select screen with NetService of type {gameService.Type} when joining!");
        _lobby = new LoadClimbLobby(gameService, this, message);
        AfterInitialized();
    }

    public override void OnSubmenuOpened()
    {
        base.OnSubmenuOpened();

        _confirmButton.Enable();

        _remotePlayerContainer.Initialize(_lobby, true);

        _ascensionPanel.Initialize(MultiplayerUiMode.Load);
        _ascensionPanel.SetAscensionLevel(_lobby.Climb.Ascension);

        _modifiersList.Initialize(MultiplayerUiMode.Load);
        _modifiersList.SyncModifierList(_lobby.Climb.Modifiers.Select(ModifierModel.FromSerializable).ToList());

        _readyAndWaitingContainer.Visible = false;
        ProcessMode = ProcessModeEnum.Inherit;
    }

    public override void OnSubmenuClosed()
    {
        base.OnSubmenuClosed();
        _confirmButton.Disable();
        _remotePlayerContainer.Cleanup();

        // Note that this method is not called when we transition to a climb, and is only called when this menu is
        // popped off the stack, e.g. by the back button, so we always pass true to disconnect session
        CleanUpLobby(true);
    }

    private void OnEmbarkPressed(NButton _)
    {
        _confirmButton.Disable();
        _lobby.SetReady(true);
        _readyAndWaitingContainer.Visible = true;
    }

    public override void _Process(double delta)
    {
        if (_lobby.NetService.IsConnected)
        {
            _lobby.NetService.Update();
        }
    }

    private void CleanUpLobby(bool disconnectSession)
    {
        _lobby.CleanUp(disconnectSession);
        _lobby = null!;

        if (disconnectSession)
        {
            NGame.Instance!.RemoteCursorContainer.Deinitialize();
            NGame.Instance.ReactionContainer.DeinitializeNetworking();
        }

        // Sometimes we get deallocated before this point
        if (IsInstanceValid(this))
        {
            ProcessMode = ProcessModeEnum.Disabled;
        }
    }

    public async Task<bool> ShouldAllowClimbToBegin()
    {
        // If not all players have joined the climb, then confirm that we want to proceed first.
        if (_lobby.ConnectedPlayerIds.Count >= _lobby.Climb.Players.Count) return true;

        LocString body = new("gameplay_ui", "CONFIRM_LOAD_SAVE.body");
        body.Add("MissingCount", _lobby.Climb.Players.Count - _lobby.ConnectedPlayerIds.Count);

        NGenericMultiplayerConfirmationPopup confirmation = NGenericMultiplayerConfirmationPopup.Create()!;
        NModalContainer.Instance!.Add(confirmation);
        bool confirmed = await confirmation.WaitForConfirmation(
            body,
            new LocString("gameplay_ui", "CONFIRM_LOAD_SAVE.header"),
            new LocString("gameplay_ui", "CONFIRM_LOAD_SAVE.cancel"),
            new LocString("gameplay_ui", "CONFIRM_LOAD_SAVE.confirm")
        );

        return confirmed;
    }

    private async Task StartClimb()
    {
        Log.Info($"Loading a custom multiplayer climb. Players: {string.Join(",", _lobby.ConnectedPlayerIds)}.");
        SerializablePlayer player = _lobby.Climb.Players.First(p => p.NetId == _lobby.NetService.NetId);
        await NGame.Instance!.Transition.FadeOut(0.8f, ModelDb.GetById<CharacterModel>(player.CharacterId!).CharacterSelectTransitionPath);

        ClimbState climbState = ClimbState.FromSerializable(_lobby.Climb);
        ClimbManager.Instance.SetUpSavedMultiPlayer(climbState, _lobby);

        await NGame.Instance.LoadClimb(climbState, _lobby.Climb.PreFinishedRoom);
        CleanUpLobby(false);
        await NGame.Instance.Transition.FadeIn();
    }

    public void PlayerConnected(ulong playerId)
    {
        Log.Info($"Player connected: {playerId}");
        _remotePlayerContainer.OnPlayerConnected(playerId);
    }

    public void PlayerReadyChanged(ulong playerId)
    {
        Log.Info($"Player ready changed: {playerId}");
        _remotePlayerContainer.OnPlayerChanged(playerId);

        // Re-enable confirm button if player has become unready
        if (playerId == _lobby.NetService.NetId && !_lobby.IsPlayerReady(playerId))
        {
            _confirmButton.Enable();
        }
    }

    public void RemotePlayerDisconnected(ulong playerId)
    {
        Log.Info($"Player disconnected: {playerId}");
        _remotePlayerContainer.OnPlayerDisconnected(playerId);
    }

    public void BeginClimb()
    {
        NAudioManager.Instance?.StopMusic();
        TaskHelper.RunSafely(StartClimb());
    }

    public void LocalPlayerDisconnected(NetErrorInfo info)
    {
        // If we're the one that disconnected, then do nothing
        if (info.SelfInitiated && info.GetReason() == NetError.Quit) return;

        // Otherwise, close window and display error
        _stack.Pop();

        if (TestMode.IsOff)
        {
            NErrorPopup? popup = NErrorPopup.Create(info);

            if (popup != null)
            {
                NModalContainer.Instance!.AddChildSafely(popup);
            }
        }
    }

    private void AfterInitialized()
    {
        // Note that this happens for both multiplayer and singleplayer. Map drawings rely on the remote cursor
        // container's synchronizer for drawing state even in singleplayer.
        NGame.Instance!.RemoteCursorContainer.Initialize(_lobby.InputSynchronizer, _lobby.ConnectedPlayerIds);
        NGame.Instance.ReactionContainer.InitializeNetworking(_lobby.NetService);

        // We might want to remove this at some point. It's here for debugging purposes.
        Logger.logLevelTypeMap[LogType.Network] = _lobby.NetService.Type == NetGameType.Singleplayer ? LogLevel.Info : LogLevel.Debug;
        Logger.logLevelTypeMap[LogType.Actions] = _lobby.NetService.Type == NetGameType.Singleplayer ? LogLevel.Info : LogLevel.VeryDebug;
        Logger.logLevelTypeMap[LogType.GameSync] = _lobby.NetService.Type == NetGameType.Singleplayer ? LogLevel.Info : LogLevel.VeryDebug;

        NGame.Instance.DebugSeedOverride = null;
    }
}
