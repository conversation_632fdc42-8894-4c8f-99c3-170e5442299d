using Godot;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Platform;

namespace MegaCrit.Sts2.Core.Nodes.Screens.DailyClimb;

public partial class NDailyClimbCharacterContainer : Control
{
    private static readonly LocString _ascensionLoc = new ("main_menu_ui", "DAILY_CLIMB_MENU.ASCENSION");

    private Control _characterContainer = default!;
    private Control _characterIconContainer = default!;
    private Label _playerNameLabel = default!;
    private Label _characterNameLabel = default!;
    private Label _ascensionLabel = default!;
    private Label _ascensionNumberLabel = default!;
    private Control _readyIndicator = default!;

    public override void _Ready()
    {
        _characterContainer = GetNode<Control>("%CharacterContainer");
        _characterIconContainer = GetNode<Control>("%CharacterIconContainer");
        _playerNameLabel = GetNode<Label>("%PlayerNameLabel");
        _characterNameLabel = GetNode<Label>("%CharacterNameLabel");
        _ascensionLabel = GetNode<Label>("%AscensionLabel");
        _ascensionNumberLabel = GetNode<Label>("%AscensionNumberLabel");
        _readyIndicator = GetNode<Control>("%ReadyIndicator");
    }

    public void Fill(CharacterModel character, ulong playerId, int ascension, INetGameService netService)
    {
        _ascensionLoc.Add("ascension", ascension);
        bool isMultiplayer = netService.Type.IsMultiplayer();
        Control characterIcon = character.Icon;

        foreach (Node node in _characterIconContainer.GetChildren())
        {
            _characterIconContainer.RemoveChildSafely(node);
        }

        // Local player name only visible in multiplayer. If multiplayer, player name is gold and character name is cream.
        _playerNameLabel.Visible = isMultiplayer;
        _characterNameLabel.Modulate = isMultiplayer ? StsColors.cream : StsColors.gold;

        _characterIconContainer.AddChildSafely(characterIcon);
        _characterNameLabel.Text = character.Title.GetFormattedText();
        _playerNameLabel.Text = PlatformUtil.GetPlayerName(netService.Platform, playerId);
        _ascensionLabel.Text = _ascensionLoc.GetFormattedText();
        _ascensionNumberLabel.Text = ascension.ToString();
    }

    public void SetIsReady(bool isReady)
    {
        _readyIndicator.Visible = isReady;
    }
}
