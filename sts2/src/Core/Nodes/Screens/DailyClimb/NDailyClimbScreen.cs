using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Daily;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Modifiers;
using MegaCrit.Sts2.Core.Multiplayer;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Multiplayer.Game.Lobby;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Lobby;
using MegaCrit.Sts2.Core.Nodes.Audio;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Multiplayer;
using MegaCrit.Sts2.Core.Nodes.Screens.MainMenu;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Screens.DailyClimb;

public partial class NDailyClimbScreen : NSubmenu, IStartClimbLobbyListener
{
    private static readonly LocString _timeLeftLoc = new ("main_menu_ui", "DAILY_CLIMB_MENU.TIME_LEFT");
    public static readonly string dateFormat = LocManager.Instance.GetTable("main_menu_ui").GetRawText("DAILY_CLIMB_MENU.DATE_FORMAT");
    private static readonly string _timeLeftFormat = LocManager.Instance.GetTable("main_menu_ui").GetRawText("DAILY_CLIMB_MENU.TIME_FORMAT");

    private Control _loadingTimeText = default!;

    private MegaLabel _dateLabel = default!;
    private MegaLabel _timeLeftLabel = default!;
    private NDailyClimbCharacterContainer _characterContainer = default!;
    private NConfirmButton _embarkButton = default!;
    private NDailyClimbLeaderboard _leaderboard = default!;
    private Label _modifiersTitleLabel = default!;
    private Control _modifiersContainer = default!;
    private readonly List<NDailyClimbScreenModifier> _modifierContainers = [];
    private NRemoteLobbyPlayerContainer _remotePlayerContainer = default!;
    private Control _readyAndWaitingContainer = default!;

    private DateTimeOffset _endOfDay;

    private INetGameService _netService = default!;
    private StartClimbLobby? _lobby;

    private int? _lastSetTimeLeftSecond;

    public override void _Ready()
    {
        ConnectSignals();

        _loadingTimeText = GetNode<Control>("%LoadingText");
        _embarkButton = GetNode<NConfirmButton>("%ConfirmButton");
        _timeLeftLabel = GetNode<MegaLabel>("%TimeLeft");
        _dateLabel = GetNode<MegaLabel>("%Date");
        _leaderboard = GetNode<NDailyClimbLeaderboard>("%Leaderboards");
        _modifiersTitleLabel = GetNode<Label>("%ModifiersLabel");
        _modifiersContainer = GetNode<Control>("%ModifiersContainer");
        _characterContainer = GetNode<NDailyClimbCharacterContainer>("%CharacterContainer");
        _remotePlayerContainer = GetNode<NRemoteLobbyPlayerContainer>("%RemotePlayerContainer");
        _readyAndWaitingContainer = GetNode<Control>("%ReadyAndWaitingPanel");

        foreach (NDailyClimbScreenModifier modifierContainer in _modifiersContainer.GetChildren().OfType<NDailyClimbScreenModifier>())
        {
            _modifierContainers.Add(modifierContainer);
        }

        _embarkButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OnEmbarkPressed));
        _embarkButton.Disable();

        _remotePlayerContainer.Visible = false;
        _readyAndWaitingContainer.Visible = false;
        _leaderboard.Cleanup();
    }

    public void InitializeMultiplayerAsHost(INetGameService gameService)
    {
        if (gameService.Type != NetGameType.Host) throw new InvalidOperationException($"Initialized character select screen with GameService of type {gameService.Type} when hosting!");
        _netService = gameService;
        // The lobby gets initialized after we query the time server
    }

    public void InitializeMultiplayerAsClient(INetGameService gameService, ClientLobbyJoinResponseMessage message)
    {
        if (gameService.Type != NetGameType.Client) throw new InvalidOperationException($"Initialized character select screen with GameService of type {gameService.Type} when joining!");

        _netService = gameService;

        _lobby = new StartClimbLobby(GameMode.Daily, gameService, this, message.dailyTime!.Value, -1);
        _lobby.InitializeFromMessage(message);

        SetupLobbyParams(_lobby);
        AfterLobbyInitialized();
    }

    public void InitializeSingleplayer()
    {
        _netService = new NetSingleplayerGameService();
        // The lobby gets initialized after we query the time server
    }

    public override void OnSubmenuOpened()
    {
        base.OnSubmenuOpened();

        if (_netService.Type is NetGameType.Host or NetGameType.Singleplayer)
        {
            TaskHelper.RunSafely(SetupLobbyForHostOrSingleplayer());
        }
        else
        {
            SetIsLoading(false);
        }
    }

    public override void OnSubmenuClosed()
    {
        _embarkButton.Disable();
        _remotePlayerContainer.Cleanup();
        _leaderboard.Cleanup();

        // Note that this method is not called when we transition to a climb, and is only called when this menu is
        // popped off the stack, e.g. by the back button, so we always pass true to disconnect session
        CleanUpLobby(true);
    }

    /// <summary>
    /// This is called:
    /// - Once when the screen is shown
    /// - Anytime when the player count changes
    /// </summary>
    private void InitializeLeaderboard()
    {
        _leaderboard.Initialize(_lobby!.DailyTime!.Value.serverTime, _lobby.Players.Select(p => p.id), false);
    }

    /// <summary>
    /// Fetches the time from the time server and initializes the lobby for multiplayer or singleplayer.
    /// After this completes, _lobby will be initialized. Its time will be set with the time fetched from the time server,
    /// or with the local time, if we couldn't fetch the time from the server.
    /// </summary>
    private async Task SetupLobbyForHostOrSingleplayer()
    {
        if (_netService.Type != NetGameType.Host && _netService.Type != NetGameType.Singleplayer) throw new InvalidOperationException("Should only be called as host or singleplayer!");

        SetIsLoading(true);

        TimeServerResult timeServerResult = await GetTimeServerTime();

        // Now that we have the timeserver time, initialize lobby
        _lobby = new StartClimbLobby(GameMode.Daily, _netService, this, timeServerResult, 4);
        _lobby.AddLocalHostPlayer(SaveManager.Instance.ProgressSave);

        SetupLobbyParams(_lobby);
        AfterLobbyInitialized();
        SetIsLoading(false);

        Log.Info($"Daily initialized with seed: {_lobby.Seed} time: {GetServerRelativeTime()}");
    }

    /// <summary>
    /// Attempt to get the time from the time server.
    /// If any time server request succeeded in the past, this uses the cached time server time. Otherwise, it requests
    /// a new time.
    /// </summary>
    private async Task<TimeServerResult> GetTimeServerTime()
    {
        TimeServerResult? result = null;

        // Time might already be available from a previous request - check for it
        if (TimeServer.RequestTimeTask?.IsCompleted ?? false)
        {
            // The request was already completed when we got here.
            if (!TimeServer.RequestTimeTask.IsFaulted)
            {
                result = await TimeServer.RequestTimeTask;
            }

            if (result == null)
            {
                try
                {
                    // ... but it completed with failure, so re-request it
                    result = await TimeServer.FetchDailyTime();
                }
                catch (HttpRequestException e)
                {
                    // HTTP exceptions are completely normal when offline, so treat this as a server failure and do not
                    // rethrow
                    Log.Error(e.ToString());
                }
            }
        }
        else
        {
            // Another request was still in-flight when we got here. Await it and do not re-request even upon failure,
            // otherwise we can get into a bad state if the player exits & re-enters this menu very quickly
            try
            {
                result = await TimeServer.FetchDailyTime();
            }
            catch (HttpRequestException e)
            {
                Log.Error(e.ToString());
            }
        }

        // We couldn't retrieve the time from the time server, so fall back to local time mode
        if (result == null)
        {
            Log.Info("Couldn't retrieve time from time server, using local time");

            result = new TimeServerResult
            {
                serverTime = DateTimeOffset.UtcNow,
                localReceivedTime = DateTimeOffset.UtcNow
            };
        }

        return result.Value;
    }

    /// <summary>
    /// Returns the time on the Megacrit time server.
    /// </summary>
    private DateTimeOffset GetServerRelativeTime()
    {
        return _lobby!.DailyTime!.Value.serverTime + (DateTimeOffset.UtcNow - _lobby!.DailyTime!.Value.localReceivedTime);
    }

    /// <summary>
    /// This is called on both host and client after the lobby is setup to sync the state of the lobby.
    /// It is also called when any player leaves or rejoins the game to reroll modifiers so that CharacterCards can
    /// properly avoid hitting any characters that have been rolled.
    /// </summary>
    private void SetupLobbyParams(StartClimbLobby lobby)
    {
        DateTimeOffset serverTime = GetServerRelativeTime();
        string seedNoPlayers = SeedHelper.CanonicalizeSeed(serverTime.ToString("dd_MM_yyyy"));
        string seedWithPlayers = SeedHelper.CanonicalizeSeed(serverTime.ToString($"dd_MM_yyyy_{lobby.Players.Count}p"));

        // Ascension modifiers, and characters are not dependent on player count. They should be separate so that
        // character (player) count doesn't affect ascension or modifiers
        Rng baseRng = new((uint)StringHelper.GetDeterministicHashCode(seedNoPlayers));
        Rng characterRng = new(baseRng.NextUnsignedInt());
        Rng ascensionRng = new(baseRng.NextUnsignedInt());
        Rng modifierRng = new(baseRng.NextUnsignedInt());

        // Random characters
        CharacterModel localCharacter = default!;

        foreach (LobbyPlayer player in lobby.Players)
        {
            // We have to roll characters for everyone to keep the RNG converging, but we only care about our local one
            CharacterModel character = characterRng.NextItem(ModelDb.Characters)!;

            if (player.id == lobby.LocalPlayer.id)
            {
                localCharacter = character;
            }
        }

        // Random ascension and modifiers
        int ascension = ascensionRng.NextInt(0, AscensionManager.maxAscensionAllowed + 1);
        List<ModifierModel> modifiers = RollModifiers(modifierRng);

        // Only the host sets these
        if (lobby.NetService.Type is NetGameType.Host or NetGameType.Singleplayer)
        {
            // The seed used to setup & run the game is different depending on the players
            if (lobby.Seed != seedWithPlayers)
            {
                lobby.SetSeed(seedWithPlayers);
            }

            if (lobby.Ascension != ascension)
            {
                lobby.SyncAscensionChange(ascension);
            }

            // If any modifiers in our current list are not equivalent to a modifier in the lobby (i.e. if the lists don't match)
            if (modifiers.Any(m => lobby.Modifiers.FirstOrDefault(m.IsEquivalent) == null))
            {
                lobby.SetModifiers(modifiers);
            }
        }

        // Each player is responsible for setting their character
        if (lobby.LocalPlayer.character != localCharacter)
        {
            lobby.SetLocalCharacter(localCharacter);
        }

        InitializeDisplay();
    }

    private void InitializeDisplay()
    {
        if (_lobby == null) throw new InvalidOperationException("Tried to initialize daily climb display before lobby was initialized!");

        // Setup time left
        DateTimeOffset currentTime = GetServerRelativeTime();
        _endOfDay = new DateTimeOffset(currentTime.Year, currentTime.Month, currentTime.Day + 1, 0, 0, 0, TimeSpan.Zero);

        // Set all the controls on the window
        _remotePlayerContainer.Visible = _lobby.NetService.Type.IsMultiplayer();

        CharacterModel character = _lobby.LocalPlayer.character;
        _characterContainer.Fill(character, _lobby.LocalPlayer.id, _lobby.Ascension, _lobby.NetService);

        _dateLabel.Text = currentTime.ToString(dateFormat);

        for (int i = 0; i < _lobby.Modifiers.Count; i++)
        {
            _modifierContainers[i].Fill(_lobby.Modifiers[i]);
        }
    }

    private List<ModifierModel> RollModifiers(Rng rng)
    {
        // Two good modifiers, one bad modifier
        List<ModifierModel> modifiers = [];
        List<ModifierModel> goodModifiers = ModelDb.GoodModifiers.ToList().StableShuffle(rng);

        for (int i = 0; i < 2; i++)
        {
            ModifierModel? canonicalModifier = rng.NextItem(goodModifiers);

            if (canonicalModifier == null) throw new InvalidOperationException("There were not enough good modifiers to fill the daily!");

            ModifierModel mutableModifier = canonicalModifier.ToMutable();

            if (mutableModifier is CharacterCards cardsModifier)
            {
                IEnumerable<CharacterModel> charactersToExclude = _lobby!.Players.Select(p => p.character);
                cardsModifier.CharacterModel = rng.NextItem(ModelDb.Characters.Except(charactersToExclude))!.Id;
            }

            modifiers.Add(mutableModifier);
            goodModifiers.Remove(canonicalModifier);

            // If this modifier can't be combined with other modifiers, remove them from the list too
            IReadOnlySet<ModifierModel>? mutuallyExclusiveModifiers = ModelDb.MutuallyExclusiveModifiers.FirstOrDefault(s => s.Contains(canonicalModifier));

            if (mutuallyExclusiveModifiers != null)
            {
                foreach (ModifierModel exclusiveModifier in mutuallyExclusiveModifiers)
                {
                    goodModifiers.Remove(exclusiveModifier);
                }
            }
        }

        modifiers.Add(rng.NextItem(ModelDb.BadModifiers)!.ToMutable());

        return modifiers;
    }

    private void SetIsLoading(bool isLoading)
    {
        if (isLoading)
        {
            _remotePlayerContainer.Visible = false;
            _readyAndWaitingContainer.Visible = false;
        }

        _loadingTimeText.Visible = isLoading;
        _timeLeftLabel.Visible = !isLoading;
        _dateLabel.Visible = !isLoading;
        _characterContainer.Visible = !isLoading;
        _modifiersTitleLabel.Visible = !isLoading;
        _modifiersContainer.Visible = !isLoading;

        if (isLoading)
        {
            _embarkButton.Disable();
        }
        else
        {
            _embarkButton.Enable();
        }
    }

    public override void _Process(double delta)
    {
        if (_lobby == null) return;

        // If the time left rolls over to the end of day while we're looking at the lobby, re-initialize the daily params
        DateTimeOffset serverRelativeTime = GetServerRelativeTime();
        if (serverRelativeTime > _endOfDay)
        {
            SetupLobbyParams(_lobby);
        }

        TimeSpan timeLeft = _endOfDay - serverRelativeTime;

        // Update the time left if the seconds have changed
        if (_lastSetTimeLeftSecond != timeLeft.Seconds)
        {
            string timeLeftString = timeLeft.ToString(_timeLeftFormat);
            _timeLeftLoc.Add("time", timeLeftString);
            _timeLeftLabel.Text = _timeLeftLoc.GetFormattedText();
            _lastSetTimeLeftSecond = timeLeft.Seconds;
        }

        if (_lobby.NetService.IsConnected)
        {
            _lobby.NetService.Update();
        }
    }

    public void PlayerConnected(LobbyPlayer player)
    {
        _remotePlayerContainer.OnPlayerConnected(player);
        SetupLobbyParams(_lobby!);
        InitializeLeaderboard();
    }

    public void PlayerChanged(LobbyPlayer player)
    {
        _remotePlayerContainer.OnPlayerChanged(player);

        // If it's the local player and we're not in singleplayer, show checkmark on the player container
        if (player.id == _netService.NetId && _netService.Type.IsMultiplayer())
        {
            _characterContainer.SetIsReady(player.isReady);
        }
    }

    public void MaxAscensionChanged()
    {
        // We don't care about max multiplayer ascension. Just ignore it.
    }

    public void AscensionChanged()
    {
        InitializeDisplay();
    }

    public void SeedChanged()
    {
        // Nothing, we don't display the seed explicitly
    }

    public void ModifiersChanged()
    {
        InitializeDisplay();
    }

    public void RemotePlayerDisconnected(LobbyPlayer player)
    {
        _remotePlayerContainer.OnPlayerDisconnected(player);
        SetupLobbyParams(_lobby!);
        InitializeLeaderboard();
    }

    public void BeginClimb(string seed, IReadOnlyList<ModifierModel> modifiers, string act1)
    {
        NAudioManager.Instance?.StopMusic();

        if (_lobby!.NetService.Type == NetGameType.Singleplayer)
        {
            TaskHelper.RunSafely(StartNewSingleplayerClimb(seed, modifiers, act1));
        }
        else
        {
            TaskHelper.RunSafely(StartNewMultiplayerClimb(seed, modifiers, act1));
        }
    }

    public void LocalPlayerDisconnected(NetErrorInfo info)
    {
        // If we're the one that disconnected, then do nothing
        if (info.SelfInitiated && info.GetReason() == NetError.Quit) return;

        // Otherwise, close window and display error
        _stack.Pop();

        if (TestMode.IsOff)
        {
            NErrorPopup? popup = NErrorPopup.Create(info);

            if (popup != null)
            {
                NModalContainer.Instance!.AddChildSafely(popup);
            }
        }
    }

    private void OnEmbarkPressed(NButton _)
    {
        _embarkButton.Disable();
        _lobby!.SetReady(true);

        // In Multiplayer embarking sets us in a Ready state
        if (_lobby.NetService.Type != NetGameType.Singleplayer)
        {
            if (_lobby.Players.Count == 1 || _lobby.Players.Any(p => !p.isReady))
            {
                _readyAndWaitingContainer.Visible = true;
            }
        }
    }

    public async Task StartNewSingleplayerClimb(string seed, IReadOnlyList<ModifierModel> modifiers, string act1)
    {
        Log.Info($"Embarking on a {_lobby!.LocalPlayer.character.Id.Entry} climb with {_lobby.Players.Count} players. Ascension: {_lobby.Ascension}");
        await NGame.Instance!.Transition.FadeOut(0.8f, _lobby.LocalPlayer.character.CharacterSelectTransitionPath);

        List<ActModel> acts = ActModel.GetRandomList(seed).ToList();

        await NGame.Instance.StartNewSingleplayerClimb(_lobby.LocalPlayer.character, true, acts, modifiers, seed, _lobby.Ascension, _lobby.DailyTime!.Value.serverTime);

        await SaveManager.Instance.SaveClimb(null);
        NControllerManager.Instance!.ClearScreens();

        CleanUpLobby(false);
    }

    public async Task StartNewMultiplayerClimb(string seed, IReadOnlyList<ModifierModel> modifiers, string act1)
    {
        Log.Info($"Embarking on a multiplayer climb. Players: {string.Join(",", _lobby!.Players)}. Ascension: {_lobby.Ascension}");
        await NGame.Instance!.Transition.FadeOut(0.8f, _lobby.LocalPlayer.character.CharacterSelectTransitionPath);

        List<ActModel> acts = ActModel.GetRandomList(seed).ToList();

        await NGame.Instance.StartNewMultiplayerClimb(_lobby, true, acts, modifiers, seed, _lobby.Ascension, _lobby.DailyTime!.Value.serverTime);
        await SaveManager.Instance.SaveClimb(null);
        CleanUpLobby(false);
    }

    private void CleanUpLobby(bool disconnectSession)
    {
        _lobby?.CleanUp(disconnectSession);
        _lobby = null!;

        if (disconnectSession)
        {
            NGame.Instance!.RemoteCursorContainer.Deinitialize();
            NGame.Instance.ReactionContainer.DeinitializeNetworking();
        }
    }

    private void AfterLobbyInitialized()
    {
        // Note that this happens for both multiplayer and singleplayer. Map drawings rely on the remote cursor
        // container's synchronizer for drawing state even in singleplayer.
        NGame.Instance!.RemoteCursorContainer.Initialize(_lobby!.InputSynchronizer, _lobby.Players.Select(p => p.id));
        NGame.Instance.ReactionContainer.InitializeNetworking(_lobby.NetService);

        _remotePlayerContainer.Initialize(_lobby, false);

        // We might want to remove this at some point. It's here for debugging purposes.
        Logger.logLevelTypeMap[LogType.Network] = _lobby.NetService.Type == NetGameType.Singleplayer ? LogLevel.Info : LogLevel.Debug;
        Logger.logLevelTypeMap[LogType.Actions] = _lobby.NetService.Type == NetGameType.Singleplayer ? LogLevel.Info : LogLevel.VeryDebug;
        Logger.logLevelTypeMap[LogType.GameSync] = _lobby.NetService.Type == NetGameType.Singleplayer ? LogLevel.Info : LogLevel.VeryDebug;

        NGame.Instance.DebugSeedOverride = null;

        // Only allow embarking after the timeserver is queried and multiplayer lobby (if necessary) is initialized.
        // This can't go in InitializeDisplay because that gets called every time a player connects, or when the time
        // rolls over, and we don't want to un-ready the player in those instances.
        _embarkButton.Enable();
    }
}
