using System;
using Godot;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;

namespace MegaCrit.Sts2.Core.Nodes.Screens.DailyClimb;

public partial class NLeaderboardPageArrow : NButton
{
    private TextureRect _image = default!;
    private ShaderMaterial _hsv = default!;
    private Tween? _tween;
    private Vector2 _baseScale;
    private Action? _onRelease;

    public override void _Ready()
    {
        ConnectSignals();

        _image = GetNode<TextureRect>("Image");
        _baseScale = _image.Scale;
        _hsv = (ShaderMaterial)_image.Material;
    }

    public void Connect(Action onRelease)
    {
        _onRelease = onRelease;
    }

    protected override void OnDisable()
    {
        Modulate = StsColors.exhaustGray;
    }

    protected override void OnEnable()
    {
        Modulate = Colors.White;
    }

    protected override void OnRelease()
    {
        _onRelease?.Invoke();

        _tween?.Kill();
        _tween = CreateTween().SetParallel();
        _tween.TweenProperty(_image, "scale", _baseScale * 1.1f, 0.05);
        _tween.TweenMethod(Callable.From<float>(UpdateShaderV), _hsv.GetShaderParameter("v"), 1.5f, 0.05);
    }

    protected override void OnFocus()
    {
        base.OnFocus(); // Plays sfx

        _tween?.Kill();
        _tween = CreateTween().SetParallel();
        _tween.TweenProperty(_image, "scale", _baseScale * 1.1f, 0.05);
        _tween.TweenMethod(Callable.From<float>(UpdateShaderV), _hsv.GetShaderParameter("v"), 1.5f, 0.05);
    }

    protected override void OnUnfocus()
    {
        base.OnUnfocus();

        _tween?.Kill();
        _tween = CreateTween().SetParallel();
        _tween.TweenProperty(_image, "scale", _baseScale, 0.5)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _tween.TweenMethod(Callable.From<float>(UpdateShaderV), _hsv.GetShaderParameter("v"), 0.9f, 0.5)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    protected override void OnPressDown()
    {
        base.OnPressDown();

        _tween?.Kill();
        _tween = CreateTween().SetParallel();
        _tween.TweenProperty(_image, "scale", _baseScale * 0.9f, 0.5)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _tween.TweenMethod(Callable.From<float>(UpdateShaderV), _hsv.GetShaderParameter("v"), 0.8f, 0.5)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    private void UpdateShaderV(float value)
    {
        _hsv.SetShaderParameter("v", value);
    }
}
