using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;

namespace MegaCrit.Sts2.Core.Nodes.Screens.FeedbackScreen;

/// <summary>
/// The language dropdown in the OptionsScreen.
/// </summary>
public partial class NFeedbackCategoryDropdown : NDropdown
{
    private static string ScenePath => SceneHelper.GetScenePath("screens/feedback_screen/category_dropdown_item");
    public static IEnumerable<string> AssetPaths => [ScenePath];

    private static readonly string[] _categories =
    [
        "bug",
        "balance",
        "art",
        "audio",
        "ux",
        "none"
    ];

    private static readonly LocString[] _categoryLoc =
    [
        new("settings_ui", "FEEDBACK_CATEGORY.bug"),
        new("settings_ui", "FEEDBACK_CATEGORY.balance"),
        new("settings_ui", "FEEDBACK_CATEGORY.art"),
        new("settings_ui", "FEEDBACK_CATEGORY.audio"),
        new("settings_ui", "FEEDBACK_CATEGORY.ux"),
        new("settings_ui", "FEEDBACK_CATEGORY.none")
    ];

    private int _currentCategoryIndex = _categories.IndexOf("none");

    public string CurrentCategory => _categories[_currentCategoryIndex];

    public override void _Ready()
    {
        ConnectSignals();
        _currentOptionHighlight = GetNode<Panel>("%Highlight");
        _currentOptionLabel = GetNode<Label>("%Label");
        PopulateOptions();
    }

    protected override void OnFocus()
    {
        _currentOptionHighlight.Modulate = new Color("afcdde");
    }

    protected override void OnUnfocus()
    {
        _currentOptionHighlight.Modulate = Colors.White;
    }

    private void PopulateOptions()
    {
        Control dropdownItems = GetNode<Control>("DropdownContainer/VBoxContainer");

        // Remove any junk that's already there
        foreach (Node n in dropdownItems.GetChildren())
        {
            dropdownItems.RemoveChildSafely(n);
            n.QueueFreeSafely();
        }

        // Populate the list
        for (int i = 0; i < _categories.Length; i++)
        {
            NFeedbackCategoryDropdownItem dropdownItem = PreloadManager.Cache.GetScene(ScenePath).Instantiate<NFeedbackCategoryDropdownItem>();
            dropdownItems.AddChildSafely(dropdownItem);
            dropdownItem.Connect(NDropdownItem.SignalName.Selected, Callable.From<NDropdownItem>(OnDropdownItemSelected));
            dropdownItem.Init(i, _categoryLoc[i].GetFormattedText());
        }

        dropdownItems.GetParent<NDropdownContainer>().RefreshLayout();
    }

    private void OnDropdownItemSelected(NDropdownItem item)
    {
        NFeedbackCategoryDropdownItem dropdownItem = (NFeedbackCategoryDropdownItem)item;
        if (dropdownItem.CategoryIndex == _currentCategoryIndex) return;
        CloseDropdown();
        _currentCategoryIndex = dropdownItem.CategoryIndex;
        _currentOptionLabel.Text = _categoryLoc[_currentCategoryIndex].GetFormattedText();
    }
}
