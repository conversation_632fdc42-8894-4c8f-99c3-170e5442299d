using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.Screens.Capstones;

namespace MegaCrit.Sts2.Core.Nodes.Screens.FeedbackScreen;

/// <summary>
/// Opens the feedback screen from anywhere when a hotkey is pressed.
/// </summary>
public partial class NFeedbackScreenOpener : Node
{
    public static NFeedbackScreenOpener Instance { get; private set; } = default!;

    public override void _EnterTree()
    {
        Instance = this;
    }

    public override void _ExitTree()
    {
        Instance = null!;
    }

    public override void _Input(InputEvent inputEvent)
    {
        if (inputEvent is not InputEventKey keyEvent) return;
        if (!keyEvent.Pressed) return;

        if (keyEvent.Keycode != Key.F2) return;

        // If the feedback scren is already open, don't open a second instance
        if (NGame.Instance?.MainMenu?.SubmenuStack.Peek() is NSendFeedbackScreen) return;
        if (NCapstoneContainer.Instance?.CurrentCapstoneScreen is NCapstoneSubmenuStack { ScreenType: NetScreenType.Feedback }) return;

        TaskHelper.RunSafely(OpenFeedbackScreen());
    }

    public async Task OpenFeedbackScreen()
    {
        Image screenshot = GetViewport().GetTexture().GetImage();

        // Wait one frame for the screenshot to be taken, otherwise the screenshot can be inaccurate
        await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);

        if (NGame.Instance!.MainMenu != null)
        {
            NGame.Instance.MainMenu.FeedbackScreen.SetScreenshot(screenshot);
            NGame.Instance.MainMenu.SubmenuStack.Push(NGame.Instance.MainMenu.FeedbackScreen);
        }
        else if (NClimb.Instance != null)
        {
            NClimb.Instance.GlobalUi.SubmenuStack.ShowScreen(CapstoneSubmenuType.Feedback);
            NCapstoneSubmenuStack stack = (NCapstoneSubmenuStack)NCapstoneContainer.Instance!.CurrentCapstoneScreen!;
            NSendFeedbackScreen feedbackScreen = (NSendFeedbackScreen)stack.Stack.Peek()!;
            feedbackScreen.SetScreenshot(screenshot);
        }
    }
}
