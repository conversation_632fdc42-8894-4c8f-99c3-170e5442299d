using Godot;
using MegaCrit.Sts2.Core.ControllerInput;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;

namespace MegaCrit.Sts2.Core.Nodes.Screens.GameOverScreen;

public partial class NViewClimbButton : NButton
{
    protected override string[] Hotkeys => [MegaInput.accept];

    private Tween? _tween;
    private Tween? _hoverTween;
    private Vector2 _showPosition;

    private ShaderMaterial _hsv = default!;
    private const float _hoverS = 1.2f;
    private const float _hoverV = 1.4f;
    private const float _unhoverS = 1f;
    private const float _unhoverV = 1f;
    private const float _pressDownS = 1f;
    private const float _pressDownV = 1f;

    public override void _Ready()
    {
        ConnectSignals();
        _hsv = (ShaderMaterial)GetNode<TextureRect>("Image").Material;
        _showPosition = GetPosition();
        Position = _showPosition + new Vector2(140f, 0f);
    }

    // Start click/holding button down...
    protected override void OnPressDown()
    {
        base.OnPressDown(); // NOTE: Remove me and play a better sfx...

        _hoverTween?.Kill();
        _hoverTween = CreateTween().SetParallel();
        _hoverTween.TweenProperty(this, "scale", Vector2.One, 1.0)
            .SetTrans(Tween.TransitionType.Expo)
            .SetEase(Tween.EaseType.Out);
        _hoverTween.TweenMethod(Callable.From<float>(UpdateShaderS), _hsv.GetShaderParameter("s"),
                _pressDownS, 1.0)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _hoverTween.TweenMethod(Callable.From<float>(UpdateShaderV), _hsv.GetShaderParameter("v"),
                _pressDownV, 1.0)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    /// <summary>
    /// Call when we want this button to animate in.
    /// Called when we transition from Death intermission -> summary and when returning from Climb History/Settings.
    /// </summary>
    protected override void OnEnable()
    {
        base.OnEnable();
        _isEnabled = true;

        _tween = CreateTween().SetParallel();
        _tween.TweenProperty(this, "position", _showPosition, 0.5)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Quart);
        _tween.TweenProperty(this, "modulate", Colors.White, 0.5)
            .From(StsColors.transparentBlack);
    }

    /// <summary>
    /// Called to hide this button (Open Settings/Climb History or something during Death summary)
    /// </summary>
    protected override void OnDisable()
    {
        base.OnDisable();
        _isEnabled = false;

        _tween?.Kill();
        _tween = CreateTween().SetParallel();
        _tween.TweenProperty(this, "position:y", _showPosition.Y + 190f, 0.5)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    // Hover
    protected override void OnFocus()
    {
        base.OnFocus();

        _hoverTween?.Kill();
        _hoverTween = CreateTween().SetParallel();
        _hoverTween.TweenProperty(this, "scale", Vector2.One * 1.1f, 0.05);
        _hoverTween.TweenMethod(Callable.From<float>(UpdateShaderS), _hsv.GetShaderParameter("s"),
                _hoverS, 0.05)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _hoverTween.TweenMethod(Callable.From<float>(UpdateShaderV), _hsv.GetShaderParameter("v"),
                _hoverV, 0.05)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    // Unhover
    protected override void OnUnfocus()
    {
        base.OnUnfocus();

        _hoverTween?.Kill();
        _hoverTween = CreateTween().SetParallel();
        _hoverTween.TweenProperty(this, "scale", Vector2.One, 1.0)
            .SetTrans(Tween.TransitionType.Expo)
            .SetEase(Tween.EaseType.Out);
        _hoverTween.TweenMethod(Callable.From<float>(UpdateShaderS), _hsv.GetShaderParameter("s"),
                _unhoverS, 1.0)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _hoverTween.TweenMethod(Callable.From<float>(UpdateShaderV), _hsv.GetShaderParameter("v"),
                _unhoverV, 1.0)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    private void UpdateShaderS(float value)
    {
        _hsv.SetShaderParameter("s", value);
    }

    private void UpdateShaderV(float value)
    {
        _hsv.SetShaderParameter("v", value);
    }
}
