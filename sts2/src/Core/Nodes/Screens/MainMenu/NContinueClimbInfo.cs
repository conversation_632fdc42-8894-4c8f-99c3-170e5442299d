using System;
using System.Globalization;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Nodes.Screens.MainMenu;

/// <summary>
/// Shows climb info in the main menu! Handy!
/// </summary>
public partial class NContinueClimbInfo : Control
{
    private Tween? _visTween;
    private Vector2 _initPosition;

    private Control _climbInfoContainer = default!;
    private Control _errorContainer = default!;

    private MegaRichTextLabel _dateLabel = default!;
    private MegaRichTextLabel _goldLabel = default!;
    private MegaRichTextLabel _healthLabel = default!;
    private MegaRichTextLabel _progressLabel = default!;
    private MegaRichTextLabel _ascensionLabel = default!;

    private TextureRect _charIcon = default!;

    public override void _Ready()
    {
        _initPosition = Position;
        Modulate = StsColors.transparentWhite;

        _climbInfoContainer = GetNode<VBoxContainer>("%ClimbInfoContainer");
        _errorContainer = GetNode<Control>("%ErrorContainer");
        _dateLabel = GetNode<MegaRichTextLabel>("%DateLabel");
        _goldLabel = GetNode<MegaRichTextLabel>("%GoldLabel");
        _healthLabel = GetNode<MegaRichTextLabel>("%HealthLabel");
        _progressLabel = GetNode<MegaRichTextLabel>("%ProgressLabel");
        _charIcon = GetNode<TextureRect>("%CharacterIcon");
        _ascensionLabel = GetNode<MegaRichTextLabel>("%AscensionLabel");
    }

    public void AnimShow()
    {
        _visTween?.Kill();
        _visTween = CreateTween().SetParallel();
        _visTween.TweenProperty(this, "position", _initPosition + new Vector2(0f, -20f), 0.2f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Back);
        _visTween.TweenProperty(this, "modulate:a", 1f, 0.2f);
    }

    public void AnimHide()
    {
        _visTween?.Kill();
        _visTween = CreateTween().SetParallel();
        _visTween.TweenProperty(this, "position", _initPosition, 0.2f);
        _visTween.TweenProperty(this, "modulate:a", 0f, 0.2f);
    }

    public void SetResult(ReadSaveResult<SerializableClimb>? result)
    {
        if (result is { Success: true, SaveData: not null })
        {
            ShowInfo(result.SaveData);
        }
        else
        {
            ShowError();
        }
    }

    private void ShowInfo(SerializableClimb save)
    {
        _errorContainer.Visible = false;
        _climbInfoContainer.Visible = true;

        // Last saved Date & Time
        DateTimeFormatInfo dateInfo = LocManager.Instance.CultureInfo.DateTimeFormat;
        DateTimeOffset dateTimeOffset = DateTimeOffset.FromUnixTimeSeconds(save.SaveTime);
        DateTime dateTime = TimeZoneInfo.ConvertTimeFromUtc(dateTimeOffset.UtcDateTime, TimeZoneInfo.Local);
        string dateTimeStr = dateTime.ToString("dddd MMM d, yyyy h:mmtt", dateInfo);
        LocString dateSaved = new("main_menu_ui", "CONTINUE_CLIMB_INFO.saved");
        _dateLabel.Text = $"{dateSaved.GetFormattedText()}\n{dateTimeStr}";

        if (save.Ascension > 0)
        {
            LocString ascension = new("main_menu_ui", "CONTINUE_CLIMB_INFO.ascension");
            _ascensionLabel.Text = $"{ascension.GetFormattedText()} {save.Ascension}";
        }
        else
        {
            _ascensionLabel.Visible = false;
        }

        // Character Icon and Act/Floor info
        ActModel currentAct = ModelDb.GetById<ActModel>(save.Acts[save.CurrentActIndex].Id!);
        SerializablePlayer player = save.Players[0];
        _charIcon.Texture = ModelDb.GetById<CharacterModel>(player.CharacterId!).IconTexture;

        string actStr = currentAct.Title.GetFormattedText();
        string floorStr = new LocString("main_menu_ui", "CONTINUE_CLIMB_INFO.floor").GetFormattedText();

        int floor = save.VisitedMapCoords.Count;
        for (int i = 0; i < save.CurrentActIndex; i++)
        {
            floor += ModelDb.GetById<ActModel>(save.Acts[i].Id!).NumberOfFloors;
        }

        _progressLabel.Text = $"{actStr} [blue]- {floorStr} {floor}[/blue]";

        // Health & Gold
        _healthLabel.Text = $"[red]{player.CurrentHp}/{player.MaxHp}[/red]";
        _goldLabel.Text = $"[gold]{player.Gold}[/gold]";
    }

    private void ShowError()
    {
        _climbInfoContainer.Visible = false;
        _errorContainer.Visible = true;
    }
}
