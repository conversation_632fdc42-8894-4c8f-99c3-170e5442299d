using Godot;

namespace MegaCrit.Sts2.Core.Nodes.Screens.MainMenu;

public partial class NMainMenuBg : Control
{
    private Window _window = default!;
    private Control _bg = default!;
    private SpineSprite _logo = default!;
    private Tween? _logoTween;

    // Handles scaling the bg asset to fit when the aspect ratio is narrower than 16:10
    private static readonly Vector2 _defaultBgScale = Vector2.One * 1.01f; // Slightly off because of <PERSON>'s past mistakes
    private const float _bgScaleRatioThreshold = 1.5f;

    public override void _Ready()
    {
        _bg = GetNode<Control>("BgContainer");
        _logo = GetNode<SpineSprite>("%Logo");

        _window = GetTree().Root;
        _window.Connect(Viewport.SignalName.SizeChanged, Callable.From(OnWindowChange));
    }

    private void OnWindowChange()
    {
        ScaleBgIfNarrow((float)_window.Size.X / _window.Size.Y);
    }

    private void ScaleBgIfNarrow(float ratio)
    {
        // If we're starting to get a bit narrow, we need to scale up the bg asset.
        if (ratio < _bgScaleRatioThreshold)
        {
            _bg.Scale = Vector2.One * 1.04f;
        }
        else
        {
            // The screen getting wider is handled by Godot's default behavior
            _bg.Scale = _defaultBgScale;
        }
    }

    public void HideLogo()
    {
        _logoTween?.Kill();
        _logoTween = CreateTween();
        _logoTween.TweenProperty(_logo, "modulate:a", 0f, 1.0)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic);
    }

    public void ShowLogo()
    {
        _logoTween?.Kill();
        _logoTween = CreateTween();
        _logoTween.TweenProperty(_logo, "modulate:a", 1f, 1.0)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic);
    }
}
