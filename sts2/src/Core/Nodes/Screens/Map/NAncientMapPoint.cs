using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Nodes.Screens.Map;

/// <summary>
/// The special roughly 200x200 (in 1080p) map icon for the Ancient room.
/// TODO: Bump up asset to ~266x266 px (as we're targeting 1440p)
/// NOTE: Outline is created as a 2px stroke for the 1080p asset and named the same as the icon with _outline appended.
/// </summary>
public partial class NAncientMapPoint : NMapPoint
{
    private TextureRect _icon = default!;
    private TextureRect _outline = default!;
    private Tween? _tween;

    // Colors for the icon
    protected override Color TraveledColor => StsColors.pathDotTraveled;
    protected override Color UntravelableColor => StsColors.bossNodeUntraveled;
    protected override Color HoveredColor => StsColors.pathDotTraveled;

    // Animation variables
    protected override Vector2 HoverScale => Vector2.One * 1.1f;
    protected override Vector2 DownScale => Vector2.One * 0.9f;

    // Pulsing effect
    private const float _pulseSpeed = 4f;
    private const float _scaleAmount = 0.05f;
    private const float _scaleBase = 1f;

    private float _elapsedTime = Rng.Chaotic.NextFloat(3140f);

    private static string UntravelableMaterialPath => "res://materials/boss_map_point_unavailable.tres";
    private static string AncientMapPointPath => SceneHelper.GetScenePath("ui/ancient_map_point");

    public static IEnumerable<string> AssetPaths => [UntravelableMaterialPath, AncientMapPointPath];

    private Material? TargetMaterial
    {
        get
        {
            if (IsTravelable || State == MapPointState.Traveled)
            {
                return null;
            }

            return PreloadManager.Cache.GetMaterial(UntravelableMaterialPath);
        }
    }

    public static NAncientMapPoint Create(MapPoint point, NMapScreen screen, IClimbState climbState)
    {
        NAncientMapPoint node = PreloadManager.Cache.GetScene(AncientMapPointPath).Instantiate<NAncientMapPoint>();
        node.Point = point;
        node._screen = screen;
        node._climbState = climbState;

        return node;
    }

    public override void _Ready()
    {
        ConnectSignals();
        _icon = GetNode<TextureRect>("Icon");
        _icon.Texture = _climbState.Act.Ancient.Icon;
        _outline = GetNode<TextureRect>("Icon/Outline");
        _outline.Texture = _climbState.Act.Ancient.IconOutline;
        _outline.Modulate = _climbState.Act.MapBgColor;

        RefreshColorInstantly();
    }

    public override void _Process(double delta)
    {
        if (State == MapPointState.Travelable)
        {
            if (!IsFocused && IsInputAllowed())
            {
                _elapsedTime += (float)delta * _pulseSpeed;
                Scale = Vector2.One * (Mathf.Sin(_elapsedTime) * _scaleAmount + _scaleBase);
            }
            else
            {
                // Lerp to one over time
                Scale = Scale.Lerp(Vector2.One, 0.5f);
            }
        }
    }

    protected override void OnFocus()
    {
        base.OnFocus();
        if (!IsInputAllowed()) return;

        AnimHover();

        if (NControllerManager.Instance!.IsUsingController)
        {
            _controllerSelectionReticle.OnSelect();
        }
    }

    protected override void OnUnfocus()
    {
        base.OnUnfocus();
        if (!IsInputAllowed()) return;

        AnimUnhover();
        _controllerSelectionReticle.OnDeselect();
    }

    protected override void OnPressDown()
    {
        if (!IsTravelable) return;

        AnimPressDown();
        _controllerSelectionReticle.OnDeselect();
    }

    public override void OnSelected()
    {
        State = MapPointState.Traveled;

        _tween?.Kill();
        _tween = CreateTween().SetParallel();

        _tween.TweenProperty(_icon, "scale", Vector2.One, 0.3)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Back);
        _tween.TweenProperty(_icon, "self_modulate", TargetColor, 0.3)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _tween.TweenProperty(_outline, "modulate", _climbState.Act.MapBgColor, 0.3)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    protected override void RefreshColorInstantly()
    {
        _icon.SelfModulate = TargetColor;
    }

    private void AnimHover()
    {
        _tween?.Kill();
        _tween = CreateTween().SetParallel();

        _tween.TweenProperty(_icon, "scale", HoverScale, 0.05);
        _tween.TweenProperty(_icon, "self_modulate", HoveredColor, 0.05);

        if (IsTravelable)
        {
            _tween.TweenProperty(_outline, "modulate", _outlineColor, 0.05);
        }
    }

    private void AnimUnhover()
    {
        _tween?.Kill();
        _tween = CreateTween().SetParallel();

        _tween.TweenProperty(_icon, "scale", Vector2.One, _unhoverAnimDur)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic);
        _tween.TweenProperty(_icon, "self_modulate", TargetColor, _unhoverAnimDur)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _tween.TweenProperty(_outline, "modulate", _climbState.Act.MapBgColor, _unhoverAnimDur)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    private void AnimPressDown()
    {
        _tween?.Kill();
        _tween = CreateTween().SetParallel();

        _tween.TweenProperty(_icon, "scale", DownScale, _pressDownDur)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic);
        _tween.TweenProperty(_outline, "modulate", _climbState.Act.MapBgColor, _unhoverAnimDur)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }
}
