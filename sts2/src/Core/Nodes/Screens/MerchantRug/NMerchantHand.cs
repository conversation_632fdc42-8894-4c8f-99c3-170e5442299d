using System.Threading;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Helpers;

namespace MegaCrit.Sts2.Core.Nodes.Screens.MerchantRug;

public partial class NMerchantHand : SpineSprite
{
    private Vector2 _startPos;
    private Vector2 _targetPos;

    private SpineBone _bone = default!;
    private FastNoiseLite _noise = default!;
    private float _time;

    private CancellationTokenSource? _stopPointingToken;

    public override void _Ready()
    {
        _startPos = GlobalPosition;
        _targetPos = _startPos;

        _noise = new FastNoiseLite();
        _noise.NoiseType = FastNoiseLite.NoiseTypeEnum.Perlin;
        _noise.Frequency = 1f;

        _bone = GetSkeleton().FindBone("rotate_me");

        GetAnimationState().SetAnimation("default");
    }

    public override void _ExitTree()
    {
        base._ExitTree();

        _stopPointingToken?.Cancel();
    }

    public override void _Process(double delta)
    {
        _time += (float)delta;
        float offsetX = _noise.GetNoise1D(_time * 0.1f) + 0.4f;
        float offsetY = _noise.GetNoise1D((_time + 0.25f) * 0.1f) - 0.5f;
        GlobalPosition = GlobalPosition.Lerp(_targetPos + new Vector2(offsetX, offsetY) * 100f, (float)delta * 4f);

        Control parentNode = (Control)GetParent();
        _bone.SetRotation(Mathf.Lerp(-10f, 10f, (Position.X - parentNode.Size.X * 0.5f - 50f) * 0.01f));
    }

    public void PointAtTarget(Vector2 pos)
    {
        _stopPointingToken?.Cancel();
        _targetPos = pos - Vector2.One * 50f;
    }

    public void StopPointing(float lingerTime)
    {
        _stopPointingToken?.Cancel();
        _stopPointingToken = new();
        TaskHelper.RunSafely(WaitAndReturn(_stopPointingToken, lingerTime));
    }

    private async Task WaitAndReturn(CancellationTokenSource cancelToken, float lingerTime)
    {
        float timer = 0f;
        while (timer < lingerTime)
        {
            if (cancelToken.IsCancellationRequested) return;
            await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
            timer += (float)GetProcessDeltaTime();
        }

        _targetPos = _startPos;
    }
}
