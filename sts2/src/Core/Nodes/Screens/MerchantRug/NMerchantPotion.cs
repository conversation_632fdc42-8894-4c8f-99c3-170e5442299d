using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Entities.Merchant;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.HoverTips;
using MegaCrit.Sts2.Core.Nodes.Potions;

namespace MegaCrit.Sts2.Core.Nodes.Screens.MerchantRug;

public partial class NMerchantPotion : NMerchantSlot
{
    private Control _potionHolder = default!;
    private NPotion? _potionNode;
    private MerchantPotionEntry _potionEntry = default!;

    public override MerchantEntry Entry => _potionEntry;
    protected override CanvasItem Visual => _potionHolder;

    public override void _Ready()
    {
        ConnectSignals();
        _potionHolder = GetNode<Control>("%PotionHolder");
    }

    public void FillSlot(MerchantPotionEntry potionEntry)
    {
        _potionEntry = potionEntry;
        _potionEntry.EntryUpdated += UpdateVisual;
        _potionEntry.PurchaseFailed += OnPurchaseFailed;
        UpdateVisual();
    }

    protected override void UpdateVisual()
    {
        base.UpdateVisual();

        if (_potionEntry.Model == null)
        {
            Visible = false;
            MouseFilter = MouseFilterEnum.Ignore;

            if (_potionNode != null)
            {
                _potionNode.QueueFreeSafely();
                _potionNode = null;
            }

            ClearHoverTip();
        }
        else
        {
            if (_potionNode != null && _potionNode.Model != _potionEntry.Model)
            {
                _potionNode.QueueFreeSafely();
                _potionNode = null;
            }

            if (_potionNode == null)
            {
                _potionNode = NPotion.Create(_potionEntry.Model)!;
                _potionHolder.AddChildSafely(_potionNode);
                _potionNode.Position = Vector2.Zero;
            }

            _costLabel.Text = _potionEntry.Cost.ToString();
            _costLabel.Modulate = _potionEntry.EnoughGold ? StsColors.cream : StsColors.red;
        }
    }

    protected override async Task OnTryPurchase(MerchantInventory? inventory)
    {
        // Potion changes after the purchase, so cache it
        PotionModel purchasedPotion = _potionEntry.Model!;
        Vector2? position = _potionNode?.GlobalPosition;

        bool success = await _potionEntry.OnTryPurchaseWrapper(inventory);

        if (success)
        {
            NClimb.Instance?.GlobalUi.TopBar.PotionContainer.AnimatePotion(purchasedPotion, position);
            UpdateVisual();
        }
    }

    protected override void CreateHoverTip()
    {
        NHoverTipSet tip = NHoverTipSet.CreateAndShow(this, _potionNode!.Model.HoverTips);
        tip.GlobalPosition = GlobalPosition;

        bool alignLeft = GlobalPosition.X > GetViewport().GetVisibleRect().Size.X / 2f;
        if (alignLeft)
        {
            tip.SetAlignment(this, HoverTipAlignment.Left);
            tip.GlobalPosition -= Size / 2f * Scale;
        }
        else
        {
            tip.GlobalPosition += Vector2.Right * Size.X / 2f * Scale + Vector2.Up * Size.Y / 2f * Scale;
        }
    }
}
