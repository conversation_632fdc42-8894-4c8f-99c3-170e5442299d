using System.Text;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Modding;

namespace MegaCrit.Sts2.Core.Nodes.Screens.ModdingScreen;

public partial class NModInfoContainer : Control
{
    private MegaRichTextLabel _title = default!;
    private TextureRect _image = default!;
    private MegaRichTextLabel _description = default!;

    public override void _Ready()
    {
        _title = GetNode<MegaRichTextLabel>("ModTitle");
        _image = GetNode<TextureRect>("ModImage");
        _description = GetNode<MegaRichTextLabel>("ModDescription");

        _title.Text = "";
        _image.Texture = null;
        _description.Text = "";
    }

    public void Fill(Mod mod)
    {
        if (mod.wasLoaded)
        {
            _title.Text = mod.manifest!.name!;
            _image.Texture = PreloadManager.Cache.GetAsset<Texture2D>($"res://{mod.pckName}/mod_image.png");

            StringBuilder sb = new();
            sb.AppendLine($"[gold]Author[/gold]: {mod.manifest.author ?? "unknown"}");
            sb.AppendLine($"[gold]Version[/gold]: {mod.manifest.version ?? "unknown"}");
            sb.AppendLine();
            sb.AppendLine($"{mod.manifest.description ?? "No description"}");

            _description.Text = sb.ToString();
        }
        else
        {
            _title.Text = mod.pckName;
            _image.Texture = NModMenuRow.GetPlatformIcon(mod.modSource);
            _description.Text = new LocString("settings_ui", "MODDING_SCREEN.MOD_UNLOADED_DESCRIPTION").GetFormattedText();
        }
    }
}
