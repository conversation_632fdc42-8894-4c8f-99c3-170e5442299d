using System;
using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.Screens.Capstones;
using MegaCrit.Sts2.Core.Nodes.Screens.MainMenu;

namespace MegaCrit.Sts2.Core.Nodes.Screens;

/// <summary>
/// Controls submenu stacks that show up as a capstone (compendium, settings).
/// Allows submenus to stack on top of each other and act as one big capstone. They are all dismissed if another
/// capstone shows up.
/// </summary>
public partial class NCapstoneSubmenuStack : Control, ICapstoneScreen
{
    private static string ScenePath => SceneHelper.GetScenePath("screens/capstone_submenu_stack");
    public static IEnumerable<string> AssetPaths => [ScenePath];

    public CapstoneSubmenuType Type { get; private set; }
    public NSubmenuStack Stack { get; private set; } = default!;
    public NetScreenType ScreenType => GetCapstoneSubmenuType();

    public NSubmenu ShowScreen(CapstoneSubmenuType type)
    {
        while (Stack.Peek() != null)
        {
            Stack.Pop();
        }

        string initialMenuName = type switch
        {
            CapstoneSubmenuType.Compendium => "CompendiumSubmenu",
            CapstoneSubmenuType.Feedback => "FeedbackScreen",
            CapstoneSubmenuType.PauseMenu => "PauseMenu",
            CapstoneSubmenuType.Settings => "SettingsScreen",
            _ => throw new ArgumentOutOfRangeException(nameof(type), type, null)
        };

        NSubmenu submenu = GetNode<NSubmenu>($"Submenus/{initialMenuName}");
        Stack.Push(submenu);
        Type = type;

        NDebugAudioManager.Instance?.Play(TmpSfx.openCardScreen);
        NCapstoneContainer.Instance!.Open(this);

        return submenu;
    }

    private NetScreenType GetCapstoneSubmenuType()
    {
        return Type switch
        {
            CapstoneSubmenuType.Compendium => NetScreenType.Compendium,
            CapstoneSubmenuType.Feedback => NetScreenType.Feedback,
            CapstoneSubmenuType.PauseMenu => NetScreenType.PauseMenu,
            CapstoneSubmenuType.Settings => NetScreenType.Settings,
            _ => throw new ArgumentOutOfRangeException()
        };
    }

    public override void _Ready()
    {
        Stack = GetNode<NSubmenuStack>("%Submenus");
        Stack.Connect(NSubmenuStack.SignalName.StackModified, Callable.From(OnSubmenuStackChanged));
    }

    private void OnSubmenuStackChanged()
    {
        // If the last submenu just popped itself off the stack, and we're still the current capstone, then close us.
        // The second check is necessary because if we opened another capstone, triggering us to close, we're already
        // closed and don't want to close the other capstone that just opened.
        if (Stack.Peek() == null && NCapstoneContainer.Instance!.CurrentCapstoneScreen == this)
        {
            NCapstoneContainer.Instance.Close();
        }
    }

    public void AfterCapstoneOpened()
    {
        NGlobalUi globalUi = NClimb.Instance!.GlobalUi;
        globalUi.TopBar.AnimHide();
        globalUi.RelicInventory.AnimHide();
        globalUi.MultiplayerPlayerContainer.AnimHide();

        // We want these containers to be displayed underneath specific screens (pause menu, feedback screen, settings,
        // compendium). However, during gameplay they need to render above the top bar. So move them under the capstone
        // stack when these screens are shown.
        // Note that this does _not_ include the map or the deck view! These containers should still render above those
        // screens, as those are still gameplay-related.
        globalUi.MoveChild(globalUi.AboveTopBarVfxContainer, globalUi.CapstoneContainer.GetIndex());
        globalUi.MoveChild(globalUi.CardPreviewContainer, globalUi.CapstoneContainer.GetIndex());
        globalUi.MoveChild(globalUi.MessyCardPreviewContainer, globalUi.CapstoneContainer.GetIndex());

        Visible = true;
    }

    public void AfterCapstoneClosed()
    {
        while (Stack.Peek() != null)
        {
            Stack.Pop();
        }

        NGlobalUi globalUi = NClimb.Instance!.GlobalUi;
        globalUi.TopBar.AnimShow();
        globalUi.RelicInventory.AnimShow();
        globalUi.MultiplayerPlayerContainer.AnimShow();

        // Move these back above the top bar
        globalUi.MoveChild(globalUi.AboveTopBarVfxContainer, globalUi.TopBar.GetIndex() + 1);
        globalUi.MoveChild(globalUi.CardPreviewContainer, globalUi.TopBar.GetIndex() + 1);
        globalUi.MoveChild(globalUi.MessyCardPreviewContainer, globalUi.TopBar.GetIndex() + 1);

        Visible = false;
    }

    public bool UseSharedBackstop => true;
}
