using Godot;
using MegaCrit.Sts2.Core.Entities.Potions;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.Screens.MainMenu;

namespace MegaCrit.Sts2.Core.Nodes.Screens.PotionLab;

public partial class NPotionLab : NSubmenu
{
    private Control _screenContents = default!;

    // If we get more categories, we could spawn these in dynamically
    private NPotionLabCategory _common = default!;
    private NPotionLabCategory _uncommon = default!;
    private NPotionLabCategory _rare = default!;
    private NPotionLabCategory _special = default!;

    private Tween? _screenTween;

    public override void _Ready()
    {
        ConnectSignals();
        _screenContents = GetNode<Control>("%ScreenContents");
        _common = GetNode<NPotionLabCategory>("%Common");
        _uncommon = GetNode<NPotionLabCategory>("%Uncommon");
        _rare = GetNode<NPotionLabCategory>("%Rare");
        _special = GetNode<NPotionLabCategory>("%Special");
    }

    public override void OnSubmenuOpened()
    {
        LoadPotions();
    }

    protected override void OnSubmenuShown()
    {
        _screenTween?.Kill();
        _screenTween = CreateTween();
        _screenTween.TweenProperty(_screenContents, "modulate:a", 1f, 0.25)
            .From(0f);
    }

    protected override void OnSubmenuHidden()
    {
        _screenTween?.Kill();
        ClearPotions();
    }

    private void LoadPotions()
    {
        _common.LoadPotions(PotionRarity.Common, new LocString("potion_lab", "COMMON"));
        _uncommon.LoadPotions(PotionRarity.Uncommon, new LocString("potion_lab", "UNCOMMON"));
        _rare.LoadPotions(PotionRarity.Rare, new LocString("potion_lab", "RARE"));
        _special.LoadPotions(PotionRarity.Event, new LocString("potion_lab", "SPECIAL"));
        _special.AddPotions(PotionRarity.Token);
    }

    private void ClearPotions()
    {
        _common.ClearPotions();
        _uncommon.ClearPotions();
        _rare.ClearPotions();
        _special.ClearPotions();
    }

    public override void OnFocusScreen()
    {
        _common.OnFocus();
    }
}
