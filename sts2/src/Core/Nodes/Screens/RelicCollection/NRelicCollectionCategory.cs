using System.Collections.Generic;
using System.Linq;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.Nodes.Screens.RelicCollection;

public partial class NRelicCollectionCategory : VBoxContainer
{
    private NRelicCollection _collection = default!;
    private MegaRichTextLabel _headerLabel = default!;
    private Control _relicsContainer = default!;

    public override void _Ready()
    {
        _headerLabel = GetNode<MegaRichTextLabel>("Header");
        _relicsContainer = GetNode<Control>("%RelicsContainer");
    }

    public void LoadRelics(RelicRarity relicRarity, NRelicCollection collection)
    {
        _collection = collection;

        foreach (Node child in _relicsContainer.GetChildren())
        {
            child.QueueFreeSafely();
        }

        // Must use GetByIdOrNull because deprecated potions can stick around in the ProgressSave
        HashSet<RelicModel> seenRelics = SaveManager.Instance.ProgressSave.DiscoveredRelics
            .Select(ModelDb.GetByIdOrNull<RelicModel>)
            .OfType<RelicModel>()
            .ToHashSet();

        IEnumerable<RelicModel> modelIds = ModelDb.Relics.Where(relic => relic.Rarity == relicRarity);
        _collection.AddRelics(modelIds);

        // Create and hook up the Relic Buttons
        foreach (RelicModel relic in modelIds)
        {
            NRelicCollectionEntry relicNode = NRelicCollectionEntry.Create(relic, seenRelics.Contains(relic));
            _relicsContainer.AddChildSafely(relicNode);
            relicNode.Connect(NClickableControl.SignalName.Released, Callable.From<NRelicCollectionEntry>(OnRelicEntryPressed));
        }

        LocString rarityString = new("gameplay_ui", $"RELIC_RARITY.{relicRarity.ToString().ToUpper()}");
        _headerLabel.Text = $"[gold][b]{rarityString.GetFormattedText()}s:[/b][/gold]"; // TODO: Update loc, this is evil code
    }

    public void ClearRelics()
    {
        foreach (Node child in _relicsContainer.GetChildren())
        {
            child.QueueFreeSafely();
        }
    }

    private void OnRelicEntryPressed(NRelicCollectionEntry obj)
    {
        NGame.Instance!.InspectRelicScreen.Open(_collection.Relics, obj.relic);
    }

    public void OnFocus()
    {
        _relicsContainer.GetChild<Control>(0).TryGrabFocus();
    }
}
