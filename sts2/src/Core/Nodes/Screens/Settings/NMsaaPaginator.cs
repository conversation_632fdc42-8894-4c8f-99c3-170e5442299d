using Godot;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.Nodes.Screens.Settings;

/// <summary>
/// Sets your 2D MSAA setting. Affects how "jagged" sprites and other 2D things look. Notably when rotated.
/// </summary>
public partial class NMsaaPaginator : NPaginator
{
    public override void _Ready()
    {
        ConnectSignals();

        _options.Add("0");
        _options.Add("2");
        _options.Add("4");
        _options.Add("8");

        // Sets the index and label of our MSAA paginator based on the player's setting.
        // If invalid, sets it to None
        int settingsSaveIndex = _options.IndexOf(SaveManager.Instance.SettingsSave.Msaa.ToString());
        _currentIndex = settingsSaveIndex != -1 ? settingsSaveIndex : 3;
        _label.Text = GetMsaaLabel(int.Parse(_options[_currentIndex]));
    }

    protected override void OnIndexChanged(int index)
    {
        _currentIndex = index;
        _label.Text = GetMsaaLabel(int.Parse(_options[index]));
        SaveManager.Instance.SettingsSave.Msaa = int.Parse(_options[index]);
        Log.Info($"MSAA: {_label.Text}");
        RenderingServer.ViewportSetMsaa2D(GetViewport().GetViewportRid(), GetMsaa(SaveManager.Instance.SettingsSave.Msaa));
    }

    private string GetMsaaLabel(int msaaAmount)
    {
        return msaaAmount switch
        {
            2 => "2x",
            4 => "4x",
            8 => "8x",
            _ => new LocString("settings_ui", "MSAA_NONE").GetFormattedText()
        };
    }

    private RenderingServer.ViewportMsaa GetMsaa(int index)
    {
        return index switch
        {
            2 => RenderingServer.ViewportMsaa.Msaa2X,
            4 => RenderingServer.ViewportMsaa.Msaa4X,
            8 => RenderingServer.ViewportMsaa.Msaa8X,
            _ => RenderingServer.ViewportMsaa.Disabled
        };
    }
}
