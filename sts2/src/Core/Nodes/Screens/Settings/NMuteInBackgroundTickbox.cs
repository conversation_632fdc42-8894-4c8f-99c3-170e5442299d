using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.Nodes.Screens.Settings;

public partial class NMuteInBackgroundTickbox : NTickbox
{
    private NSettingsScreen _settingsScreen = default!;

    public override void _Ready()
    {
        ConnectSignals();

        _settingsScreen = this.GetAncestorOfType<NSettingsScreen>()!;
        IsTicked = SaveManager.Instance.SettingsSave.MuteInBackground;
    }

    protected override void OnTick()
    {
        _settingsScreen.ShowToast(new LocString("settings_ui", "TOAST_MUTE_IN_BACKGROUND_ON"));
        SaveManager.Instance.SettingsSave.MuteInBackground = true;
    }

    protected override void OnUntick()
    {
        _settingsScreen.ShowToast(new LocString("settings_ui", "TOAST_MUTE_IN_BACKGROUND_OFF"));
        SaveManager.Instance.SettingsSave.MuteInBackground = false;
    }
}
