using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.Nodes.Screens.Settings;

public partial class NRunTimerTickbox : NTickbox
{
    private NSettingsScreen _settingsScreen = default!;

    public override void _Ready()
    {
        ConnectSignals();

        _settingsScreen = this.GetAncestorOfType<NSettingsScreen>()!;
        IsTicked = SaveManager.Instance.SettingsSave.ShowRunTimer;
    }

    protected override void OnTick()
    {
        _settingsScreen.ShowToast(new LocString("settings_ui", "TOAST_RUN_TIMER_ON"));
        SaveManager.Instance.SettingsSave.ShowRunTimer = true;
        TryRefreshRunTimer();
    }

    protected override void OnUntick()
    {
        _settingsScreen.ShowToast(new LocString("settings_ui", "TOAST_RUN_TIMER_OFF"));
        SaveManager.Instance.SettingsSave.ShowRunTimer = false;
        TryRefreshRunTimer();
    }

    private static void TryRefreshRunTimer()
    {
        // If we are in run, show the timer
        if (NClimb.Instance != null)
        {
            NClimb.Instance.GlobalUi.TopBar.Timer.RefreshVisibility();
        }
    }
}
