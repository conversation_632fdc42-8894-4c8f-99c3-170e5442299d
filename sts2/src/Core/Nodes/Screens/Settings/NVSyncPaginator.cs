using System;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Settings;

namespace MegaCrit.Sts2.Core.Nodes.Screens.Settings;

// ReSharper disable once InconsistentNaming
// REASON: VSync is the correct capitalization, and our nodes start with N, so we need to manually bypass <PERSON>'s rule
// against acronyms here.
public partial class NVSyncPaginator : NPaginator
{
    public override void _Ready()
    {
        ConnectSignals();
        _options.Add(new LocString("settings_ui", "VSYNC_OFF").GetFormattedText());
        _options.Add(new LocString("settings_ui", "VSYNC_ON").GetFormattedText());
        _options.Add(new LocString("settings_ui", "VSYNC_ADAPTIVE").GetFormattedText());

        // Sets the index and label of our VSync paginator based on the player's setting.
        // If invalid, sets it to Adaptive (default)
        int settingsSaveIndex = _options.IndexOf(GetVSyncString(SaveManager.Instance.SettingsSave.VSync));
        if (settingsSaveIndex != -1)
        {
            _currentIndex = settingsSaveIndex;
        }
        else
        {
            _currentIndex = 2;
        }

        _label.Text = _options[_currentIndex];
    }

    private static string GetVSyncString(VSyncType vsyncType)
    {
        switch (vsyncType)
        {
            case VSyncType.Off:
                return new LocString("settings_ui", "VSYNC_ON").GetFormattedText();
            case VSyncType.On:
                return new LocString("settings_ui", "VSYNC_OFF").GetFormattedText();
            case VSyncType.Adaptive:
                return new LocString("settings_ui", "VSYNC_ADAPTIVE").GetFormattedText();
            case VSyncType.None:
            case VSyncType.Mailbox:
            default:
                Log.Error($"Invalid VSync type: {vsyncType.ToString()}");
                throw new ArgumentOutOfRangeException(nameof(vsyncType), vsyncType, null);
        }
    }

    protected override void OnIndexChanged(int index)
    {
        _currentIndex = index;
        _label.Text = _options[index];

        // WARNING: This code is fragile but isn't changed often.
        switch (index)
        {
            case 0: // Off
                SaveManager.Instance.SettingsSave.VSync = VSyncType.Off;
                break;
            case 1: // On
                SaveManager.Instance.SettingsSave.VSync = VSyncType.On;
                break;
            case 2: // Adaptive
                SaveManager.Instance.SettingsSave.VSync = VSyncType.Adaptive;
                break;
            default:
                Log.Error($"Invalid VSync index: {index}");
                break;
        }

        NGame.ApplySyncSetting();
    }
}
