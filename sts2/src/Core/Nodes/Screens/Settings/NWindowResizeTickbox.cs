using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.Nodes.Screens.Settings;

public partial class NWindowResizeTickbox : NTickbox
{
    private NSettingsScreen _settingsScreen = default!;

    public override void _Ready()
    {
        ConnectSignals();

        _settingsScreen = this.GetAncestorOfType<NSettingsScreen>()!;
        IsTicked = SaveManager.Instance.SettingsSave.ResizeWindows;
        GetViewport().GetWindow().Unresizable = !IsTicked;
    }

    protected override void OnTick()
    {
        _settingsScreen.ShowToast(new LocString("settings_ui", "TOAST_WINDOW_RESIZING_ON"));
        SaveManager.Instance.SettingsSave.ResizeWindows = true;
        GetViewport().GetWindow().Unresizable = !IsTicked;
    }

    protected override void OnUntick()
    {
        _settingsScreen.ShowToast(new LocString("settings_ui", "TOAST_WINDOW_RESIZING_OFF"));
        SaveManager.Instance.SettingsSave.ResizeWindows = false;
        GetViewport().GetWindow().Unresizable = !IsTicked;
    }
}
