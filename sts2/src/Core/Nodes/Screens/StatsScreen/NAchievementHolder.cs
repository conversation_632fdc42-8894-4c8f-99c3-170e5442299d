using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Godot;
using MegaCrit.Sts2.Core.Achievements;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.HoverTips;
using MegaCrit.Sts2.Core.Platform;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Screens.StatsScreen;

public partial class NAchievementHolder : Control
{
    private const string _scenePath = "screens/stats_screen/achievement_holder";

    private TextureRect _textureRect = default!;
    public Control Hitbox { get; private set; } = default!;

    private Achievement _achievement;
    private Tween? _tween;

    public static IEnumerable<string> AssetPaths => new[]
    {
        SceneHelper.GetScenePath(_scenePath)
    }.Concat(Enum.GetValues<Achievement>().SelectMany(GetAchievementAssetPaths));

    private static IEnumerable<string> GetAchievementAssetPaths(Achievement achievement)
    {
        string achievementId = StringHelper.SnakeCase(achievement.ToString()).ToLower();
        return
        [
            ImageHelper.GetImagePath($"packed/achievements/locked/{achievementId}.png"),
            ImageHelper.GetImagePath($"packed/achievements/unlocked/{achievementId}.png")
        ];
    }

    public static NAchievementHolder? Create(Achievement achievement)
    {
        if (TestMode.IsOn) return null;
        NAchievementHolder holder = PreloadManager.Cache.GetScene(SceneHelper.GetScenePath(_scenePath)).Instantiate<NAchievementHolder>();
        holder._achievement = achievement;
        return holder;
    }

    public override void _Ready()
    {
        _textureRect = GetNode<TextureRect>("TextureRect");
        Hitbox = GetNode<Control>("Hitbox");

        RefreshUnlocked();

        Hitbox.Connect(Control.SignalName.MouseEntered, Callable.From(OnFocus));
        Hitbox.Connect(Control.SignalName.MouseExited, Callable.From(OnUnfocus));
        Hitbox.Connect(Control.SignalName.FocusEntered, Callable.From(OnFocus));
        Hitbox.Connect(Control.SignalName.FocusExited, Callable.From(OnUnfocus));
    }

    public void RefreshUnlocked()
    {
        _textureRect.Texture = GetImage();
    }

    private void OnFocus()
    {
        string achievementId = StringHelper.SnakeCase(_achievement.ToString()).ToUpper();

        AchievementMetadata? metadata = SaveManager.Instance.ProgressSave.AchievementMetadata.FirstOrDefault(a => a.achievement == _achievement);
        string dateString = "???";

        if (metadata != null)
        {
            DateTimeFormatInfo dateInfo = LocManager.Instance.CultureInfo.DateTimeFormat;
            DateTimeOffset dateTimeOffset = DateTimeOffset.FromUnixTimeSeconds(metadata.unlockTime);
            DateTime dateTime = TimeZoneInfo.ConvertTimeFromUtc(dateTimeOffset.UtcDateTime, TimeZoneInfo.Local);

            // Separate components for formatting
            string dayOfWeek = dateTime.ToString("dddd", dateInfo); // Full day name (e.g., "Friday")
            string calendarDate = dateTime.ToString("MMMM d, yyyy", dateInfo); // "February 16, 2024"
            string time = dateTime.ToString("h:mm:ss tt", dateInfo); // "9:21:19 PM"

            dateString = $"{dayOfWeek}, {calendarDate} {time}";
        }

        LocString title = new("achievements", $"{achievementId}.title");
        LocString description = new("achievements", $"{achievementId}.description");
        LocString descriptionWrapper = new("achievements", "DESCRIPTION_WITH_UNLOCK_TIME");
        descriptionWrapper.Add("Description", description);
        descriptionWrapper.Add("UnlockTime", dateString);

        NHoverTipSet tip = NHoverTipSet.CreateAndShow(this, new HoverTip(title, descriptionWrapper));
        tip.GlobalPosition = GlobalPosition + new Vector2(Size.X * 1.1f, 0);
        tip.SetFollowOwner();

        Scale = Vector2.One * 1.1f;
    }

    private void OnUnfocus()
    {
        NHoverTipSet.Remove(this);

        _tween?.Kill();
        _tween = CreateTween();
        _tween.TweenProperty(this, "scale", Vector2.One, 0.25f).SetTrans(Tween.TransitionType.Expo).SetEase(Tween.EaseType.Out);
    }

    private Texture2D GetImage()
    {
        bool isUnlocked = AchievementsUtil.IsUnlocked(_achievement);
        string unlockedString = isUnlocked ? "unlocked" : "locked";
        string achievementId = StringHelper.SnakeCase(_achievement.ToString()).ToLower();
        string path = ImageHelper.GetImagePath($"packed/achievements/{unlockedString}/{achievementId}.png");
        return PreloadManager.Cache.GetTexture2D(path);
    }
}
