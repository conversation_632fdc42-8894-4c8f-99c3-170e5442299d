using Godot;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Screens.MainMenu;
using MegaCrit.Sts2.Core.Nodes.Screens.Settings;

namespace MegaCrit.Sts2.Core.Nodes.Screens.StatsScreen;

public partial class NStatsScreen : NSubmenu
{
    private NStatsTabManager _statsTabManager = default!;
    private NSettingsTab _statsTab = default!;
    private NSettingsTab _achievementsTab = default!;
    private NGeneralStatsGrid _statsGrid = default!;
    private NAchievementsGrid _achievementsGrid = default!;

    private Tween? _screenTween;

    public override void _Ready()
    {
        ConnectSignals();

        // Setup tabs
        _statsTab = GetNode<NSettingsTab>("%StatsTab");
        _statsTab.SetLabel(new LocString("stats_screen", "TAB_STATS.header").GetFormattedText());
        _achievementsTab = GetNode<NSettingsTab>("%Achievements");
        _achievementsTab.SetLabel(new LocString("stats_screen", "TAB_ACHIEVEMENT.header").GetFormattedText());
        _statsTab.Connect(NClickableControl.SignalName.Released, Callable.From<NClickableControl>(_ => OpenStatsMenu()));
        _achievementsTab.Connect(NClickableControl.SignalName.Released, Callable.From<NClickableControl>(_ => OpenAchievementsMenu()));
        _statsTabManager = GetNode<NStatsTabManager>("%Tabs");
        _statsGrid = GetNode<NGeneralStatsGrid>("%StatsGrid");
        _achievementsGrid = GetNode<NAchievementsGrid>("%AchievementsGrid");
    }

    public override void OnSubmenuOpened()
    {
        _screenTween?.Kill();
        _screenTween = CreateTween();
        _screenTween.TweenProperty(this, "modulate:a", 1f, 0.4)
            .From(0f);

        Visible = true;
        OpenStatsMenu();
        _statsTabManager.ResetTabs();
    }

    public override void OnFocusScreen()
    {

        if (_achievementsGrid.Visible)
        {
            _achievementsGrid.OnFocus();
        }
        else
        {
            _statsGrid.OnFocus();
        }

    }

    private void OpenStatsMenu()
    {
        _statsGrid.Visible = true;
        _achievementsGrid.Visible = false;
        _statsGrid.LoadStats();
        _statsGrid.OnFocus();
    }

    private void OpenAchievementsMenu()
    {
        _statsGrid.Visible = false;
        _achievementsGrid.Visible = true;
        _achievementsGrid.OnFocus();
    }
}
