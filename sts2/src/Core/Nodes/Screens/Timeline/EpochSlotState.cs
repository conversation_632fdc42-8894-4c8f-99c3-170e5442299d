namespace MegaCrit.Sts2.Core.Nodes.Screens.Timeline;

public enum EpochSlotState
{
    None = 0,

    /// <summary>
    /// The Epoch is revealed and the player has inspected it before.
    /// </summary>
    Complete = 1,

    /// <summary>
    /// The Epoch is obtained so the player is able to click on something in the Timeline to reveal/complete it!
    /// </summary>
    Obtained = 2,

    /// <summary>
    /// This Epoch has never been obtained. Used to show empty slots, can be useful to communicate how to obtain the Epoch.
    /// </summary>
    NotObtained = 3
}
