using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Timeline;

namespace MegaCrit.Sts2.Core.Nodes.Screens.Timeline.UnlockScreens;

/// <summary>
/// Unlock screen which appears after you place the first Epoch and you unlock the three Epochs:
/// <PERSON>w, Silent, and Regent Epochs. Used during Timeline Expansion One (slotting Ironclad Enters) and after you slot your fourth Epoch.
/// </summary>
public partial class NUnlockEpochScreen : NUnlockScreen
{
    private IReadOnlyList<EpochModel> _unlockedEpochs = default!;
    private Tween? _cardFlyTween;
    private const double _initDelay = 0.3;
    private RichTextLabel _infoLabel = default!;

    public override void _Ready()
    {
        ConnectSignals();

        _infoLabel = GetNode<RichTextLabel>("%InfoLabel");
        LocString unlockText = new("timeline", "UNLOCK_EPOCHS");
        _infoLabel.Text = $"[center]{unlockText.GetFormattedText()}[/center]";
        _infoLabel.Modulate = StsColors.transparentWhite;
    }

    public override void Open()
    {
        base.Open();

        _cardFlyTween = CreateTween().SetParallel();
        double tweenDelay = _initDelay;
        Vector2 centerPosition = GetNode<Control>("%Center").Position;

        PackedScene epochCardScene = ResourceLoader.Load<PackedScene>("res://scenes/timeline_screen/epoch.tscn");

        // Different behavior for layout based on number of Epochs
        if (_unlockedEpochs.Count == 3)
        {
            for (int i = 0; i < _unlockedEpochs.Count; i++)
            {
                NEpochCard card = epochCardScene.Instantiate<NEpochCard>();
                card.Init(_unlockedEpochs[i]);
                Control slotNode = GetNode<Control>($"Slot{i}");
                slotNode.AddChildSafely(card);
                card.SetToWigglyUnlockPreviewMode();

                _cardFlyTween.TweenProperty(slotNode, "modulate", Colors.White, 1.0)
                    .SetDelay(tweenDelay - _initDelay);
                _cardFlyTween.TweenProperty(slotNode, "position", slotNode.Position, 1.0)
                    .SetEase(Tween.EaseType.Out)
                    .SetTrans(Tween.TransitionType.Back)
                    .SetDelay(tweenDelay);

                slotNode.Modulate = StsColors.transparentBlack;
                slotNode.Position = centerPosition;
                tweenDelay += 0.25;
            }
        }
        else if (_unlockedEpochs.Count == 2)
        {
            for (int i = 0; i < _unlockedEpochs.Count; i++)
            {
                NEpochCard card = epochCardScene.Instantiate<NEpochCard>();
                card.Init(_unlockedEpochs[i]);
                Control slotNode = GetNode<Control>($"Slot{3 + i}");
                slotNode.AddChildSafely(card);
                card.SetToWigglyUnlockPreviewMode();

                _cardFlyTween.TweenProperty(slotNode, "modulate", Colors.White, 1.0)
                    .SetDelay(tweenDelay - _initDelay);
                _cardFlyTween.TweenProperty(slotNode, "position", slotNode.Position, 1.0)
                    .SetEase(Tween.EaseType.Out)
                    .SetTrans(Tween.TransitionType.Back)
                    .SetDelay(tweenDelay);

                slotNode.Modulate = StsColors.transparentBlack;
                slotNode.Position = centerPosition;
                tweenDelay += 0.33;
            }
        }
        else
        {
            Log.Error("Unlocking exactly 1 OR more than 3 Epochs are not supported.");
        }

        _cardFlyTween.TweenProperty(_infoLabel, "modulate", Colors.White, 1.0)
            .SetDelay(0.25);
    }

    public void SetUnlocks(IReadOnlyList<EpochModel> epochs)
    {
        _unlockedEpochs = epochs;
    }
}
