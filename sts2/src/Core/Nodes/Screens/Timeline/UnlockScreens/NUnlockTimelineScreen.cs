using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Timeline;

namespace MegaCrit.Sts2.Core.Nodes.Screens.Timeline.UnlockScreens;

/// <summary>
/// Unlock screen which isn't really a screen but just an animation.
/// Extends UUnlockScreen so it supports queueing it up like the other unlocks on the TimelineScreen.
/// </summary>
public partial class NUnlockTimelineScreen : NUnlockScreen
{
    private List<EpochSlotData> _erasToUnlock = default!;

    /// <summary>
    /// Empty _Ready() function so we don't initialize the Confirm button like other unlock screens.
    /// Better ways to handle but w/e.
    /// </summary>
    public override void _Ready() { }

    /// <summary>
    /// Set which era slots are unlocked when this "screen" is triggered.
    /// </summary>
    /// <param name="eras"></param>
    public void SetUnlocks(List<EpochSlotData> eras)
    {
        _erasToUnlock = eras.OrderBy(a => a.EraPosition).ToList();
    }

    public override void Open()
    {
        base.Open();
        _ = TaskHelper.RunSafely(AnimateExpansion());
    }

    private async Task AnimateExpansion()
    {
        await NTimelineScreen.Instance!.HideBackstopAndShowUi();
        await NTimelineScreen.Instance.AddEpochSlots(_erasToUnlock, true);

        NTimelineScreen.Instance.ShowHeaderAndActionsUi();
        NTimelineScreen.Instance.EnableInput();
        NTimelineScreen.Instance.CheckScreenDraggabilityAndScaleTheTimeline();

        _ = TaskHelper.RunSafely(Close());
    }
}
