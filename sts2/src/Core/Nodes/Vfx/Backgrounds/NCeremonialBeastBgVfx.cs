using System.Linq;
using Godot;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Afflictions;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Nodes.Audio;

namespace MegaCrit.Sts2.Core.Nodes.Vfx.Backgrounds;

public partial class NCeremonialBeastBgVfx : SpineSprite
{
    private bool _isGlowOn;
    private bool _areSkullsOn;

    public override void _Ready()
    {
        Visible = false;
    }

    public override void _EnterTree()
    {
        CombatManager.Instance.StateTracker.CombatStateChanged += UpdateState;
    }

    public override void _ExitTree()
    {
        CombatManager.Instance.StateTracker.CombatStateChanged -= UpdateState;
    }

    private void UpdateState(CombatState combatState)
    {
        UpdateRingingSfx(combatState);
        UpdateVfxAndMusic(combatState);
    }

    private void UpdateRingingSfx(CombatState combatState)
    {
        bool handHasRingingCards = PileType.Hand.GetPile(LocalContext.GetMe(combatState)!)
            .Cards
            .Any(c => c.Affliction is Ringing);

        NClimbMusicController.Instance?.UpdateMusicParameter("ringing", handHasRingingCards ? 1 : 0);
    }

    private void UpdateVfxAndMusic(CombatState combatState)
    {
        Creature? creature = combatState.Creatures.FirstOrDefault(c => c.Monster is CeremonialBeast);
        if (creature == null) return;

        if (creature.CurrentHp > creature.MaxHp * 0.66f)
        {
            Visible = false;
        }
        else
        {
            Visible = true;

            if (creature.CurrentHp > creature.MaxHp * 0.33f)
            {
                NClimbMusicController.Instance?.UpdateMusicParameter("ceremonial_beast_progress", 1);
                PlayGlow();
            }
            else if (creature.IsAlive)
            {
                NClimbMusicController.Instance?.UpdateMusicParameter("ceremonial_beast_progress", 1);
                PlaySkulls();
            }
            else
            {
                NClimbMusicController.Instance?.UpdateMusicParameter("ceremonial_beast_progress", 5);
                PlayFlowers();
            }
        }
    }

    private void PlayGlow()
    {
        if (_isGlowOn) return;

        _isGlowOn = true;
        SpineAnimationState state = GetAnimationState();
        state.SetAnimation("glow_spawn");
        state.AddAnimation("glow_idle");
    }

    private void PlaySkulls()
    {
        if (_areSkullsOn) return;

        _areSkullsOn = true;
        SpineAnimationState state = GetAnimationState();
        state.SetAnimation("skulls_spawn");
        state.AddAnimation("glow_and_skulls_idle");
    }

    private void PlayFlowers()
    {
        SpineAnimationState state = GetAnimationState();
        state.SetAnimation("glow_and_skulls_idle");
        state.AddAnimation("plants_spawn", 4.5f, false);
    }
}
