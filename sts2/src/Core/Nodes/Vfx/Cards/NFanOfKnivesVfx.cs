using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx.Cards;

public partial class NFanOfKnivesVfx : Node2D
{
    private static readonly string _scenePath = SceneHelper.GetScenePath("vfx/fan_of_knives_vfx");
    public static IEnumerable<string> AssetPaths => [_scenePath];

    private readonly List<Node2D> _shivs = [];
    private Node2D _shiv1 = default!;
    private Node2D _shiv2 = default!;
    private Node2D _shiv3 = default!;
    private Node2D _shiv4 = default!;
    private Node2D _shiv5 = default!;
    private Node2D _shiv6 = default!;
    private Node2D _shiv7 = default!;
    private Node2D _shiv8 = default!;
    private Node2D _shiv9 = default!;

    private Vector2 _spawnPosition;
    private const double _fanDuration = 0.8;


    public static NFanOfKnivesVfx? Create(Creature target)
    {
        if (TestMode.IsOn) return null;

        NFanOfKnivesVfx vfx = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NFanOfKnivesVfx>();
        vfx._spawnPosition = NCombatRoom.Instance!.GetCreatureNode(target)!.VfxSpawnPosition;

        return vfx;
    }

    public override void _Ready()
    {
        _shiv1 = GetNode<Node2D>("ShivFanParticle1");
        _shiv2 = GetNode<Node2D>("ShivFanParticle2");
        _shiv3 = GetNode<Node2D>("ShivFanParticle3");
        _shiv4 = GetNode<Node2D>("ShivFanParticle4");
        _shiv5 = GetNode<Node2D>("ShivFanParticle5");
        _shiv6 = GetNode<Node2D>("ShivFanParticle6");
        _shiv7 = GetNode<Node2D>("ShivFanParticle7");
        _shiv8 = GetNode<Node2D>("ShivFanParticle8");
        _shiv9 = GetNode<Node2D>("ShivFanParticle9");

        _shivs.Add(_shiv1);
        _shivs.Add(_shiv2);
        _shivs.Add(_shiv3);
        _shivs.Add(_shiv4);
        _shivs.Add(_shiv5);
        _shivs.Add(_shiv6);
        _shivs.Add(_shiv7);
        _shivs.Add(_shiv8);
        _shivs.Add(_shiv9);

        foreach (Node2D shiv in _shivs)
        {
            shiv.Scale = Vector2.One * Rng.Chaotic.NextFloat(0.98f, 1.02f);
            shiv.GlobalPosition = _spawnPosition;
        }

        TaskHelper.RunSafely(Animate());
    }

    private async Task Animate()
    {
        // Fade in + offset adjust + fade out
        Tween spawnTween = CreateTween().SetParallel();

        foreach (Node2D shiv in _shivs)
        {
            float duration = Rng.Chaotic.NextFloat(0.4f, 0.8f);
            spawnTween.TweenProperty(shiv, "offset:y", -180f, duration)
                .From(0f)
                .SetEase(Tween.EaseType.Out)
                .SetTrans(Tween.TransitionType.Back);
            spawnTween.TweenProperty(shiv, "modulate", Colors.White, duration)
                .From(StsColors.transparentBlack);
            spawnTween.TweenProperty(shiv.GetNode<Node2D>("Shadow"), "offset:y", -180f, duration)
                .From(0f)
                .SetEase(Tween.EaseType.Out)
                .SetTrans(Tween.TransitionType.Back);
        }

        spawnTween.Chain();

        foreach (Node2D shiv in _shivs)
        {
            spawnTween.TweenProperty(shiv, "modulate", StsColors.transparentWhite, 0.4)
                .SetDelay(Rng.Chaotic.NextDouble(0.25, 0.5));
        }

        // Fanning!
        Tween fanTween = CreateTween().SetParallel();
        fanTween.TweenInterval(0.4f);
        fanTween.Chain();

        fanTween.TweenProperty(_shiv1, "rotation", -100f * MathHelper.degToRad, _fanDuration)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Back);
        fanTween.TweenProperty(_shiv2, "rotation", -75f * MathHelper.degToRad, _fanDuration)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Back);
        fanTween.TweenProperty(_shiv3, "rotation", -50f * MathHelper.degToRad, _fanDuration)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Back);
        fanTween.TweenProperty(_shiv4, "rotation", -25f * MathHelper.degToRad, _fanDuration)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Back);
        fanTween.TweenProperty(_shiv6, "rotation", 25f * MathHelper.degToRad, _fanDuration)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Back);
        fanTween.TweenProperty(_shiv7, "rotation", 50f * MathHelper.degToRad, _fanDuration)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Back);
        fanTween.TweenProperty(_shiv8, "rotation", 75f * MathHelper.degToRad, _fanDuration)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Back);
        fanTween.TweenProperty(_shiv9, "rotation", 100f * MathHelper.degToRad, _fanDuration)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Back);

        await ToSignal(spawnTween, Tween.SignalName.Finished);
        this.QueueFreeSafely();
    }
}
