using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx.Cards;

public partial class NSpikeSplashVfx : Node2D
{
    private static readonly string _scenePath = SceneHelper.GetScenePath("vfx/spike_splash_vfx");

    private float _duration = 1f;
    private int _spikeAmount = 6; // It's x4! We create a group of 6 on the left, right, foreground, and background
    private Vector2 _spawnPosition;
    private VfxColor _vfxColor;

    public static NSpikeSplashVfx? Create(Creature target, VfxColor vfxColor = VfxColor.Red)
    {
        if (TestMode.IsOn) return null;

        NSpikeSplashVfx vfx = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NSpikeSplashVfx>();
        vfx._spawnPosition = NCombatRoom.Instance!.GetCreatureNode(target)!.GetBottomOfHitbox();
        vfx._vfxColor = vfxColor;

        return vfx;
    }

    public override void _Ready()
    {
        for (int i = 0; i < _spikeAmount; i++)
        {
            NFgGroundSpikeVfx? vfx = NFgGroundSpikeVfx.Create(_spawnPosition, true, _vfxColor);
            NCombatRoom.Instance!.CombatVfxContainer.AddChildSafely(vfx);
            vfx = NFgGroundSpikeVfx.Create(_spawnPosition, false, _vfxColor);
            NCombatRoom.Instance.CombatVfxContainer.AddChildSafely(vfx);
        }

        for (int i = 0; i < _spikeAmount; i++)
        {
            NBgGroundSpikeVfx? vfx = NBgGroundSpikeVfx.Create(_spawnPosition, true, _vfxColor);
            NCombatRoom.Instance!.BackCombatVfxContainer.AddChildSafely(vfx);
            vfx = NBgGroundSpikeVfx.Create(_spawnPosition, false, _vfxColor);
            NCombatRoom.Instance.BackCombatVfxContainer.AddChildSafely(vfx);
        }

        TaskHelper.RunSafely(SelfDestruct());
    }

    private async Task SelfDestruct()
    {
        await Task.Delay(2000);
        QueueFree();
    }
}
