using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx.Events;

public partial class NDenseVegetationVfx : Node2D
{
    private static string ScenePath => SceneHelper.GetScenePath("vfx/whole_screen/dense_vegetation_vfx");

    public static NDenseVegetationVfx? Create()
    {
        if (TestMode.IsOn) return null;

        return PreloadManager.Cache.GetScene(ScenePath).Instantiate<NDenseVegetationVfx>();
    }

    public override void _Ready()
    {
        Position = new Vector2(152f, 16f);
    }
}
