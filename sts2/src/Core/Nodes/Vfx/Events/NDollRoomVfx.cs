using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx.Events;

public partial class NDollRoomVfx : Node2D
{
    private static string ScenePath => SceneHelper.GetScenePath("vfx/whole_screen/doll_room_vfx");

    public static NDollRoomVfx? Create()
    {
        if (TestMode.IsOn) return null;

        return PreloadManager.Cache.GetScene(ScenePath).Instantiate<NDollRoomVfx>();
    }

    public override void _Ready()
    {
        Position = new Vector2(268f, 49f);
    }
}
