using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx.Events;

public partial class NInfestedAutomatonVfx : Node2D
{
    private static string ScenePath => SceneHelper.GetScenePath("vfx/whole_screen/infested_automaton_vfx");

    public static NInfestedAutomatonVfx? Create()
    {
        if (TestMode.IsOn) return null;

        return PreloadManager.Cache.GetScene(ScenePath).Instantiate<NInfestedAutomatonVfx>();
    }

    public override void _Ready()
    {
        Position = new Vector2(292f, 68f);
    }
}
