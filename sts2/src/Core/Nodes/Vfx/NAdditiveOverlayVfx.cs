using System;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

/// <summary>
/// A simple vfx which renders a colorRect to tint the entire screen using an additive blend.
/// </summary>
public partial class NAdditiveOverlayVfx : ColorRect
{
    private static readonly string _scenePath = SceneHelper.GetScenePath("vfx/additive_overlay_vfx");
    private VfxColor _vfxColor;

    public static NAdditiveOverlayVfx? Create(VfxColor vfxColor = VfxColor.Red)
    {
        if (TestMode.IsOn) return null;

        NAdditiveOverlayVfx? vfx = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NAdditiveOverlayVfx>();
        vfx._vfxColor = vfxColor;
        return vfx;
    }

    public override void _Ready()
    {
        SetVfxColor();

        Tween tween = CreateTween();
        tween.TweenProperty(this, "modulate:a", 0.1f, 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        tween.TweenInterval(0.5f);
        tween.TweenProperty(this, "modulate:a", 0f, 0.5f);
        tween.Finished += OnTweenFinished;
    }

    private void SetVfxColor()
    {
        switch (_vfxColor)
        {
            case VfxColor.Red:
                // Default, do nothing
                break;
            case VfxColor.Green:
                Modulate = new Color("00ff1500");
                break;
            case VfxColor.Blue:
                Modulate = new Color("001aff00");
                break;
            case VfxColor.Purple:
                Modulate = new Color("b300ff00");
                break;
            case VfxColor.White:
                Modulate = new Color("ffffff00");
                break;
            case VfxColor.Cyan:
                Modulate = new Color("00fffb00");
                break;
            case VfxColor.Gold:
                Modulate = new Color("b17e0000");
                break;
            case VfxColor.Black:
                break;
            default:
                throw new ArgumentOutOfRangeException();
        }
    }

    private void OnTweenFinished()
    {
        QueueFree();
    }
}
