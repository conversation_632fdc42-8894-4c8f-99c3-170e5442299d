using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Cards;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NCardEnchantVfx : Node2D
{
    private static string ScenePath => SceneHelper.GetScenePath("vfx/vfx_card_enchant");
    public static IEnumerable<string> AssetPaths => [ScenePath];

    private const string _enchantSfx = "card_enchant.ogg";

    [Export]
    public Curve? EmbossCurve { get; set; }

    private CardModel _cardModel = default!;
    private NCard _cardNode = default!;
    private GpuParticles2D _enchantmentSparkles = default!;
    private TextureRect _enchantmentTextureRect = default!;
    private TextureRect _enchantmentIcon = default!;
    private Label _enchantmentLabel = default!;

    public static NCardEnchantVfx? Create(CardModel card)
    {
        if (TestMode.IsOn) return null;
        if (!LocalContext.IsMine(card)) return null;

        NCardEnchantVfx node = PreloadManager.Cache.GetScene(ScenePath).Instantiate<NCardEnchantVfx>();
        node._cardModel = card;

        return node;
    }

    public override void _Ready()
    {
        _cardNode = GetNode<NCard>("%Card");
        _enchantmentSparkles = GetNode<GpuParticles2D>("%EnchantmentAppearSparkles");
        _enchantmentTextureRect = GetNode<TextureRect>("%EnchantmentViewportTextureRect");
        _enchantmentIcon = GetNode<TextureRect>("%EnchantmentInViewport/Icon");
        _enchantmentLabel = GetNode<Label>("%EnchantmentInViewport/Label");

        _enchantmentIcon.Texture = _cardModel.Enchantment!.Icon;
        _enchantmentLabel.Text = _cardModel.Enchantment.DisplayAmount.ToString();
        _enchantmentLabel.Visible = _cardModel.Enchantment.ShowAmount;

        _cardNode.Model = _cardModel;
        _cardNode.UpdateVisuals(PileType.None);
        _cardNode.GetNode<Control>("%Enchantment").Visible = false;

        TaskHelper.RunSafely(PlayAnimation());
    }

    private async Task PlayAnimation()
    {
        ((ShaderMaterial)_enchantmentTextureRect.Material).SetShaderParameter("progress", 0f);
        Tween tween = CreateTween();

        tween.TweenProperty(_enchantmentTextureRect, "material:shader_parameter/progress", 1f, 1f).SetEase(Tween.EaseType.InOut).SetTrans(Tween.TransitionType.Quad);
        tween.Parallel().TweenCallback(Callable.From(() => _enchantmentSparkles.Emitting = true)).SetDelay(0.2f);
        tween.Parallel().TweenProperty(_enchantmentSparkles, "position:x", _enchantmentSparkles.Position.X + 72f, 0.4f).SetDelay(0.2f);

        await ToSignal(tween, Tween.SignalName.Finished);
        await Cmd.Wait(1f);

        CardModel card = _cardNode.Model!;

        if (_cardNode.IsInsideTree() && card.Pile == null)
        {
            tween = CreateTween();
            tween.TweenProperty(this, "scale", Vector2.Zero, 0.15f);
            await ToSignal(tween, Tween.SignalName.Finished);
        }
        else if (_cardNode.IsInsideTree())
        {
            Vector2 targetPos = card.Pile!.Type.GetTargetPosition(_cardNode);

            NCardFlyVfx fly = NCardFlyVfx.Create(_cardNode, targetPos, false, card.Owner.Character.TrailPath)!;

            // This renders the card behind the "View Deck" button but above the TopBar asset when adding cards to deck.
            NClimb.Instance?.GlobalUi.TopBar.TrailContainer.AddChildSafely(fly);

            if (fly.SwooshAwayCompletion != null)
            {
                await fly.SwooshAwayCompletion.Task;
            }
        }

        this.QueueFreeSafely();
    }
}
