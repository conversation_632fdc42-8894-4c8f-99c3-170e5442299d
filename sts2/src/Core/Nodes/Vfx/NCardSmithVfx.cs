using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using HarmonyLib;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Cards;
using MegaCrit.Sts2.Core.Nodes.Vfx.Utilities;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NCardSmithVfx : Node2D
{
    private static string ScenePath => SceneHelper.GetScenePath("vfx/vfx_card_smith");
    public static IEnumerable<string> AssetPaths => [ScenePath];

    public const string smithSfx = "card_smith.ogg";

    private readonly List<CardModel> _cards = [];
    private NCard? _cardNode;
    private Control _cardContainer = default!;

    public float SfxVolume { get; set; } = 1f;

    public static NCardSmithVfx? Create(IEnumerable<CardModel> cards)
    {
        NCardSmithVfx? node = Create();
        if (node == null) return null;
        node._cards.AddRange(cards);

        return node;
    }

    public static NCardSmithVfx? Create(NCard card)
    {
        NCardSmithVfx? node = Create();
        if (node == null) return null;

        node._cardNode = card;

        return node;
    }

    public static NCardSmithVfx? Create()
    {
        if (TestMode.IsOn) return null;
        return PreloadManager.Cache.GetScene(ScenePath).Instantiate<NCardSmithVfx>();
    }

    public override void _Ready()
    {
        if (_cardNode != null) // this probably means the card is just in your hand
        {
            GlobalPosition = _cardNode.GlobalPosition;
            GlobalScale = _cardNode.Scale;
            TaskHelper.RunSafely(PlayAnimation());
        }
        else if (_cards.Count > 0)
        {
            TaskHelper.RunSafely(PlayAnimation(_cards));
        }
        else
        {
            TaskHelper.RunSafely(PlayAnimation());
        }
    }

    private async Task PlayAnimation()
    {
        NDebugAudioManager.Instance?.Play(smithSfx, SfxVolume, PitchVariance.Small);

        Tween tween = CreateTween();
        tween.Parallel().TweenCallback(Callable.From(() => PlaySubParticles(GetNode<Control>("Spark1"))));
        tween.TweenInterval(0.25f);
        tween.Chain().TweenCallback(Callable.From(() => PlaySubParticles(GetNode<Control>("Spark2"))));
        tween.TweenInterval(0.25f);
        tween.Chain().TweenCallback(Callable.From(() => PlaySubParticles(GetNode<Control>("Spark3"))));

        tween.TweenInterval(0.4f);

        await ToSignal(tween, Tween.SignalName.Finished);
        this.QueueFreeSafely();
    }

    private async Task PlayAnimation(IEnumerable<CardModel> cards)
    {
        Control cardContainer = GetNode<Control>("%CardContainer");
        List<NCard> cardNodes = [];

        foreach (CardModel card in cards)
        {
            NCard cardNode = NCard.Create(card)!;
            cardContainer.AddChildSafely(cardNode);
            cardNode.UpdateVisuals(PileType.None);
            cardNodes.Add(cardNode);
        }

        Tween tween = CreateTween();

        foreach (NCard cardNode in cardNodes)
        {
            tween.Parallel().TweenProperty(cardNode, "scale", Vector2.One * 1f, 0.25f)
                .From(Vector2.Zero)
                .SetEase(Tween.EaseType.Out)
                .SetTrans(Tween.TransitionType.Cubic);
        }

        tween.Chain().TweenCallback(Callable.From(() => { NDebugAudioManager.Instance?.Play(smithSfx, SfxVolume, PitchVariance.Small); }));
        tween.Parallel().TweenCallback(Callable.From(() => PlaySubParticles(GetNode<Control>("Spark1"))));
        tween.Parallel().TweenCallback(Callable.From(() => NGame.Instance?.ScreenShake(ShakeStrength.Weak, ShakeDuration.Short, 180f + Rng.Chaotic.NextFloat(-10f, 10f))));

        foreach (NCard cardNode in cardNodes)
        {
            tween.Parallel().TweenProperty(cardNode, "rotation_degrees", 20, 0.05f).SetTrans(Tween.TransitionType.Elastic).SetEase(Tween.EaseType.Out);
        }

        tween.TweenInterval(0.25f);
        tween.Chain().TweenCallback(Callable.From(() => PlaySubParticles(GetNode<Control>("Spark2"))));
        foreach (NCard cardNode in cardNodes)
        {
            tween.Parallel().TweenProperty(cardNode, "rotation_degrees", -10, 0.05f).SetTrans(Tween.TransitionType.Elastic).SetEase(Tween.EaseType.Out);
        }

        tween.Parallel().TweenCallback(Callable.From(() => NGame.Instance?.ScreenShake(ShakeStrength.Weak, ShakeDuration.Short, 180f + Rng.Chaotic.NextFloat(-10f, 10f))));

        tween.TweenInterval(0.25f);
        tween.Chain().TweenCallback(Callable.From(() => PlaySubParticles(GetNode<Control>("Spark3"))));

        foreach (NCard cardNode in cardNodes)
        {
            tween.Parallel().TweenProperty(cardNode, "rotation_degrees", 5, 0.05f).SetTrans(Tween.TransitionType.Elastic).SetEase(Tween.EaseType.Out);
        }

        tween.Parallel().TweenCallback(Callable.From(() => NGame.Instance?.ScreenShake(ShakeStrength.Weak, ShakeDuration.Short, 180f + Rng.Chaotic.NextFloat(-10f, 10f))));

        tween.TweenInterval(0.4f);

        await ToSignal(tween, Tween.SignalName.Finished);

        if (cardNodes[0].IsInsideTree() && _cards[0].Pile == null) // assumes that all cards here dont have piles
        {
            tween = CreateTween();
            foreach (NCard cardNode in cardNodes)
            {
                tween.SetParallel().TweenProperty(cardNode, "scale", Vector2.Zero, 0.15f);
            }

            await ToSignal(tween, Tween.SignalName.Finished);
        }
        else if (cardNodes[0].IsInsideTree())
        {
            for (int i = 0; i < cardNodes.Count; i++)
            {
                Vector2 targetPos = cardNodes[i].Model!.Pile!.Type.GetTargetPosition(cardNodes[i]);

                // We need to reparent away from the viewport so that it can swoosh on its own
                Vector2 pos = cardNodes[i].GlobalPosition;
                cardNodes[i].Reparent(this);
                cardNodes[i].GlobalPosition = pos;

                NCardFlyVfx fly = NCardFlyVfx.Create(cardNodes[i], targetPos, false, cardNodes[i].Model!.Owner.Character.TrailPath)!;

                // This renders the card behind the "View Deck" button but above the TopBar asset when adding cards to deck.
                NClimb.Instance?.GlobalUi.TopBar.TrailContainer.AddChildSafely(fly);

                if (fly.SwooshAwayCompletion != null && i == cardNodes.Count - 1) //only wait for the last card node to finish
                {
                    await fly.SwooshAwayCompletion.Task;
                }
            }
            this.QueueFreeSafely();
        }
    }

    private void PlaySubParticles(Node node)
        {
            foreach (CpuParticles2D cpuParticles2D in node.GetChildren().OfType<CpuParticles2D>())
            {
                cpuParticles2D.Emitting = true;
            }
        }
    }
