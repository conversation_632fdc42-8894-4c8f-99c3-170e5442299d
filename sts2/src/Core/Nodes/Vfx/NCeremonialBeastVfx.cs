using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Helpers;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NCeremonialBeastVfx : SpineSprite, IDeathDelayer
{
    [Export]
    private GpuParticles2D _deathParticles = default!;

    [Export]
    private CpuParticles2D _energyParticlesFront = default!;

    [Export]
    private CpuParticles2D _energyParticlesBack = default!;

    private TaskCompletionSource _deathTask = new();

    public override void _Ready()
    {
        Connect(SpineSprite.SignalName.AnimationEvent, Callable.From<GodotObject, GodotObject, GodotObject, GodotObject>(OnAnimationEvent));

        _deathParticles.OneShot = true;
        _deathParticles.Emitting = false;

        _energyParticlesBack.Emitting = true;
        _energyParticlesFront.Emitting = true;
    }

    private void OnAnimationEvent(GodotObject _, GodotObject __, GodotObject ___, GodotObject spineEvent)
    {
        switch (((SpineEvent)spineEvent).GetData().GetEventName())
        {
            case "turnOffEnergy":
                TurnOffEnergyParticles();
                break;
            case "turnOnEnergy":
                TurnOnEnergyParticles();
                break;
            case "deathParticles":
                TurnOnDeathParticles();
                break;
        }
    }

    public Task GetDelayTask()
    {
        return _deathTask.Task;
    }

    private void TurnOnDeathParticles()
    {
        _deathParticles.Restart();
        TaskHelper.RunSafely(FinishTaskWhenDeathParticlesFinished());
    }

    private async Task FinishTaskWhenDeathParticlesFinished()
    {
        await ToSignal(_deathParticles, CpuParticles2D.SignalName.Finished);
        _deathTask.SetResult();
    }

    private void TurnOnEnergyParticles()
    {
        _energyParticlesFront.Emitting = true;
        _energyParticlesBack.Emitting = true;
    }

    private void TurnOffEnergyParticles()
    {
        _energyParticlesFront.Emitting = false;
        _energyParticlesBack.Emitting = false;
    }
}
