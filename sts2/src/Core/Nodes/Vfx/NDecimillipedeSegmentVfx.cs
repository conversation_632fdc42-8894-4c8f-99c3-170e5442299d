using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NDecimillipedeSegmentVfx : SpineSprite
{
    [Export]
    private CpuParticles2D[] _damageParticleNodes = default!;

    private readonly Vector2 _particleGravity = new(0f, 300f);
    private float _particleSpeedScale = 2f;
    private Vector2 _particleVelocityMinMax = new(400f, 600f);

    [Export]
    private Node2D[] _sprayNodes = default!;

    private readonly List<Vector2> _sprayNodeScales = [];

    public override void _Ready()
    {
        foreach (Node2D sprayNode in _sprayNodes)
        {
            sprayNode.Visible = false;
            _sprayNodeScales.Add(sprayNode.Scale);
        }

        Connect(SpineSprite.SignalName.AnimationEvent, Callable.From<GodotObject, GodotObject, GodotObject, GodotObject>(OnAnimationEvent));
    }

    public void Regenerate()
    {
        for (int i = 0; i < _sprayNodes.Length; i++)
        {
            Node2D sprayNode = _sprayNodes[i];
            sprayNode.Visible = true;
            ShaderMaterial material = (ShaderMaterial)sprayNode.Material;

            material.SetShaderParameter("direction", -1);
            Tween tw = CreateTween().SetParallel();
            float randTime = Rng.Chaotic.NextFloat(0.5f);
            material.SetShaderParameter("opacity", 0.5f);

            tw.TweenProperty(sprayNode, "scale", _sprayNodeScales[i], randTime + 0.5f).SetTrans(Tween.TransitionType.Expo).SetEase(Tween.EaseType.Out);
            tw.TweenProperty(sprayNode.Material, "shader_parameter/opacity", 0.9f, randTime).SetTrans(Tween.TransitionType.Expo).SetEase(Tween.EaseType.Out);
        }
    }

    private void EndRegenerate()
    {
        foreach (Node2D sprayNode in _sprayNodes)
        {
            Vector2 startScale = sprayNode.Scale;

            Tween tw = CreateTween();

            float randTime = Rng.Chaotic.NextFloat(0.9f, 1.25f);
            tw.TweenProperty(sprayNode, "scale", Vector2.Zero, randTime).SetTrans(Tween.TransitionType.Quad).SetEase(Tween.EaseType.In);
            tw.TweenProperty(sprayNode, "visible", false, 0);
            tw.TweenProperty(sprayNode, "scale", startScale, 0);
        }
    }

    private void Wither()
    {
        foreach (CpuParticles2D damageParticleNode in _damageParticleNodes)
        {
            damageParticleNode.Gravity = _particleGravity;
            damageParticleNode.SpeedScale = _particleSpeedScale;
            damageParticleNode.InitialVelocityMin = _particleVelocityMinMax.X;
            damageParticleNode.InitialVelocityMax = _particleVelocityMinMax.Y;
            damageParticleNode.Restart();
        }
    }

    private void OnAnimationEvent(GodotObject _, GodotObject __, GodotObject ___, GodotObject animEvent)
    {
        switch (((SpineEvent)animEvent).GetData().GetEventName())
        {
            case "suck_complete":
                EndRegenerate();
                break;
            case "explode":
                Wither();
                break;
        }
    }
}
