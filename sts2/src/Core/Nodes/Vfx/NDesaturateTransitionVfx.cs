using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NDesaturateTransitionVfx : Node
{
    public static IEnumerable<string> AssetPaths => [ScenePath];
    private static string ScenePath => SceneHelper.GetScenePath("vfx/desaturate_transition_vfx");

    public static NDesaturateTransitionVfx? Create()
    {
        if (TestMode.IsOn) return null;

        NDesaturateTransitionVfx vfx = PreloadManager.Cache.GetScene(ScenePath).Instantiate<NDesaturateTransitionVfx>();
        return vfx;
    }

    public override void _Ready()
    {
        TaskHelper.RunSafely(Animate());
    }

    private async Task Animate()
    {
        WorldEnvironment env = NGame.Instance!.ActivateWorldEnvironment();

        Tween tween = CreateTween().SetParallel();

        // tween.SetSpeedScale(1.5f); // Fast mode? It's about 3 seconds standard
        tween.TweenProperty(env, "environment:tonemap_exposure", 1f, 1f);
        tween.TweenProperty(env, "environment:adjustment_brightness", 0.1f, 1f);
        tween.TweenProperty(env, "environment:adjustment_contrast", 0.7f, 1f);
        tween.TweenProperty(env, "environment:adjustment_saturation", 0.25f, 1f);
        tween.Chain();
        tween.TweenProperty(env, "environment:adjustment_contrast", 0.8f, 1f);
        tween.Chain();
        tween.TweenProperty(env, "environment:adjustment_brightness", 1f, 1f);
        tween.TweenProperty(env, "environment:adjustment_contrast", 1f, 1f);
        tween.TweenProperty(env, "environment:adjustment_saturation", 1f, 1f);

        await ToSignal(tween, Tween.SignalName.Finished);

        NGame.Instance.DeactivateWorldEnvironment();
        this.QueueFreeSafely();
    }
}
