using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx.Utilities;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

/// <summary>
/// Manages the VFX for Doom (and its sub-emitters).
/// Moves the creatures visuals into itself to handle sucking it into Hell.
/// </summary>
public partial class NDoomVfx : Node2D
{
    private CancellationTokenSource VfxCancellationToken { get; } = new();

    private static string ScenePath => SceneHelper.GetScenePath("vfx/vfx_doom");

    public static IEnumerable<string> AssetPaths => [ScenePath];

    private NDoomSubEmitterVfx _back = default!;
    private NDoomSubEmitterVfx _front = default!;

    private NCreatureVisuals _creatureVisuals = default!;
    private Vector2 _position;
    private Vector2 _size;
    private bool _shouldDie;
    private CancellationToken _cancelToken;

    // This is eyeballed from the width of the particle width.
    private const float _doomVfxSize = 260f;

    public Task? VfxTask { get; private set; }

    public static NDoomVfx? Create(NCreatureVisuals creatureVisuals, Vector2 position, Vector2 size, bool shouldDie)
    {
        if (TestMode.IsOn) return null;

        NDoomVfx node = PreloadManager.Cache.GetScene(ScenePath).Instantiate<NDoomVfx>();
        node._creatureVisuals = creatureVisuals;
        node._position = position;
        node._size = size;
        node._shouldDie = shouldDie;

        return node;
    }

    public override void _ExitTree()
    {
        base._ExitTree();
        VfxCancellationToken.Cancel();
    }

    public override void _Ready()
    {
        _back = GetNode<NDoomSubEmitterVfx>("DoomVfxBack");
        _front = GetNode<NDoomSubEmitterVfx>("DoomVfxFront");

        _cancelToken = VfxCancellationToken.Token;
        VfxTask = TaskHelper.RunSafely(PlayVfx(_creatureVisuals, _position, _size, _shouldDie));
    }

    private async Task PlayVfx(NCreatureVisuals creatureVisuals, Vector2 position, Vector2 size, bool shouldDie)
    {
        if (_cancelToken.IsCancellationRequested) return;

        NDebugAudioManager.Instance?.Play("doom.ogg", 1f, PitchVariance.Medium);

        GlobalPosition = position + new Vector2(size.X * 0.5f, size.Y) * NCombatRoom.Instance!.SceneContainer.Scale;
        Scale = NCombatRoom.Instance.SceneContainer.Scale;

        SubViewport viewport = GetNode<SubViewport>("Viewport");
        Vector2 viewportSize = size;
        viewportSize.X *= 1.5f;
        viewportSize.Y *= 1.5f;
        viewport.Size = (Vector2I)viewportSize;

        if (shouldDie)
        {
            Vector2 creatureOffset = new Vector2(viewportSize.X / 2, viewport.Size.Y) + creatureVisuals.Body.Position;
            await Reparent(creatureVisuals.Body, viewport);
            creatureVisuals.Body.Position = creatureOffset;
            creatureVisuals.Body.Scale *= creatureVisuals.Scale;
        }

        if (_cancelToken.IsCancellationRequested) return;
        await PlayVfxInternal();
    }

    private async Task PlayVfxInternal()
    {
        try
        {
            // Make sure we are in the correct positions.
            SubViewport viewport = GetNode<SubViewport>("Viewport");
            Sprite2D visual = GetNode<Sprite2D>("%Visual");
            visual.Position += Vector2.Up * viewport.Size.Y * 0.5f;

            NGame.Instance?.ScreenShake(ShakeStrength.Weak, ShakeDuration.Short, 180f + Rng.Chaotic.NextFloat(-10f, 10f));
            ShowOrHideParticles(viewport.Size.X / _doomVfxSize, 0.5f);

            Tween tween = CreateTween();
            tween.TweenProperty(visual, "position:y", visual.Position.Y + viewport.Size.Y, 0.75f)
                .SetEase(Tween.EaseType.In)
                .SetDelay(0.75f)
                .SetTrans(Tween.TransitionType.Expo);

            await ToSignal(tween, Tween.SignalName.Finished);
            ShowOrHideParticles(0, 0.25f);

            await Task.Delay(2000, _cancelToken);
        }
        finally
        {
            // Since the task can get interrupted, make sure we go away.
            // We have to check IsInstanceValid in case the creature node was removed from the scene during the delay.
            if (IsInstanceValid(this))
            {
                this.QueueFreeSafely();
            }
        }
    }

    private void ShowOrHideParticles(float widthScale, float tweenTime)
    {
        _back.ShowOrHide(widthScale, tweenTime);
        _front.ShowOrHide(widthScale, tweenTime);
    }

    private async Task Reparent(Node creatureNode, Node newParent)
    {
        Node? parent = creatureNode.GetParent();

        bool removeCompleted = false;
        Callable reparent = Callable.From(() => removeCompleted = true);

        creatureNode.Connect(Node.SignalName.TreeExited, reparent);
        parent!.RemoveChildSafely(creatureNode);

        while (!removeCompleted)
        {
            await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
        }

        newParent.AddChildSafely(creatureNode);
        creatureNode.Disconnect(Node.SignalName.TreeExited, reparent);
    }
}
