using Godot;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NKinFollowerVfx : SpineSprite
{

    private NBasicTrail _trail1 = default!;
    private NBasicTrail _trail2 = default!;
    private GpuParticles2D _hay = default!;

    public override void _Ready()
    {
        _trail1 = GetNode<NBasicTrail>("Boomerang1Slot/Trail");
        _trail2 = GetNode<NBasicTrail>("Boomerang2Slot/Trail");
        _hay = GetNode<GpuParticles2D>("HaySlot/HayParticles");

        _trail1.Visible = false;
        _trail2.Visible = false;
        _hay.Emitting = false;
        _hay.OneShot = true;


        Connect(SpineSprite.SignalName.AnimationEvent, Callable.From<GodotObject, GodotObject, GodotObject, GodotObject>(OnAnimationEvent));

    }


    private void OnAnimationEvent(GodotObject _, GodotObject __, GodotObject ___, GodotObject spineEvent)
    {
        switch (((SpineEvent)spineEvent).GetData().GetEventName())
        {
            case "start_trail1":
                StartTrail1();
                break;
            case "end_trail1":
                EndTrail1();
                break;
            case "start_trail2":
                StartTrail2();
                break;
            case "end_trail2":
                EndTrail2();
                break;
            case "start_hay":
                StartHay();
                break;

        }

    }


    // Called every frame. 'delta' is the elapsed time since the previous frame.
    public override void _Process(double delta)
    {

    }

    private void StartTrail1()
    {
        _trail1.ClearPoints();
        _trail1.Visible = true;
    }

    private void StartTrail2()
    {
        _trail2.ClearPoints();
        _trail2.Visible = true;
    }

    private void EndTrail1()
    {
        _trail1.Visible = false;
    }

    private void EndTrail2()
    {
        _trail2.Visible = false;
    }

    private void StartHay()
    {
        _hay.Restart();
    }

}
