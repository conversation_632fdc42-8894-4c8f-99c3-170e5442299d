using Godot;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NLaserVfx : Node2D
{
    private SpineSprite _spine = default!;
    private Node2D _targetingBone = default!;

    public override void _Ready()
    {
        _spine = GetNode<SpineSprite>("SpineSprite");
        _targetingBone = GetNode<Node2D>("SpineSprite/TargetingBone");

        _spine.GetAnimationState().SetAnimation("animation");
        _spine.Visible = false;
    }

    public void ExtendLaser(Vector2 targetPos)
    {
        _spine.Visible = true;
        _spine.GetAnimationState().SetAnimation("animation");
        _targetingBone.GlobalPosition = GlobalPosition;

        Tween tween = CreateTween();
        tween.TweenProperty(_targetingBone, "position", targetPos, 0.15f)
            .SetTrans(Tween.TransitionType.Expo)
            .SetEase(Tween.EaseType.Out);
        tween.Chain().TweenProperty(_spine, "modulate", Colors.Red, 0.2f);
    }

    public void RetractLaser()
    {
        Tween tween = CreateTween();
        tween.TweenProperty(_targetingBone, "position", Position, 0.15f)
            .SetTrans(Tween.TransitionType.Expo)
            .SetEase(Tween.EaseType.In);
        tween.Chain().TweenProperty(_spine, "visible", false, 0f);
    }

    public void ResetLaser()
    {
        _targetingBone.Position = Position;
    }

    private void SetLaserColor(Color color)
    {
        ((ShaderMaterial)_spine.AdditiveMaterial).SetShaderParameter("Color", color);
    }
}
