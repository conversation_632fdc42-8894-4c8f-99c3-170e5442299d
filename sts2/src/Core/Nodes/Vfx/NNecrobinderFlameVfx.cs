using Godot;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NNecrobinderFlameVfx : SpineSlotNode
{
    public override void _Ready()
    {
        GetParent<SpineSprite>().Connect(SpineSprite.SignalName.AnimationStarted, Callable.From<GodotObject, GodotObject, GodotObject>(TryHideFlame));
    }

    private void TryHideFlame(GodotObject spineSprite, GodotObject animationState, GodotObject trackEntry)
    {
        Visible = ((SpineAnimationState)animationState).GetCurrent(0).GetAnimation().GetName() != "die";
    }
}
