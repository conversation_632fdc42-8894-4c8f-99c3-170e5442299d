using Godot;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NOilSpillVfx : SpineSprite
{
    private const int _slamSprayAmount = 800;
    private const int _sprayAttackAmount = 2000;
    private const int _deathSprayAmount = 500;

    private const float _slamSprayLifetime = .75f;


    private GpuParticles2D _droolParticles = default!;
    private GpuParticles2D _sprayParticles = default!;
    private GpuParticles2D _rainDropParticles = default!;

    public override void _Ready()
    {
        _droolParticles = GetNode<GpuParticles2D>("MouthDribbleBoneNode/DribbleParticles");
        _sprayParticles = GetNode<GpuParticles2D>("MouthSpraySlot/SprayParticles");
        _rainDropParticles = GetNode<GpuParticles2D>("MouthSpraySlot/RainDropParticles");

        _rainDropParticles.OneShot = true;


        _droolParticles.Restart();
        _rainDropParticles.Restart();
        _sprayParticles.Restart();


        TurnOffSprayAttack();
        TurnOffSlamSpray();

        Connect(SpineSprite.SignalName.AnimationEvent, Callable.From<GodotObject, GodotObject, GodotObject, GodotObject>(OnAnimationEvent));
    }

    private void OnAnimationEvent(GodotObject _, GodotObject __, GodotObject ___, GodotObject spineEvent)
    {
        switch (((SpineEvent)spineEvent).GetData().GetEventName())
        {
            case "spray_start":
                TurnOnSprayAttack();
                break;
            case "spray_end":
                TurnOffSprayAttack();
                break;
            case "slam_spray_start":
                TurnOnSlamSpray();
                break;
            case "slam_spray_end":
                TurnOffSlamSpray();
                break;
            case "death_spray_start":
                TurnOnDeathSpray();
                break;
            case "death_spray_end":
                TurnOffDeathSpray();
                break;
            case "drool_start":
                TurnOnDrool();
                break;
            case "drool_end":
                TurnOffDrool();
                break;
        }
    }

    private void TurnOnSprayAttack()
    {
        _sprayParticles.Amount = _sprayAttackAmount;
        _sprayParticles.Emitting = true;
    }

    private void TurnOffSprayAttack()
    {
        _sprayParticles.Emitting = false;
    }


    private void TurnOnSlamSpray()
    {
        _rainDropParticles.OneShot = false;
        _rainDropParticles.Amount = _slamSprayAmount;
        _rainDropParticles.Explosiveness = 0;
        _rainDropParticles.Lifetime = _slamSprayLifetime;
        _rainDropParticles.Restart();
    }

    private void TurnOffSlamSpray()
    {
        _rainDropParticles.Emitting = false;
    }

    private void TurnOnDeathSpray()
    {
        _sprayParticles.Amount = _deathSprayAmount;
        _sprayParticles.Emitting = true;
    }

    private void TurnOffDeathSpray()
    {
        _sprayParticles.Emitting = false;
    }

    private void TurnOnDrool()
    {
        _droolParticles.Emitting = true;
    }

    private void TurnOffDrool()
    {
        _droolParticles.Emitting = false;
    }
}
