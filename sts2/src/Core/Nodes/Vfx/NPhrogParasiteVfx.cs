using Godot;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NPhrogParasiteVfx : SpineSprite
{
    private GpuParticles2D _bubbleParticlesA = default!;
    private GpuParticles2D _bubbleParticlesB = default!;
    private GpuParticles2D _bubbleParticlesC = default!;
    private GpuParticles2D _gooParticlesDeath = default!;
    private GpuParticles2D _wormParticlesDeath = default!;

    public override void _Ready()
    {
        _bubbleParticlesA = GetNode<GpuParticles2D>("BubbleABoneNode/WormParticlesA");
        _bubbleParticlesB = GetNode<GpuParticles2D>("BubbleBSlotNode/WormParticlesB");
        _bubbleParticlesC = GetNode<GpuParticles2D>("BubbleCBoneNode/WormParticlesC");
        _gooParticlesDeath = GetNode<GpuParticles2D>("DeathParticles");
        _wormParticlesDeath = GetNode<GpuParticles2D>("DeathWormParticles");

        Connect(SpineSprite.SignalName.AnimationEvent, Callable.From<GodotObject, GodotObject, GodotObject, GodotObject>(OnAnimationEvent));

        _bubbleParticlesA.Emitting = false;
        _bubbleParticlesB.Emitting = false;
        _bubbleParticlesC.Emitting = false;
        _gooParticlesDeath.Emitting = false;
        _wormParticlesDeath.Emitting = false;
        _gooParticlesDeath.OneShot = true;
        _wormParticlesDeath.OneShot = true;


        GetAnimationState().SetAnimation("die", true);
    }

    private void OnAnimationEvent(GodotObject _, GodotObject __, GodotObject ___, GodotObject spineEvent)
    {
        switch (((SpineEvent)spineEvent).GetData().GetEventName())
        {
            case "infect":
                TurnOnInfect();
                break;
            case "stop_infect":
                TurnOffInfect();
                break;
            case "explode":
                StartExplode();
                break;
        }
    }

    void TurnOnInfect()
    {
        _bubbleParticlesA.Emitting = true;
        _bubbleParticlesB.Emitting = true;
        _bubbleParticlesC.Emitting = true;
    }

    void TurnOffInfect()
    {
        _bubbleParticlesA.Emitting = false;
        _bubbleParticlesB.Emitting = false;
        _bubbleParticlesC.Emitting = false;
    }

    void StartExplode()
    {
        _gooParticlesDeath.Restart();
        _wormParticlesDeath.Restart();
    }
}
