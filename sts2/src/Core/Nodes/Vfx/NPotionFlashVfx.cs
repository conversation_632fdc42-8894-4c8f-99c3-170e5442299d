using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.Potions;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NPotionFlashVfx : Node2D
{
    public static string ScenePath => SceneHelper.GetScenePath("vfx/vfx_potion_flash");

    private Control _flash = default!;

    public static NPotionFlashVfx? Create(NPotion originPotion)
    {
        if (TestMode.IsOn) return null;

        NPotionFlashVfx node = PreloadManager.Cache.GetScene(ScenePath).Instantiate<NPotionFlashVfx>();
        node._flash = (Control)originPotion.Duplicate();

        // This is a pretty weird pattern, see https://github.com/godotengine/godot/issues/31994
        // We need to disconnect the NPotion script so that it doesn't try to execute potion things
        ulong objId = node._flash.GetInstanceId();
        node._flash.SetScript(default);
        node._flash = (Control)InstanceFromId(objId)!;

        return node;
    }

    public override void _Ready()
    {
        GetNode<SubViewport>("SubViewport").AddChildSafely(_flash);
        _flash.Position = Vector2.Zero;

        TaskHelper.RunSafely(FlashAndFree());
    }

    private async Task FlashAndFree()
    {
        CpuParticles2D particles = GetNode<CpuParticles2D>("%Flash");
        particles.Emitting = true;
        await ToSignal(particles, CpuParticles2D.SignalName.Finished);
        this.QueueFreeSafely();
    }
}
