// HOW IT WORKS *************//
// When you call Forge() you pass in a charge pct. The idea is that each time you forge, it scales by that pct.
// Forge(0.1f) causes it to increase by 10% of its original size. The additional scale is additive.
// So calling Forge(0.1f) 10x results in the additional scale being 1 (displays 2x its original size);
// Right now, the node that is dynamically scaled is 'SpineSword'.
// There's another node called 'ScaleContainer' that I added so I could scale the enormous art to a reasonable size.

// The spine animation is pretty small - Just some nice smooth sword wiggling when it hovers and some shaking
// around when it attacks. The Spine animation is invisible, but it drives the 'ScaleContainer' node via a
// SpineBone node.

// Forging and attacking are done with tweens.

// There's a boolean you can pass into Forge() to show flames. I use it the first time a new sword is forged, but not
// for subsequent forges.

using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.HoverTips;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NSovereignBladeVfx : Node2D
{
    public static IEnumerable<string> AssetPaths => [_scenePath];
    private static readonly string _scenePath = SceneHelper.GetScenePath("vfx/sovereign_blade");

    public CardModel Card { get; private set; } = default!;
    private Player _owner = default!;

    //Assets
    private SpineSprite _spineObject = default!;
    private Node2D _bladeGlow = default!;
    private GpuParticles2D _forgeSparks = default!;
    private GpuParticles2D _spawnFlames = default!;
    private GpuParticles2D _spawnFlamesBack = default!;
    private GpuParticles2D _slashParticles = default!;
    private GpuParticles2D _chargeParticles = default!;
    private GpuParticles2D _spikeParticles = default!;
    private GpuParticles2D _spikeParticles2 = default!;
    private GpuParticles2D _spikeCircle = default!;
    private GpuParticles2D _spikeCircle2 = default!;
    private TextureRect _hilt = default!;
    private TextureRect _hilt2 = default!;
    private TextureRect _detail = default!;
    private Line2D _trail = default!;
    private Path2D _orbitPath = default!;
    private Control _hitbox = default!;
    private NSelectionReticle _selectionReticle = default!;

    //Bunch of Tweens
    private Tween _attackTween = default!;
    private Tween _scaleTween = default!;
    private Tween _sparkDelay = default!;
    private Tween _glowTween = default!;

    //Actual variables
    private Vector2 _trailStart;
    private float _bladeSize;

    // Orbit logic
    public double OrbitProgress { get; set; }
    private const float _orbitSpeed = 60f;
    private Vector2 _targetOrbitPosition;
    private bool _isBehindCharacter;

    // Set how much it can increase before it grows a bigger hilt
    private const float _hiltThreshold = 0.3f;

    // Set how much it can increase before an encrusted detail appears on the blade
    private const float _detailThreshold = 0.66f;

    private bool _isFocused;
    private NHoverTipSet? _hoverTip;

    // Testing stuffs
    private bool _isForging;
    private bool _isAttacking;
    private bool _isKeyPressed;
    private float _testCharge;

    // Called when the node enters the scene tree for the first time.
    public override void _Ready()
    {
        _spineObject = GetNode<SpineSprite>("SpineSword");
        _bladeGlow = GetNode<Node2D>("SpineSword/SwordBone/ScaleContainer/BladeGlow");
        _forgeSparks = GetNode<GpuParticles2D>("SpineSword/SwordBone/ScaleContainer/ForgeSparks");
        _spawnFlames = GetNode<GpuParticles2D>("SpineSword/SwordBone/ScaleContainer/SpawnFlames");
        _spawnFlamesBack = GetNode<GpuParticles2D>("SpineSword/SwordBone/ScaleContainer/SpawnFlamesBack");
        _slashParticles = GetNode<GpuParticles2D>("SpineSword/SlashParticles");
        _chargeParticles = GetNode<GpuParticles2D>("SpineSword/SwordBone/ScaleContainer/ChargeParticles");
        _spikeParticles = GetNode<GpuParticles2D>("SpineSword/SwordBone/ScaleContainer/Spikes");
        _spikeParticles2 = GetNode<GpuParticles2D>("SpineSword/SwordBone/ScaleContainer/Spikes2");
        _spikeCircle = GetNode<GpuParticles2D>("SpineSword/SwordBone/ScaleContainer/SpikeCircle");
        _spikeCircle2 = GetNode<GpuParticles2D>("SpineSword/SwordBone/ScaleContainer/SpikeCircle2");
        _hilt = GetNode<TextureRect>("SpineSword/SwordBone/ScaleContainer/Hilt");
        _hilt2 = GetNode<TextureRect>("SpineSword/SwordBone/ScaleContainer/Hilt2");
        _detail = GetNode<TextureRect>("SpineSword/SwordBone/ScaleContainer/Detail");
        _trail = GetNode<Line2D>("Trail");
        _orbitPath = GetNode<Path2D>("%Path");
        _hitbox = GetNode<Control>("%Hitbox");
        _selectionReticle = GetNode<NSelectionReticle>("%SelectionReticle");

        _hitbox.Connect(Control.SignalName.MouseEntered, Callable.From(OnFocused));
        _hitbox.Connect(Control.SignalName.MouseExited, Callable.From(OnUnfocused));
        _hitbox.Connect(Control.SignalName.FocusEntered, Callable.From(OnFocused));
        _hitbox.Connect(Control.SignalName.FocusExited, Callable.From(OnUnfocused));

        // Init
        _forgeSparks.Emitting = false;
        _forgeSparks.OneShot = true;
        _spawnFlames.Emitting = false;
        _spawnFlames.OneShot = true;
        _spawnFlamesBack.Emitting = false;
        _spawnFlamesBack.OneShot = true;
        _slashParticles.Emitting = false;
        _slashParticles.OneShot = true;
        _chargeParticles.Emitting = false;
        _spikeParticles2.Emitting = false;
        _spikeCircle2.Emitting = false;
        _bladeGlow.Modulate = Colors.Transparent;
        _bladeGlow.Visible = false;
        _trail.GlobalPosition = Vector2.Zero;
        _trail.ClearPoints();
        _spineObject.GetAnimationState().SetAnimation("idle_loop");
        _spineObject.Scale = Vector2.Zero;
        _spineObject.Visible = true;

        NTargetManager.Instance.Connect(NTargetManager.SignalName.TargetingBegan, Callable.From(OnTargetingBegan));
        NTargetManager.Instance.Connect(NTargetManager.SignalName.TargetingEnded, Callable.From(OnTargetingEnded));
        _owner = Card.Owner;
        _owner.Creature.Died += OnOwnerDied;
    }

    public override void _ExitTree()
    {
        _owner.Creature.Died -= OnOwnerDied;
    }

    public static NSovereignBladeVfx? Create(CardModel card)
    {
        if (TestMode.IsOn) return null;

        NSovereignBladeVfx blade = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NSovereignBladeVfx>();
        blade.Card = card;
        return blade;
    }

    public override void _Process(double delta)
    {
        float pathLength = _orbitPath.Curve.GetBakedLength();

        // If we are displaying hovertip, don't orbit
        if (_hoverTip == null)
        {
            OrbitProgress += _orbitSpeed * delta / pathLength;
        }

        bool shouldBeBehindCharacter = OrbitProgress % 1 is > 0.25f and < 0.78f;
        if (shouldBeBehindCharacter != _isBehindCharacter && _bladeSize < 0.6f)
        {
            _isBehindCharacter = !_isBehindCharacter;
            GetParent().MoveChild(this, shouldBeBehindCharacter ? 0 : GetParent().GetChildCount() - 1);
        }

        Transform2D nextCurveSample = _orbitPath.Curve.SampleBakedWithRotation((float)(OrbitProgress % 1) * pathLength);

        // lessen the horizontal orbit based on how big the sword is. The bigger the sword. The more steady it will be
        Vector2 desiredGlobalPosition = _orbitPath.GlobalTransform * nextCurveSample.Origin;
        desiredGlobalPosition.X = Mathf.Lerp(desiredGlobalPosition.X, GlobalPosition.X + 200, Mathf.Clamp(_bladeSize / 1.25f, 0, 1));

        // move the orbit position up based off of how big the sword is
        _targetOrbitPosition = desiredGlobalPosition + Vector2.Up * (_spineObject.Scale.Y - 1f) * 100;

        if (!_isAttacking)
        {
            _spineObject.GlobalPosition = _spineObject.GlobalPosition.Lerp(_targetOrbitPosition, (float)delta * 7.0f);
        }
    }

    public void Forge(float bladeDamage = 0f, bool showFlames = false) //, bool newBlade = false)
    {
        if (_isForging)
        {
            CleanupForge();
        }

        _bladeSize = Mathf.Clamp(Mathf.Lerp(0, 1, bladeDamage / 200f), 0, 1);

        _isForging = true;

        // Particle Amount
        int partAmount = (int)(_bladeSize * 30);
        if (partAmount > 0)
        {
            _chargeParticles.Amount = partAmount;
            _chargeParticles.Emitting = true;
        }
        else
        {
            _chargeParticles.Emitting = false;
        }

        // Sword Features
        _hilt.Visible = _bladeSize < _hiltThreshold;
        _hilt2.Visible = !_hilt.Visible;
        _spikeParticles.Emitting = _spikeParticles.Visible = _hilt.Visible;
        _spikeParticles2.Emitting = _spikeParticles2.Visible = !_hilt.Visible;
        _spikeCircle.Emitting = _spikeCircle.Visible = _hilt.Visible;
        _spikeCircle2.Emitting = _spikeCircle2.Visible = !_hilt.Visible;
        _detail.Visible = bladeDamage >= _detailThreshold;

        // Glow
        _bladeGlow.Visible = true;
        Color targColor = Color.FromHtml("#ff7300");
        Color transColor = targColor;
        transColor.A = 0;
        _glowTween = CreateTween();
        if (showFlames) FireFlames();
        _glowTween.TweenProperty(_bladeGlow, "modulate", targColor, .05).SetEase(Tween.EaseType.Out);
        _glowTween.Chain().TweenProperty(_bladeGlow, "modulate", transColor, 0.5f).SetEase(Tween.EaseType.In).SetTrans(Tween.TransitionType.Cubic);
        _glowTween.Chain().TweenCallback(Callable.From(CleanupForge));

        // Scale pop
        Vector2 newScale = Vector2.One * Mathf.Lerp(0.9f, 2f, _bladeSize);
        _scaleTween = CreateTween();
        _scaleTween.TweenProperty(_spineObject, "scale", newScale * 1.2f, 0.05f).SetEase(Tween.EaseType.Out).SetTrans(Tween.TransitionType.Cubic);
        _scaleTween.Chain().TweenCallback(Callable.From(FireSparks));
        _scaleTween.Chain().TweenProperty(_spineObject, "scale", newScale, 0.3f).SetEase(Tween.EaseType.InOut).SetTrans(Tween.TransitionType.Cubic);
    }

    public void Attack(Vector2 targetPos)
    {
        if (_isAttacking)
        {
            CleanupAttack();
        }

        _isAttacking = true;
        _spineObject.GetAnimationState().SetAnimation("attack", false);
        _attackTween = CreateTween();

        Vector2 windUpPos = new(_spineObject.GlobalPosition.X - 50f, _spineObject.GlobalPosition.Y);
        _trail.Visible = true;
        _trailStart = windUpPos;

        _attackTween.TweenProperty(_spineObject, "rotation", _spineObject.GetAngleTo(targetPos), 0.05f);
        _attackTween.Parallel().TweenProperty(_spineObject, "global_position", windUpPos, .08f).SetEase(Tween.EaseType.Out).SetTrans(Tween.TransitionType.Expo);
        _attackTween.Chain().TweenProperty(_spineObject, "rotation", _spineObject.GetAngleTo(targetPos), 0.0f);
        _attackTween.Parallel().TweenProperty(_spineObject, "global_position", targetPos, .05f).SetEase(Tween.EaseType.In).SetTrans(Tween.TransitionType.Expo);
        _attackTween.Chain().TweenCallback(Callable.From(EndSlash));
        _attackTween.TweenInterval(0.25f);
        _attackTween.Chain().TweenCallback(Callable.From(FireSparks)).SetDelay(0.3f);
        _attackTween.Chain().TweenCallback(Callable.From(CleanupAttack));

        UpdateHoverTip();
    }

    private void OnTargetingBegan()
    {
        // When targeting begins, don't allow us to stop the mouse
        _hitbox.MouseFilter = Control.MouseFilterEnum.Ignore;
        UpdateHoverTip();
    }

    private void OnTargetingEnded()
    {
        _hitbox.MouseFilter = Control.MouseFilterEnum.Stop;
        UpdateHoverTip();
    }

    private void OnFocused()
    {
        _isFocused = true;

        // if i am dragging around a card, i dont want to be able to hover this
        if (!NCombatRoom.Instance!.Ui.Hand.InCardPlay)
        {
            UpdateHoverTip();
        }
    }

    private void OnUnfocused()
    {
        _isFocused = false;

        if (!NCombatRoom.Instance!.Ui.Hand.InCardPlay)
        {
            UpdateHoverTip();
        }
    }

    private void UpdateHoverTip()
    {
        bool displayHoverTip = _isFocused && !_isAttacking && !NTargetManager.Instance.IsInSelection && _hitbox.MouseFilter != Control.MouseFilterEnum.Ignore;

        if (displayHoverTip && _hoverTip == null)
        {
            _hoverTip = NHoverTipSet.CreateAndShow(_hitbox, HoverTipFactory.FromCard(Card));
            _hoverTip.GlobalPosition = _hitbox.GlobalPosition + Vector2.Right * _hitbox.Size.X;
            _selectionReticle.OnSelect();
        }
        else if (!displayHoverTip && _hoverTip != null)
        {
            NHoverTipSet.Remove(_hitbox);
            _selectionReticle.OnDeselect();
            _hoverTip = null;
        }
    }

    private void FireSparks()
    {
        _forgeSparks.Restart();
    }

    private void FireFlames()
    {
        _spawnFlames.Restart();
        _spawnFlamesBack.Restart();
    }

    private void EndSlash()
    {
        _chargeParticles.Emitting = false;
        _chargeParticles.Restart();
        _slashParticles.Rotation = _spineObject.GetAngleTo(_trailStart) - 1.5708f; // offset 90º in radians
        _slashParticles.Restart();
        _trail.AddPoint(_trailStart);
        _trail.AddPoint(GetNode<Node2D>("SpineSword/SwordBone/ScaleContainer/SpikeCircle").GlobalPosition);
        _trail.Modulate = Colors.White;
        CreateTween().TweenProperty(_trail, "modulate:a", 0f, 0.2f);
    }

    private void CleanupForge()
    {
        _isForging = false;
        _scaleTween.Kill();
        _glowTween.Kill();
    }

    private void CleanupAttack()
    {
        _isAttacking = false;
        _attackTween.Kill();
        _spineObject.GetAnimationState().SetAnimation("idle_loop");
        _spineObject.Rotation = 0f;
        _trail.ClearPoints();
    }

    public void RemoveSovereignBlade()
    {
        _scaleTween.Kill();
        _scaleTween = CreateTween();
        _scaleTween.TweenProperty(_spineObject, "scale", Vector2.Zero, 0.2f).SetEase(Tween.EaseType.Out).SetTrans(Tween.TransitionType.Cubic);
        _scaleTween.Chain().TweenCallback(Callable.From(this.QueueFreeSafely));
    }

    private void OnOwnerDied(Creature creature)
    {
        _hitbox.MouseFilter = Control.MouseFilterEnum.Ignore;
        UpdateHoverTip();
        RemoveSovereignBlade();
    }
}
