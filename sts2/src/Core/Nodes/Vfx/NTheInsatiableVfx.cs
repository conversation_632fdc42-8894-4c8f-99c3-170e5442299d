using Godot;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NTheInsatiableVfx : SpineSprite
{
    [Export]
    private CpuParticles2D[] _continuousParticles = default!;

    private CpuParticles2D _salivaFountainParticles = default!;
    private CpuParticles2D _salivaDroolParticles = default!;
    private CpuParticles2D _salivaCloudParticles = default!;

    private GpuParticles2D _baseBlastParticles = default!;

    private SpineAnimationState _animState = default!;

    public override void _Ready()
    {
        _salivaFountainParticles = GetNode<CpuParticles2D>("SalivaSlotNode/SalivaFountainParticles");
        _salivaDroolParticles = GetNode<CpuParticles2D>("SalivaSlotNode/SalivaDroolParticles");
        _salivaCloudParticles = GetNode<CpuParticles2D>("SalivaSlotNode/SalivaCloudParticles");

        _baseBlastParticles = GetNode<GpuParticles2D>("BaseBlastSlot/BaseBlastParticles");
        _animState = GetAnimationState();

        _salivaFountainParticles.Emitting = false;
        _salivaDroolParticles.Emitting = false;
        _salivaCloudParticles.Emitting = false;
        _baseBlastParticles.Emitting = false;

        Connect(SpineSprite.SignalName.AnimationEvent, Callable.From<GodotObject, GodotObject, GodotObject, GodotObject>(OnAnimationEvent));
        Connect(SpineSprite.SignalName.AnimationStarted, Callable.From<GodotObject, GodotObject, GodotObject>(OnAnimationStart));

        // For testing
        // GetAnimationState().SetAnimation("salivate");
    }

    private void OnAnimationEvent(GodotObject _, GodotObject __, GodotObject ___, GodotObject spineEvent)
    {
        switch (((SpineEvent)spineEvent).GetData().GetEventName())
        {
            case "saliva_start":
                TurnOnSaliva();
                break;
            case "saliva_end":
                TurnOffSaliva();
                break;
            case "drool_start":
                TurnOnDrool();
                break;
            case "drool_end":
                TurnOffDrool();
                break;
            case "base_blast_start":
                TurnOnBaseBlast();
                break;
            case "base_blast_end":
                TurnOffBaseBlast();
                break;
            case "death_end":
                TurnOffContinuousParticles();
                break;
        }
    }

    private void TurnOnSaliva()
    {
        _salivaFountainParticles.Restart();
        _salivaCloudParticles.Restart();
    }

    private void TurnOffSaliva()
    {
        _salivaFountainParticles.Emitting = false;
        _salivaCloudParticles.Emitting = false;
    }

    private void TurnOnDrool()
    {
        _salivaDroolParticles.Restart();
    }

    private void TurnOffDrool()
    {
        _salivaDroolParticles.Emitting = false;
    }

    private void TurnOnBaseBlast()
    {
        _baseBlastParticles.Emitting = true;
    }

    private void TurnOffBaseBlast()
    {
        _baseBlastParticles.Emitting = false;
    }

    private void TurnOffContinuousParticles()
    {
        foreach (CpuParticles2D p in _continuousParticles)
        {
            p.Emitting = false;
        }
    }

    // Check if we want to make sure we turn off any vfx between animations. We have to do this if the animation that is supposed
    // to turn off the vfx is interrupted early.
    private void OnAnimationStart(GodotObject spineSprite, GodotObject animationState, GodotObject trackEntry)
    {
        if (((SpineAnimationState)animationState).GetCurrent(0).GetAnimation().GetName() != "attack_thrash")
        {
            TurnOffBaseBlast();
        }
    }
}
