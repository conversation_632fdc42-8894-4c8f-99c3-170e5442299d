using Godot;
using MegaCrit.Sts2.Core.Helpers;

namespace MegaCrit.Sts2.Core.Nodes.Vfx.Utilities;

/// <summary>
/// Plays all one shot particles in a vfx scene.
/// Destroys the Vfx after "_lifetime" seconds.
/// </summary>
public partial class NVfxParticleSystem : Node2D
{
    [Export]
    private float _lifetime = 1f;

    public override void _Ready()
    {
        foreach (Node child in GetChildren())
        {
            switch (child)
            {
                case CpuParticles2D particles:
                    particles.Emitting = true;
                    break;
                case GpuParticles2D particles:
                    particles.Emitting = true;
                    break;
            }
        }

        SceneTreeTimer timer = GetTree().CreateTimer(_lifetime);
        timer.Connect(SceneTreeTimer.SignalName.Timeout, Callable.From(AfterExpired));
    }

    private void AfterExpired()
    {
        if (IsInstanceValid(this))
        {
            this.QueueFreeSafely();
        }
    }
}
