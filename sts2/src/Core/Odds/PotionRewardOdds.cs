using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Odds;

public class PotionRewardOdds : AbstractOdds
{
    /// <summary>
    /// The percentage of normal combats that we expect to roll potions over the course of a climb.
    /// While our rolling logic is more complex than this (to avoid streakiness and other game-feel reasons), we should
    /// converge on this percentage.
    /// </summary>
    public const float targetOdds = 0.5f;

    public const float eliteBonus = 0.25f;
    public const float scarcityPenalty = 0.25f;

    private const float _basePotionRewardOdds = 0.4f;

    /// <summary>
    /// For creating at the start of a climb.
    /// </summary>
    /// <param name="rng">RNG to use for rolls.</param>
    public PotionRewardOdds(Rng rng) : base(_basePotionRewardOdds, rng) { }

    /// <summary>
    /// For restoring from save.
    /// </summary>
    /// <param name="initialValue">Restored value at the saved spot in the climb.</param>
    /// <param name="rng">RNG to use for rolls.</param>
    public PotionRewardOdds(float initialValue, Rng rng) : base(initialValue, rng) { }

    /// <summary>
    /// Roll for whether or not a potion should be included in a set of rewards.
    /// Using this will modify the odds of future potion rewards.
    /// </summary>
    /// <param name="player">The player who will receive the potential potion reward.</param>
    /// <param name="ascensionManager">Manager containing ascension level information that affects potion rewards.</param>
    /// <param name="roomType">Room type for roll, affects bonus chance for potions.</param>
    /// <returns>Whether or not to give a potion reward.</returns>
    /// <remarks>
    /// This method implements a pity system where:
    /// - If a potion is rolled, future potion chances decrease by 10%
    /// - If a potion is not rolled, future potion chances increase by 10%
    /// - The Scarcity ascension level reduces potion rewards by 25%
    /// - Elite rooms provide a 25% bonus chance for potions
    /// - The HookBus allows for external modification of potion reward odds
    /// </remarks>
    public bool Roll(Player player, AscensionManager ascensionManager, RoomType roomType)
    {
        float val = CurrentValue;

        val = (float)Hook.ModifyPotionRewardOdds(player.ClimbState, player, roomType, (decimal)val);

        float roll = _rng.NextFloat();

        if (roll < val)
        {
            // If you DO roll a potion reward, you're 10% LESS likely to roll another one in your next set of rewards.
            CurrentValue -= 0.1f;
        }
        else
        {
            // If you DON'T roll a potion reward, you're 10% MORE likely to roll one in your next set of rewards.
            CurrentValue += 0.1f;
        }

        bool potionRolled;

        // If we have the Scarcity ascension, the number of potions we see in a climb should be reduced by 25%.
        // We factor this in after modifying the current value to avoid interfering with the 50% convergence logic.
        if (ascensionManager.HasLevel(AscensionLevel.Scarcity) && _rng.NextFloat() < scarcityPenalty)
        {
            potionRolled = false;
        }
        else
        {
            float bonus = roomType switch
            {
                // Elites give potions 25% more often.
                RoomType.Elite => eliteBonus,
                _ => 0f
            };

            // We factor this in after modifying CurrentValue to avoid interfering with the 50% convergence logic.
            potionRolled = roll < val + bonus * targetOdds;
        }

        return potionRolled;
    }
}
