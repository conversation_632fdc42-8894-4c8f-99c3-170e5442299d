using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Odds;

/// <summary>
/// Keeps track of the odds for different room types to be rolled when the player visits an unknown map point type.
///
/// Note: If a room type has negative odds, that means it should NEVER be rolled, and its odds shouldn't increase as
/// other types are rolled. This is relevant for effects like the Deadly Enemies modifier.
/// </summary>
public class UnknownMapPointOdds : AbstractOdds
{
    private const float _baseMonsterOdds = 0.1f;
    private const float _baseEliteOdds = -1f;
    private const float _baseTreasureOdds = 0.02f;
    private const float _baseShopOdds = 0.03f;

    private static readonly ReadOnlyDictionary<RoomType, float> _baseOdds = new(new Dictionary<RoomType, float>
    {
        [RoomType.Monster] = _baseMonsterOdds,
        [RoomType.Elite] = _baseEliteOdds,
        [RoomType.Treasure] = _baseTreasureOdds,
        [RoomType.Shop] = _baseShopOdds
    });

    /// <summary>
    /// Contains the odds of all non-Event rooms being rolled.
    /// If we roll higher than the sum of all these odds, the result will be considered an Event room.
    /// </summary>
    private readonly Dictionary<RoomType, float> _nonEventOdds = new()
    {
        [RoomType.Monster] = _baseMonsterOdds,
        [RoomType.Elite] = _baseEliteOdds,
        [RoomType.Treasure] = _baseTreasureOdds,
        [RoomType.Shop] = _baseShopOdds
    };

    public float MonsterOdds
    {
        get => _nonEventOdds[RoomType.Monster];
        set => _nonEventOdds[RoomType.Monster] = value;
    }

    public float EliteOdds
    {
        get => _nonEventOdds[RoomType.Elite];
        set => _nonEventOdds[RoomType.Elite] = value;
    }

    public float TreasureOdds
    {
        get => _nonEventOdds[RoomType.Treasure];
        set => _nonEventOdds[RoomType.Treasure] = value;
    }

    public float ShopOdds
    {
        get => _nonEventOdds[RoomType.Shop];
        set => _nonEventOdds[RoomType.Shop] = value;
    }

    // Events take up "the rest" of the odds.
    public float EventOdds => Math.Max(0f, 1f - _nonEventOdds.Values.Where(v => v > 0f).Sum());

    /// <summary>
    /// For creating at the start of a climb.
    /// </summary>
    /// <param name="rng">RNG to use for rolls.</param>
    public UnknownMapPointOdds(Rng rng) : base(0f, rng) { }

    /// <summary>
    /// Roll for the next room type and update future odds based on what's rolled.
    /// </summary>
    /// <param name="blacklist">Room types that we shouldn't be able to roll.</param>
    /// <param name="climbState">The state of the climb that this room is being entered in.</param>
    /// <returns>A RoomType.</returns>
    public RoomType Roll(IEnumerable<RoomType> blacklist, IClimbState climbState)
    {
        IReadOnlySet<RoomType> validRoomTypes = _nonEventOdds.Keys
            .Append(RoomType.Event)
            .Except(blacklist)
            .ToHashSet();

        validRoomTypes = Hook.ModifyUnknownMapPointRoomTypes(climbState, validRoomTypes);

        // We call .Order() on validRoomTypes so the same contents will always yield the same result from .First().
        RoomType resultRoomType = validRoomTypes.Contains(RoomType.Event) ? RoomType.Event : validRoomTypes.Order().First();

        // Generate a random number between 0 and 1. This will ultimately determine what type of room is rolled.
        float roll = _rng.NextFloat();

        float threshold = 0f;

        // Iterate through all possible room types. A room type's odds represent the size of the "bucket" that the
        // random number could fall into. If the random number falls into a given room type's odds, that room type wins.
        foreach ((RoomType roomType, float odds) in _nonEventOdds)
        {
            // Skip invalid room types, since these should never be rolled and shouldn't count towards the threshold.
            if (!validRoomTypes.Contains(roomType)) continue;
            if (odds < 0) continue;

            threshold += odds;

            if (roll <= threshold)
            {
                resultRoomType = roomType;
                break;
            }
        }

        // Iterate through all possible room types again. This time, we want to reset the winning room types odds to its
        // base level, and increase all other room types' odds for future rolls.
        foreach ((RoomType roomType, float odds) in _baseOdds)
        {
            if (resultRoomType == roomType)
            {
                // If this room type was rolled, reset its odds to base.
                _nonEventOdds[roomType] = odds;
            }
            else if (validRoomTypes.Contains(roomType))
            {
                float oddsIncrease = Hook.ModifyOddsIncreaseForUnrolledRoomType(climbState, roomType, odds);

                // If this room type was not rolled, increase its odds for next time.
                //
                // Note: we don't increase odds of invalid room types in here. If we did, their odds would become very
                // high after visiting a bunch of unknown rooms, which could cause weird side-effects if the room type
                // ever becomes valid again in the future (like if the relic that's banning room types were removed).
                _nonEventOdds[roomType] += oddsIncrease;
            }
        }

        return resultRoomType;
    }
}
