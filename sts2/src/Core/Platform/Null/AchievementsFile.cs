using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using MegaCrit.Sts2.Core.Achievements;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.Platform.Null;

public class AchievementsFile : ISaveSchema
{
    public List<Achievement> Unlocked { get; set; } = [];

    [SuppressMessage("ReSharper", "UnassignedGetOnlyAutoProperty", Justification = "Read via reflection")]
    public int SchemaVersion { get; set; }
}
