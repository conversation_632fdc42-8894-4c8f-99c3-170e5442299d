using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization;

namespace MegaCrit.Sts2.Core.Rewards;

// A set of rewards in which you are only allowed to select 1.
// Remaining rewards are removed automatically.
// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Original usage has been removed, but we'd like this functionality for the future.
public class LinkedRewardSet : Reward
{
    public override RewardType RewardType => RewardType.None;

    private static LocString HoverTipTitle => new("static_hover_tips", "LINKED_REWARDS.title");
    private static LocString HoverTipDesc => new("static_hover_tips", "LINKED_REWARDS.description");

    public static HoverTip HoverTip => new(HoverTipTitle, HoverTipDesc);

    private readonly List<Reward> _rewards;

    public IReadOnlyList<Reward> Rewards => _rewards.ToList();

    [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Original usage has been removed, but we'd like this functionality for the future.")]
    public LinkedRewardSet(List<Reward> rewards, Player player) : base(player)
    {
        _rewards = rewards;

        foreach (Reward reward in _rewards)
        {
            reward.ParentRewardSet = this;
        }
    }

    public override bool IsPopulated => _rewards.All(r => r.IsPopulated);

    public override async Task Populate()
    {
        foreach (Reward reward in _rewards)
        {
            await reward.Populate();
        }
    }

    public void RemoveReward(Reward reward)
    {
        _rewards.Remove(reward);
    }

    protected override Task<bool> OnSelect()
    {
        // no-op
        return Task.FromResult(true);
    }

    public override void OnSkipped()
    {
        foreach (Reward reward in _rewards)
        {
            reward.OnSkipped();
        }
    }

    public override void MarkContentAsSeen()
    {
        foreach (Reward reward in _rewards)
        {
            reward.MarkContentAsSeen();
        }
    }

    public override LocString Description => new("gameplay_ui", "COMBAT_REWARD_LINKED");
}
