using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Climbs.History;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Factories;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Relics;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.Rewards;

public class RelicReward : Reward
{
    public override RewardType RewardType => RewardType.Relic;

    private readonly RelicRarity _rarity = RelicRarity.None;

    private RelicModel? _relic;
    private bool _wasTaken;

    public RelicRarity Rarity => _rarity;
    public RelicModel? ClaimedRelic { get; private set; }

    // Note: if we get NPEs on these, it means we're incorrectly trying to display the reward somewhere before
    // populating it.
    public override LocString Description => _relic!.Title;
    protected override IEnumerable<IHoverTip> ExtraHoverTips => _relic!.HoverTips;

    public RelicReward(Player player) : base(player) { }

    public RelicReward(RelicModel relic, Player player) : base(player)
    {
        relic.AssertMutable();
        _relic = relic;
    }

    public RelicReward(RelicRarity rarity, Player player) : base(player)
    {
        _rarity = rarity;
    }

    public override bool IsPopulated => _relic != null;

    public override Task Populate()
    {
        if (_relic != null) return Task.CompletedTask;

        if (_rarity == RelicRarity.None)
        {
            _relic = RelicFactory.PullNextRelicFromFront(Player).ToMutable();
        }
        else
        {
            _relic = RelicFactory.PullNextRelicFromFront(Player, _rarity).ToMutable();
        }

        return Task.CompletedTask;
    }

    public override TextureRect CreateIcon()
    {
        TextureRect icon = new();
        icon.Texture = _relic!.BigIcon;
        icon.Material = (ShaderMaterial)PreloadManager.Cache.GetMaterial(NRelic.relicMatPath).Duplicate(true);
        _relic.UpdateTexture(icon);

        icon.SetAnchorsPreset(Control.LayoutPreset.FullRect);
        icon.ExpandMode = TextureRect.ExpandModeEnum.IgnoreSize;

        return icon;
    }

    protected override async Task<bool> OnSelect()
    {
        ClaimedRelic = await RelicCmd.Obtain(_relic!, Player);
        ClimbManager.Instance.RewardSynchronizer.SyncLocalObtainedRelic(_relic!);
        _wasTaken = true;
        return true;
    }

    public override void OnSkipped()
    {
        if (_wasTaken) return;

        Player.ClimbState.CurrentMapPointHistoryEntry!.GetEntry(LocalContext.NetId!.Value)
            .RelicChoices
            .Add(new ModelChoiceHistoryEntry(_relic!.Id, false));
        ClimbManager.Instance.RewardSynchronizer.SyncLocalSkippedRelic(_relic);
    }

    public override void MarkContentAsSeen()
    {
        SaveManager.Instance.MarkRelicAsSeen(_relic!);
    }
}
