using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Rewards;
using MegaCrit.Sts2.Core.Saves.Climbs;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Rooms;

public class CombatRoom : AbstractRoom
{
    public override RoomType RoomType => Encounter.RoomType;

    /// <summary>
    /// The mutable encounter that the player is facing in this room.
    /// </summary>
    public EncounterModel Encounter => CombatState.Encounter!;

    /// <summary>
    /// The state of the combat that is currently in progress in this room.
    /// </summary>
    public CombatState CombatState { get; }

    private bool _isPreFinished;
    public override bool IsPreFinished => _isPreFinished;

    // Amount of rewards to receive. Usually 1, but is reduced if enemies escape from combat.
    // This is set at the end of combat before rewards. It is deserialized in the case of a pre-finished combat room.
    private float _goldProportion = 1f;
    private readonly Dictionary<Player, List<Reward>> _extraRewards = [];

    public IReadOnlyDictionary<Player, List<Reward>> ExtraRewards => _extraRewards;
    public float GoldProportion => _goldProportion;

    public CombatRoom(EncounterModel encounter, IClimbState? climbState)
    {
        encounter.AssertMutable();
        CombatState = new CombatState(encounter, climbState, climbState?.Modifiers, climbState?.MultiplayerScalingModel);
    }

    public new static CombatRoom FromSerializable(SerializableRoom serializableRoom, IClimbState? climbState)
    {
        if (serializableRoom.ExtraRewards.Count > 0 && climbState == null)
        {
            throw new InvalidOperationException("Cannot load extra rewards without a climb state.");
        }

        EncounterModel encounter = ModelDb.GetById<EncounterModel>(serializableRoom.EncounterId!).ToMutable();
        CombatRoom room = new(encounter, climbState)
        {
            _goldProportion = serializableRoom.GoldProportion,
            _isPreFinished = serializableRoom.IsPreFinished
        };

        foreach ((ulong playerId, List<SerializableReward> serializableRewards) in serializableRoom.ExtraRewards)
        {
            // Safe to assume climbState is not null here because of the guard clause above.
            Player player = climbState!.GetPlayer(playerId)!;
            List<Reward> rewards = serializableRewards.Select(sr => Reward.FromSerializable(sr, player)).ToList();
            room._extraRewards.Add(player, rewards);
        }

        if (serializableRoom.IsPreFinished)
        {
            room.MarkPreFinished();
        }

        return room;
    }

    public override async Task Enter(IClimbState? climbState)
    {
        foreach (Player player in climbState?.Players ?? [])
        {
            CombatState.AddPlayer(player);
        }

        if (IsPreFinished)
        {
            await StartPreFinishedCombat();
        }
        else
        {
            await StartCombat(climbState);
        }
    }

    public override Task Exit(IClimbState? climbState)
    {
        CombatManager.Instance.Reset();

        // Remove all players from pre-finished combats.
        // In regular combats, CombatManager handles this, but we don't set it up for pre-finished combats.
        if (IsPreFinished)
        {
            foreach (Creature player in CombatState.PlayerCreatures.ToList())
            {
                CombatState.RemoveCreature(player);
            }
        }

        return Task.CompletedTask;
    }

    public override Task Resume(AbstractRoom _, IClimbState? climbState) => throw new NotImplementedException();

    public override SerializableRoom ToSerializable()
    {
        SerializableRoom result = base.ToSerializable();
        result.EncounterId = Encounter.Id;
        result.IsPreFinished = IsPreFinished;
        result.GoldProportion = _goldProportion;

        foreach ((Player player, List<Reward> rewards) in ExtraRewards)
        {
            result.ExtraRewards[player.NetId] = rewards.Select(r => r.ToSerializable()).ToList();
        }

        return result;
    }

    public void MarkPreFinished()
    {
        _isPreFinished = true;
    }

    public void AddExtraReward(Player player, Reward reward)
    {
        if (!ExtraRewards.ContainsKey(player))
        {
            _extraRewards.Add(player, []);
        }

        ExtraRewards[player].Add(reward);
    }

    private async Task StartCombat(IClimbState? climbState)
    {
        Encounter.GenerateMonstersWithSlots(CombatState.ClimbState);
        await PreloadManager.LoadRoomCombatAssets(Encounter);

        CombatState.ClimbState.CurrentMapPointHistoryEntry!.MonsterIds.Clear();

        foreach ((MonsterModel monster, string? slot) in Encounter.MonstersWithSlots)
        {
            monster.AssertMutable();
            CombatState.CreateCreature(monster, CombatSide.Enemy, slot);

            CombatState.ClimbState.CurrentMapPointHistoryEntry!.MonsterIds.Add(monster.Id);
        }

        NClimb.Instance?.SetCurrentRoom(NCombatRoom.Create(this));
        CombatManager.Instance.SetUpCombat(CombatState);

        if (climbState != null)
        {
            await Hook.AfterRoomEntered(climbState, this);
        }

        CombatManager.Instance.AfterCombatRoomLoaded();
    }

    public void OnCombatEnded()
    {
        _goldProportion = 1f - (float)CombatState.EscapedCreatures.Count / Encounter.MonstersWithSlots.Count;
    }

    private async Task StartPreFinishedCombat()
    {
        if (TestMode.IsOn) return;

        Player player = LocalContext.GetMe(CombatState)!;
        Encounter.GenerateMonstersWithSlots(CombatState.ClimbState);
        await PreloadManager.LoadRoomCombatFinishedAssets(Encounter);

        NClimb.Instance?.SetCurrentRoom(NFinishedCombatRoom.Create(CombatState));

        // Instead of fully setting up combat, we just set the player up with an empty combat state, so parts of the
        // combat room that depend on PlayerCombatState work as expected.
        player.ResetCombatState();

        ClimbManager.Instance.ActionExecutor.Unpause();

        IReadOnlyList<Reward> rewards = await RewardsCmd.GenerateForRoom(player, this);

        // RewardsCmd.Offer blocks until the rewards are done. We do not want to block loading on this, so run it
        // in parallel
        _ = TaskHelper.RunSafely(RewardsCmd.Offer(player, rewards, true));
    }
}
