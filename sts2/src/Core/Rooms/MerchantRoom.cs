using System;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Climbs.History;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Merchant;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Rooms;

public class MerchantRoom : AbstractRoom
{
    public override RoomType RoomType => RoomType.Shop;
    public MerchantInventory Inventory { get; private set; } = default!;

    public override async Task Enter(IClimbState? climbState)
    {
        Inventory = new MerchantInventory(LocalContext.GetMe(climbState)!);

        await PreloadManager.LoadRoomMerchantAssets();
        NClimb.Instance?.SetCurrentRoom(NMerchantRoom.Create(this, climbState?.Players ?? []));

        if (climbState != null)
        {
            await Hook.AfterRoomEntered(climbState, this);
        }
    }

    public override Task Exit(IClimbState? climbState)
    {
        if (TestMode.IsOn) return Task.CompletedTask;

        PlayerMapPointHistoryEntry? entry = climbState?.CurrentMapPointHistoryEntry?.GetEntry(LocalContext.NetId!.Value);
        if (entry == null) return Task.CompletedTask;

        foreach (MerchantCardEntry cardEntry in Inventory.CharacterCardEntries)
        {
            if (cardEntry.IsStocked)
            {
                entry.CardChoices.Add(new ModelChoiceHistoryEntry(cardEntry.CreationResult!.Card.Id, false));
            }
        }

        foreach (MerchantCardEntry cardEntry in Inventory.ColorlessCardEntries)
        {
            if (cardEntry.IsStocked)
            {
                entry.CardChoices.Add(new ModelChoiceHistoryEntry(cardEntry.CreationResult!.Card.Id, false));
            }
        }

        foreach (MerchantRelicEntry relicEntry in Inventory.RelicEntries)
        {
            if (relicEntry.IsStocked)
            {
                entry.RelicChoices.Add(new ModelChoiceHistoryEntry(relicEntry.Model!.Id, false));
            }
        }

        foreach (MerchantPotionEntry potionEntry in Inventory.PotionEntries)
        {
            if (potionEntry.IsStocked)
            {
                entry.PotionChoices.Add(new ModelChoiceHistoryEntry(potionEntry.Model!.Id, false));
            }
        }

        return Task.CompletedTask;
    }

    public override Task Resume(AbstractRoom _, IClimbState? climbState) => throw new NotImplementedException();
}
