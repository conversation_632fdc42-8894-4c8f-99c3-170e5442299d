using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Godot;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;

namespace MegaCrit.Sts2.Core.Saves.Climbs;

public static class SavedPropertiesTypeCache
{
    private static readonly Dictionary<Type, List<PropertyInfo>> _cache = [];

    /// For serializing over the network, to save space, we map the string names statically to integers
    private static readonly Dictionary<string, int> _propertyNameToNetIdMap = [];

    private static readonly List<string> _netIdToPropertyNameMap = [];

    public static int NetIdBitSize { get; private set; }

    static SavedPropertiesTypeCache()
    {
        List<Type> abstractModelTypes = Assembly.GetAssembly(typeof(AbstractModel))!
            .GetTypes()
            .Where(t => t.IsSubclassOf(typeof(AbstractModel)) && !t.IsAbstract)
            .ToList();

        abstractModelTypes.Sort((t1, t2) => string.CompareOrdinal(t1.Name, t2.Name));

        foreach (Type abstractModelType in abstractModelTypes)
        {
            CachePropertiesForType(abstractModelType);
        }

        NetIdBitSize = Mathf.CeilToInt(Math.Log2(_netIdToPropertyNameMap.Count));
    }

    private static void CachePropertiesForType(Type type)
    {
        List<PropertyInfo> jsonProperties = type
            .GetProperties(ReflectionHelper.allAccessLevels)
            .Where(p => p.GetCustomAttribute<SavedPropertyAttribute>() != null)
            .ToList();

        jsonProperties.Sort(CompareProperties);

        if (jsonProperties.Count > 0)
        {
            _cache[type] = jsonProperties;
        }

        foreach (PropertyInfo propertyInfo in jsonProperties)
        {
            if (!_propertyNameToNetIdMap.ContainsKey(propertyInfo.Name))
            {
                _propertyNameToNetIdMap[propertyInfo.Name] = _netIdToPropertyNameMap.Count;
                _netIdToPropertyNameMap.Add(propertyInfo.Name);
            }
        }
    }

    private static int CompareProperties(PropertyInfo p1, PropertyInfo p2)
    {
        SavedPropertyAttribute attr1 = p1.GetCustomAttribute<SavedPropertyAttribute>()!;
        SavedPropertyAttribute attr2 = p2.GetCustomAttribute<SavedPropertyAttribute>()!;

        if (attr1.order != attr2.order)
        {
            return attr1.order.CompareTo(attr2.order);
        }

        return string.CompareOrdinal(p1.Name, p2.Name);
    }

    public static int GetNetIdForPropertyName(string propertyName)
    {
        if (!_propertyNameToNetIdMap.TryGetValue(propertyName, out int netId))
        {
            throw new ArgumentException($"SavedProperty name {propertyName} could not be mapped to any net ID!");
        }

        return netId;
    }

    public static string GetPropertyNameForNetId(int netId)
    {
        if (netId < 0 || netId >= _netIdToPropertyNameMap.Count)
        {
            throw new ArgumentOutOfRangeException($"SavedProperty net ID {netId} is out of range! We have {_netIdToPropertyNameMap.Count} property names");
        }

        return _netIdToPropertyNameMap[netId];
    }

    public static List<PropertyInfo>? GetJsonPropertiesForType(Type t)
    {
        if (_cache.TryGetValue(t, out List<PropertyInfo>? properties))
        {
            return properties;
        }

        return null;
    }

    // For use in tests only
    public static void InjectTypeIntoCache(Type type)
    {
        CachePropertiesForType(type);
    }
}