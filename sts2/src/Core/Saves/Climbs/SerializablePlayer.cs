using System.Collections.Generic;
using System.Text.Json.Serialization;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;

namespace MegaCrit.Sts2.Core.Saves.Climbs;

public class SerializablePlayer : IPacketSerializable
{
    [JsonPropertyName("character_id")]
    public ModelId? CharacterId { get; set; }

    [JsonPropertyName("current_hp")]
    public int CurrentHp { get; set; }

    [JsonPropertyName("max_hp")]
    public int MaxHp { get; set; }

    [JsonPropertyName("max_energy")]
    public int MaxEnergy { get; set; }

    [JsonPropertyName("max_potion_slot_count")]
    public int MaxPotionSlotCount { get; set; } = Player.initialMaxPotionSlotCount;

    [JsonPropertyName("gold")]
    public int Gold { get; set; }

    [JsonPropertyName("base_orb_slot_count")]
    public int BaseOrbSlotCount { get; set; }

    [JsonPropertyName("net_id")]
    public ulong NetId { get; set; }

    [JsonPropertyName("deck")]
    [JsonSerializeCondition(SerializationCondition.SaveIfNotCollectionEmptyOrNull)]
    public List<SerializableCard> Deck { get; set; } = [];

    [JsonPropertyName("relics")]
    [JsonSerializeCondition(SerializationCondition.SaveIfNotCollectionEmptyOrNull)]
    public List<SerializableRelic> Relics { get; set; } = [];

    [JsonPropertyName("potions")]
    [JsonSerializeCondition(SerializationCondition.SaveIfNotCollectionEmptyOrNull)]
    public List<SerializablePotion> Potions { get; set; } = [];

    [JsonPropertyName("rng")]
    public SerializablePlayerRngSet Rng { get; set; } = default!;

    [JsonPropertyName("odds")]
    public SerializablePlayerOddsSet Odds { get; set; } = default!;

    [JsonPropertyName("relic_grab_bag")]
    public SerializableRelicGrabBag RelicGrabBag { get; set; } = default!;

    [JsonPropertyName("extra_fields")]
    public SerializableExtraPlayerFields ExtraFields { get; set; } = default!;

    #region Discoveries

    [JsonPropertyName("discovered_cards")]
    public uint DiscoveredCards { get; set; }

    [JsonPropertyName("discovered_enemies")]
    public uint DiscoveredEnemies { get; set; }

    [JsonPropertyName("discovered_epochs")]
    public uint DiscoveredEpochs { get; set; }

    [JsonPropertyName("discovered_potions")]
    public uint DiscoveredPotions { get; set; }

    [JsonPropertyName("discovered_relics")]
    public uint DiscoveredRelics { get; set; }

    #endregion

    public void Serialize(PacketWriter writer)
    {
        writer.WriteULong(NetId);
        writer.WriteModelEntry(CharacterId!);
        writer.WriteInt(CurrentHp, 16);
        writer.WriteInt(MaxHp, 16);
        writer.WriteInt(MaxEnergy, 8);
        writer.WriteInt(MaxPotionSlotCount, 3);
        writer.WriteInt(Gold, 16);
        writer.WriteInt(BaseOrbSlotCount, 8);
        writer.WriteList(Deck);
        writer.WriteList(Relics);
        writer.WriteList(Potions);
        writer.Write(Rng);
        writer.Write(Odds);
        writer.Write(RelicGrabBag);
        writer.Write(ExtraFields);
        writer.WriteUInt(DiscoveredCards, 16);
        writer.WriteUInt(DiscoveredEnemies, 8);
        writer.WriteUInt(DiscoveredEpochs, 8);
        writer.WriteUInt(DiscoveredPotions, 8);
        writer.WriteUInt(DiscoveredRelics, 16);
    }

    public void Deserialize(PacketReader reader)
    {
        NetId = reader.ReadULong();
        CharacterId = reader.ReadModelIdAssumingType<CharacterModel>();
        CurrentHp = reader.ReadInt(16);
        MaxHp = reader.ReadInt(16);
        MaxEnergy = reader.ReadInt(8);
        MaxPotionSlotCount = reader.ReadInt(3);
        Gold = reader.ReadInt(16);
        BaseOrbSlotCount = reader.ReadInt(8);
        Deck = reader.ReadList<SerializableCard>();
        Relics = reader.ReadList<SerializableRelic>();
        Potions = reader.ReadList<SerializablePotion>();
        Rng = reader.Read<SerializablePlayerRngSet>();
        Odds = reader.Read<SerializablePlayerOddsSet>();
        RelicGrabBag = reader.Read<SerializableRelicGrabBag>();
        ExtraFields = reader.Read<SerializableExtraPlayerFields>();
        DiscoveredCards = reader.ReadUInt(16);
        DiscoveredEnemies = reader.ReadUInt(8);
        DiscoveredEpochs = reader.ReadUInt(8);
        DiscoveredPotions = reader.ReadUInt(8);
        DiscoveredRelics = reader.ReadUInt(16);
    }
}
