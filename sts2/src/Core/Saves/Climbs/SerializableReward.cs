using System.Collections.Generic;
using System.Text.Json.Serialization;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;
using MegaCrit.Sts2.Core.Rewards;

namespace MegaCrit.Sts2.Core.Saves.Climbs;

public class SerializableReward : IPacketSerializable
{
    [JsonPropertyName("reward_type")]
    public RewardType RewardType { get; set; }

    // the reward model_id for this reward
    // the type of model Id depends on the type of reward
    [JsonPropertyName("quest_card")]
    public SerializableCard QuestCard { get; set; } = default!;

    // amount of gold if this is a gold reward
    [JsonPropertyName("gold_amount")]
    public int GoldAmount { get; set; }

    [JsonPropertyName("was_gold_stolen_back")]
    public bool WasGoldStolenBack { get; set; }

    // the below properties are for if we are serializing a card reward
    [JsonPropertyName("source")]
    public int Source { get; set; }

    [JsonPropertyName("option_count")]

    public int OptionCount { get; set; }


    public void Serialize(PacketWriter writer)
    {
        writer.WriteInt((int)RewardType);
        writer.Write(QuestCard);
        writer.WriteInt(GoldAmount);
        writer.WriteBool(WasGoldStolenBack);
        writer.WriteInt(Source);
        writer.WriteInt(OptionCount);
    }

    public void Deserialize(PacketReader reader)
    {
        RewardType = (RewardType)reader.ReadInt();
        QuestCard = reader.Read<SerializableCard>();
        GoldAmount = reader.ReadInt();
        WasGoldStolenBack = reader.ReadBool();
        Source = reader.ReadInt();
        OptionCount = reader.ReadInt();
    }
}
