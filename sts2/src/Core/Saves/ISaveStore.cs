using System.IO;
using System.Threading.Tasks;

namespace MegaCrit.Sts2.Core.Saves;

/// <summary>
/// An abstraction layer around how we store save data.
/// </summary>
public interface ISaveStore
{
    string? ReadFile(string path);
    void WriteFile(string path, string content);
    Task WriteFileAsync(string path, Stream contentStream);
    bool FileExists(string path);
    void DeleteFile(string path);
    void RenameFile(string sourcePath, string destinationPath);
    string[] GetFilesInDirectory(string directoryPath);
    void CreateDirectory(string directoryPath);
    void DeleteTemporaryFiles(string directoryPath);

    // Methods to get full paths for specific save files
    string GetFullPath(string filename);
}
