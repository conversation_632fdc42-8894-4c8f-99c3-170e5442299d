using System.Collections.Generic;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;

namespace MegaCrit.Sts2.Core.Saves.MapDrawing;

/// <summary>
/// A serializable form of the drawings that all players have drawn on the map.
/// </summary>
public class SerializableMapDrawings : IPacketSerializable
{
    public List<SerializablePlayerMapDrawings> drawings = [];

    public void Serialize(PacketWriter writer)
    {
        writer.WriteList(drawings);
    }

    public void Deserialize(PacketReader reader)
    {
        drawings = reader.ReadList<SerializablePlayerMapDrawings>();
    }
}
