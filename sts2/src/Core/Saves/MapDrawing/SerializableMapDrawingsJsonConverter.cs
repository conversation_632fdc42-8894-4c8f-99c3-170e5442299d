using System;
using System.IO;
using System.IO.Compression;
using System.Text.Json;
using System.Text.Json.Serialization;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;

namespace MegaCrit.Sts2.Core.Saves.MapDrawing;

/// <summary>
/// Converts a SerializableMapDrawings instance into a base64-encoded string that can be saved into the JSON save file.
/// When we serialize the map drawings to a multiplayer client, we serialize it directly as a binary blob. JSON can't
/// do this and so this class exists as an adapter.
/// The gains from gzipping the output haven't really been measured, so if we find that it's causing performance problems
/// we can remove it.
/// </summary>
public class SerializableMapDrawingsJsonConverter : JsonConverter<SerializableMapDrawings>
{
    public override SerializableMapDrawings Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        using MemoryStream outputStream = new();

        using (MemoryStream inputStream = new(Convert.FromBase64String(reader.GetString()!)))
        using (GZipStream zipStream = new(inputStream, CompressionMode.Decompress))
        {
            zipStream.CopyTo(outputStream);
        }

        PacketReader packetReader = new();
        packetReader.Reset(outputStream.ToArray());
        return packetReader.Read<SerializableMapDrawings>();
    }

    public override void Write(Utf8JsonWriter writer, SerializableMapDrawings mapDrawings, JsonSerializerOptions options)
    {
        PacketWriter packetWriter = new();
        packetWriter.Write(mapDrawings);

        using MemoryStream outputStream = new();

        using (GZipStream zipStream = new(outputStream, CompressionLevel.Fastest, true))
        {
            zipStream.Write(packetWriter.Buffer, 0, packetWriter.BytePosition);
        }

        string base64 = Convert.ToBase64String(outputStream.ToArray());
        writer.WriteStringValue(base64);
    }
}
