using System.Text.Json.Nodes;

namespace MegaCrit.Sts2.Core.Saves.Migrations.ClimbHistory;

/// <summary>
/// Initial migration establishing schema versioning for ClimbHistory.
/// Removes redundant text_key field from ancient_choice entries.
/// v0 files contain both title (LocString) and text_key fields, but the current schema only expects title.
/// </summary>
[Migration(typeof(Core.Climbs.ClimbHistory), 0, 1)]
public class ClimbHistoryV0ToV1 : MigrationBase<Core.Climbs.ClimbHistory>
{
    protected override void ApplyMigration(MigratingData saveData)
    {
        // Remove redundant text_key field from ancient_choice entries
        // The text_key field is located at: map_point_history[].player_stats[].ancient_choice[]
        if (!saveData.Has("map_point_history")) return;

        JsonNode? mapPointHistoryNode = saveData.GetRawNode("map_point_history");
        if (mapPointHistoryNode is not JsonArray mapPointHistory) return;

        foreach (JsonNode? actNode in mapPointHistory)
        {
            if (actNode is not JsonArray actArray) continue;
            foreach (JsonNode? mapPointNode in actArray)
            {
                if (mapPointNode is not JsonObject mapPoint ||
                    !mapPoint.TryGetPropertyValue("player_stats", out JsonNode? playerStatsNode)) continue;
                if (playerStatsNode is not JsonArray playerStats) continue;
                foreach (JsonNode? playerStatNode in playerStats)
                {
                    if (playerStatNode is not JsonObject playerStat ||
                        !playerStat.TryGetPropertyValue("ancient_choice", out JsonNode? ancientChoiceNode)) continue;
                    if (ancientChoiceNode is not JsonArray ancientChoices) continue;
                    foreach (JsonNode? choiceNode in ancientChoices)
                    {
                        if (choiceNode is JsonObject choice)
                        {
                            // Remove the redundant text_key field
                            choice.Remove("text_key");
                        }
                    }
                }
            }
        }
    }
}
