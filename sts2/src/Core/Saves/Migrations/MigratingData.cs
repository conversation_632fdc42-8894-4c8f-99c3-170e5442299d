using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Nodes;
using MegaCrit.Sts2.Core.Models;

namespace MegaCrit.Sts2.Core.Saves.Migrations;

/// <summary>
/// A mutable JSON representation of the data being migrated. It serves as a loosely typed mutable blob we can
/// deserialize into and modify with our migration classes until finally parsing into the latest structured save schema.
/// This implementation uses System.Text.Json's JsonNode instead of a custom implementation.
/// </summary>
public class MigratingData
{
    private readonly JsonObject _data;

    /// <summary>
    /// Creates a JSON object from a JsonDocument.
    /// </summary>
    /// <param name="document">The JsonDocument to convert</param>
    public MigratingData(JsonDocument document)
    {
        string json = JsonSerializer.Serialize(document);
        _data = (JsonObject)JsonNode.Parse(json)!;
    }

    /// <summary>
    /// Creates a JSON object from a JSON string.
    /// </summary>
    /// <param name="json">The JSON string</param>
    public MigratingData(string json)
    {
        _data = (JsonObject)JsonNode.Parse(json)!;
    }

    /// <summary>
    /// Creates a JSON object from a JsonObject.
    /// </summary>
    /// <param name="jsonObject">The JsonObject</param>
    public MigratingData(JsonObject jsonObject)
    {
        _data = jsonObject;
    }

    /// <summary>
    /// Parses this JsonObject to a strongly typed object.
    /// </summary>
    /// <typeparam name="T">The type to convert to</typeparam>
    /// <returns>An instance of the specified type</returns>
    public T ToObject<T>() where T : new()
    {
        string json = _data.ToJsonString();
        T? result = JsonSerializer.Deserialize<T>(json, JsonSerializationUtility.serializerOptions);
        return result != null ? result : new T();
    }

    /// <summary>
    /// Gets or sets a property in the JSON object.
    /// </summary>
    /// <param name="key">The property name</param>
    /// <returns>The property value</returns>
    public object? this[string key]
    {
        get => _data.TryGetPropertyValue(key, out JsonNode? node) ? ConvertJsonNodeToObject(node) : null;
        set => _data[key] = value != null ? JsonValue.Create(value) : null;
    }

    /// <summary>
    /// Removes a property from the JSON object.
    /// </summary>
    /// <param name="key">The property name to remove</param>
    public void Remove(string key)
    {
        _data.Remove(key);
    }

    /// <summary>
    /// Renames a property in the JSON object.
    /// </summary>
    /// <param name="oldKey">The current property name</param>
    /// <param name="newKey">The new property name</param>
    public void Rename(string oldKey, string newKey)
    {
        if (_data.TryGetPropertyValue(oldKey, out JsonNode? value))
        {
            // Clone the value before assigning it to the new key to avoid parent issues
            _data[newKey] = value?.DeepClone(); // if the value is null, cloning will cause a runtime error
            _data.Remove(oldKey);
        }
        else
        {
            throw new MigrationException($"Cannot rename a key that doesn't exist. Key={oldKey}");
        }
    }

    /// <summary>
    /// Checks if the JSON object has a property.
    /// </summary>
    /// <param name="key">The property name</param>
    /// <returns>True if the property exists</returns>
    public bool Has(string key) => _data.ContainsKey(key);

    /// <summary>
    /// Helper method to convert JsonNode to .NET objects
    /// </summary>
    private static object? ConvertJsonNodeToObject(JsonNode? node)
    {
        return node switch
        {
            null => null,
            JsonObject => node.Deserialize<Dictionary<string, object>>(),
            JsonArray => node.Deserialize<List<object>>(),
            JsonValue jsonValue when jsonValue.TryGetValue(out string? strVal) => strVal,
            JsonValue jsonValue when jsonValue.TryGetValue(out int intVal) => intVal,
            JsonValue jsonValue when jsonValue.TryGetValue(out long longVal) => longVal,
            JsonValue jsonValue when jsonValue.TryGetValue(out double doubleVal) => doubleVal,
            JsonValue jsonValue when jsonValue.TryGetValue(out bool boolVal) => boolVal,
            // If none of the specific types match, return the raw JSON
            JsonValue jsonValue => jsonValue.ToString(),
            _ => null
        };
    }

    /// <summary>
    /// Gets a property as a specific type.
    /// </summary>
    /// <typeparam name="T">The type to convert to</typeparam>
    /// <param name="key">The property name</param>
    /// <returns>The property value as the specified type</returns>
    public T GetAs<T>(string key)
    {
        if (!_data.TryGetPropertyValue(key, out JsonNode? node) || node == null)
        {
            throw new MigrationException($"Cannot get value of key={key}");
        }

        try
        {
            Type targetType = typeof(T);

            // Handle nested objects
            if (targetType == typeof(MigratingData) && node is JsonObject jObj)
            {
                return (T)(object)new MigratingData(jObj);
            }
            // Handle JSON arrays
            else if (targetType == typeof(List<MigratingData>) && node is JsonArray jArray)
            {
                List<MigratingData> result = [];
                foreach (JsonNode? item in jArray)
                {
                    if (item is JsonObject itemObj)
                    {
                        result.Add(new MigratingData(itemObj));
                    }
                    else
                    {
                        throw new MigrationException($"Cannot convert array item to MigratingData: {item}");
                    }
                }

                return (T)(object)result;
            }

            // Handle ModelId conversion from string to ModelId
            if (targetType == typeof(ModelId))
            {
                string modelIdStr = node.GetValue<string>();
                if (string.IsNullOrEmpty(modelIdStr))
                    return (T)(object)ModelId.none;
                return (T)(object)ModelId.Deserialize(modelIdStr);
            }

            // Primitive types
            if (targetType == typeof(string))
                return (T)(object)node.GetValue<string>();
            if (targetType == typeof(int))
                return (T)(object)node.GetValue<int>();
            if (targetType == typeof(long))
                return (T)(object)node.GetValue<long>();
            if (targetType == typeof(double))
                return (T)(object)node.GetValue<double>();
            if (targetType == typeof(bool))
                return (T)(object)node.GetValue<bool>();
            if (targetType == typeof(DateTime))
                return (T)(object)node.GetValue<DateTime>();

            // Generic deserialization
            return node.Deserialize<T>() ??
                throw new MigrationException($"Unable to convert {key} to {typeof(T).Name}");
        }
        catch (Exception ex) when (ex is not MigrationException)
        {
            throw new MigrationException($"Cannot convert value of key={key} to {typeof(T)}: {ex.Message}");
        }
    }

    /// <summary>
    /// Gets a string value from the JSON object.
    /// </summary>
    /// <param name="key">The property name</param>
    /// <returns>The string value</returns>
    public string GetString(string key) => GetAs<string>(key);

    /// <summary>
    /// Gets a boolean value from the JSON object.
    /// </summary>
    /// <param name="key">The property name</param>
    /// <returns>The boolean value</returns>
    public bool GetBool(string key) => GetAs<bool>(key);

    /// <summary>
    /// Gets an integer value from the JSON object.
    /// </summary>
    /// <param name="key">The property name</param>
    /// <returns>The integer value</returns>
    public int GetInt(string key) => GetAs<int>(key);

    /// <summary>
    /// Gets a nested JSON object from the JSON object.
    /// </summary>
    /// <param name="key">The property name</param>
    /// <returns>The nested JSON object</returns>
    public MigratingData GetObject(string key) => GetAs<MigratingData>(key);

    /// <summary>
    /// Sets a nested object property in the JSON object.
    /// </summary>
    /// <param name="key">The property name</param>
    /// <param name="value">The MigratingData object to set</param>
    public void SetObject(string key, MigratingData value)
    {
        _data[key] = value._data.DeepClone();
    }

    /// <summary>
    /// Gets a list of objects from the JSON array.
    /// </summary>
    /// <param name="key">The property name</param>
    /// <returns>The list of objects</returns>
    public List<MigratingData> GetList(string key) => GetAs<List<MigratingData>>(key);

    /// <summary>
    /// Gets the raw JsonNode for a property, useful for direct manipulation in migrations.
    /// </summary>
    /// <param name="key">The property name</param>
    /// <returns>The JsonNode or null if not found</returns>
    public JsonNode? GetRawNode(string key)
    {
        return _data.TryGetPropertyValue(key, out JsonNode? node) ? node : null;
    }

    /// <summary>
    /// Sets an array property in the JSON object.
    /// </summary>
    /// <typeparam name="T">The type of items in the array</typeparam>
    /// <param name="key">The property name</param>
    /// <param name="items">The collection of items to set</param>
    public void SetList<T>(string key, IEnumerable<T> items)
    {
        // Special handling for MigratingData collections
        if (typeof(T) == typeof(MigratingData))
        {
            JsonArray array = [];
            foreach (T item in items)
            {
                if (item is MigratingData migratingData)
                {
                    // Add a deep clone of the JsonObject
                    array.Add(migratingData._data.DeepClone());
                }
            }

            _data[key] = array;
        }
        else
        {
            // Regular serialization for other types
            string json = JsonSerializer.Serialize(items);
            _data[key] = JsonNode.Parse(json);
        }
    }
}
