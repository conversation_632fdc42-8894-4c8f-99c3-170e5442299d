using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using MegaCrit.Sts2.Core.Logging;

namespace MegaCrit.Sts2.Core.Saves.Migrations;

/// <summary>
/// Registry for save migrations.
/// This class will eventually be auto-generated by a source generator.
/// </summary>
/// <remarks>
/// IMPORTANT: This is a temporary reflection-based implementation.
/// Eventually, this class will be generated at compile time by a source generator
/// that scans for all classes with the [Migration] attribute.
/// </remarks>
public class MigrationRegistry
{
    public Dictionary<Type, List<IMigration>> Migrations { get; } = new();

    /// <summary>
    /// Registers all migrations with the migration manager.
    /// </summary>
    /// <param name="manager">The migration manager to register migrations with</param>
    public void RegisterAllMigrations(MigrationManager manager)
    {
        // TODO: Replace this reflection-based approach with a source generator
        // for better performance and compile-time validation

        try
        {
            // Get the assembly containing the migration types (the core assembly)
            // Use the IMigration type to ensure we get the correct assembly
            Assembly assembly = typeof(IMigration).Assembly;
            Type migrationType = typeof(IMigration);

            // Find all concrete implementation classes of IMigration
            List<Type> migrationTypes = assembly.GetTypes()
                .Where(t => migrationType.IsAssignableFrom(t) && t is { IsInterface: false, IsAbstract: false })
                .ToList();

            // Create instances and register each migration
            foreach (Type type in migrationTypes)
            {
                try
                {
                    object? typeInstance = Activator.CreateInstance(type);
                    if (typeInstance is IMigration migration)
                    {
                        manager.RegisterMigration(migration);
                        Log.Info($"Registered migration: {type.Name}");
                    }
                    else
                    {
                        Log.Error($"Failed to instantiate migration {type.Name}: Created instance is not an IMigration");
                    }
                }
                catch (Exception e)
                {
                    Log.Error($"Failed to instantiate migration {type.Name}: {e.Message}");
                }
            }

            Log.Info($"Registered {migrationTypes.Count} migrations");
        }
        catch (Exception e)
        {
            Log.Error($"Error registering migrations: {e.Message}");
        }
    }
}
