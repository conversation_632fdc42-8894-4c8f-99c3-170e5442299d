using MegaCrit.Sts2.Core.Logging;

namespace MegaCrit.Sts2.Core.Saves.Migrations.SettingsSave;

/// <summary>
/// Migration from schema v1 to v2 for SettingsSave.
/// </summary>
[Migration(typeof(Saves.SettingsSave), 1, 2)]
public class SettingsSaveV1ToV2 : MigrationBase<Saves.SettingsSave>
{
    protected override void ApplyMigration(MigratingData saveData)
    {
        Log.Info("Migration: removing legacy 'screenshake'");
        saveData.Remove("screenshake");
    }
}
