using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using MegaCrit.Sts2.Core.Climbs.History;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;
using MegaCrit.Sts2.Core.Platform;
using MegaCrit.Sts2.Core.Saves.Climbs;
using MegaCrit.Sts2.Core.Saves.MapDrawing;

namespace MegaCrit.Sts2.Core.Saves;

public class SerializableClimb : ISaveSchema, IPacketSerializable
{
    /// <summary>
    /// The schema version of this save.
    /// </summary>
    [JsonPropertyName("schema_version")]
    public int SchemaVersion { get; set; }

    [JsonPropertyName("acts")]
    [JsonSerializeCondition(SerializationCondition.SaveIfNotCollectionEmptyOrNull)]
    public List<SerializableActModel> Acts { get; set; } = [];

    [JsonPropertyName("modifiers")]
    public List<SerializableModifier> Modifiers { get; set; } = [];

    /// <summary>
    /// This is null if the run is not a daily.
    /// Otherwise, it contains the date from the time server of the daily.
    /// </summary>
    [JsonPropertyName("dailyTime")]
    [JsonSerializeCondition(SerializationCondition.SaveIfNotTypeDefault)]
    public DateTimeOffset? DailyTime { get; set; }

    [JsonPropertyName("current_act_index")]
    public int CurrentActIndex { get; set; }

    [JsonPropertyName("events_seen")]
    [JsonSerializeCondition(SerializationCondition.SaveIfNotCollectionEmptyOrNull)]
    public List<ModelId> EventsSeen { get; set; } = [];

    [JsonPropertyName("pre_finished_room")]
    public SerializableRoom? PreFinishedRoom { get; set; }

    [JsonPropertyName("odds")]
    public SerializableClimbOddsSet SerializableOdds { get; set; } = default!;

    [JsonPropertyName("shared_relic_grab_bag")]
    public SerializableRelicGrabBag SerializableSharedRelicGrabBag { get; set; } = default!;

    [JsonPropertyName("players")]
    public List<SerializablePlayer> Players { get; set; } = default!;

    [JsonPropertyName("rng")]
    public SerializableClimbRngSet SerializableRng { get; set; } = default!;

    [JsonPropertyName("visited_map_coords")]
    [JsonSerializeCondition(SerializationCondition.SaveIfNotCollectionEmptyOrNull)]
    public List<MapCoord> VisitedMapCoords { get; set; } = [];

    [JsonPropertyName("map_point_history")]
    [JsonSerializeCondition(SerializationCondition.SaveIfNotCollectionEmptyOrNull)]
    public List<List<MapPointHistoryEntry>> MapPointHistory { get; set; } = [];

    /// <summary>
    /// When this save was created or last updated
    /// </summary>
    [JsonPropertyName("save_time")]
    public long SaveTime { get; set; }

    [JsonPropertyName("start_time")]
    public long StartTime { get; set; }

    [JsonPropertyName("climb_time")]
    public long ClimbTime { get; set; }

    [JsonPropertyName("ascension")]
    public int Ascension { get; set; }

    [JsonPropertyName("spawned_neow")]
    public bool SpawnedNeow { get; set; }

    // This is primarily here for climb history purposes and should not be used in loading the climb
    [JsonPropertyName("platform_type")]
    public PlatformType PlatformType { get; set; }

    [JsonConverter(typeof(SerializableMapDrawingsJsonConverter))]
    [JsonPropertyName("map_drawings")]
    public SerializableMapDrawings? MapDrawings { get; set; }

    [JsonPropertyName("extra_fields")]
    public SerializableExtraClimbFields ExtraFields { get; set; } = new();

    public void Serialize(PacketWriter writer)
    {
        writer.WriteInt(SchemaVersion);
        writer.WriteList(Acts);
        writer.WriteList(Modifiers);

        writer.WriteBool(DailyTime != null);
        if (DailyTime != null)
        {
            writer.WriteLong(DailyTime.Value.ToUnixTimeSeconds());
        }

        writer.WriteInt(CurrentActIndex, 4);
        writer.WriteModelEntriesInList(EventsSeen);

        writer.WriteBool(PreFinishedRoom != null);
        if (PreFinishedRoom != null)
        {
            writer.Write(PreFinishedRoom);
        }

        writer.Write(SerializableOdds);
        writer.WriteList(Players);
        writer.Write(SerializableRng);
        writer.Write(SerializableSharedRelicGrabBag);
        writer.WriteList(VisitedMapCoords);

        writer.WriteInt(MapPointHistory.Count);
        foreach (List<MapPointHistoryEntry> entries in MapPointHistory)
        {
            writer.WriteList(entries);
        }

        writer.WriteLong(SaveTime);
        writer.WriteLong(StartTime);
        writer.WriteLong(ClimbTime);
        writer.WriteInt(Ascension, 8);

        writer.WriteBool(MapDrawings != null);
        if (MapDrawings != null)
        {
            writer.Write(MapDrawings);
        }

        writer.WriteBool(SpawnedNeow);
        writer.Write(ExtraFields);
    }

    public void Deserialize(PacketReader reader)
    {
        SchemaVersion = reader.ReadInt();
        Acts = reader.ReadList<SerializableActModel>();
        Modifiers = reader.ReadList<SerializableModifier>();

        if (reader.ReadBool())
        {
            DailyTime = DateTimeOffset.FromUnixTimeSeconds(reader.ReadLong());
        }

        CurrentActIndex = reader.ReadInt(4);
        EventsSeen = reader.ReadModelIdListAssumingType<EventModel>();

        bool hasPreFinishedRoom = reader.ReadBool();
        if (hasPreFinishedRoom)
        {
            PreFinishedRoom = reader.Read<SerializableRoom>();
        }

        SerializableOdds = reader.Read<SerializableClimbOddsSet>();
        Players = reader.ReadList<SerializablePlayer>();
        SerializableRng = reader.Read<SerializableClimbRngSet>();
        SerializableSharedRelicGrabBag = reader.Read<SerializableRelicGrabBag>();
        VisitedMapCoords = reader.ReadList<MapCoord>();

        int mapPointHistoryCount = reader.ReadInt();
        for (int i = 0; i < mapPointHistoryCount; i++)
        {
            List<MapPointHistoryEntry> entries = reader.ReadList<MapPointHistoryEntry>();
            MapPointHistory.Add(entries);
        }

        SaveTime = reader.ReadLong();
        StartTime = reader.ReadLong();
        ClimbTime = reader.ReadLong();
        Ascension = reader.ReadInt(8);

        bool hasMapDrawings = reader.ReadBool();
        if (hasMapDrawings)
        {
            MapDrawings = reader.Read<SerializableMapDrawings>();
        }

        SpawnedNeow = reader.ReadBool();
        ExtraFields = reader.Read<SerializableExtraClimbFields>();
    }
}
