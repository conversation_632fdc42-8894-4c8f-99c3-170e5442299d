using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using MegaCrit.Sts2.Core.Entities.Rngs;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;

namespace MegaCrit.Sts2.Core.Saves;

public class SerializablePlayerRngSet : IPacketSerializable
{
    [JsonPropertyName("seed")]
    public uint Seed { get; set; }

    [JsonPropertyName("counters")]
    public Dictionary<PlayerRngType, int> Counters { get; set; } = [];

    public void Serialize(PacketWriter writer)
    {
        writer.WriteUInt(Seed);

        List<KeyValuePair<PlayerRngType, int>> countersList = Counters.ToList();
        countersList.Sort((p1, p2) => p1.Key.CompareTo(p2.Key));

        writer.WriteInt(countersList.Count, 8);

        foreach (KeyValuePair<PlayerRngType, int> counter in countersList)
        {
            writer.WriteInt((int)counter.Key);
            writer.WriteInt(counter.Value);
        }
    }

    public void Deserialize(PacketReader reader)
    {
        Seed = reader.ReadUInt();

        int counterCount = reader.ReadInt(8);

        for (int i = 0; i < counterCount; i++)
        {
            PlayerRngType key = (PlayerRngType)reader.ReadInt();
            int value = reader.ReadInt();
            Counters[key] = value;
        }
    }
}
