namespace MegaCrit.Sts2.Core.Settings;

/// <summary>
/// Enum for various available aspect ratio settings
/// </summary>
public enum AspectRatioSetting
{
    None = 0, // Don't use
    FourByThree = 1, // iPad and very old monitors
    SixteenByTen = 2, // Many modern desktop setups
    SixteenByNine = 3, // Standard television, most monitors, Nintendo Switch. 1080p, 1440p are 16:9
    TwentyOneByNine = 4, // Ultra-wide
    Auto = 5
}
