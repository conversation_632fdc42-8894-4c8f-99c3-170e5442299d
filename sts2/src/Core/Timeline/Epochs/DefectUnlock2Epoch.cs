using System.Collections.Generic;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Nodes.Screens.Timeline;

namespace MegaCrit.Sts2.Core.Timeline.Epochs;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class DefectUnlock2Epoch : EpochModel
{
    public override EpochEra Era => EpochEra.Blight2Contagion;
    public override int EraPosition => 3;

    public override void QueueUnlocks()
    {
        List<RelicModel> unlockedRelics = [];
        unlockedRelics.Add(ModelDb.Relic<Circlet>());
        unlockedRelics.Add(ModelDb.Relic<Circlet>());
        unlockedRelics.Add(ModelDb.Relic<Circlet>());
        NTimelineScreen.Instance!.QueueRelicUnlock(unlockedRelics);
    }
}
