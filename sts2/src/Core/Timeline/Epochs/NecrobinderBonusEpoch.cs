using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.Screens.Timeline;

namespace MegaCrit.Sts2.Core.Timeline.Epochs;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class NecrobinderBonusEpoch : EpochModel
{
    public override EpochEra Era => EpochEra.Banana;
    public override int EraPosition => 0;

    public override void QueueUnlocks()
    {
        LocString unlockText = new("epochs", $"{Id}.unlock");
        NTimelineScreen.Instance!.QueueMiscUnlock($"[center]{unlockText.GetFormattedText()}[/center]");
    }
}
