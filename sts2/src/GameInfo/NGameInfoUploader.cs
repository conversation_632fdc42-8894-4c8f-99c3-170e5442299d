using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization.Metadata;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Encounters;
using MegaCrit.Sts2.Core.Saves.Climbs;
using MegaCrit.Sts2.GameInfo.Objects;
using Environment = System.Environment;
using HttpClient = System.Net.Http.HttpClient;

namespace MegaCrit.Sts2.GameInfo;

// Uploads GameInfo objects to the feedbackbot for use in discord.
// Called from the res://src/GameInfo/GameInfoUploader.tscn
public partial class NGameInfoUploader : Node
{
    public static bool IsRunning { get; private set; }

#if DEBUG
    [GeneratedRegex(@"\[.*?\]")]
    private static partial Regex BbCodeRegex();

    [GeneratedRegex(@"\s(\d+)\s")]
    private static partial Regex IntegerMatchRegex();

    [GeneratedRegex(@"\d+")]
    private static partial Regex IntegerNoSpacingRegex();

    [GeneratedRegex(@"\[img\]res://images/packed/sprite_fonts/(\w+)_energy_icon\.png\[/img\]", RegexOptions.Compiled)]
    private static partial Regex EnergyIconRegex();

    [GeneratedRegex(@"(\[img\]res://.*?star_icon\.png\[/img\])", RegexOptions.Compiled)]
    private static partial Regex StarIconRegex();

    private const string _starEmoji = "<:star_cost:1273406481529831529>";

    // Used as an entrypoint for the CI to upload game info objects
    public override void _Ready()
    {
        int exitCode = 0;
        try
        {
            Upload();
        }
        catch (Exception e)
        {
            exitCode = 1;
            GD.PrintErr($"Error uploading game info: {e}");
        }

        GetTree().Quit(exitCode);
    }

    public override void _EnterTree()
    {
        IsRunning = true;
    }

    public override void _ExitTree()
    {
        IsRunning = false;
    }

    private static string Stylize(string str)
    {
        str = str.Replace("\n", "\n> ");
        // Replace color tags with bold markdown
        str = str.Replace("[gold]", "**").Replace("[/gold]", "**");
        str = str.Replace("[blue]", "**").Replace("[/blue]", "**");
        str = str.Replace("[green]", "**").Replace("[/green]", "**");

        // Replace energy icons
        str = EnergyIconRegex().Replace(str, match => DiscordEmoji(match.Groups[1].Value));

        // Replace star icons
        str = StarIconRegex().Replace(str, _starEmoji);

        // Handle integer formatting (assuming you want to keep this from your original code)
        str = IntegerMatchRegex().Replace(str, " **$1** ");

        return str;
    }

    private static string DiscordEmoji(string name)
    {
        return name.ToUpper() switch
        {
            "IRONCLAD" => "<:red_energy2:1251298030091501578>",
            "DEFECT" => "<:blue_energy2:1251298021912744029>",
            "SILENT" => "<:green_energy2:1251298024777449564>",
            "REGENT" => "<:orange_energy2:1273406648953737327>",
            "NECROBINDER" => "<:pink_energy2:1251298027696820266>",
            "WATCHER" => "<:purple_energy2:1251298029080936526>",
            _ => "<:colorless_energy2:1251298023451918447>"
        };
    }

    private static CardInfo ToCardInfo(CardModel card, bool isUpgraded)
    {
        if (isUpgraded)
        {
            // If we're getting the cardInfo for an upgraded card we need to update all its values first
            card.UpgradeInternal();
        }

        int baseDamage = (int)card.DynamicVars.Values.OfType<DamageVar>().Sum(v => v.BaseValue);

        string description = isUpgraded ? card.GetDescriptionForUpgradePreview() : card.GetDescriptionForPile(PileType.None);

        string title = card.Title;
        string rarity = card.Rarity.ToString();
        string type = card.Type.ToString();
        string color = card.Pool.EnergyColorName;
        int energy = card.BaseEnergyCost;
        int starCost = card.BaseStarCost;

        // specific config request by Jake R
        if (card.HasEnergyCostX)
        {
            energy = -2;
        }

        // specific config request by Jake R
        if (card.Keywords.Contains(CardKeyword.Unplayable))
        {
            energy = -1;
        }

        string starCostStr = starCost > 0 ? $"`{starCost}` {_starEmoji}" : "";

        string botText = $"**{title}**\n`{rarity}`\t`{type}`\t`{energy}` " +
            $"{DiscordEmoji(color)} {starCostStr}\t`{color}`\n{FormatDiscordDesc(description)}";

        return new CardInfo
        {
            BotKeyword = "card",
            BotText = botText,
            Name = title,
            Id = card.Id,
            Upgraded = isUpgraded,
            Rarity = rarity,
            Type = type,
            Text = description,
            Energy = energy,
            HasArt = card.HasPortrait,
            HasJokeArt = card.HasBetaPortrait,
            BaseDamage = baseDamage,
            Color = color
        };
    }

    private static List<CardInfo> CollectCardInfo()
    {
        // It's ok to use CardModel.ToMutable() without a CardCreator here because we're just grabbing cards to upload
        // to the GameInfo service, not using them in a climb/combat.
        IEnumerable<CardModel> cards = ModelDb.Cards.Where(c => c.ShouldShowInCardLibrary).Select(c => c.ToMutable());
        List<CardInfo> cardInfos = [];
        foreach (CardModel card in cards)
        {
            CardInfo cardInfo = ToCardInfo(card, isUpgraded: false);
            cardInfos.Add(cardInfo);

            // If the card can't be upgraded, skip to the next card
            if (card.MaxUpgradeLevel <= 0) continue;

            // Add the upgraded cardInfo
            CardInfo upgradedCardInfo = ToCardInfo(card, isUpgraded: true);
            cardInfos.Add(upgradedCardInfo);
        }

        return cardInfos;
    }

    private static string RelicToColor(RelicModel relic)
    {
        List<CharacterModel> characters = ModelDb.Characters
            .Where(c => c.StartingRelics.Contains(relic) || c.RelicPool.Relics.Contains(relic))
            .ToList();

        return characters.Count == 0 ? "shared" : characters[0].CardPool.EnergyColorName;
    }

    private static List<RelicInfo> CollectRelicInfo()
    {
        List<RelicInfo> relicInfos = [];

        foreach (RelicModel relic in ModelDb.Relics)
        {
            string text = relic.DynamicDescription.GetFormattedText();
            string title = relic.Title.GetFormattedText();
            string botText = $"**{title}**\n`{relic.Rarity}` " +
                $"`{RelicToColor(relic)}`\n{FormatDiscordDesc(text)}";

            RelicInfo relicInfo = new()
            {
                BotKeyword = "relic",
                BotText = botText,
                Id = relic.Id,
                Name = title,
                Rarity = relic.Rarity.ToString(),
                Color = RelicToColor(relic),
                Text = text
            };
            relicInfos.Add(relicInfo);
        }

        return relicInfos;
    }

    /// <summary>
    /// Formats a description for Discord (to standardize how we do it and avoid inconsistencies).
    /// Quote-blocks the description, replaces bbcode with appropriate formatting, and replaces icons with a star with
    /// Discord icon.
    /// </summary>
    private static string FormatDiscordDesc(string text)
    {
        return "> " + Stylize(text);
    }

    private static string ReplaceAllBbCode(string input, string replacement = "**")
    {
        return BbCodeRegex().Replace(input, replacement);
    }

    private static IEnumerable<AncientChoiceInfo> CollectAncientChoiceInfo()
    {
        List<AncientChoiceInfo> ancientChoices = [];

        foreach (AncientEventModel ancient in ModelDb.AncientEvents().Distinct())
        {
            // Add all the ancient's choice options
            foreach (EventOption option in ancient.AllPossibleOptions)
            {
                string title = option.Title.GetFormattedText();
                LocString desc = option.Description;
                ancient.DynamicVars.AddTo(desc);
                string text = desc.GetFormattedText();

                AncientChoiceInfo choice = new()
                {
                    BotKeyword = "ancient",
                    BotText = $"**{title}**\n{FormatDiscordDesc(text)}", // the feedbackbot formatted text for the choice
                    Ancient = ancient.Id.Entry,
                    Id = option.TextKey.Split('.')[^1], // choice id
                    Name = title, // human-readable name for the choice
                    Text = text // text for the choice
                };

                ancientChoices.Add(choice);
            }
        }

        return ancientChoices;
    }

    private static string PotionToColor(PotionModel potion)
    {
        List<CharacterModel> characters = ModelDb.Characters
            .Where(c => c.PotionPool.Potions.Contains(potion))
            .ToList();

        return characters.Count == 0 ? "All" : characters[0].CardPool.EnergyColorName;
    }

    private static List<PotionInfo> CollectPotionInfo()
    {
        List<PotionInfo> potionInfos = [];
        foreach (PotionModel potion in ModelDb.AllPotions)
        {
            string text = potion.DynamicDescription.GetFormattedText();
            string title = potion.Title.GetFormattedText();

            PotionInfo potionInfo = new()
            {
                BotKeyword = "potion",
                BotText = $"**{title}**\n`{potion.Rarity}`\n{FormatDiscordDesc(text)}",
                Text = text,
                Name = title,
                Id = potion.Id,
                Rarity = potion.Rarity.ToString(),
                Color = PotionToColor(potion)
            };

            potionInfos.Add(potionInfo);
        }

        return potionInfos;
    }

    private static List<EncounterInfo> CollectEncounterInfo()
    {
        IEnumerable<ActModel> acts = ModelDb.Acts;
        List<EncounterInfo> encounterInfos = [];

        foreach (ActModel act in acts)
        {
            string actTitle = act.Title.GetFormattedText();

            AddEncounters(act.WeakEncounters, actTitle, "Weak");
            AddEncounters(act.RegularEncounters, actTitle, "Normal");
            AddEncounters(act.EliteEncounters, actTitle, "Elite");
            AddEncounters(act.BossEncounters, actTitle, "Boss");
        }

        return encounterInfos;

        // Embedded helper method
        void AddEncounters(IEnumerable<EncounterModel> encounters, string actTitle, string tier)
        {
            foreach (EncounterModel encounter in encounters.Select(e => e.ToMutable()))
            {
                string title = encounter.Title.GetFormattedText();
                encounter.DebugRandomizeRng();

                // use full qualified import name to avoid getting the wrong id since we lose type-safety
                List<ModelId> ignoreEncounters = [ModelDb.GetId<TestSubjectBoss>()];

                string monsterList;
                if (ignoreEncounters.Contains(encounter.Id))
                {
                    // if we can't get the monster names for that encounter, let's just ignore
                    monsterList = "[CLASSIFIED]";
                }
                else
                {
                    encounter.GenerateMonstersWithSlots(NullClimbState.Instance);
                    IEnumerable<MonsterModel> monsters = encounter.MonstersWithSlots.Select(t => t.Item1);

                    // Create a bullet-pointed list of monsters
                    monsterList = string.Join("\n", monsters.Select(m => $"- {m.Title.GetFormattedText()}"));
                }

                EncounterInfo encounterInfo = new()
                {
                    BotKeyword = "encounter",
                    Name = title,
                    BotText = $"**{title}**\n`{actTitle}`\t`{tier}`\n\n**Monsters:**\n{monsterList}",
                    Id = encounter.Id,
                    Act = actTitle,
                    Tier = tier
                };

                encounterInfos.Add(encounterInfo);
            }
        }
    }

    private static List<EventInfo> CollectEventInfo()
    {
        List<EventInfo> eventInfos = [];

        foreach (ActModel act in ModelDb.Acts)
        {
            string actTitle = act.Title.GetFormattedText();
            foreach (EventModel eventModel in act.Events)
            {
                EventInfo eventInfo = CreateEventInfoInternal(eventModel, actTitle, $"`{actTitle}`");
                eventInfos.Add(eventInfo);
            }
        }

        foreach (EventModel eventModel in ModelDb.SharedEvents)
        {
            EventInfo eventInfo = CreateEventInfoInternal(eventModel, "Any", "`Shared`");
            eventInfos.Add(eventInfo);
        }

        return eventInfos;
    }

    private static List<EnchantmentInfo> CollectEnchantmentInfo()
    {
        List<EnchantmentInfo> enchantmentInfos = [];
        foreach (EnchantmentModel enchantment in ModelDb.DebugEnchantments)
        {
            string text = enchantment.DynamicDescription.GetFormattedText();
            string title = enchantment.Title.GetFormattedText();
            string desc = FormatDiscordDesc(text);
            if (enchantment.ShowAmount) // if show amount is true, then the zero represents a dynamic value
            {
                desc = ReplaceDynamicZero(desc);
            }

            EnchantmentInfo enchantmentInfo = new()
            {
                BotKeyword = "enchantment",
                BotText = $"**{title}**\n{desc}",
                Name = title,
                Id = enchantment.Id,
                Text = ReplaceDynamicZero(text)
            };

            enchantmentInfos.Add(enchantmentInfo);
        }

        return enchantmentInfos;

        // Embedded helper method
        string ReplaceDynamicZero(string text)
        {
            // There are dynamically determined values for some enchantments like "Toothed" which default to 0.
            // We'll use 'N' to represent that.
            return IntegerNoSpacingRegex().Replace(text, match =>
            {
                // If the match is exactly "0", replace it with "N"
                if (match.Value == "0")
                {
                    return "N";
                }

                // Otherwise keep the original number
                return match.Value;
            });
        }
    }

    private static List<Tuple<string, string>> ParseOptionTitleDesc(List<LocString> options)
    {
        List<Tuple<string, string>> optionTitleDesc = [];
        string? optionTitle = null;
        string? optionDesc = null;

        foreach (LocString option in options)
        {
            if (option.LocEntryKey.EndsWith(".title"))
            {
                // HEADS-UP!
                // If you get test errors around here, the event that's erroring is probably very dynamic and should be
                // overriding EventModel.GameInfoOptions.
                // Check out TheFutureOfPotions for an example.
                optionTitle = BbCodeRegex().Replace(option.GetFormattedText(), "");
            }
            else
            {
                optionDesc = BbCodeRegex().Replace(option.GetFormattedText(), "");
            }

            if (optionTitle != null && optionDesc != null)
            {
                optionTitleDesc.Add(new Tuple<string, string>(optionTitle, optionDesc));
                optionTitle = null;
                optionDesc = null;
            }
        }

        return optionTitleDesc;
    }

    private static string FormatBotOptionsText(List<Tuple<string, string>> optionTitleDesc)
    {
        string botOptionsText = "";
        foreach (Tuple<string, string> tuple in optionTitleDesc)
        {
            botOptionsText += $"\n- {tuple.Item1}";
            botOptionsText += $"\n> {tuple.Item2}";
        }

        return botOptionsText;
    }

    private static EventInfo CreateEventInfoInternal(EventModel eventModel, string actTitle, string actText)
    {
        eventModel = eventModel.ToMutable();
        string title = eventModel.Title.GetFormattedText();
        LocString eventDesc = eventModel.InitialDescription;
        eventModel.DynamicVars.AddTo(eventDesc);
        List<LocString> options = eventModel.GameInfoOptions.ToList();

        List<Tuple<string, string>> optionTitleDesc = ParseOptionTitleDesc(options);

        // Format the text to match the desired output
        string formattedText = $"# **{title}**\n`{actText}`\n\n> {Stylize(eventDesc.GetFormattedText())}\n\n";

        // Add separator
        formattedText += "---\n\n";

        // Format options differently
        string formattedOptions = string.Join("\n", optionTitleDesc.Select(opt =>
            $"- **{opt.Item1}**\n   {opt.Item2}"));

        return new EventInfo
        {
            BotKeyword = "event",
            Name = title,
            BotText = formattedText + formattedOptions,
            Id = eventModel.Id,
            Act = actTitle,
            Options = options.Select(loc => loc.GetFormattedText()).ToList(),
        };
    }

    private static async Task UploadToBot(List<IGameInfo> data)
    {
        string? token = Environment.GetEnvironmentVariable("FEEDBACKBOT_TOKEN");
        string? url = Environment.GetEnvironmentVariable("FEEDBACKBOT_URL");

        if (string.IsNullOrEmpty(token))
        {
            throw new InvalidOperationException("Missing FEEDBACKBOT_TOKEN!");
        }

        if (string.IsNullOrEmpty(url))
        {
            throw new InvalidOperationException("Missing FEEDBACKBOT_URL!");
        }

        JsonSerializerOptions options = new()
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            IncludeFields = true,
            TypeInfoResolver = new DefaultJsonTypeInfoResolver { Modifiers = { JsonSerializeConditionAttribute.CheckJsonSerializeConditionsModifier } },
            Converters = { new ModelIdMetricsConverter() },
        };

        string json = JsonSerializer.Serialize(data, options);

        // Truncating the json log because otherwise it overfills the ci log
        const int maxCharLength = 500;
        //Debug.Log(json.Substring(0, maxCharLength));
        if (json.Length > maxCharLength)
            GD.Print("**TRUNCATED JSON**");

        // Write a copy to disk
        await File.WriteAllTextAsync("gamedata.json", json);

        // Upload to feedbackbot
        GD.Print($"Uploading to feedbackbot: url={url}");
        using HttpClient client = new HttpClient();
        client.DefaultRequestHeaders.Add("Authorization", "Bearer " + token);

        HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Put, url)
        {
            Content = new StringContent(json, Encoding.UTF8, "application/json")
        };

        GD.Print($"Request method={request.Method} uri={request.RequestUri?.AbsoluteUri} contentType={request.Content.Headers.ContentType} headers={client.DefaultRequestHeaders}");

        HttpResponseMessage response = await client.SendAsync(request);

        if (response.IsSuccessStatusCode)
        {
            GD.Print("Upload successful!");
        }
        else
        {
            string responseHeaders = string.Join(", ", response.Headers.Select(h => h.Key + ": " + string.Join(", ", h.Value)));
            string responseContent = await response.Content.ReadAsStringAsync();
            GD.PrintErr($"Upload FAILED: statusCode='{response.StatusCode}' reasonPhrase='{response.ReasonPhrase}' headers=[{responseHeaders}] content='{responseContent}'");
            throw new HttpRequestException("Failed to upload!");
        }
    }

    public static List<IGameInfo> CollectGameInfo()
    {
        GD.Print("Collecting card data...");
        List<CardInfo> cards = CollectCardInfo();
        GD.Print("Collecting potion data...");
        List<PotionInfo> potions = CollectPotionInfo();
        GD.Print("Collecting relic data...");
        List<RelicInfo> relics = CollectRelicInfo();
        GD.Print("Collecting encounter data...");
        List<EncounterInfo> encounters = CollectEncounterInfo();
        GD.Print("Collecting event data...");
        List<EventInfo> events = CollectEventInfo();
        GD.Print("Collecting enchantment data...");
        List<EnchantmentInfo> enchantments = CollectEnchantmentInfo();
        GD.Print("Collecting ancient choice data...");
        IEnumerable<AncientChoiceInfo> ancientChoices = CollectAncientChoiceInfo();

        List<IGameInfo> gameInfo = new List<IGameInfo>()
            .Concat(cards)
            .Concat(potions)
            .Concat(relics)
            .Concat(encounters)
            .Concat(events)
            .Concat(enchantments)
            .Concat(ancientChoices)
            .ToList();

        // Validate required fields
        foreach (IGameInfo info in gameInfo)
        {
            if (string.IsNullOrEmpty(info.Name))
            {
                throw new Exception($"{info.GetType()} has an empty 'name'");
            }

            if (string.IsNullOrEmpty(info.BotKeyword))
            {
                throw new Exception($"{info.BotKeyword} has an empty 'bot_keyword'");
            }

            if (string.IsNullOrEmpty(info.BotText))
            {
                throw new Exception($"{info.BotText} has an empty 'bot_text'");
            }
        }

        return gameInfo;
    }

    // Called from the CI. Uploads game info objects.
    private static void Upload()
    {
        OneTimeInitialization.Execute();

        LocManager.Instance.SetLanguage("eng");

        List<IGameInfo> gameInfo = CollectGameInfo();

        Task.Run(() => UploadToBot(gameInfo)).GetAwaiter().GetResult();
    }

#endif
}
