using System;
using System.Text.Json.Serialization;
using MegaCrit.Sts2.Core.Models;

namespace MegaCrit.Sts2.GameInfo.Objects;

[Serializable]
public class PotionInfo : IGameInfo
{
    /* For FeedbackBot: */
    [JsonPropertyName("name")]
    public required string Name { get; init; }

    [JsonPropertyName("bot_keyword")]
    public required string BotKeyword { get; init; }

    [JsonPropertyName("bot_text")]
    public required string BotText { get; init; }

    /* For Metrics: */
    [JsonPropertyName("id")]
    public required ModelId Id { get; init; }

    [JsonPropertyName("rarity")]
    public required string Rarity { get; init; }

    /* For GameInfo Site: */
    [JsonPropertyName("text")]
    public required string Text { get; init; }

    [JsonPropertyName("color")]
    public required string Color { get; init; }
}