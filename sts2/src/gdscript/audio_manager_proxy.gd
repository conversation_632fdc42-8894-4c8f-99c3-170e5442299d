# HACK: A way for C# scripts to access FMOD commands.
class_name AudioManagerProxy extends Node

var master_bus: FmodBus
var sfx_bus: FmodBus
var ambient_bus: FmodBus
var bgm_bus: FmodBus

var music_track : FmodEvent 
var loops : Dictionary

#Note: this kind of breaks if you have to play the same sound effect loop at the same time
func play_loop(_path : String):
    if !FmodServer.check_event_path(_path):
        printerr("cannot find sfx path: " + _path)
        return
        
    var sfx : FmodEvent = FmodServer.create_event_instance(_path)
    if !loops.has(_path):
        loops[_path] = [];
        
    loops[_path].append(sfx);
    sfx.start()
    sfx.release()
    
func set_param(_path : String, _param : String, _val : float):
    if loops.has(_path):
        var arr = loops[_path]
        arr[0].set_parameter_by_name(_param,_val)
    
func stop_loop(_path : String):
    if loops.has(_path):
        var arr = loops[_path]
        arr.pop_at(0).set_parameter_by_name("loop",1)
        if arr.size() <= 0:
            loops.erase(_path)
    
func stop_all_loops() -> void:
    for key in loops.keys():
        var arr = loops[key]
        for i in arr.size():
            stop_loop(key)

func play_one_shot(_path : String, _parameters):

    if !FmodServer.check_event_path(_path):
        printerr("cannot find sfx path: " + _path)
        return
        
    var sfx : FmodEvent = FmodServer.create_event_instance(_path)
    for key in _parameters:
        if sfx.get_parameter_by_name(key) == null:
            return;  
        sfx.set_parameter_by_name(key, _parameters[key])
        
    sfx.start()
    sfx.release()
    
func play_music(_music : String):
    if !FmodServer.check_event_path(_music):
        printerr("cannot find music path: " + _music)
        return
        
    stop_music()
    music_track = FmodServer.create_event_instance(_music)
    music_track.start()
    
func stop_music():
    if music_track != null:
        music_track.stop(0)
        music_track.release()
        music_track = null

func set_master_volume(_val : float):
    if master_bus == null :
        master_bus = FmodServer.get_bus("bus:/master")
    master_bus.volume = _val
    
func set_sfx_volume(_val : float):
    if sfx_bus == null :
        sfx_bus = FmodServer.get_bus("bus:/master/sfx")
    sfx_bus.volume = _val

func set_ambience_volume(_val : float):
    if ambient_bus == null :
        ambient_bus = FmodServer.get_bus("bus:/master/ambience")
    ambient_bus.volume = _val

func set_bgm_volume(_val : float):
    if bgm_bus == null :
        bgm_bus = FmodServer.get_bus("bus:/master/music")
    bgm_bus.volume = _val
