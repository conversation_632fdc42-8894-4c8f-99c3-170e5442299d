# HACK: A way for C# scripts to access FMOD commands.
class_name MusicControllerProxy extends Node

var _musicEv: FmodEvent
var _currentTrack

var _ambienceEv: FmodEvent
var _currentAmbience

func update_music(track):
    _currentTrack = track
    stop_music()
    
    if !FmodServer.check_event_path(_currentTrack):
        printerr("cannot find music path: " + _currentTrack)
        return
    
    _musicEv = FmodServer.create_event_instance(_currentTrack)
    _musicEv.start()

func update_music_parameter(label, labelIndex):
    if _musicEv == null:
        printerr("missing music track: " + _currentTrack)
        return
        
    _musicEv.set_parameter_by_name(label, labelIndex)

func update_global_parameter(label, labelIndex):
    FmodServer.set_global_parameter_by_name(label, labelIndex)
    
func stop_music():
    if _musicEv != null:
        _musicEv.stop(0)
        _musicEv.release()
        _musicEv = null

func update_ambience(track):
    _currentAmbience = track
    stop_ambience()
    
    if !FmodServer.check_event_path(_currentAmbience):
        printerr("cannot find ambience path: " + _currentAmbience)
        return
    
    _ambienceEv = FmodServer.create_event_instance(_currentAmbience)
    _ambienceEv.start()
    
func update_campfire_ambience(trackIndex):
    if !FmodServer.check_event_path(_currentAmbience):
        printerr("cannot find ambience path: " + _currentAmbience)
        return
        
    _ambienceEv.set_parameter_by_name("Campfire", trackIndex)
    
func stop_ambience():
    if _ambienceEv != null:
        _ambienceEv.stop(0)
        _ambienceEv.release()
        _ambienceEv = null
