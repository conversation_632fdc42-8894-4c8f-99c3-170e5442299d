using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Achievements;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Multiplayer;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Platform;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Test.Multiplayer;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Achievements;

public class AchievementTest
{
    private ClimbState? _climbState;

    [SetUp]
    public void Setup()
    {
        SaveManager.Instance.ProgressSave = new ProgressSave();

        foreach (Achievement achievement in Enum.GetValues<Achievement>())
        {
            AchievementsUtil.Revoke(achievement);
        }
    }

    protected async Task<ClimbState> SetupClimb(CharacterModel? character = null, int ascension = 0, IReadOnlyList<ActModel>? canonicalActs = null)
    {
        _climbState = ClimbState.CreateForTest(
            [Player.CreateForNewClimb(character ?? ModelDb.Character<Deprived>(), NetSingleplayerGameService.defaultNetId)],
            canonicalActs,
            ascensionLevel: ascension
        );
        ClimbManager.Instance.SetUpTest(_climbState, new TestGameService(1, NetGameType.Singleplayer));

        await ClimbManager.Instance.GenerateMap();
        ClimbManager.Instance.GenerateRooms();

        ClimbManager.Instance.Launch();
        _climbState.Map = new MockActMap();

        await ClimbManager.Instance.EnterRoomDebug(RoomType.Map);

        return _climbState;
    }

    protected async Task SetupMultiplayerClimb(List<Player> players, int ascension = 0, IReadOnlyList<ActModel>? actList = null)
    {
        _climbState = ClimbState.CreateForTest(players, actList, ascensionLevel: ascension);
        ClimbManager.Instance.SetUpTest(_climbState, new TestGameService(1, NetGameType.Host));

        await ClimbManager.Instance.GenerateMap();
        ClimbManager.Instance.GenerateRooms();

        ClimbManager.Instance.Launch();
        _climbState.Map = new MockActMap();

        await ClimbManager.Instance.EnterRoomDebug(RoomType.Map);
    }

    public async Task WinClimb()
    {
        while (GetPlayer().Creature.IsAlive)
        {
            await ClimbManager.Instance.EnterNextAct();
        }
    }

    public async Task LoseClimb()
    {
        foreach (Player player in _climbState!.Players)
        {
            await CreatureCmd.Kill(player.Creature);
        }

        ClimbManager.Instance.OnEnded(false);
    }

    public void AssertThatOnlyOneAchievementIsUnlocked(Achievement achievement, IEnumerable<Achievement>? ignore = null)
    {
        AssertThatOnlyGivenAchievementsAreUnlocked([achievement], ignore);
    }

    public void AssertThatOnlyGivenAchievementsAreUnlocked(IEnumerable<Achievement> achievements, IEnumerable<Achievement>? ignore = null)
    {
        foreach (Achievement toCheck in Enum.GetValues<Achievement>())
        {
            if (ignore != null && ignore.Contains(toCheck)) continue;

            Assert.That(AchievementsUtil.IsUnlocked(toCheck), Is.EqualTo(achievements.Contains(toCheck)), $"Achievement {toCheck} is unlocked: {AchievementsUtil.IsUnlocked(toCheck)}, but should be opposite");
        }
    }

    protected T CreateCard<T>(Player? player = null, CardScope scope = CardScope.Combat) where T : CardModel
    {
        player ??= GetPlayer();
        return ICardScope.DebugOnlyGet(scope).CreateCard<T>(player);
    }

    protected MockAttackCard MockAttack(Player? player = null, CardScope scope = CardScope.Combat) => CreateCard<MockAttackCard>(player, scope);
    protected MockSkillCard MockSkill(Player? player = null, CardScope scope = CardScope.Combat) => CreateCard<MockSkillCard>(player, scope);
    protected MockPowerCard MockPower(Player? player = null, CardScope scope = CardScope.Combat) => CreateCard<MockPowerCard>(player, scope);

    protected Player GetPlayer() => _climbState!.Players[0];

    [TearDown]
    public void Teardown()
    {
        ClimbManager.Instance.CleanUp();
        SaveManager.Instance.ProgressSave = new ProgressSave();
        _climbState = null;
    }
}
