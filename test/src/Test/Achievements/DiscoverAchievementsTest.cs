using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Achievements;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Events;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Platform;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Timeline;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Achievements;

public class DiscoverAchievementsTest : AchievementTest
{
    [Test]
    public async Task TestDiscoveringAllRelicsUnlocksAchievement()
    {
        // Set up progress save with all relics except one
        SaveManager.Instance.ProgressSave.DiscoveredRelics = ModelDb.Relics.Except([ModelDb.Relic<Strawberry>()]).Select(r => r.Id).ToList();

        await SetupClimb();
        await RelicCmd.Obtain<Strawberry>(GetPlayer());
        await LoseClimb();

        AssertThatOnlyOneAchievementIsUnlocked(Achievement.DiscoverAllRelics);
    }

    [Test]
    public async Task TestDiscoveringSomeRelicsDoesNotUnlockAchievement()
    {
        SaveManager.Instance.ProgressSave.DiscoveredRelics = ModelDb.Relics.Except([ModelDb.Relic<Strawberry>()]).Select(r => r.Id).ToList();

        await SetupClimb();
        await LoseClimb();
        Assert.That(AchievementsUtil.IsUnlocked(Achievement.DiscoverAllRelics), Is.False);
    }

    [Test]
    public async Task TestDiscoveringAllCardsUnlocksAchievement()
    {
        // Set up progress save with all cards except one
        SaveManager.Instance.ProgressSave.DiscoveredCards = ModelDb.Cards.Except([ModelDb.Card<ShrugItOff>()]).Select(c => c.Id).ToList();

        await SetupClimb();
        SaveManager.Instance.MarkCardAsSeen(ModelDb.Card<ShrugItOff>());
        await LoseClimb();

        AssertThatOnlyOneAchievementIsUnlocked(Achievement.DiscoverAllCards);
    }

    [Test]
    public async Task TestDiscoveringSomeCardsDoesNotUnlockAchievement()
    {
        SaveManager.Instance.ProgressSave.DiscoveredCards = ModelDb.Cards.Except([ModelDb.Card<ShrugItOff>()]).Select(c => c.Id).ToList();

        await SetupClimb();
        await LoseClimb();

        Assert.That(AchievementsUtil.IsUnlocked(Achievement.DiscoverAllCards), Is.False);
    }

    [Test]
    public async Task TestDiscoveringAllEventsUnlocksAchievement()
    {
        // Set up progress save with all events except one
        SaveManager.Instance.ProgressSave.DiscoveredEvents = ModelDb.Events.Except([ModelDb.Event<Reflections>()]).Select(e => e.Id).ToList();

        await SetupClimb();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Event, MapPointType.Unknown, ModelDb.Event<Reflections>());
        await LoseClimb();

        AssertThatOnlyOneAchievementIsUnlocked(Achievement.DiscoverAllEvents);
    }

    [Test]
    public async Task TestDiscoveringSomeEventsDoesNotUnlockAchievement()
    {
        SaveManager.Instance.ProgressSave.DiscoveredEvents = ModelDb.Events.Except([ModelDb.Event<Reflections>()]).Select(e => e.Id).ToList();

        await SetupClimb();
        await LoseClimb();

        Assert.That(AchievementsUtil.IsUnlocked(Achievement.DiscoverAllEvents), Is.False);
    }

    [Test]
    public void TestCompletingTimelineUnlocksAchievement()
    {
        SaveManager.Instance.ProgressSave.Epochs = EpochModel.AllIds().Select(id => new SerializableEpoch(id, EpochState.Revealed)).ToList();
        AchievementsHelper.CheckTimelineComplete();
        AssertThatOnlyOneAchievementIsUnlocked(Achievement.CompleteTimeline);
    }

    [Test]
    public void TestAlmostCompletingTimelineDoesNotUnlockAchievement()
    {
        SaveManager.Instance.ProgressSave.Epochs = EpochModel.AllIds().Select(id =>
            new SerializableEpoch(id, id != "BANANA_EPOCH" ? EpochState.Revealed : EpochState.Obtained)).ToList();

        AchievementsHelper.CheckTimelineComplete();
        Assert.That(AchievementsUtil.IsUnlocked(Achievement.CompleteTimeline), Is.False);
    }
}
