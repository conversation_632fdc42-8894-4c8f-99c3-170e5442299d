using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Achievements;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Platform;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Achievements;

public class WinAchievementsTest : AchievementTest
{
    private static IEnumerable<CharacterModel> AllCharacters => ModelDb.Characters;

    [Test]
    [TestCaseSource(nameof(AllCharacters))]
    public async Task TestWinningAsCharacterUnlocksAchievement(CharacterModel character)
    {
        await SetupClimb(character);
        await WinClimb();
        AssertThatOnlyOneAchievementIsUnlocked(character.ClimbWonAchievement, [Achievement.NoRelicWin]);
    }

    [Test]
    [TestCaseSource(nameof(AllCharacters))]
    public async Task TestLosingAsCharacterDoesNotUnlockAchievement(CharacterModel character)
    {
        ClimbState climbState = await SetupClimb(character);
        await LoseClimb();

        Assert.That(climbState.IsGameOver, Is.True);
        Assert.That(AchievementsUtil.IsUnlocked(character.ClimbWonAchievement), Is.False);
    }

    [Test]
    [TestCaseSource(nameof(AllCharacters))]
    public async Task TestWinningAsCharacterOnAscension10UnlocksAchievement(CharacterModel character)
    {
        await SetupClimb(character, 9);
        await WinClimb();
        AssertThatOnlyGivenAchievementsAreUnlocked([character.Ascension10WonAchievement, character.ClimbWonAchievement], [Achievement.NoRelicWin]);
    }

    [Test]
    public async Task TestWinningWhenOtherPlayerIsAnotherCharacterOnlyUnlocksAchievementForLocalCharacter()
    {
        await SetupMultiplayerClimb([Player.CreateForNewClimb<Ironclad>(1), Player.CreateForNewClimb<Silent>(1000)]);
        await WinClimb();
        AssertThatOnlyOneAchievementIsUnlocked(Achievement.IroncladWin, [Achievement.NoRelicWin]);
    }

    [Test]
    public async Task TestWinningOnAscension10WhenOtherPlayerIsAnotherCharacterOnlyUnlocksAchievementForLocalCharacter()
    {
        await SetupMultiplayerClimb([Player.CreateForNewClimb<Ironclad>(1), Player.CreateForNewClimb<Silent>(1000)], ascension: 9);
        await WinClimb();
        AssertThatOnlyOneAchievementIsUnlocked(Achievement.IroncladAscension10, [Achievement.NoRelicWin, Achievement.IroncladWin]);
    }
}
