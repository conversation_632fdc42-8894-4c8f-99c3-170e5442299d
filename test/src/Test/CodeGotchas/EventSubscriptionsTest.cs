using System.Collections.Generic;
using System.IO;
using System.Text.RegularExpressions;
using MegaCrit.Sts2.Test.Helpers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.CodeGotchas;

[TestFixture]
public class EventSubscriptionTest
{
    private static readonly Regex _subscribePattern = new(@"\s*(\w+)\s*\+=\s*(\w+)", RegexOptions.Compiled | RegexOptions.Singleline);
    private static readonly Regex _unsubscribePattern = new(@"\s*(\w+)\s*-=\s*(\w+)", RegexOptions.Compiled);
    private static readonly Regex _methodPattern = new(@"\b(public|private|protected|internal)(\s+static)?\s+\w+\s+(\w+)\s*\(", RegexOptions.Compiled);
    private static readonly Regex _lambdaPattern = new(@"\s*(\w+)\s*\+=\s*(?!.*\d+).*=>.*", RegexOptions.Compiled | RegexOptions.Singleline);

    /// <summary>
    /// This test checks for whether we forgot to unsubscribe an event somewhere.
    /// </summary>
    [Test]
    public void CheckForUnmatchingEventSubscriptions()
    {
        List<string> violations = [];

        foreach (string file in FileHelper.GetAllCSharpFilePaths())
        {
            IEnumerable<string> lines = File.ReadLines(file);
            HashSet<string> methods = [];
            Dictionary<string, List<int>> subscriptionLines = new();
            Dictionary<string, List<int>> unsubscriptionLines = new();

            int lineNumber = 0;
            foreach (string line in lines)
            {
                lineNumber++;

                foreach (Match match in _methodPattern.Matches(line))
                {
                    methods.Add(match.Groups[3].Value);
                }

                foreach (Match match in _subscribePattern.Matches(line))
                {
                    string eventName = match.Groups[1].Value;
                    string handler = match.Groups[2].Value;

                    if (!methods.Contains(handler) && !_lambdaPattern.IsMatch(line)) continue;
                    subscriptionLines.TryGetValue(eventName, out List<int>? list);
                    list ??= subscriptionLines[eventName] = [];
                    list.Add(lineNumber);
                }

                foreach (Match match in _unsubscribePattern.Matches(line))
                {
                    string eventName = match.Groups[1].Value;
                    string handler = match.Groups[2].Value;

                    if (!methods.Contains(handler)) continue;
                    unsubscriptionLines.TryGetValue(eventName, out List<int>? list);
                    list ??= unsubscriptionLines[eventName] = [];
                    list.Add(lineNumber);
                }
            }

            foreach (string eventName in subscriptionLines.Keys)
            {
                int subscriptionCount = subscriptionLines[eventName].Count;
                int unsubscriptionCount = unsubscriptionLines.TryGetValue(eventName, out List<int>? unsubscription) ? unsubscription.Count : 0;

                if (subscriptionCount == unsubscriptionCount) continue;
                string filename = Path.GetFileName(file);
                string subscriptionLineNumbers = string.Join(", ", subscriptionLines[eventName]);
                string message = $"{filename}(line={subscriptionLineNumbers}): Event '{eventName}' has {subscriptionCount} subscriptions but {unsubscriptionCount} unsubscriptions.";
                violations.Add(message);
            }
        }

        if (violations.Count != 0)
        {
            Assert.Fail($"Found={violations.Count}\n" + string.Join((string?)System.Environment.NewLine, violations));
        }
    }

    /// <summary>
    /// This test checks for whether we subscribed using a lambda function. Lambda functions can't be unsubscribed,
    /// so any case where we did this will cause a memory leak.
    /// </summary>
    [Test]
    public void CheckForLambdaExpressionEventHandlers()
    {
        List<string> violations = [];

        foreach (string file in FileHelper.GetAllCSharpFilePaths())
        {
            IEnumerable<string> lines = File.ReadLines(file);
            int lineNumber = 0;

            foreach (string line in lines)
            {
                lineNumber++;

                foreach (Match match in _lambdaPattern.Matches(line))
                {
                    string eventName = match.Groups[1].Value;
                    string filename = Path.GetFileName(file);
                    violations.Add($"{filename}(line={lineNumber}): Event '{eventName}' is subscribed with a lambda expression which cannot be easily unsubscribed.");
                }
            }
        }

        if (violations.Count != 0)
        {
            Assert.Fail($"Found={violations.Count}\n" + string.Join((string?)System.Environment.NewLine, violations));
        }
    }
}