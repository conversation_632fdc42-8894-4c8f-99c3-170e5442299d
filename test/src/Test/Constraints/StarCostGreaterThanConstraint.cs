using System;
using MegaCrit.Sts2.Core.Models;
using NUnit.Framework.Constraints;

namespace MegaCrit.Sts2.Test.Constraints;

public class StarCostGreaterThanConstraint(int minCost) : Constraint
{
    public override string Description => $"Card to have star cost greater than {minCost}";

    public override ConstraintResult ApplyTo<T>(T actual)
    {
        if (actual is not CardModel card) throw new ArgumentException($"Argument must be of type {nameof(CardModel)}, but was of type {typeof(T)}");

        return Is.GreaterThan(minCost).ApplyTo(card.GetStarCostWithModifiers());
    }
}