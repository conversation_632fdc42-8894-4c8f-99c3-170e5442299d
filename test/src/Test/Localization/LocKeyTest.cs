using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Monsters;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Localization;

[TestFixture]
public class LocKeyTest
{
    [Test]
    public void TestRelicKeys()
    {
        List<string> errors = [];
        List<RelicModel> relics = GetAllModels<RelicModel>();
        foreach (RelicModel relic in relics)
        {
            TestLocString(relic.Title.GetFormattedText, ref errors);
            TestLocString(relic.Description.GetFormattedText, ref errors);
            TestLocString(relic.Flavor.GetFormattedText, ref errors);
            TestLocString(relic.DynamicDescription.GetFormattedText, ref errors);
        }

        if (errors.Count != 0)
        {
            string message = "Found the following loc errors:\n" + string.Join("\n", errors);
            Assert.Fail(message);
        }
    }

    [Test]
    public void TestPowerKeys()
    {
        List<string> errors = [];
        List<PowerModel> powers = GetAllModels<PowerModel>();
        foreach (PowerModel power in powers)
        {
            TestLocString(power.Title.GetFormattedText, ref errors);
            TestLocString(power.Description.GetFormattedText, ref errors);
            if (power.HasSmartDescription)
            {
                TestLocString(power.SmartDescription.GetFormattedText, ref errors);
            }
        }

        if (errors.Count != 0)
        {
            string message = "Found the following loc errors:\n" + string.Join("\n", errors);
            Assert.Fail(message);
        }
    }

    [Test]
    public void TestPotionKeys()
    {
        List<string> errors = [];
        List<PotionModel> potions = GetAllModels<PotionModel>();
        foreach (PotionModel potion in potions)
        {
            TestLocString(potion.Title.GetFormattedText, ref errors);
            TestLocString(potion.Description.GetFormattedText, ref errors);
            TestLocString(potion.DynamicDescription.GetFormattedText, ref errors);
            TestLocString(potion.StaticDescription.GetFormattedText, ref errors);
        }

        if (errors.Count != 0)
        {
            string message = "Found the following loc errors:\n" + string.Join("\n", errors);
            Assert.Fail(message);
        }
    }

    [Test]
    public void TestCardKeys()
    {
        List<string> errors = [];
        List<CardModel> cards = GetAllModels<CardModel>();
        foreach (CardModel card in cards)
        {
            TestLocString(() => card.Title, ref errors);
            TestLocString(card.Description.GetFormattedText, ref errors);
        }

        if (errors.Count != 0)
        {
            string message = "Found the following loc errors:\n" + string.Join("\n", errors);
            Assert.Fail(message);
        }
    }

    [Test]
    public void TestCardDescriptionForPile()
    {
        PileType[] cardPileTargets = (PileType[])Enum.GetValues(typeof(PileType));
        IEnumerable<CardModel> cards = GetAllModels<CardModel>()
            .Where(card => card is not MadScience); // skip Mad Science

        foreach (CardModel card in cards)
        {
            foreach (PileType pile in cardPileTargets)
            {
                // This has thrown an exception in the past, so we need to test it too.
                card.GetDescriptionForPile(pile);
            }
        }
    }

    [Test]
    public void TestEnchantmentKeys()
    {
        List<string> errors = [];
        List<EnchantmentModel> enchantments = GetAllModels<EnchantmentModel>();
        foreach (EnchantmentModel enchantment in enchantments)
        {
            TestLocString(enchantment.Title.GetFormattedText, ref errors);
            TestLocString(enchantment.Description.GetFormattedText, ref errors);
            if (enchantment.HasExtraCardText)
            {
                TestLocString(enchantment.ExtraCardText.GetFormattedText, ref errors);
            }
        }

        if (errors.Count != 0)
        {
            string message = "Found the following loc errors:\n" + string.Join("\n", errors);
            Assert.Fail(message);
        }
    }

    [Test]
    public void TestOrbsKeys()
    {
        List<string> errors = [];
        List<OrbModel> orbs = GetAllModels<OrbModel>();
        foreach (OrbModel orb in orbs)
        {
            TestLocString(orb.Title.GetFormattedText, ref errors);
            TestLocString(orb.Description.GetFormattedText, ref errors);
            if (orb.HasSmartDescription)
            {
                TestLocString(orb.SmartDescription.GetFormattedText, ref errors);
            }
        }

        if (errors.Count != 0)
        {
            string message = "Found the following loc errors:\n" + string.Join("\n", errors);
            Assert.Fail(message);
        }
    }

    [Test]
    public void TestMonstersKeys()
    {
        List<string> errors = [];
        List<MonsterModel> monsters = GetAllModels<MonsterModel>();
        foreach (MonsterModel monster in monsters)
        {
            if (monster is TestSubject) continue; // ignore test subject because his name is dynamic and relies on progress save

            TestLocString(monster.Title.GetFormattedText, ref errors);
        }

        if (errors.Count != 0)
        {
            string message = "Found the following loc errors:\n" + string.Join("\n", errors);
            Assert.Fail(message);
        }
    }

    [Test]
    public void TestEventsKeys()
    {
        List<string> errors = [];
        List<EventModel> events = GetAllModels<EventModel>();
        foreach (EventModel eventModel in events)
        {
            TestLocString(eventModel.Title.GetFormattedText, ref errors);
            TestLocString(eventModel.InitialDescription.GetFormattedText, ref errors);
        }

        if (errors.Count != 0)
        {
            string message = "Found the following loc errors:\n" + string.Join("\n", errors);
            Assert.Fail(message);
        }
    }

    [Test]
    public void TestEncountersKeys()
    {
        List<string> errors = [];
        List<EncounterModel> encounters = GetAllModels<EncounterModel>();

        foreach (EncounterModel encounter in encounters)
        {
            if (encounter.IsDebugEncounter) continue; // Skip debug encounters.

            TestLocString(encounter.Title.GetFormattedText, ref errors);
        }

        if (errors.Count != 0)
        {
            string message = "Found the following loc errors:\n" + string.Join("\n", errors);
            Assert.Fail(message);
        }
    }

    [Test]
    public void TestCharactersKeys()
    {
        List<string> errors = [];
        List<CharacterModel> characters = GetAllModels<CharacterModel>();
        foreach (CharacterModel character in characters)
        {
            TestLocString(character.Title.GetFormattedText, ref errors);
        }

        if (errors.Count != 0)
        {
            string message = "Found the following loc errors:\n" + string.Join("\n", errors);
            Assert.Fail(message);
        }
    }

    [Test]
    public void TestAfflictionsKeys()
    {
        List<string> errors = [];
        List<AfflictionModel> afflictions = GetAllModels<AfflictionModel>();
        foreach (AfflictionModel affliction in afflictions)
        {
            TestLocString(affliction.Title.GetFormattedText, ref errors);
            TestLocString(affliction.Description.GetFormattedText, ref errors);
            if (affliction.HasExtraCardText)
            {
                TestLocString(affliction.ExtraCardText.GetFormattedText, ref errors);
            }
        }

        if (errors.Count != 0)
        {
            string message = "Found the following loc errors:\n" + string.Join("\n", errors);
            Assert.Fail(message);
        }
    }

    [Test]
    public void TestActsKeys()
    {
        List<string> errors = [];
        List<ActModel> acts = GetAllModels<ActModel>();
        foreach (ActModel act in acts)
        {
            TestLocString(act.Title.GetFormattedText, ref errors);
        }

        if (errors.Count != 0)
        {
            string message = "Found the following loc errors:\n" + string.Join("\n", errors);
            Assert.Fail(message);
        }
    }

    private static void TestLocString(Func<string> func, ref List<string> errors)
    {
        try
        {
            func.Invoke();
        }
        catch (LocException e)
        {
            if (e.Message.Contains("No source extension"))
            {
                return;
            }

            errors.Add(e.Message);
        }
        catch (Exception e)
        {
            errors.Add(e.Message);
        }
    }

    private static List<T> GetAllModels<T>()
    {
        // Use reflection to get all subclasses of T.
        Assembly assembly = Assembly.GetAssembly(typeof(T))!;
        List<Type> types = assembly.GetTypes()
            .Where(t => t is { IsClass: true, IsAbstract: false } && t.IsSubclassOf(typeof(T)) && t.GetConstructor(Type.EmptyTypes) != null)
            .ToList();

        List<T> instances = [];
        foreach (Type type in types)
        {
            // Call ModelDb.GetOrCreate<T>() via reflection to create an instance of the model class.
            MethodInfo getOrCreateMethod = typeof(ModelDb).GetMethod("GetOrCreate", BindingFlags.NonPublic | BindingFlags.Static, null, CallingConventions.Any, [], null)!;
            MethodInfo genericMethod = getOrCreateMethod.MakeGenericMethod(type);
            T instance = (T)genericMethod.Invoke(null, null)!;
            instances.Add(instance);
        }

        return instances;
    }
}
