using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class AdaptiveStrikeTest : ModelTest
{
    [Test]
    public async Task TestBaseCopying()
    {
        await Play<AdaptiveStrike>(GetEnemy());

        CardPile discardPile = GetPile(PileType.Discard);
        Assert.That(discardPile, Has.Cards(typeof(AdaptiveStrike), typeof(AdaptiveStrike)));
        Assert.That(discardPile.Cards, Has.None.Upgraded());
        Assert.That(discardPile.Cards.First(), Has.EnergyCost(0));

    }

    [Test]
    public async Task TestUpgradedCopying()
    {
        await PlayUpgraded<AdaptiveStrike>(GetEnemy());

        CardPile discardPile = GetPile(PileType.Discard);
        Assert.That(discardPile, Has.Cards(typeof(AdaptiveStrike), typeof(AdaptiveStrike)));
        Assert.That(discardPile.Cards, Has.All.Upgraded());
    }
}
