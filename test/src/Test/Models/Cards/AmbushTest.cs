using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class AmbushTest : ModelTest
{
    [Test]
    public async Task TestPlayZeroSkills()
    {
        await Play<Ambush>(GetEnemy());
        Assert.That(GetPlayer(), Has.SpentEnergy(3));
    }

    [Test]
    public async Task TestPlaySkillsBetweenTurns()
    {
        await Play(MockSkill());
        await Play(MockSkill());

        await PassToNextPlayerTurn();

        await Play<Ambush>(GetEnemy());
        Assert.That(GetPlayer(), Has.SpentEnergy(3));
    }

    [Test]
    public async Task TestPlayTwoSkills()
    {
        await Play(MockSkill());
        await Play(MockSkill());

        await Play<Ambush>(GetEnemy());

        // 1 + 1 + (3->1)
        Assert.That(GetPlayer(), Has.SpentEnergy(3));
    }

    [Test]
    public async Task TestPlayTwoNonSkills()
    {
        await Play(MockPower());
        await Play(MockAttack(), GetEnemy());

        await Play<Ambush>(GetEnemy());

        // 1 + 1 + 3
        Assert.That(GetPlayer(), Has.SpentEnergy(5));
    }
}
