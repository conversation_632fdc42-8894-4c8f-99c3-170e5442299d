using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class AngerTest : ModelTest
{
    [Test]
    public async Task TestBaseCopying()
    {
        await Play<Anger>(GetEnemy());

        CardPile discardPile = GetPile(PileType.Discard);
        Assert.That(discardPile, Has.Cards(typeof(Anger), typeof(Anger)));
        Assert.That(discardPile.Cards, Has.None.Upgraded());
    }

    [Test]
    public async Task TestUpgradedCopying()
    {
        await PlayUpgraded<Anger>(GetEnemy());

        CardPile discardPile = GetPile(PileType.Discard);
        Assert.That(discardPile, Has.Cards(typeof(Anger), typeof(Anger)));
        Assert.That(discardPile.Cards, Has.All.Upgraded());
    }
}
