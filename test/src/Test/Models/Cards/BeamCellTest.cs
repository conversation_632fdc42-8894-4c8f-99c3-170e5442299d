using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class BeamCellTest : ModelTest
{
    [Test]
    public async Task TestBaseDamageAndVulnerable()
    {
        Creature enemy = GetEnemy();

        await Play<BeamCell>(enemy);

        Assert.That(enemy, Has.LostHp(3));
        Assert.That(enemy, Has.PowerAmount<Vulnerable>(1));
    }

    [Test]
    public async Task TestUpgradedDamageAndVulnerable()
    {
        Creature enemy = GetEnemy();

        await PlayUpgraded<BeamCell>(enemy);

        Assert.That(enemy, Has.LostHp(4));
        Assert.That(enemy, Has.PowerAmount<Vulnerable>(2));
    }
}