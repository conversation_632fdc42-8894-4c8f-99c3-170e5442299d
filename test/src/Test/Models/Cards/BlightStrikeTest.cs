using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class BlightStrikeTest : ModelTest
{
    [Test]
    public async Task TestDamageAndDoom()
    {
        Creature enemy = GetEnemy();

        await Play<BlightStrike>(enemy);

        Assert.That(enemy, Has.LostHp(7));
        Assert.That(enemy, Has.PowerAmount<Doom>(7));
    }

    [Test]
    public async Task TestWithBlock()
    {
        Creature enemy = GetEnemy();

        await CreatureCmd.GainBlock(enemy, 2, BlockProps.nonCardUnpowered, null);
        await Play<BlightStrike>(enemy);

        Assert.That(enemy, Has.LostHp(5));
        Assert.That(enemy, Has.Power<PERSON>mount<Doom>(5));
    }

    [Test]
    public async Task TestWithStrength()
    {
        Creature enemy = GetEnemy();
        Player player = GetPlayer();

        await PowerCmd.Apply<Strength>(player.Creature, 2, null, null);
        await Play<BlightStrike>(enemy);

        Assert.That(enemy, Has.LostHp(9));
        Assert.That(enemy, Has.PowerAmount<Doom>(9));
    }


    [Test]
    public async Task TestWithDeathPreventionPower()
    {
        Creature enemy = GetEnemy();
        Player player = GetPlayer();

        await PowerCmd.Apply<Strength>(player.Creature, enemy.MaxHp, null, null);
        await PowerCmd.Apply<HardToKill>(enemy, 9, null, null);

        await Play<BlightStrike>(enemy);

        // make sure doom still gets applied for the total damage dealt,
        // even though technically the monster only lost maxHp/2 (since it healed back to half from exoskeleton)
        Assert.That(enemy, Has.PowerAmount<Doom>(enemy.MaxHp));
    }
}
