using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class BurningPactTest : ModelTest
{
    [Test]
    public async Task TestWithEmptyHandAndDrawPile()
    {
        await Play<BurningPact>();

        // Note: This means Burning Pact should *not* draw itself.
        Assert.That(GetPile(PileType.Hand).Cards, Is.Empty);
        Assert.That(GetPile(PileType.Exhaust).Cards, Is.Empty);
    }

    [Test]
    public async Task TestWithOneCardInHand()
    {
        CardPile hand = GetPile(PileType.Hand);
        await CardPileCmd.Add(CreateCard<Bash>(), hand);

        await Play<BurningPact>();

        Assert.That(GetPile(PileType.Hand).Cards, Is.Empty);
        Assert.That(GetPile(PileType.Exhaust), Has.Cards(typeof(Bash)));
    }

    [Test]
    public async Task TestWithTwoCardsInHand()
    {
        CardPile hand = GetPile(PileType.Hand);
        CardModel card = CreateCard<BodySlam>();
        await CardPileCmd.Add(card, hand);
        await CardPileCmd.Add(CreateCard<Bash>(), hand);

        PrepareToSelect(card);
        await Play<BurningPact>();

        Assert.That(hand, Has.Cards(typeof(Bash)));
        Assert.That(GetPile(PileType.Exhaust), Has.Cards(typeof(BodySlam)));
    }

    [Test]
    public async Task TestWithOneCardInDrawPile()
    {
        CardPile drawPile = GetPile(PileType.Draw);
        await CardPileCmd.Add(CreateCard<StrikeSilent>(), drawPile);

        await Play<BurningPact>();

        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(StrikeSilent)));
    }

    [Test]
    public async Task TestWithThreeCardsInDrawPile()
    {
        CardPile drawPile = GetPile(PileType.Draw);

        for (int i = 0; i < 3; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), drawPile);
        }

        await Play<BurningPact>();

        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(DefendSilent), typeof(DefendSilent)));
        Assert.That(drawPile, Has.Cards(typeof(DefendSilent)));
    }

    [Test]
    public async Task TestUpgraded()
    {
        CardPile drawPile = GetPile(PileType.Draw);

        for (int i = 0; i < 4; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), drawPile);
        }

        await PlayUpgraded<BurningPact>();

        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(DefendSilent), typeof(DefendSilent), typeof(DefendSilent)));
        Assert.That(drawPile, Has.Cards(typeof(DefendSilent)));
    }
}
