using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class ChargeBatteryTest : ModelTest
{
    [Test]
    public async Task TestGainsNoEnergyThisTurn()
    {
        await Play<ChargeBattery>();
        Assert.That(GetPlayer().<PERSON><PERSON>ture, Has.Block(7));
        Assert.That(GetPlayer(), Has.SpentEnergy(1));
    }

    [Test]
    public async Task TestGainsEnergyNextTurn()
    {
        await Play<ChargeBattery>();
        await PassToNextPlayerTurn();

        Assert.That(GetPlayer(), Has.ExtraEnergy(1));
    }

    [Test]
    public async Task TestUpgraded()
    {
        await PlayUpgraded<ChargeBattery>();
        Assert.That(GetPlayer().<PERSON>reature, Has<PERSON>Block(10));
    }
}