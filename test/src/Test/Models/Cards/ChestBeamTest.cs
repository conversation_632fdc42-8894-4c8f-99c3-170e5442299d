using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Afflictions;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Potions;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class ChestBeamTest : ModelTest
{
    [Test]
    public async Task TestBaseDamage()
    {
        await Play<ChestBeam>(GetEnemy());

        Assert.That(GetEnemy(), Has.LostHp(22));
    }

    [Test]
    public async Task TestCooldown()
    {
        CardModel card = CreateCard<ChestBeam>();
        await Play(card, GetEnemy());

        Assert.That(card, Has.Affliction(ModelDb.Affliction<Cooldown>()));
    }

    [Test]
    public async Task TestWithHistoryCourse()
    {
        Player player = GetPlayer();
        await RelicCmd.Obtain<HistoryCourse>(player);

        // fill draw pile so we don't draw chest beam on our next turn
        CardPile drawPile = GetPile(PileType.Draw);
        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), drawPile);
        }

        CardModel card = CreateCard<ChestBeam>();
        await Play(card, GetEnemy());

        await PassToNextPlayerTurn();

        // attack from previous turn. overriding cooldown
        Assert.That(GetEnemy(), Has.LostHp(44));
    }

    [Test]
    public async Task TestWithMultiplay()
    {
        await UsePotion<Duplicator>();

        CardModel card = CreateCard<ChestBeam>();
        await Play(card, GetEnemy());

        // attacks twice
        Assert.That(GetEnemy(), Has.LostHp(44));
        // cooldown increased to 4 because we forced it to override te cooldown affliction
        Assert.That(card.Affliction!.Amount, Is.EqualTo(4));
    }
}
