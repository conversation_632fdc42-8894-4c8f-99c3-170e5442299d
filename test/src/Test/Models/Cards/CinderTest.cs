using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class CinderTest : ModelTest
{
    [Test]
    public async Task TestExhaustCards()
    {
        await CardPileCmd.Add(CreateCard<StrikeIronclad>(), PileType.Draw);
        await CardPileCmd.Add(CreateCard<StrikeIronclad>(), PileType.Draw);
        await CardPileCmd.Add(CreateCard<Bash>(), PileType.Draw);

        await Play<Cinder>(GetEnemy());

        Assert.That(GetPile(PileType.Exhaust), Has.Cards(typeof(StrikeIronclad)));
        Assert.That(GetPile(PileType.Draw), Has.Cards(typeof(Bash),typeof(StrikeIronclad)));
    }

    [Test]
    public async Task TestShufflesOnEmptyDrawPile()
    {
        for (int i = 0; i < 3; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), PileType.Discard);
        }

        await Play<Cinder>(GetEnemy());

        // After shuffling, all Strikes end up in the draw pile. One stays there...
        Assert.That(GetPile(PileType.Draw), Has.Cards(typeof(StrikeIronclad),typeof(StrikeIronclad)));

        // ...the rest get exhausted by Cinder.
        Assert.That(GetPile(PileType.Exhaust), Has.Cards(typeof(StrikeIronclad)));

        // Cinder itself ends up in the discard pile after it's being done played (which is after the shuffle).
        Assert.That(GetPile(PileType.Discard), Has.Cards(typeof(Cinder)));
    }
}
