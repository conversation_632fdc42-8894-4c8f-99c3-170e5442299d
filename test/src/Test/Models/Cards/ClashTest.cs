using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Test.Exceptions;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class ClashTest : ModelTest
{
    [Test]
    public async Task TestPlayability()
    {
        await CardPileCmd.Add(CreateCard<MockAttackCard>(), GetPile(PileType.Hand));
        await Play<Clash>(GetEnemy());
        Assert.That(GetEnemy(), <PERSON>.<PERSON>p(14));
    }

    [Test]
    public async Task TestWithNonAttackInHand()
    {
        Creature enemy = GetEnemy();

        await CardPileCmd.Add(CreateCard<MockAttackCard>(), GetPile(PileType.Hand));
        await CardPileCmd.Add(CreateCard<MockAttackCard>(), GetPile(PileType.Hand));
        await CardPileCmd.Add(CreateCard<MockSkillCard>(), GetPile(PileType.Hand));

        Assert.That(async () => await Play<Clash>(enemy), Throws.TypeOf<TestCardPlayException>());
    }
}
