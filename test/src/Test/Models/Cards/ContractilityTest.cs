using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class ContractilityTest : ModelTest
{
    [Test]
    public async Task TestGainBlockNextTurn()
    {
        Creature player = GetPlayer().Creature;
        await CreatureCmd.GainBlock(player, 9, BlockProps.nonCardUnpowered, null);

        await Play<Contractility>();
        await PassToNextPlayerTurn();

        Assert.That(player, Has.Block(9));
    }
    
    [Test]
    public async Task TestDoesNothingInTwoTurns()
    {
        Creature player = GetPlayer().Creature;
        await CreatureCmd.GainBlock(player, 9, BlockProps.nonCardUnpowered, null);

        await Play<Contractility>();
        await PassToNextPlayerTurn();
        await PassToNextPlayerTurn();

        Assert.That(player, <PERSON>.Block(0));
    }

    [Test]
    public async Task TestStacksCorrectly()
    {
        Creature player = GetPlayer().Creature;
        await CreatureCmd.GainBlock(player, 9, BlockProps.nonCardUnpowered, null);

        for (int i = 0; i < 2; i++)
        {
            await Play<Contractility>();
        }

        await PassToNextPlayerTurn();

        Assert.That(player, Has.Block(18));
    }
}