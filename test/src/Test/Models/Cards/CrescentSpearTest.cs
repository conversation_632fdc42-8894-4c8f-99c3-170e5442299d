using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class CrescentSpearTest : ModelTest
{
    [Test]
    public async Task TestBaseDamage()
    {
        Creature enemy = GetEnemy();

        await Play<CrescentSpear>(enemy);

        Assert.That(enemy, Has.LostHp(8));
    }

    [Test]
    public async Task TestWith1Star()
    {
        Creature enemy = GetEnemy();

        await PlayerCmd.GainStars(1, GetPlayer());
        await Play<CrescentSpear>(enemy);

        // 6 + 2
        Assert.That(enemy, <PERSON>.LostHp(9));
    }

    [Test]
    public async Task TestWith3Stars()
    {
        Creature enemy = GetEnemy();

        await PlayerCmd.GainStars(3, GetPlayer());
        await Play<CrescentSpear>(enemy);

        // 8 + 1 + 1 + 1
        Assert.That(enemy, <PERSON>.<PERSON>Hp(11));
    }
}