using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class DarkEmbraceTest : ModelTest
{
    [Test]
    public async Task TestDrawsOnExhaust()
    {
        // Put a card in the draw pile to be drawn by Dark Embrace
        await CardPileCmd.Add(CreateCard<StrikeIronclad>(), PileType.Draw);

        await Play<DarkEmbrace>();
        await CardCmd.Exhaust(new ThrowingPlayerChoiceContext(), CreateCard<StrikeIronclad>());

        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(StrikeIronclad)));
        Assert.That(GetPlayer(), Has.SpentEnergy(2));
    }

    [Test]
    public async Task TestDrawOnTurn2()
    {
        await Play<DarkEmbrace>();
        await PassToNextPlayerTurn();

        // Put a card in the draw pile to be drawn by Dark Embrace
        await CardPileCmd.Add(CreateCard<StrikeIronclad>(), PileType.Draw);
        await CardCmd.Exhaust(new ThrowingPlayerChoiceContext(), CreateCard<StrikeIronclad>());

        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(StrikeIronclad)));
    }


    [Test]
    public async Task TestUpgradeDecreasesCost()
    {
        await PlayUpgraded<DarkEmbrace>();
        Assert.That(GetPlayer(), Has.SpentEnergy(1));
    }

    [Test]
    public async Task TestWithEtherealCard()
    {
        CardPile drawPile = GetPile(PileType.Draw);
        CardPile hand = GetPile(PileType.Hand);

        for (int i = 0; i < 7; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), drawPile);
        }

        await CardPileCmd.Add(CreateCard<Dazed>(), hand);
        await Play<DarkEmbrace>();
        await PassToNextPlayerTurn();

        // Since Dazed is Ethereal, it exhausts at end of turn, which should trigger Dark Embrace to draw a card.
        // Since the end-of-turn hand flush event has already occurred, the drawn card should still be in your hand
        // on your next turn, for a total of 6 cards in hand (5 default + 1 from Dark Embrace).
        Assert.That(hand.Cards.Count, Is.EqualTo(6));

        // Only 1 card should be left in your draw pile, since you started with 7 and 6 were drawn.
        Assert.That(drawPile.Cards.Count, Is.EqualTo(1));
    }
}
