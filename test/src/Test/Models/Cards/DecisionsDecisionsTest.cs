using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class DecisionsDecisionsTest : ModelTest
{
    [Test]
    public async Task TestBaseStarCost()
    {
        Player player = GetPlayer();

        await PlayerCmd.GainStars(5, player);
        await Play<DecisionsDecisions>();

        Assert.That(player.PlayerCombatState!.Stars, Is.EqualTo(0));
    }

    [Test]
    public async Task TestDrawsCardsPlaysSkillMultipleTimes()
    {
        Player player = GetPlayer();

        for (int i = 0; i < 4; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendRegent>(), PileType.Draw);
        }

        await PlayerCmd.GainStars(5, player);
        PrepareToSelectAtIndices(0);
        await Play<DecisionsDecisions>();

        // 5 block * 3 plays
        Assert.That(player.Creature, Has.Block(15));
        Assert.That(GetPile(PileType.Draw), Has.Cards(typeof(DefendRegent)));
    }

    [Test]
    public async Task TestIncreaseRepeatOnUpgrade()
    {
        Player player = GetPlayer();

        for (int i = 0; i < 6; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendRegent>(), PileType.Draw);
        }

        await PlayerCmd.GainStars(5, player);
        PrepareToSelectAtIndices(0);
        await PlayUpgraded<DecisionsDecisions>();

        // 5 block * 3 plays
        Assert.That(player.Creature, Has.Block(15));
        Assert.That(GetPile(PileType.Draw), Has.Cards(typeof(DefendRegent)));
    }

    [Test]
    public async Task TestDoesNotPlayNonSkills()
    {
        Player player = GetPlayer();

        await PlayerCmd.GainStars(5, player);
        await CardPileCmd.Add(CreateCard<FlickFlack>(), PileType.Hand);
        await Play<DecisionsDecisions>();

        Assert.That(GetEnemy(), Has.LostHp(0));
    }
}
