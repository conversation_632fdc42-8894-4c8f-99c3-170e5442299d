using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class DodgeAndRollTest : ModelTest
{
    [Test]
    public async Task TestBaseBlockThisTurn()
    {
        await Play<DodgeAndRoll>();
        Assert.That(GetPlayer().Creature, Has.Block(4));
    }

    [Test]
    public async Task TestWhenCurrentlyHaveMaxBlock()
    {
        // unable to get any more block this turn
        await CreatureCmd.GainBlock(GetPlayer().Creature, 999, BlockProps.nonCardUnpowered, null);
        await Play<DodgeAndRoll>();

        await PassToNextPlayerTurn();
        Assert.That(GetPlayer().Creature, Has.Block(4));
    }

    [Test]
    public async Task TestAffectedByDex()
    {
        await PowerCmd.Apply<Dexterity>(GetPlayer().Creature, 2, null, null);
        await Play<DodgeAndRoll>();

        await PassToNextPlayerTurn();
        Assert.That(GetPlayer().Creature, Has.Block(6));
    }

    [Test]
    public async Task TestAffectedByFrail()
    {
        await PowerCmd.Apply<Frail>(GetPlayer().Creature, 1, null, null);
        await Play<DodgeAndRoll>();

        await PassToNextPlayerTurn();
        Assert.That(GetPlayer().Creature, Has.Block(3));
    }

    [Test]
    public async Task TestAgainstAfterImage()
    {
        await Play<AfterImage>();
        await Play<DodgeAndRoll>();
        Assert.That(GetPlayer().Creature, Has.Block(5));
        Assert.That(GetPlayer().Creature, Has.PowerAmount<BlockNextTurn>(4));
    }

    [Test]
    public async Task TestBaseBlockNextTurn()
    {
        await Play<DodgeAndRoll>();
        await PassToNextPlayerTurn();

        Assert.That(GetPlayer().Creature, Has.Block(4));
    }

    [Test]
    public async Task TestUpgradedBlockThisTurn()
    {
        await PlayUpgraded<DodgeAndRoll>();
        Assert.That(GetPlayer().Creature, Has.Block(6));
    }

    [Test]
    public async Task TestUpgradedBlockNextTurn()
    {
        await PlayUpgraded<DodgeAndRoll>();
        await PassToNextPlayerTurn();

        Assert.That(GetPlayer().Creature, Has.Block(6));
    }
}
