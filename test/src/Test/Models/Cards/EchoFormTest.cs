using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Test.Exceptions;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class EchoFormTest : ModelTest
{
    [Test]
    public async Task TestBaseDoubling()
    {
        Creature enemy = GetEnemy();

        await Play<EchoForm>();

        await PassToNextPlayerTurn();

        // Attack twice, so we can ensure that the first card is doubled but the second one isn't.
        for (int i = 0; i < 2; i++)
        {
            await Play<MockAttackCard>(enemy);
        }

        // 6*2 + 6 = 18
        Assert.That(enemy, Has.LostHp(18));
    }

    [Test]
    public async Task TestWithNormality()
    {
        Creature enemy = GetEnemy();

        await Play<EchoForm>();

        await PassToNextPlayerTurn();

        CardPile hand = GetPile(PileType.Hand);
        await CardPileCmd.Add(CreateCard<Normality>(), hand);

        // Attack twice, so we can ensure that the first card is doubled but the one isn't.
        for (int i = 0; i < 2; i++)
        {
            await Play<MockAttackCard>(enemy);
        }

        // Attack a third time and ensure that the card isn't played because of Normality. This means Normality counts
        // the "first" card play as two since it's doubled.
        Assert.That(async () => await Play<MockAttackCard>(enemy), Throws.TypeOf<TestCardPlayException>());

        // 6*2 + 6 = 18
        Assert.That(enemy, Has.LostHp(18));
    }

    [Test]
    public async Task TestWithAmplify()
    {
        await Play<Amplify>();
        await Play<EchoForm>();

        // Echo Form should be duplicated by Amplify once.
        Assert.That(GetPlayer().Creature, Has.PowerAmount<EchoFormPower>(2));
    }

    [Test]
    public async Task TestWithOracleBone()
    {
        await RelicCmd.Obtain<OracleBone>(GetPlayer());
        await Play<EchoForm>();

        // Echo Form should be duplicated by Oracle Bone once.
        Assert.That(GetPlayer().Creature, Has.PowerAmount<EchoFormPower>(2));
    }

    [Test]
    public async Task TestWithSly()
    {
        await Play<EchoForm>();

        await PassToNextPlayerTurn();

        CardModel card = MockAttack().MockKeyword(CardKeyword.Sly);
        await CardPileCmd.Add(card, PileType.Hand);

        PrepareToSelect(card);
        await Play<Survivor>();

        // Ensure that Survivor was played twice, not the attack.
        Assert.That(GetPlayer().Creature, Has.PowerAmount<EchoFormPower>(1));
        Assert.That(GetEnemy(), Has.LostHp(6));
        Assert.That(GetPlayer().Creature, Has.Block(16));
    }

    [Test]
    public async Task TestDupeKillingEnemy()
    {
        // In STS1, due to behavior where the duplicate of a card would not get played if it targeted an already-dead
        // enemy, Echo Form's power would not trigger on a card that killed an enemy, and would instead trigger on the
        // next card.
        // In STS2, we do not have this bug, and this test ensures that it is not replicated.
        // If we want to replicate the behavior, just get rid of this test.
        Creature oneHpMonster = await CreateEnemy<OneHpMonster>();
        await Play<EchoForm>();

        await PassToNextPlayerTurn();

        await Play<MockAttackCard>(oneHpMonster);
        await Play<MockAttackCard>(GetEnemy());

        Assert.That(oneHpMonster, Is.Dead());
        Assert.That(GetEnemy(), Has.LostHp(6));
    }

    [Test]
    public async Task TestNotCountFirstTurn()
    {
        Creature enemy = GetEnemy();

        await Play<EchoForm>();

        // Any Echo Form played during a turn should not count towards cards played that turn, so neither of these
        // should be duplicated
        for (int i = 0; i < 2; i++)
        {
            await Play<MockAttackCard>(enemy);
        }

        Assert.That(enemy, Has.LostHp(12));
    }

    [Test]
    public async Task TestStackingThisTurn()
    {
        Creature enemy = GetEnemy();

        await Play<EchoForm>();
        await Play<EchoForm>();

        await PassToNextPlayerTurn();

        // We have two stacks of Echo Form.
        // The first two attacks should be duplicated, and the third should not be.
        for (int i = 0; i < 3; i++)
        {
            await Play<MockAttackCard>(enemy);
        }

        Assert.That(enemy, Has.LostHp(30));
        Assert.That(GetPlayer().Creature, Has.PowerAmount<EchoFormPower>(2));
    }

    [Test]
    public async Task TestStackingOverTwoTurns()
    {
        Creature enemy = GetEnemy();

        await Play<EchoForm>();
        await PassToNextPlayerTurn();
        await Play<EchoForm>();

        // We have three stacks of Echo Form, but one of them was expended by playing Echo Form, so we have two left
        // this turn.
        // So, the first two attacks should be duplicated, and the third should not be.
        for (int i = 0; i < 3; i++)
        {
            await Play<MockAttackCard>(enemy);
        }

        // We played a total of 5 attacks (3 normally, 2 duplicates).
        Assert.That(enemy, Has.LostHp(30));
        Assert.That(GetPlayer().Creature, Has.PowerAmount<EchoFormPower>(3));
    }

    [Test]
    public async Task TestStackingOverMultipleTurns()
    {
        Creature enemy = GetEnemy();

        await Play<EchoForm>();
        await Play<EchoForm>();

        await PassToNextPlayerTurn();

        // These two should duplicate since we have two stacks of echo form
        for (int i = 0; i < 2; i++)
        {
            await Play<MockAttackCard>(enemy);
        }

        await Play<EchoForm>();

        // This card should not duplicate since we applied the Echo Form power in the middle of the turn
        await Play<MockAttackCard>(enemy);

        await PassToNextPlayerTurn();

        // All three of these cards should duplicate since we had three stacks of Echo Form at the beginning of the turn
        for (int i = 0; i < 3; i++)
        {
            await Play<MockAttackCard>(enemy);
        }

        // We played a total of 11 attacks (6 normally, 5 duplicates).
        Assert.That(enemy, Has.LostHp(66));
        Assert.That(GetPlayer().Creature, Has.PowerAmount<EchoFormPower>(3));
    }
}
