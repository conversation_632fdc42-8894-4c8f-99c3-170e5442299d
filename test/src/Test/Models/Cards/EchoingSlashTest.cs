using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class EchoingSlashTest : ModelTest
{
    [Test]
    public async Task TestWhenNoEnemiesKilled()
    {
        await Play<EchoingSlash>();
        Assert.That(GetEnemy(), Has.LostHp(9));
    }

    [Test]
    public async Task TestWhen1EnemyAreKilled()
    {
        Creature enemy = await CreateEnemy<OneHpMonster>();
        await Play<EchoingSlash>();

        Assert.That(enemy, Is.Dead());
        Assert.That(GetEnemy(), Has.LostHp(18)); // 9 * 2
    }

    [Test]
    public async Task TestWhen2EnemiesAreKilled()
    {
        Creature enemy1 = await <PERSON>reate<PERSON>nemy<OneHpMonster>();
        Creature enemy2 = await CreateEnemy<OneHpMonster>();
        await Play<EchoingSlash>();

        Assert.That(enemy1, Is.Dead());
        Assert.That(enemy2, Is.Dead());
        Assert.That(GetEnemy(), Has.LostHp(27)); // 9 * 3
    }
}
