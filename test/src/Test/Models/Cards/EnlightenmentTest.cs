using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class EnlightenmentTest : ModelTest
{
    [Test]
    public async Task TestReducesNonZeroCostsTo1()
    {
        CardPile hand = GetPile(PileType.Hand);
        CardModel originalCost3 = CreateCard<Bludgeon>();
        CardModel originalCost2 = CreateCard<Shockwave>();
        CardModel originalCost1 = CreateCard<StrikeIronclad>();
        CardModel originalCost0 = CreateCard<Anger>();

        await CardPileCmd.Add(originalCost3, hand);
        await CardPileCmd.Add(originalCost2, hand);
        await CardPileCmd.Add(originalCost1, hand);
        await CardPileCmd.Add(originalCost0, hand);

        await Play<Enlightenment>();

        Assert.That(originalCost3, Has.EnergyCost(1));
        Assert.That(originalCost2, Has.EnergyCost(1));
        Assert.That(originalCost1, Has.EnergyCost(1));
        Assert.That(originalCost0, Has.EnergyCost(0));
    }

    [Test]
    public async Task TestLasts1Turn()
    {
        CardPile hand = GetPile(PileType.Hand);
        CardModel card = CreateCard<Bludgeon>();
        await CardPileCmd.Add(card, hand);

        await Play<Enlightenment>();
        await PassToNextPlayerTurn();

        Assert.That(card, Has.EnergyCost(3));
    }

    [Test]
    public async Task TestUpgradedLastsWholeCombat()
    {
        CardPile hand = GetPile(PileType.Hand);
        CardModel card = CreateCard<Bludgeon>();
        await CardPileCmd.Add(card, hand);

        await PlayUpgraded<Enlightenment>();
        await PassToNextPlayerTurn();

        Assert.That(card, Has.EnergyCost(1));
    }
}
