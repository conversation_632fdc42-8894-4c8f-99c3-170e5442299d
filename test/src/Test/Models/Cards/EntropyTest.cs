using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class EntropyTest : ModelTest
{
    [Test]
    public async Task TestTransformCard()
    {
        CardPile drawPile = GetPile(PileType.Draw);

        for (int i = 0; i < 7; i++)
        {
            await CardPileCmd.Add(MockSkill(), drawPile);
        }

        await Play<Entropy>();

        PrepareToSelectAtIndices(0);
        await PassToNextPlayerTurn();

        Assert.That(GetPile(PileType.Hand).Cards.Count(c => c is MockSkillCard), Is.EqualTo(4));
    }

    [Test]
    public async Task TestStacking()
    {
        CardPile drawPile = GetPile(PileType.Draw);

        for (int i = 0; i < 7; i++)
        {
            await CardPileCmd.Add(MockSkill(), drawPile);
        }

        await Play<Entropy>();
        await Play<Entropy>();

        PrepareToSelectAtIndices(0, 1);
        await PassToNextPlayerTurn();

        Assert.That(GetPile(PileType.Hand).Cards.Count(c => c is MockSkillCard), Is.EqualTo(3));
    }

    [Test]
    public async Task TestUpgrade()
    {
        CardPile drawPile = GetPile(PileType.Draw);

        for (int i = 0; i < 7; i++)
        {
            await CardPileCmd.Add(MockSkill(), drawPile);
        }

        await PlayUpgraded<Entropy>();

        PrepareToSelectAtIndices(0, 1);
        await PassToNextPlayerTurn();

        Assert.That(GetPile(PileType.Hand).Cards.Count(c => c is MockSkillCard), Is.EqualTo(3));
    }
}
