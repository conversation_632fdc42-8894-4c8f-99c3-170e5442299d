using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class EscapePlanTest : ModelTest
{
    [Test]
    public async Task TestWithEmptyDrawPile()
    {
        await Play<EscapePlan>();

        Assert.That(GetPlayer().Creature, Has.Block(0));
    }

    [Test]
    public async Task TestWhenDrawingANonSkill()
    {
        await CardPileCmd.Add(CreateCard<StrikeSilent>(), PileType.Draw);
        await Play<EscapePlan>();

        Assert.That(GetPlayer().Creature, Has.Block(0));
    }

    [Test]
    public async Task TestWhenDrawingASkill()
    {
        await CardPileCmd.Add(CreateCard<DefendSilent>(), PileType.Draw);
        await Play<EscapePlan>();

        Assert.That(GetPlayer().Creature, <PERSON><PERSON>Block(3));
    }

    [Test]
    public async Task TestUpgradedBlock()
    {
        await CardPileCmd.Add(CreateCard<DefendSilent>(), PileType.Draw);
        await PlayUpgraded<EscapePlan>();

        Assert.That(GetPlayer().Creature, Has.Block(5));
    }
}
