using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class FastenTest : ModelTest
{
    [Test]
    public async Task TestAddsBlockToDefends()
    {
        await Play<Fasten>();
        await Play<DefendIronclad>();
        await Play<DefendSilent>();

        Assert.That(GetPlayer().Creature, Has.Block(20));
    }

    [Test]
    public async Task TestAddsBlockToUltimateDefend()
    {
        await Play<Fasten>();
        await Play<UltimateDefend>();

        Assert.That(GetPlayer().<PERSON>reature, Has.Block(16));
    }

    [Test]
    public async Task TestAddsNoBlockToOtherSkills()
    {
        await Play<Fasten>();
        await Play<Survivor>();

        Assert.That(GetPlayer().Creature, Has.Block(8));
    }

    [Test]
    public async Task TestAddsNoBlockToFeelNoPain()
    {
        await Play<Fasten>();
        await Play<FeelNoPain>();
        await Play<Pummel>(GetEnemy());

        Assert.That(GetPlayer().Creature, <PERSON>.Block(3));
    }
}
