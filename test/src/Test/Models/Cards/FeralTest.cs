using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Orbs;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class FeralTest : ModelTest
{
    [Test]
    public async Task TestStrikesCostLess()
    {
        await Play<Feral>();
        await PassToNextPlayerTurn();

        await Play<StrikeIronclad>(GetEnemy()); // 1 - 1
        await Play<PerfectedStrike>(GetEnemy()); // 2 - 1
        await Play<BlightStrike>(GetEnemy()); // 1 - 1

        Assert.That(GetPlayer(), Has.SpentEnergy(1));
    }

    [Test]
    public async Task TestNonStrikesCostTheSame()
    {
        await Play<Feral>();
        await PassToNextPlayerTurn();

        await Play<Pummel>(GetEnemy());
        await Play<MockAttackCard>(GetEnemy());

        Assert.That(GetPlayer(), Has.SpentEnergy(2));
    }

    [Test]
    public async Task TestStacking()
    {
        await Play<Feral>();
        await Play<Feral>();

        await PassToNextPlayerTurn();

        await Play<StrikeIronclad>(GetEnemy()); // 1 - 2
        await Play<PerfectedStrike>(GetEnemy()); // 2 - 2
        await Play<BlightStrike>(GetEnemy()); // 1 - 2

        Assert.That(GetPlayer(), Has.SpentEnergy(0));
    }
}
