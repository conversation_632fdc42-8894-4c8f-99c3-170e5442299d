using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class FiendFireTest : ModelTest
{
    [Test]
    public async Task TestBaseDamage()
    {
        Creature enemy = GetEnemy();

        await Play<FiendFire>(enemy);

        Assert.That(enemy, Has.LostHp(0));
    }

    [Test]
    public async Task TestDamageAndExhaustWithOneCardInHand()
    {
        Creature enemy = GetEnemy();
        CardPile hand = GetPile(PileType.Hand);
        await CardPileCmd.Add(CreateCard<StrikeIronclad>(), hand);

        await Play<FiendFire>(enemy);

        Assert.That(enemy, Has.LostHp(7));
        Assert.That(GetPile(PileType.Exhaust), Has.Cards(typeof(StrikeIronclad), typeof(FiendFire)));
    }

    [Test]
    public async Task TestDamageAndExhaustWithTwoCardsInHand()
    {
        Creature enemy = GetEnemy();
        CardPile hand = GetPile(PileType.Hand);

        for (int i = 0; i < 2; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), hand);
        }

        await Play<FiendFire>(enemy);

        Assert.That(enemy, Has.LostHp(14));
        Assert.That(GetPile(PileType.Exhaust), Has.Cards(typeof(StrikeIronclad), typeof(StrikeIronclad), typeof(FiendFire)));
    }
}
