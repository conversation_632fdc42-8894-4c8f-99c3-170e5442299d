using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class FightMeTest : ModelTest
{
    [Test]
    public async Task TestBaseDamage()
    {
        await Play<FightMe>(GetEnemy());

        Assert.That(GetEnemy(), Has.LostHp(20));
    }

    [Test]
    public async Task TestStrengthGain()
    {
        await Play<FightMe>(GetEnemy());

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(2));
        Assert.That(GetEnemy(), Has.PowerAmount<Strength>(2));
    }
}
