using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class FisticuffsTest : ModelTest
{
    [Test]
    public async Task TestDamagAndBlock()
    {
        Creature enemy = GetEnemy();

        await Play<Fisticuffs>(enemy);

        Assert.That(enemy, Has.LostHp(7));
        Assert.That(GetPlayer().Creature, Has.Block(7));
    }

    [Test]
    public async Task TestWithBlock()
    {
        Creature enemy = GetEnemy();

        await CreatureCmd.GainBlock(enemy, 2, BlockProps.nonCardUnpowered, null);
        await Play<Fisticuffs>(enemy);

        Assert.That(enemy, Has.LostHp(5));
        Assert.That(GetPlayer().Creature, Has.Block(5));
    }

    [Test]
    public async Task TestWithStrength()
    {
        Creature enemy = GetEnemy();
        Player player = GetPlayer();

        await PowerCmd.Apply<Strength>(player.Creature, 2, null, null);
        await Play<Fisticuffs>(enemy);

        Assert.That(enemy, Has.LostHp(9));
        Assert.That(GetPlayer().Creature, Has.Block(9));
    }
}