using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class FlechettesTest : ModelTest
{
    [Test]
    public async Task TestBaseDamage()
    {
        Creature enemy = GetEnemy();

        await Play<Flechettes>(enemy);

        Assert.That(enemy, Has.LostHp(0));
    }

    [Test]
    public async Task TestDamageWithNonSkillInHand()
    {
        Creature enemy = GetEnemy();
        await CardPileCmd.Add(CreateCard<StrikeSilent>(), PileType.Hand);

        await Play<Flechettes>(enemy);

        Assert.That(enemy, Has.LostHp(0));
    }

    [Test]
    public async Task TestDamageWith1SkillInHand()
    {
        Creature enemy = GetEnemy();
        await CardPileCmd.Add(CreateCard<DefendSilent>(), PileType.Hand);

        await Play<Flechettes>(enemy);

        Assert.That(enemy, Has.LostHp(5));
    }

    [Test]
    public async Task TestDamageWith2SkillsInHand()
    {
        Creature enemy = GetEnemy();
        CardPile hand = GetPile(PileType.Hand);

        for (int i = 0; i < 2; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), hand);
        }

        await Play<Flechettes>(enemy);

        Assert.That(enemy, Has.LostHp(10));
    }
}
