using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class FlexTest : ModelTest
{
    [Test]
    public async Task TestBaseStrengthThisTurn()
    {
        Creature player = GetPlayer().Creature;

        await Play<Flex>();

        Assert.That(player, Has.PowerAmount<Strength>(2));
        Assert.That(player, Has.PowerAmount<StrengthDown>(2));
    }

    [Test]
    public async Task TestUpgradedStrengthThisTurn()
    {
        Creature player = GetPlayer().Creature;

        await PlayUpgraded<Flex>();

        Assert.That(player, Has.Power<PERSON>mount<Strength>(4));
        Assert.That(player, Has.PowerAmount<StrengthDown>(4));
    }

    [Test]
    public async Task TestRemovesSelfAndStrengthAtEndOfTurn()
    {
        await Play<Flex>();
        await PassToNextPlayerTurn();

        Creature player = GetPlayer().Creature;
        Assert.That(player, Has.PowerAmount<Strength>(0));
        Assert.That(player, Has.PowerAmount<StrengthDown>(0));
    }

    [Test]
    public async Task TestArtifactBlocksStrengthDown()
    {
        Creature player = GetPlayer().Creature;

        await Play<Flex>();
        await PowerCmd.Apply<Artifact>(player, 1, null, null);

        await PassToNextPlayerTurn();

        Assert.That(player, Has.PowerAmount<Strength>(2));
        Assert.That(player, Has.PowerAmount<StrengthDown>(0));
    }
}
