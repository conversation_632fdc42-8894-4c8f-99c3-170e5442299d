using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class FlyingKneeTest : ModelTest
{
    [Test]
    public async Task TestEnergyGain()
    {
        await Play<FlyingKnee>(GetEnemy());
        await PassToNextPlayerTurn();

        Assert.That(GetEnemy(), <PERSON>.LostHp(8));
        Assert.That(GetPlayer(), Has.ExtraEnergy(1));
    }

    [Test]
    public async Task TestUpgrade()
    {
        await PlayUpgraded<FlyingKnee>(GetEnemy());
        await PassToNextPlayerTurn();

        Assert.That(GetEnemy(), <PERSON>.LostHp(11));
        Assert.That(GetPlayer(), Has.ExtraEnergy(1));
    }
}