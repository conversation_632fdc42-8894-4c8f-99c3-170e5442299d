using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class GeneticAlgorithmTest : ModelTest
{
    [Test]
    public async Task TestBaseBlock()
    {
        await Play<GeneticAlgorithm>();
        Assert.That(GetPlayer().Creature, Has.Block(1));
    }

    [Test]
    public async Task TestBlockIncrease()
    {
        await CardPileCmd.Add(CreateCard<GeneticAlgorithm>(CardScope.Climb), PileType.Deck);

        for (int i = 0; i < 3; i++)
        {
            await RestartCombat();
            await Play(GetPile(PileType.Hand).Cards[0]);
        }

        Assert.That(GetPlayer().Creature, Has.Block(5));
    }

    [Test]
    public async Task TestUpgradedBlockIncrease()
    {
        await CardPileCmd.Add(Upgrade(CreateCard<GeneticAlgorithm>(CardScope.Climb)), PileType.Deck);

        for (int i = 0; i < 3; i++)
        {
            await RestartCombat();
            await Play(GetPile(PileType.Hand).Cards[0]);
        }

        Assert.That(GetPlayer().Creature, Has.Block(7));
    }

    [Test]
    public async Task TestUpgradeBetweenRooms()
    {
        CardModel geneticAlgorithm = CreateCard<GeneticAlgorithm>(CardScope.Climb);
        await CardPileCmd.Add(geneticAlgorithm, PileType.Deck);
        await RestartCombat();

        await Play(GetPile(PileType.Hand).Cards[0]);

        Upgrade(geneticAlgorithm);

        for (int i = 0; i < 2; i++)
        {
            await RestartCombat();
            await Play(GetPile(PileType.Hand).Cards[0]);
        }

        Assert.That(GetPlayer().Creature, Has.Block(6));
    }

    [Test]
    public async Task TestDowngrade()
    {
        await CardPileCmd.Add(Upgrade(CreateCard<GeneticAlgorithm>(CardScope.Climb)), PileType.Deck);
        await RestartCombat();

        // Play Genetic Algorithm+, which will increase the block gain from the NEXT card play
        // from 1 to 4.
        await Play(GetPile(PileType.Hand).Cards[0]);

        await RestartCombat();

        // Downgrade Genetic Algorithm. This should reduce future block buffs this combat.
        CardModel geneticAlgorithm = GetPile(PileType.Hand).Cards[0];
        CardCmd.Downgrade(geneticAlgorithm);

        await Play(GetPile(PileType.Hand).Cards[0]);

        // Playing Genetic Algorithm after downgrading should still gain 4 block, since
        // downgrading doesn't remove the block buff from playing the card last combat.
        Assert.That(GetPlayer().Creature, Has.Block(4));
    }
}
