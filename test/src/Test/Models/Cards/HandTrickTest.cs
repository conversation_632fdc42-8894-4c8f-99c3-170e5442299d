using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class HandTrickTest : ModelTest
{
    [Test]
    public async Task TestWithEmptyHand()
    {
        await Play<HandTrick>();

        Assert.That(GetPlayer().Creature, Has.Block(7));
        Assert.That(GetPile(PileType.Hand).Cards, Is.Empty);
    }

    [Test]
    public async Task TestWithAllAttackHand()
    {
        CardModel card = CreateCard<StrikeSilent>();
        await CardPileCmd.Add(card, PileType.Hand);

        await Play<HandTrick>();
        await CardCmd.Discard(new ThrowingPlayerChoiceContext(), card);

        // Make sure discarding Strike didn't play it (meaning it didn't get Sly from Hand Trick).
        Assert.That(GetEnemy(), Has.LostHp(0));
    }

    [Test]
    public async Task TestWith1SkillHand()
    {
        CardModel card = CreateCard<DefendSilent>();
        await CardPileCmd.Add(card, PileType.Hand);
        await Play<HandTrick>();
        await CardCmd.Discard(new ThrowingPlayerChoiceContext(), card);

        // Hand Trick should give Defend Sly, so then discarding Defend should play it.
        // Block from Hand Trick: 7
        // Block from discarding Defend with Sly: 5
        // Total block: 12
        Assert.That(GetPlayer().Creature, Has.Block(12));
    }

    [Test]
    public async Task TestWith2SkillHand()
    {
        CardModel card1 = CreateCard<DefendSilent>();
        await CardPileCmd.Add(card1, PileType.Hand);
        CardModel card2 = CreateCard<Survivor>();
        await CardPileCmd.Add(card2, PileType.Hand);

        PrepareToSelect(card2);
        await Play<HandTrick>();
        await CardCmd.Discard(new NullPlayerChoiceContext(), card2);

        // Hand Trick should give Survivor Sly, so then discarding Survivor should play it.
        // Block from Hand Trick: 7
        // Block from discarding Survivor with Sly: 8
        // Total block: 15
        Assert.That(GetPlayer().Creature, Has.Block(15));
    }
}
