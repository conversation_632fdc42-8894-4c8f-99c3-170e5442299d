using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class HavocTest : ModelTest
{
    [Test]
    public async Task TestWithEmptyDrawPile()
    {
        await Play<Havoc>();

        Assert.That(GetPile(PileType.Exhaust).Cards, Is.Empty);
        Assert.That(GetEnemy(), Has.LostHp(0));
        Assert.That(GetPlayer(), Has.SpentEnergy(1));
    }

    [Test]
    public async Task TestWithCardInDrawPile()
    {
        await CardPileCmd.Add(MockAttack(), PileType.Draw);

        await Play<Havoc>();

        Assert.That(GetPile(PileType.Exhaust), Has.Cards(typeof(MockAttackCard)));
        Assert.That(GetEnemy(), <PERSON><PERSON>Hp(6));
    }

    [Test]
    public async Task TestDoesNotExhaustPowers()
    {
        await CardPileCmd.Add(MockPower(), PileType.Draw);
        await Play<Havoc>();

        Assert.That(GetPile(PileType.Exhaust).Cards, Is.Empty);
        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(2));
    }

    [Test]
    public async Task TestExhaustsUnplayableCardsWithoutPlayingThem()
    {
        await CardPileCmd.Add(MockSkill().MockKeyword(CardKeyword.Unplayable), PileType.Draw);
        await Play<Havoc>();

        Assert.That(GetPile(PileType.Exhaust), Has.Cards(typeof(MockSkillCard)));
        Assert.That(GetPlayer().Creature, Has.Block(0));
    }

    [Test]
    public async Task TestDoesNotCountTowardsCardPlayTotalsOfAutoPlayedCards()
    {
        await CardPileCmd.Add(CreateCard<CrushingDepths>(), PileType.Draw);
        await Play<Havoc>();

        Assert.That(GetEnemy(), Has.LostHp(0));
    }
}
