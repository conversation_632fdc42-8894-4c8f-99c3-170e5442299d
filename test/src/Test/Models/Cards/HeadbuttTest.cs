using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class HeadbuttTest : ModelTest
{
    [Test]
    public async Task TestBaseDamageWithEmptyDiscardPile()
    {
        Creature enemy = GetEnemy();

        await Play<Headbutt>(enemy);

        Assert.That(enemy, Has.LostHp(9));
        Assert.That(GetPile(PileType.Draw).Cards, Is.Empty);
        Assert.That(GetPile(PileType.Discard), Has.Cards(typeof(Headbutt)));
    }

    [Test]
    public async Task TestWithSingleCardDiscardPile()
    {
        Creature enemy = GetEnemy();
        CardPile discardPile = GetPile(PileType.Discard);
        await CardPileCmd.Add(CreateCard<StrikeIronclad>(), discardPile);

        await Play<Headbutt>(enemy);

        Assert.That(GetPile(PileType.Draw), Has.Cards(typeof(StrikeIronclad)));
        Assert.That(discardPile, Has.Cards(typeof(Headbutt)));
    }

    [Test]
    public async Task TestWithMultiCardDiscardPile()
    {
        Creature enemy = GetEnemy();
        CardPile discardPile = GetPile(PileType.Discard);
        CardModel strike = CreateCard<StrikeIronclad>();
        await CardPileCmd.Add(strike, discardPile);
        CardModel defend = CreateCard<DefendIronclad>();
        await CardPileCmd.Add(defend, discardPile);

        PrepareToSelect(defend);
        await Play<Headbutt>(enemy);

        Assert.That(GetPile(PileType.Draw), Has.Cards(typeof(DefendIronclad)));
        Assert.That(discardPile, Has.Cards(typeof(StrikeIronclad), typeof(Headbutt)));
    }

    [Test]
    public async Task TestUpgradedDamage()
    {
        Creature enemy = GetEnemy();
        await PlayUpgraded<Headbutt>(enemy);

        Assert.That(enemy, Has.LostHp(12));
    }
}
