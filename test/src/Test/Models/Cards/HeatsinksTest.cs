using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class HeatsinksTest : ModelTest
{
    [Test]
    public async Task TestCardDrawWhenPlayingPower()
    {
        for (int i = 0; i < 2; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), GetPile(PileType.Draw));
        }

        await Play<Heatsinks>();
        await Play<AfterImage>();

        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(StrikeIronclad)));
    }

    [Test]
    public async Task TestNoCardDrawWhenPlayingNonPower()
    {
        await CardPileCmd.Add(CreateCard<StrikeIronclad>(), GetPile(PileType.Draw));

        await Play<Heatsinks>();
        await Play<DefendDefect>();

        Assert.That(GetPile(PileType.Hand).Cards, Is.Empty);
    }

    [Test]
    public async Task TestUpgradeDrawsExtraCard()
    {
        for (int i = 0; i < 3; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), GetPile(PileType.Draw));
        }

        await PlayUpgraded<Heatsinks>();
        await Play<AfterImage>();

        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(StrikeIronclad), typeof(StrikeIronclad)));
    }

    [Test]
    public async Task TestDoesNotTriggerSelf()
    {
        await CardPileCmd.Add(CreateCard<StrikeIronclad>(), GetPile(PileType.Draw));

        await Play<Heatsinks>();

        Assert.That(GetPile(PileType.Hand).Cards, Is.Empty);
    }

    [Test]
    public async Task TestWhenPlayingSecondHeatsinks()
    {
        for (int i = 0; i < 2; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), GetPile(PileType.Draw));
        }

        await Play<Heatsinks>();
        await Play<Heatsinks>();

        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(StrikeIronclad)));
    }
}
