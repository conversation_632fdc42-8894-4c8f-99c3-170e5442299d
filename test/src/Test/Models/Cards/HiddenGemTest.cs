using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class HiddenGemTest : ModelTest
{
    [Test]
    public async Task TestShuffle()
    {
        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(MockSkill(), GetPile(PileType.Hand));
        }

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(MockSkill(), GetPile(PileType.Discard));
        }

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(MockSkill(), GetPile(PileType.Draw));
        }

        await Play<HiddenGem>();

        Assert.That(GetPile(PileType.Hand).Cards.Count, Is.EqualTo(5));
        Assert.That(GetPile(PileType.Discard).Cards.Count, Is.EqualTo(1));
        Assert.That(GetPile(PileType.Draw).Cards.Count, Is.EqualTo(10));
    }

    [Test]
    public async Task TestReplayGain()
    {
        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(MockSkill(), GetPile(PileType.Discard));
        }

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(MockSkill(), GetPile(PileType.Draw));
        }

        await Play<HiddenGem>();
        Assert.That(GetPile(PileType.Draw).Cards.Count(c => c.ReplayCount == 2), Is.EqualTo(1));
    }

    [Test]
    public async Task TestUpgradedReplayGain()
    {
        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(MockSkill(), GetPile(PileType.Discard));
        }

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(MockSkill(), GetPile(PileType.Draw));
        }

        await PlayUpgraded<HiddenGem>();
        Assert.That(GetPile(PileType.Draw).Cards.Count(c => c.ReplayCount == 2), Is.EqualTo(0));
        Assert.That(GetPile(PileType.Draw).Cards.Count(c => c.ReplayCount == 3), Is.EqualTo(1));
    }

    [Test]
    public async Task TestDoesNotAddReplayToStatusesOrCurses()
    {
        await CardPileCmd.Add(MockCurse(), GetPile(PileType.Discard));
        await CardPileCmd.Add(MockStatus(), GetPile(PileType.Discard));

        await Play<HiddenGem>();

        IReadOnlyList<CardModel> drawPileCards = GetPile(PileType.Draw).Cards;
        Assert.That(drawPileCards.Count, Is.EqualTo(2));
        Assert.That(drawPileCards.Count(c => c.ReplayCount > 0), Is.EqualTo(0));
    }
}
