using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class IronWaveTest : ModelTest
{
    [Test]
    public async Task TestBaseDamageAndBlock()
    {
        Creature enemy = GetEnemy();

        await Play<IronWave>(enemy);

        Assert.That(GetPlayer().Creature, Has.Block(5));
        Assert.That(enemy, Has.LostHp(5));
    }

    [Test]
    public async Task TestAgainstThorns()
    {
        Creature enemy = GetEnemy();
        await PowerCmd.Apply<Thorns>(enemy, 5, null, null);
        await Play<IronWave>(enemy);

        Assert.That(GetPlayer().Creature, Has.LostHp(0));
        Assert.That(GetPlayer().Creature, <PERSON>.Block(0));
    }

    [Test]
    public async Task TestUpgradedDamageAndBlock()
    {
        Creature enemy = GetEnemy();

        await PlayUpgraded<IronWave>(enemy);

        Assert.That(GetPlayer().Creature, Has.Block(7));
        Assert.That(enemy, Has.LostHp(7));
    }
}
