using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class JugglingTest : ModelTest
{
    [Test]
    public async Task TestStrengthGain()
    {
        await Play<Juggling>();

        await Play(MockAttack(), GetEnemy());
        await Play(MockAttack(), GetEnemy());

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(2));
        Assert.That(GetPlayer().Creature, Has.PowerAmount<StrengthDown>(2));
    }

    [Test]
    public async Task TestDoesNothingForNonAttacks()
    {
        await Play<Juggling>();

        await Play<Barricade>();
        await Play(MockSkill());

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(0));
        Assert.That(GetPlayer().Creature, Has.PowerAmount<StrengthDown>(0));
    }
}
