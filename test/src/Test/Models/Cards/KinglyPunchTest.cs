using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class KinglyPunchTest : ModelTest
{
    [Test]
    public async Task TestInitialDamage()
    {
        Creature enemy = GetEnemy();

        await Play<KinglyPunch>(enemy);

        Assert.That(enemy, Has.LostHp(8));
    }

    [Test]
    public async Task TestAfter1Draw()
    {
        CardModel card = CreateCard<KinglyPunch>();

        await CardPileCmd.Add(card, PileType.Draw);
        await CardPileCmd.Draw(new ThrowingPlayerChoiceContext(), GetPlayer());
        await Play(card, GetEnemy());

        // 8 + 3
        Assert.That(GetEnemy(), Has.LostHp(11));
    }

    [Test]
    public async Task TestAfter2Draws()
    {
        CardModel card = CreateCard<KinglyPunch>();

        await CardPileCmd.Add(card, PileType.Hand);
        await PassToNextPlayerTurn();
        await PassToNextPlayerTurn();

        await Play(card, GetEnemy());

        // 8 + (3 * 2)
        Assert.That(GetEnemy(), Has.LostHp(14));
    }

    [Test]
    public async Task Test2DrawsAndDowngrade()
    {
        CardModel card = CreateCard<KinglyPunch>();
        CardCmd.Upgrade(card);

        await CardPileCmd.Add(card, PileType.Hand);
        await PassToNextPlayerTurn();
        await PassToNextPlayerTurn();

        CardCmd.Downgrade(card);
        await Play(card, GetEnemy());

        // 8 + (5 * 2)
        // If this is failing:
        // * If it's just dealing 8 damage, it's probably because downgrading is incorrectly resetting the damage
        //   increase.
        // * If it's just dealing 14 damage, it's probably because we're incorrectly dynamically calculating the
        //   damage increase. Even though the non-upgraded card's damage increases are lower, the already-applied
        //   damage increases shouldn't be downgraded, just future ones.
        Assert.That(GetEnemy(), Has.LostHp(18));
    }
}
