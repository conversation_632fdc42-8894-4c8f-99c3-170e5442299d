using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class LethalityTest : ModelTest
{
    [Test]
    public async Task TestIncreasesDamage()
    {
        Creature enemy = GetEnemy();

        await Play<Lethality>();
        await Play<MockAttackCard>(enemy);

        // 6 * 1.5
        Assert.That(enemy, <PERSON>.LostHp(9));
    }

    [Test]
    public async Task TestOnlyFirstAttack()
    {
        Creature enemy = GetEnemy();

        await Play<Lethality>();

        for (int i = 0; i < 2; i++)
        {
            await Play<MockAttackCard>(enemy);
        }

        // 6 * 1.5 + 6
        Assert.That(enemy, <PERSON>.LostHp(15));
    }

    [Test]
    public async Task TestResetsBetweenTurns()
    {
        Creature enemy = GetEnemy();

        await Play<Lethality>();
        await Play<MockAttackCard>(enemy);
        await PassToNextPlayerTurn();
        await Play<MockAttackCard>(enemy);

        // 6 * 1.5 + 6 * 1.5
        Assert.That(enemy, Has.LostHp(18));
    }

    [Test]
    public async Task TestWhenPlayedAfterAttack()
    {
        Creature enemy = GetEnemy();

        await Play<MockAttackCard>(enemy);
        await Play<Lethality>();
        await Play<MockAttackCard>(enemy);

        // 6 + 6 * 1.5 (doesn't start counting until after it's played)
        Assert.That(enemy, Has.LostHp(15));
    }

    [Test]
    public async Task TestStacking()
    {
        Creature enemy = GetEnemy();

        for (int i = 0; i < 2; i++)
        {
            await Play<Lethality>();
        }

        for (int i = 0; i < 2; i++)
        {
            await Play<MockAttackCard>(enemy);
        }

        // 6 * 2 + 6
        Assert.That(enemy, Has.LostHp(18));
    }

    [Test]
    public async Task TestUpgrade()
    {
        Creature enemy = GetEnemy();

        await PlayUpgraded<Lethality>();

        for (int i = 0; i < 2; i++)
        {
            await Play<MockAttackCard>(enemy);
        }

        // 6 * 1.75 + 6
        Assert.That(enemy, Has.LostHp(16));
    }

    [Test]
    public async Task TestThatNonAttacksDontCount()
    {
        Creature enemy = GetEnemy();

        await Play<Lethality>();

        await Play<MockSkillCard>();
        await Play<MockAttackCard>(enemy);

        // 6 * 1.5
        Assert.That(enemy, Has.LostHp(9));
    }

    [Test]
    public async Task TestAppliesToOstyAttacks()
    {
        Creature enemy = GetEnemy();

        await OstyCmd.Summon(new ThrowingPlayerChoiceContext(), GetPlayer(), 1, null);
        await Play<Lethality>();
        await Play(MockAttack().MockFromOsty(), enemy);

        // floor(6 * 1.5)
        Assert.That(enemy, Has.LostHp(9));
    }
}
