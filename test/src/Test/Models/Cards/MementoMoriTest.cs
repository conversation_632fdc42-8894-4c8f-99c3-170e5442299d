using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class MementoMoriTest : ModelTest
{
    [Test]
    public async Task TestBaseDamage()
    {
        await Play<MementoMori>(GetEnemy());
        Assert.That(GetEnemy(), Has.LostHp(8));
    }

    [Test]
    public async Task TestWithDiscards()
    {
        await CardCmd.Discard(new ThrowingPlayerChoiceContext(), CreateCard<DefendSilent>());
        await CardCmd.Discard(new ThrowingPlayerChoiceContext(), CreateCard<DefendSilent>());

        await Play<MementoMori>(GetEnemy());

        // 8 + 4 * 2
        Assert.That(GetEnemy(), Has.LostHp(16));
    }

    [Test]
    public async Task TestUpgrade()
    {
        await CardCmd.Discard(new ThrowingPlayerChoiceContext(), CreateCard<DefendSilent>());
        await CardCmd.Discard(new ThrowingPlayerChoiceContext(), CreateCard<DefendSilent>());

        await PlayUpgraded<MementoMori>(GetEnemy());

        // 10 + 5 * 2
        Assert.That(GetEnemy(), Has.LostHp(20));
    }
}
