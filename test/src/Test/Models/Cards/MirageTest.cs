using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class MirageTest : ModelTest
{
    [Test]
    public async Task TestWithNoPoison()
    {
        await Play<Mirage>();

        Assert.That(GetPlayer().Creature, Has.Block(0));
        Assert.That(GetPile(PileType.Exhaust), Has.Cards(typeof(Mirage)));
    }

    [Test]
    public async Task TestWithPoisonOn1Enemy()
    {
        await CreateEnemy<BigDummy>();
        await PowerCmd.Apply<Poison>(GetEnemy(), 3, null, null);

        await Play<Mirage>();

        Assert.That(GetPlayer().Creature, Has.Block(3));
    }

    [Test]
    public async Task TestWithPoisonOn2Enemies()
    {
        await CreateEnemy<BigDummy>();
        await PowerCmd.Apply<Poison>(GetEnemies().ToList()[0], 3, null, null);
        await PowerCmd.Apply<Poison>(GetEnemies().ToList()[1], 4, null, null);


        await Play<Mirage>();

        Assert.That(GetPlayer().Creature, Has.Block(7));
    }
}
