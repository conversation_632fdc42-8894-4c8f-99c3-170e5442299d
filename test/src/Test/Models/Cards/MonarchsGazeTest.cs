using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class MonarchsGazeTest : ModelTest
{
    [Test]
    public async Task TestBaseStrengthLoss()
    {
        Creature enemy = GetEnemy();

        await Play<MonarchsGaze>();
        await CreatureCmd.Damage(enemy, 1, DamageProps.card, GetPlayer().Creature, null);

        Assert.That(enemy, Has.PowerAmount<Strength>(-1));
    }
}
