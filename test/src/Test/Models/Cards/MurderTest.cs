using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class MurderTest : ModelTest
{
    [Test]
    public async Task TestDamageAtTurnOne()
    {
        await Play<Murder>(GetEnemy());
        Assert.That(GetEnemy(), Has.LostHp(14));
    }

    [Test]
    public async Task TestDamageAtTurnThree()
    {
        await PassToNextPlayerTurn();
        await PassToNextPlayerTurn();
        await Play<Murder>(GetEnemy());

        Assert.That(GetEnemy(), Has.LostHp(28)); // 7 * 4
    }

    [Test]
    public async Task TestUpgrade()
    {
        await PassToNextPlayerTurn();
        await PassToNextPlayerTurn();
        await PlayUpgraded<Murder>(GetEnemy());

        Assert.That(<PERSON><PERSON><PERSON><PERSON>(), <PERSON><PERSON>(36)); // 9 * 4
    }
}
