using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class NecroMasteryTest : ModelTest
{
    [Test]
    public async Task TestIncreasesSummonAmountOnCards()
    {
        await Play<NecroMastery>();
        await Play<Camaraderie>();

        Assert.That(GetPlayer().Osty!.MaxHp, Is.EqualTo(7));
    }

    [Test]
    public async Task TestDoesNotIncreaseSummonAmountOnNonCardEffects()
    {
        await Play<NecroMastery>();
        await OstyCmd.Summon(new ThrowingPlayerChoiceContext(), GetPlayer(), 10, null);

        Assert.That(GetPlayer().Osty!.MaxHp, Is.EqualTo(10));
    }
}