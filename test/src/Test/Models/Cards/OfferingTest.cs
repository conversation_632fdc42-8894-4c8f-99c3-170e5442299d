using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class OfferingTest : ModelTest
{
    [Test]
    public async Task TestBaseResults()
    {
        CardPile drawPile = GetPile(PileType.Draw);

        for (int i = 0; i < 4; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendIronclad>(), drawPile);
        }

        await Play<Offering>();

        Player player = GetPlayer();
        Assert.That(player.Creature, <PERSON>.LostHp(6));
        Assert.That(player, Has.ExtraEnergy(2));
        Assert.That(GetPile(PileType.Hand), Has.Cards(Enumerable.Repeat(typeof(DefendIronclad), 3).ToArray()));
        Assert.That(GetPile(PileType.Exhaust), Has.Cards(typeof(Offering)));
    }

    [Test]
    public async Task TestOverdraw()
    {
        CardPile drawPile = GetPile(PileType.Draw);

        for (int i = 0; i < 15; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendIronclad>(), drawPile);
        }

        await Play<Offering>();
        await Play<Offering>();
        await Play<Offering>();
        await Play<Offering>();

        Assert.That(GetPile(PileType.Hand), Has.Cards(Enumerable.Repeat(typeof(DefendIronclad), 10).ToArray()));
        Assert.That(GetPile(PileType.Draw), Has.Cards(Enumerable.Repeat(typeof(DefendIronclad), 5).ToArray()));
    }

    [Test]
    public async Task TestUpgradedCardDraw()
    {
        CardPile drawPile = GetPile(PileType.Draw);

        for (int i = 0; i < 6; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendIronclad>(), drawPile);
        }

        await PlayUpgraded<Offering>();

        Assert.That(GetPile(PileType.Hand), Has.Cards(Enumerable.Repeat(typeof(DefendIronclad), 5).ToArray()));
    }
}
