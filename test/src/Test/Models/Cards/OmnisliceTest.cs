using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class OmnisliceTest : ModelTest
{
    [Test]
    public async Task TestDealsDamageToAllEnemies()
    {
        Creature otherEnemy = await CreateEnemy<BigDummy>();
        await Play<Omnislice>(GetEnemy());

        Assert.That(GetEnemy(), <PERSON>.<PERSON>Hp(8));
        Assert.That(otherEnemy, Has.LostHp(8));
    }

    [Test]
    public async Task TestVulnerableDamageCarriesToAllEnemies()
    {
        Creature otherEnemy = await CreateEnemy<BigDummy>();
        await PowerCmd.Apply<Vulnerable>(GetEnemy(), 1, GetPlayer().Creature, null);
        await Play<Omnislice>(GetEnemy());

        Assert.That(GetEnemy(), <PERSON>.LostHp(12));
        Assert.That(otherEnemy, Has.LostHp(12));
    }

    [Test]
    public async Task TestOtherEnemyVulnerableIsNotApplied()
    {
        Creature otherEnemy = await CreateEnemy<BigDummy>();
        await PowerCmd.Apply<Vulnerable>(otherEnemy, 1, GetPlayer().Creature, null);
        await Play<Omnislice>(GetEnemy());

        Assert.That(GetEnemy(), Has.LostHp(8));
        Assert.That(otherEnemy, Has.LostHp(8));
    }

    [Test]
    public async Task TestWorksInSingleEnemyCombat()
    {
        await Play<Omnislice>(GetEnemy());

        Assert.That(GetEnemy(), Has.LostHp(8));
    }
}
