using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class PanacheTest : ModelTest
{
    [Test]
    public async Task TestInstancedPower()
    {
        await Play<Panache>();
        await Play<Panache>();

        for (int i = 0; i < 6; i++)
        {
            await Play<DefendIronclad>();
        }

        Assert.That(GetEnemy(), Has.LostHp(20));
    }

    [Test]
    public async Task TestAfterPlaying4Cards()
    {
        await Play<Panache>();

        for (int i = 0; i < 4; i++)
        {
            await Play<DefendIronclad>();
        }

        Assert.That(GetEnemy(), Has.LostHp(0));
    }

    [Test]
    public async Task TestAfterPlaying5Cards()
    {
        await Play<Panache>();

        for (int i = 0; i < 5; i++)
        {
            await Play<DefendIronclad>();
        }

        Assert.That(GetEnemy(), Has.LostHp(10));
    }

    [Test]
    public async Task TestAfterPlaying9Cards()
    {
        await Play<Panache>();

        for (int i = 0; i < 9; i++)
        {
            await Play<DefendIronclad>();
        }

        Assert.That(GetEnemy(), Has.LostHp(10));
    }

    [Test]
    public async Task TestWithMayhem()
    {
        await Play<Mayhem>();
        await Play<Panache>();

        CardPile drawPile = GetPile(PileType.Draw);
        for (int i = 0; i < 7; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendIronclad>(), drawPile);
        }

        for (int i = 0; i < 4; i++)
        {
            await Play<DefendIronclad>();
        }

        await PassToNextPlayerTurn();

        Assert.That(GetPlayer().Creature, Has.Block(5)); // make sure mayhem played the block
        Assert.That(GetEnemy(), Has.LostHp(0)); // make sure playing mayhem happened after panache was reset
    }

    [Test]
    public async Task TestWithStampede()
    {
        await Play<Panache>();
        await Play<Stampede>();


        CardPile handPile = GetPile(PileType.Hand);
        await CardPileCmd.Add(CreateCard<StrikeIronclad>(), handPile);

        for (int i = 0; i < 4; i++)
        {
            await Play<DefendIronclad>();
        }

        await PassToNextPlayerTurn();


        //  6 (from strike) + 10(from panache)
        Assert.That(GetEnemy(), Has.LostHp(16));
    }

    [Test]
    public async Task TestAfterPlaying10Cards()
    {
        await Play<Panache>();

        for (int i = 0; i < 10; i++)
        {
            await Play<DefendIronclad>();
        }

        Assert.That(GetEnemy(), Has.LostHp(20));
    }

    [Test]
    public async Task TestAfterPlaying5CardsOver2Turns()
    {
        await Play<Panache>();

        for (int i = 0; i < 4; i++)
        {
            await Play<DefendIronclad>();
        }

        await PassToNextPlayerTurn();

        await Play<DefendIronclad>();

        Assert.That(GetEnemy(), Has.LostHp(0));
    }

    [Test]
    public async Task TestAfterPlayingBetween5Cards()
    {
        for (int i = 0; i < 4; i++)
        {
            await Play<DefendIronclad>();
        }

        await Play<Panache>();
        await Play<DefendIronclad>();

        Assert.That(GetEnemy(), Has.LostHp(0));
    }
}
