using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class ParryTest : ModelTest
{
    [Test]
    public async Task TestBlockAfterSovereignBladePlayed()
    {
        await Play<Parry>();
        await Play<SovereignBlade>(GetEnemy());

        Assert.That(GetPlayer().Creature, <PERSON>.Block(10));
    }

    [Test]
    public async Task TestStacking()
    {
        await Play<Parry>();
        await Play<Parry>();
        await Play<SovereignBlade>(GetEnemy());

        Assert.That(GetPlayer().Creature, Has.Block(20));
    }

    [Test]
    public async Task TestTriggersAfterThorns()
    {
        Creature enemy = GetEnemy();
        await Play<Parry>();
        await PowerCmd.Apply<Thorns>(enemy, 1, null, null);

        await Play<SovereignBlade>(GetEnemy());

        Creature player = GetPlayer().Creature;
        Assert.That(player, <PERSON><PERSON>(10));
        Assert.That(player, Has.LostHp(1));
    }
}
