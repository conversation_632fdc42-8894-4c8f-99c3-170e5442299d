using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class PhantomBladesTest : ModelTest
{
    [Test]
    public async Task TestShivsRetained()
    {
        await Play<PhantomBlades>();
        await Play<CloakAndDagger>();

        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(Shiv)));
        Assert.That(GetPile(PileType.Hand).Cards.First(), Has.Keyword(CardKeyword.Retain));
    }

    [Test]
    public async Task TestShivsAlreadyInHandHaveRetain()
    {
        await Play<CloakAndDagger>();
        await Play<PhantomBlades>();

        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(Shiv)));
        Assert.That(GetPile(PileType.Hand).Cards.First(), Has.Keyword(CardKeyword.Retain));
    }

    [Test]
    public async Task TestIncreasedShivDamage()
    {
        await Play<PhantomBlades>();

        for (int i = 0; i < 3; i++)
        {
            await Play<Shiv>(GetEnemy());
        }

        // (4 * 3) + 4 + 4
        Assert.That(GetEnemy(), Has.LostHp(20));
    }

    [Test]
    public async Task TestStacking()
    {
        await Play<PhantomBlades>();
        await Play<PhantomBlades>();

        await Play<Shiv>(GetEnemy()); // 4 * 6
        await Play<Shiv>(GetEnemy()); // 4

        Assert.That(GetEnemy(), Has.LostHp(28));
    }

    [Test]
    public async Task TestDoesNotWorkForNonShivs()
    {
        await Play<PhantomBlades>();
        await Play<MockAttackCard>(GetEnemy());

        Assert.That(GetEnemy(), Has.LostHp(6));
    }

    [Test]
    public async Task TestResetsBetweenTurns()
    {
        await Play<PhantomBlades>();
        await Play<Shiv>(GetEnemy()); // 4 * 3
        await PassToNextPlayerTurn();
        await Play<Shiv>(GetEnemy()); // 4 * 3

        Assert.That(GetEnemy(), Has.LostHp(24));
    }
}
