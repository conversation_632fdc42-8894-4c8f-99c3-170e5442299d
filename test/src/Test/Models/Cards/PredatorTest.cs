using System;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class PredatorTest : ModelTest
{
    [Test]
    public async Task TestCardDrawNextTurn()
    {
        CardPile drawPile = GetPile(PileType.Draw);

        // Make sure there's more than enough cards to draw 2 extra
        for (int i = 0; i < 8; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), drawPile);
        }

        await Play<Predator>(GetEnemy());
        await PassToNextPlayerTurn();

        Type[] names = Enumerable.Repeat(typeof(DefendSilent), 7).ToArray();
        Assert.That(GetPile(PileType.Hand), Has.Cards(names));
        Assert.That(drawPile, Has.Cards(typeof(DefendSilent)));
    }

    [Test]
    public async Task TestBaseDamage()
    {
        Creature enemy = GetEnemy();

        await Play<Predator>(enemy);

        Assert.That(enemy, Has.LostHp(15));
    }

    [Test]
    public async Task TestUpgradedDamage()
    {
        Creature enemy = GetEnemy();

        await PlayUpgraded<Predator>(enemy);

        Assert.That(enemy, Has.LostHp(20));
    }
}
