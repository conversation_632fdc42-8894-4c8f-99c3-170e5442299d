using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class PrepTimeTest : ModelTest
{
    [Test]
    public async Task TestBaseVigor()
    {
        await Play<PrepTime>();
        await PassToNextPlayerTurn();

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Vigor>(3));
    }

    [Test]
    public async Task TestStacking()
    {
        await Play<PrepTime>();
        await Play<PrepTime>();
        await PassToNextPlayerTurn();

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Vigor>(6));
    }
    
    [Test]
    public async Task TestUpgrade()
    {
        await PlayUpgraded<PrepTime>();
        await PassToNextPlayerTurn();

        Assert.That(GetPlayer().<PERSON><PERSON><PERSON>, Has.PowerAmount<Vigor>(4));
    }
}