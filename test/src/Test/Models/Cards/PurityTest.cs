using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class PurityTest : ModelTest
{
    [Test]
    public async Task TestWithEmptyHand()
    {
        await Play<Purity>();

        Assert.That(GetPile(PileType.Exhaust), Has.Cards(typeof(Purity)));
    }

    [Test]
    public async Task TestSelecting0Cards()
    {
        await FillHand();

        PrepareToSkipSelection();
        await Play<Purity>();

        Assert.That(GetPile(PileType.Exhaust), Has.Cards(typeof(Purity)));
    }

    [Test]
    public async Task TestSelecting1Card()
    {
        await FillHand();
        IReadOnlyList<CardModel> cards = GetPile(PileType.Hand).Cards;

        PrepareToSelect(cards[0]);
        await Play<Purity>();

        Assert.That(GetPile(PileType.Exhaust), Has.Cards(typeof(Purity), typeof(DefendIronclad)));
    }

    [Test]
    public async Task TestSelecting2Cards()
    {
        await FillHand();
        IReadOnlyList<CardModel> cards = GetPile(PileType.Hand).Cards;

        PrepareToSelect(cards[0], cards[1]);
        await Play<Purity>();

        Assert.That(GetPile(PileType.Exhaust), Has.Cards(typeof(Purity), typeof(DefendIronclad), typeof(DefendIronclad)));
    }

    [Test]
    public async Task TestSelecting3Cards()
    {
        await FillHand();
        IReadOnlyList<CardModel> cards = GetPile(PileType.Hand).Cards;

        PrepareToSelect(cards[0], cards[1], cards[2]);
        await Play<Purity>();

        Assert.That(GetPile(PileType.Exhaust), Has.Cards(typeof(Purity), typeof(DefendIronclad), typeof(DefendIronclad), typeof(DefendIronclad)));
    }

    [Test]
    public async Task TestSelecting5CardsWhenUpgraded()
    {
        await FillHand();
        IReadOnlyList<CardModel> cards = GetPile(PileType.Hand).Cards;

        PrepareToSelect(cards[0], cards[1], cards[2], cards[3], cards[4]);
        await PlayUpgraded<Purity>();

        Assert.That(GetPile(PileType.Exhaust), Has.Cards(typeof(Purity), typeof(DefendIronclad), typeof(DefendIronclad), typeof(DefendIronclad), typeof(DefendIronclad), typeof(DefendIronclad)));
    }

    private async Task FillHand()
    {
        CardPile hand = GetPile(PileType.Hand);

        for (int i = 0; i < 6; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendIronclad>(), hand);
        }
    }
}
