using System.Threading.Tasks;

using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class PutrefyTest : ModelTest
{
    [Test]
    public async Task TestBasePowerAmounts()
    {
        Creature enemy = GetEnemy();
        await Play<Putrefy>(enemy);
        
        Assert.That(enemy, Has.PowerAmount<Weak>(2));
        Assert.That(enemy, Has.PowerAmount<Vulnerable>(2));
    }
    
    [Test]
    public async Task TestUpgradedPowerAmounts()
    {
        Creature enemy = GetEnemy();
        await PlayUpgraded<Putrefy>(enemy);
        
        Assert.That(enemy, Has.PowerAmount<Weak>(3));
        Assert.That(enemy, Has.PowerAmount<Vulnerable>(3));
    }
}