using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.CardPools;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class QuasarTest : ModelTest
{
    [Test]
    public async Task TestGeneratedCard()
    {
        await PlayerCmd.GainStars(2, GetPlayer());
        CardPile hand = GetPile(PileType.Hand);

        PrepareToSelectAtIndices(0);
        await Play<Quasar>();

        Assert.That(hand.Cards.Last(), Is.InPool<ColorlessCardPool>());
    }

    [Test]
    public async Task TestResetCost()
    {
        await PlayerCmd.GainStars(2, GetPlayer());
        CardPile hand = GetPile(PileType.Hand);

        PrepareToSelectAtIndices(0);
        await Play<Quasar>();

        CardModel card = hand.Cards.Last();
        await PassToNextPlayerTurn();

        Assert.That(card, Is.InPool<ColorlessCardPool>());
    }
}
