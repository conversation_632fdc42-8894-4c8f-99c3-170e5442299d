using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Afflictions;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class RecycleTest : ModelTest
{
    [Test]
    public async Task TestWithEmptyHand()
    {
        await Play<Recycle>();
        Assert.That(GetPlayer(), Has.SpentEnergy(1));
    }

    [Test]
    public async Task TestWithZeroCostCard()
    {
        await CardPileCmd.Add(MockSkill().MockEnergyCost(0), GetPile(PileType.Hand));
        await Play<Recycle>();

        Assert.That(GetPlayer(), Has.SpentEnergy(1));
    }

    [Test]
    public async Task TestWithOneCostCard()
    {
        await CardPileCmd.Add(MockSkill(), GetPile(PileType.Hand));
        await Play<Recycle>();

        Assert.That(GetPlayer(), Has.SpentEnergy(0));
    }

    [Test]
    public async Task TestWithFiveCostCard()
    {
        await CardPileCmd.Add(MockSkill().MockEnergyCost(5), PileType.Hand);
        await Play<Recycle>();

        Assert.That(GetPlayer(), Has.ExtraEnergy(4));
    }

    [Test]
    public async Task TestUpgrade()
    {
        await CardPileCmd.Add(MockSkill(), PileType.Hand);
        await PlayUpgraded<Recycle>();

        Assert.That(GetPlayer(), Has.ExtraEnergy(1));
    }

    [Test]
    public async Task TestXCostCard()
    {
        await CardPileCmd.Add(MockSkill().MockEnergyCostX(), GetPile(PileType.Hand));
        await Play<Recycle>();

        Assert.That(GetPlayer(), Has.ExtraEnergy(98));
    }

    [Test]
    public async Task TestWithEnergyCostIncreaseFromAffliction()
    {
        CardModel card = MockSkill();
        await CardCmd.Afflict<Frozen>(card, 1);
        await CardPileCmd.Add(card, GetPile(PileType.Hand));
        await Play<Recycle>();

        Assert.That(GetPlayer(), Has.ExtraEnergy(1));
    }
}
