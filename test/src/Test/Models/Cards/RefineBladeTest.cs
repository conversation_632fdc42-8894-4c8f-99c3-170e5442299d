using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class RefineBladeTest : ModelTest
{
    [Test]
    public async Task TestForge()
    {
        await Play<RefineBlade>();
        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(SovereignBlade)));
    }

    [Test]
    public async Task TestMultipleForges()
    {
        await Play<RefineBlade>();
        await Play<RefineBlade>();
        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(SovereignBlade)));
    }

    [Test]
    public async Task TestDamage()
    {
        // Get base values from fresh cards
        int sovereignBladeBaseDamage = CreateCard<SovereignBlade>().DynamicVars.Damage.IntValue;
        int refineBladeForge = CreateCard<RefineBlade>().DynamicVars.Forge.IntValue;

        await Play<RefineBlade>();
        SovereignBlade sovereignBlade = (SovereignBlade)GetPile(PileType.Hand).Cards[0];
        await Play(sovereignBlade, GetEnemy());

        // RefineBlade creates a SovereignBlade with forge amount from RefineBlade
        int expectedDamage = sovereignBladeBaseDamage + refineBladeForge;
        Assert.That(GetEnemy(), Has.LostHp(expectedDamage));
    }

    [Test]
    public async Task TestDamageAfterMultipleForges()
    {
        // Get base values from fresh cards
        int sovereignBladeBaseDamage = CreateCard<SovereignBlade>().DynamicVars.Damage.IntValue;
        int refineBladeForge = CreateCard<RefineBlade>().DynamicVars.Forge.IntValue;

        await Play<RefineBlade>();
        await Play<RefineBlade>();
        SovereignBlade sovereignBlade = (SovereignBlade)GetPile(PileType.Hand).Cards[0];
        await Play(sovereignBlade, GetEnemy());

        // After 2 RefineBlade plays, SovereignBlade has base + (2 * forge amount)
        int expectedDamage = sovereignBladeBaseDamage + (2 * refineBladeForge);
        Assert.That(GetEnemy(), Has.LostHp(expectedDamage));
    }
}
