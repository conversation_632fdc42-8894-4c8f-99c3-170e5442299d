using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Enchantments;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class ReflectiveFortressTest : ModelTest
{
    [Test]
    public async Task TestReflectsDamage()
    {
        await Play<ReflectiveFortress>();
        await CreatureCmd.GainBlock(GetPlayer().Creature, 10, BlockProps.nonCardUnpowered, null);
        await CreatureCmd.Damage(GetPlayer().Creature, 8, DamageProps.monsterMove, GetEnemy(), null);

        Assert.That(GetEnemy(), Has.LostHp(8));
    }

    [Test]
    public async Task TestStacking()
    {
        await Play<ReflectiveFortress>();
        await Play<ReflectiveFortress>();
        await Play<ReflectiveFortress>();

        await CreatureCmd.GainBlock(GetPlayer().Creature, 10, BlockProps.nonCardUnpowered, null);
        await CreatureCmd.Damage(GetPlayer().Creature, 8, DamageProps.monsterMove, GetEnemy(), null);

        Assert.That(GetEnemy(), Has.LostHp(24));
    }

    [Test]
    public async Task TestReflectsDamageWhenBlockIsJustBroken()
    {
        await Play<ReflectiveFortress>();
        await CreatureCmd.GainBlock(GetPlayer().Creature, 10, BlockProps.nonCardUnpowered, null);
        await CreatureCmd.Damage(GetPlayer().Creature, 10, DamageProps.monsterMove, GetEnemy(), null);

        Assert.That(GetEnemy(), Has.LostHp(10));
    }

    [Test]
    public async Task TestDoesNotReflectsDamageWhenBlockIsBroken()
    {
        await Play<ReflectiveFortress>();
        await CreatureCmd.GainBlock(GetPlayer().Creature, 10, BlockProps.nonCardUnpowered, null);
        await CreatureCmd.Damage(GetPlayer().Creature, 11, DamageProps.monsterMove, GetEnemy(), null);

        Assert.That(GetEnemy(), Has.LostHp(0));
    }

    [Test]
    public async Task TestDoesNotReflectsDamageWhenNoBlock()
    {
        await Play<ReflectiveFortress>();
        await CreatureCmd.Damage(GetPlayer().Creature, 11, DamageProps.monsterMove, GetEnemy(), null);

        Assert.That(GetEnemy(), Has.LostHp(0));
    }
}
