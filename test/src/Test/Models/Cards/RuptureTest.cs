using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class RuptureTest : ModelTest
{
    [Test]
    public async Task TestAddsStrengthWhenLosingHp()
    {
        await Play<Rupture>();
        await Play<Bloodletting>();

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(1));
    }

    [Test]
    public async Task TestAddsStrengthWhenTakingDamage()
    {
        await CardPileCmd.Add(CreateCard<Burn>(), PileType.Hand);
        await Play<Rupture>();
        await PassToNextPlayerTurn();

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(1));
    }

    [Test]
    public async Task TestNotTriggeredByThorns()
    {
        await PowerCmd.Apply<Thorns>(GetEnemy(), 2, null, null);
        await Play<Rupture>();
        await Play<StrikeIronclad>(GetEnemy());

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(0));
    }

    [Test]
    public async Task TestTriggeredByCombust()
    {
        await Play<Rupture>();
        await Play<Combust>();
        await PassToNextPlayerTurn();
        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(1));
    }

    [Test]
    public async Task TestDoesNotAddStrengthWhenDamageIsBlocked()
    {
        Creature player = GetPlayer().Creature;
        await CardPileCmd.Add(CreateCard<Burn>(), PileType.Hand);

        await CreatureCmd.GainBlock(player, 10, BlockProps.nonCardUnpowered, null);
        await Play<Rupture>();
        await PassToNextPlayerTurn();

        Assert.That(player, Has.PowerAmount<Strength>(0));
    }

    [Test]
    public async Task TestUpgradedStrength()
    {
        await PlayUpgraded<Rupture>();
        await Play<Bloodletting>();

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(2));
    }

    [Test]
    public async Task TestTimingWithBreakthrough()
    {
        await Play<Rupture>();
        await Play<Breakthrough>();

        // breakthrough should deal its damage before the strength gets applied
        Assert.That(GetEnemy(), Has.LostHp(9));
        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(1));
    }

    [Test]
    public async Task TestFromEnemyDamage()
    {
        await Play<Rupture>();
        await CreatureCmd.Damage(GetPlayer().Creature, 10, DamageProps.monsterMove, GetEnemy(), null);

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(0));
    }

    [Test]
    public async Task TestTriggerFromBurn()
    {
        await Play<Rupture>();
        await CardPileCmd.Add(CreateCard<Burn>(), PileType.Hand);
        await PassToNextPlayerTurn();
        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(1));
    }

    [Test]
    public async Task TestNotTriggerFromBlockedBurn()
    {
        await Play<Rupture>();
        await CreatureCmd.GainBlock(GetPlayer().Creature, 25, BlockProps.card, null);
        await CardPileCmd.Add(CreateCard<Burn>(), PileType.Hand);
        await PassToNextPlayerTurn();
        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(0));
    }
}
