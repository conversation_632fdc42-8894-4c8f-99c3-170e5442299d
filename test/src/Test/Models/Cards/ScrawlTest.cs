using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class ScrawlTest : ModelTest
{
    [Test]
    public async Task TestWhenHandIsEmpty()
    {
        for (int i = 0; i < 20; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), GetPile(PileType.Draw));
        }

        await Play<Scrawl>();
        Assert.That(GetPile(PileType.Hand).Cards, Has.Count.EqualTo(10));
    }

    [Test]
    public async Task TestWhenHandHas5Cards()
    {
        for (int i = 0; i < 20; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), GetPile(PileType.Draw));
        }

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), GetPile(PileType.Hand));
        }

        await Play<Scrawl>();
        Assert.That(GetPile(PileType.Hand).Cards, Has.Count.EqualTo(10));
    }
}
