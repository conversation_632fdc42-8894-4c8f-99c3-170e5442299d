using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class SculptingStrikeTest : ModelTest
{
    [Test]
    public async Task TestMakesCardsEthereal()
    {
        CardModel card = CreateCard<MockAttackCard>();
        await CardPileCmd.Add(card, GetPile(PileType.Hand));

        await Play<SculptingStrike>(GetEnemy());
        Assert.That(card, Has.Keyword(CardKeyword.TemporaryEthereal));
    }

    [Test]
    public async Task TestEtherealGoesAwayNextTurn()
    {
        CardModel card = CreateCard<MockAttackCard>();
        await CardPileCmd.Add(card, GetPile(PileType.Hand));

        await Play<SculptingStrike>(GetEnemy());
        await PassToNextPlayerTurn();
        Assert.That(card, Has.No.Keyword(CardKeyword.TemporaryEthereal));
    }

    [Test]
    public async Task TestDoesntAffectCardsWithEtherealAlready()
    {
        CardModel card = CreateCard<MockAttackCard>();
        await CardPileCmd.Add(card, GetPile(PileType.Hand));
        CardCmd.ApplyKeyword(card, CardKeyword.Ethereal);

        await Play<SculptingStrike>(GetEnemy());
        await PassToNextPlayerTurn();
        Assert.That(card, Has.Keyword(CardKeyword.Ethereal));
        Assert.That(card, Has.No.Keyword(CardKeyword.TemporaryEthereal));
    }

    [Test]
    public async Task TestWithHex()
    {
        Creature enemy = GetEnemy();
        await PowerCmd.Apply<Hex>(GetPlayer().Creature, 1, enemy, null);
        CardModel card = CreateCard<MockAttackCard>();
        await CardPileCmd.Add(card, GetPile(PileType.Hand));

        await Play<SculptingStrike>(GetEnemy());
        await CreatureCmd.Kill(enemy);
        Assert.That(card, Has.No.Keyword(CardKeyword.Ethereal));
        Assert.That(card, Has.Keyword(CardKeyword.TemporaryEthereal));
    }
}
