using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Monsters;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class ShivTest : ModelTest
{
    [Test]
    public async Task TestBaseDamage()
    {
        await CreateEnemy<BigDummy>();
        List<Creature> enemies = GetEnemies().ToList();

        await Play<Shiv>(enemies[0]);

        Assert.That(enemies[0], Has.LostHp(4));

        // Make sure that only the targeted enemy is hit, to prove that we're not falsely triggering Fan of Knives.
        Assert.That(enemies[1], Has.LostHp(0));
    }

    [Test]
    public async Task TestUpgradedDamage()
    {
        Creature enemy = GetEnemy();

        await PlayUpgraded<Shiv>(enemy);

        Assert.That(enemy, <PERSON>.LostHp(6));
    }
}
