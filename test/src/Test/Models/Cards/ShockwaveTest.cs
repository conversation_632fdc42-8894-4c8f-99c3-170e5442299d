using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class ShockwaveTest : ModelTest
{
    [Test]
    public async Task TestWithArtifact()
    {
        Creature enemy = GetEnemy();
        await PowerCmd.Apply<Artifact>(enemy, 1, null, null);

        await Play<Shockwave>();

        // Weak is the first debuff to be applied so it gets prevented.
        // This clears Artifact, which means Vulnerable gets successfully applied after.
        Assert.That(enemy, Has.PowerAmount<Vulnerable>(3));
    }
}
