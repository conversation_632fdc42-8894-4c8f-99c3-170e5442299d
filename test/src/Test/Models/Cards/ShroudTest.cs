using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class ShroudTest : ModelTest
{
    [Test]
    public async Task TestAfterApplyingBlock()
    {
        await Play<Shroud>();
        await Play<BlightStrike>(GetEnemy());

        Assert.That(GetPlayer().Creature, Has.Block(3));
    }

    [Test]
    public async Task TestStacking()
    {
        await Play<Shroud>();
        await Play<Shroud>();
        await Play<BlightStrike>(GetEnemy());

        Assert.That(GetPlayer().<PERSON><PERSON><PERSON>, <PERSON><PERSON>(6));
    }
}