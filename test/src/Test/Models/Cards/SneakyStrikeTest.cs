using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class SneakyStrikeTest : ModelTest
{
    [Test]
    public async Task TestBaseDamageAndNoEnergyGainWithoutDiscard()
    {
        Creature enemy = GetEnemy();
        await Play<SneakyStrike>(enemy);

        // -2 from playing Sneaky Strike
        // 0 from Sneaky Strike's effect, since we haven't discarded.
        Assert.That(GetPlayer(), Has.SpentEnergy(2));
        Assert.That(enemy, Has.LostHp(12));
    }

    [Test]
    public async Task TestUpgradedDamage()
    {
        Creature enemy = GetEnemy();
        await PlayUpgraded<SneakyStrike>(enemy);

        Assert.That(enemy, Has.LostHp(16));
    }

    [Test]
    public async Task TestEnergyGainWithDiscard()
    {
        await CardCmd.Discard(new ThrowingPlayerChoiceContext(), CreateCard<DefendSilent>());
        await Play<SneakyStrike>(GetEnemy());

        // -2 from playing Sneaky Strike
        // +2 from Sneaky Strike's effect, since we discarded a card first.
        Assert.That(GetPlayer(), Has.SpentEnergy(0));
    }
}
