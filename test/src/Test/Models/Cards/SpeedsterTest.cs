using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class SpeedsterTest : ModelTest
{
    [Test]
    public async Task TestDealDamageWhenDrawingCards()
    {
        for (int i = 0; i < 8; i++)
        {
            await CardPileCmd.Add(MockAttack(), GetPile(PileType.Draw));
        }

        await Play<Speedster>();
        await CardPileCmd.Draw(new ThrowingPlayerChoiceContext(), 2, GetPlayer());

        Assert.That(GetEnemy(), Has.LostHp(2));
    }

    [Test]
    public async Task TestDoesntDealDamageOnStartOfTurnDraw()
    {
        for (int i = 0; i < 8; i++)
        {
            await CardPileCmd.Add(<PERSON>ck<PERSON>ttack(), GetPile(PileType.Draw));
        }

        await Play<Speedster>();
        await PassToNextPlayerTurn();

        Assert.That(GetEnemy(), Has.LostHp(0));
    }
}
