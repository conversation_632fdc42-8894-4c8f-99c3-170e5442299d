using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Afflictions;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class StampedeTest : ModelTest
{
    [Test]
    public async Task TestPlaysAttackCard()
    {
        await Play<Stampede>();

        for (int i = 0; i < 2; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), PileType.Hand);
        }

        await PassToNextPlayerTurn();

        Assert.That(GetEnemy(), Has.LostHp(6));
    }

    [Test]
    public async Task TestNoAttacks()
    {
        await Play<Stampede>();
        await CardPileCmd.Add(CreateCard<Shockwave>(), PileType.Hand);

        await PassToNextPlayerTurn();

        Assert.That(GetEnemy(), Has.PowerAmount<Weak>(0));
        Assert.That(GetEnemy(), Has.PowerAmount<Vulnerable>(0));
    }

    [Test]
    public async Task TestMultipleStampede()
    {
        await Play<Stampede>();
        await Play<Stampede>();

        for (int i = 0; i < 2; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), PileType.Hand);
        }

        await PassToNextPlayerTurn();

        Assert.That(GetEnemy(), Has.LostHp(12));
    }

    [Test]
    public async Task TestDoesNotPlayUnplayableCard()
    {
        await Play<Stampede>();

        CardModel strike = CreateCard<StrikeIronclad>();
        await CardCmd.Afflict<Bound>(strike, 1);
        await CardPileCmd.Add(strike, PileType.Draw);

        await PassToNextPlayerTurn();

        Assert.That(GetEnemy(), Has.LostHp(0));
    }

    [Test]
    public async Task TestPlaysCardTriggeringPlayerChoice()
    {
        await Play<Stampede>();

        CardModel headbutt = CreateCard<Headbutt>();
        await CardPileCmd.Add(headbutt, PileType.Hand);

        CardModel strike1 = CreateCard<StrikeIronclad>();
        await CardPileCmd.Add(strike1, PileType.Discard);

        CardModel strike2 = CreateCard<StrikeIronclad>();
        await CardPileCmd.Add(strike2, PileType.Discard);

        PrepareToSelect(strike2);
        await PassToNextPlayerTurn();

        Assert.That(GetEnemy(), Has.LostHp(9));
        Assert.That(GetCombatState().RoundNumber, Is.EqualTo(2));
    }
}
