using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Models.Orbs;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class TeslaCoilTest : ModelTest
{
    [Test]
    public async Task TestDoesNothingWitnNoLightningOrbs()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await OrbCmd.Channel<FrostOrb>(new NullPlayerChoiceContext(), player);
        await OrbCmd.Channel<DarkOrb>(new NullPlayerChoiceContext(), player);
        await OrbCmd.Channel<PlasmaOrb>(new NullPlayerChoiceContext(), player);
        await Play<TeslaCoil>(GetEnemy());

        Assert.That(GetEnemy(), Has.LostHp(0));
    }

    [Test]
    public async Task TestWithLightningOrbs()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await OrbCmd.Channel<LightningOrb>(new NullPlayerChoiceContext(), player);
        await OrbCmd.Channel<LightningOrb>(new NullPlayerChoiceContext(), player);
        await OrbCmd.Channel<LightningOrb>(new NullPlayerChoiceContext(), player);
        await Play<TeslaCoil>(GetEnemy());

        // 3 + 3 * 3
        Assert.That(GetEnemy(), Has.LostHp(9));
    }


    [Test]
    public async Task TestWithFocus()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);
        await PowerCmd.Apply<Focus>(player.Creature, 2, player.Creature, null);

        await OrbCmd.Channel<LightningOrb>(new NullPlayerChoiceContext(), player);
        await OrbCmd.Channel<LightningOrb>(new NullPlayerChoiceContext(), player);
        await OrbCmd.Channel<LightningOrb>(new NullPlayerChoiceContext(), player);
        await Play<TeslaCoil>(GetEnemy());

        // 3 * 5
        Assert.That(GetEnemy(), Has.LostHp(15));
    }

    [Test]
    public async Task TestWithElectrodynamics()
    {
        await CreateEnemy<BigDummy>();
        await OrbCmd.AddSlots(GetPlayer(), 3);

        await Play<Electrodynamics>();
        await Play<TeslaCoil>(GetEnemy());

        List<Creature> enemies = GetEnemies().ToList();
        Assert.That(enemies[0], Has.LostHp(6)); //  3 + 3
        Assert.That(enemies[1], Has.LostHp(6)); // 3 + 3
    }
}
