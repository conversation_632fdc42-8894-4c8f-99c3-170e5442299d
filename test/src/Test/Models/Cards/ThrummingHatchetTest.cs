using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class ThrummingHatchetTest : ModelTest
{
    [Test]
    public async Task TestReturnNextTurn()
    {
        for (int i = 0; i < 20; i++)
        {
            await CardPileCmd.Add(MockSkill(), GetPile(PileType.Draw));
        }

        await Play<ThrummingHatchet>(GetEnemy());
        await PassToNextPlayerTurn();

        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(ThrummingHatchet)));
    }

    [Test]
    public async Task TestDoesNotCarryOverBetweenTurns()
    {
        for (int i = 0; i < 20; i++)
        {
            await CardPileCmd.Add(MockSkill(), GetPile(PileType.Draw));
        }

        await Play<ThrummingHatchet>(GetEnemy());

        for (int i = 0; i < 2; i++)
        {
            await PassToNextPlayerTurn();
        }

        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard)));
    }

    [Test]
    public async Task TestPlayTwoCopies()
    {
        for (int i = 0; i < 20; i++)
        {
            await CardPileCmd.Add(MockSkill(), GetPile(PileType.Draw));
        }

        for (int i = 0; i < 2; i++)
        {
            await Play<ThrummingHatchet>(GetEnemy());
        }

        await PassToNextPlayerTurn();

        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(ThrummingHatchet), typeof(ThrummingHatchet)));
    }

    [Test]
    public async Task TestPlaySameCopyTwice()
    {
        for (int i = 0; i < 20; i++)
        {
            await CardPileCmd.Add(MockSkill(), GetPile(PileType.Draw));
        }

        CardModel card = CreateCard<ThrummingHatchet>();

        for (int i = 0; i < 2; i++)
        {
            await Play(card, GetEnemy());
        }

        await PassToNextPlayerTurn();
        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(ThrummingHatchet)));
    }

    [Test]
    public async Task TestExhaust()
    {
        for (int i = 0; i < 20; i++)
        {
            await CardPileCmd.Add(MockSkill(), GetPile(PileType.Draw));
        }

        CardModel card = CreateCard<ThrummingHatchet>();
        await Play(card, GetEnemy());
        await CardCmd.Exhaust(new ThrowingPlayerChoiceContext(), card);

        await PassToNextPlayerTurn();
        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(ThrummingHatchet)));
    }

    [Test]
    public async Task TestWithFullHand()
    {
        for (int i = 0; i < 20; i++)
        {
            await CardPileCmd.Add(MockSkill(), GetPile(PileType.Draw));
        }

        await PowerCmd.Apply<DrawCardsNextTurn>(GetPlayer().Creature, 10, null, null);
        CardModel card = CreateCard<ThrummingHatchet>();
        await Play(card, GetEnemy());
        await CardCmd.Exhaust(new ThrowingPlayerChoiceContext(), card);

        await PassToNextPlayerTurn();

        // This card comes out first, and then cards are drawn (so that this doesn't get left out by a full hand).
        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(ThrummingHatchet)));
    }
}
