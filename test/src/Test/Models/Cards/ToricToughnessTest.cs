using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class ToricToughnessTest : ModelTest
{
    [Test]
    public async Task TestBaseBlockThisTurn()
    {
        await Play<ToricToughness>();
        Assert.That(GetPlayer().Creature, Has.Block(5));
    }

    [Test]
    public async Task TestWhenCurrentlyHaveMaxBlock()
    {
        // unable to get any more block this turn
        await CreatureCmd.GainBlock(GetPlayer().Creature, 999, BlockProps.nonCardUnpowered, null);
        await Play<ToricToughness>();

        await PassToNextPlayerTurn();
        Assert.That(GetPlayer().Creature, Has.Block(5));
    }

    [Test]
    public async Task TestAffectedByDex()
    {
        await PowerCmd.Apply<Dexterity>(GetPlayer().Creature, 2, null, null);
        await Play<ToricToughness>();

        await PassToNextPlayerTurn();
        Assert.That(GetPlayer().Creature, Has.Block(7));
    }

    [Test]
    public async Task TestAffectedByFrail()
    {
        await PowerCmd.Apply<Frail>(GetPlayer().Creature, 1, null, null);
        await Play<ToricToughness>();

        await PassToNextPlayerTurn();
        Assert.That(GetPlayer().Creature, Has.Block(3));
    }

    [Test]
    public async Task TestAgainstAfterImage()
    {
        await Play<AfterImage>();
        await Play<ToricToughness>();
        Assert.That(GetPlayer().Creature, Has.Block(6));
    }

    [Test]
    public async Task TestBaseBlockNextTurn()
    {
        await Play<ToricToughness>();
        await PassToNextPlayerTurn();

        Assert.That(GetPlayer().Creature, Has.Block(5));
    }

    [Test]
    public async Task TestBaseBlock2NextTurn()
    {
        await Play<ToricToughness>();
        await PassToNextPlayerTurn();
        await PassToNextPlayerTurn();

        Assert.That(GetPlayer().Creature, Has.Block(5));
    }

    [Test]
    public async Task TestNoBlockTurn3()
    {
        await Play<ToricToughness>();
        await PassToNextPlayerTurn();
        await PassToNextPlayerTurn();
        await PassToNextPlayerTurn();


        Assert.That(GetPlayer().Creature, Has.Block(0));
    }

    [Test]
    public async Task TestUpgradedBlockThisTurn()
    {
        await PlayUpgraded<ToricToughness>();
        Assert.That(GetPlayer().Creature, Has.Block(7));
    }

    [Test]
    public async Task TestUpgradedBlockNextTurn()
    {
        await PlayUpgraded<ToricToughness>();
        await PassToNextPlayerTurn();

        Assert.That(GetPlayer().Creature, Has.Block(7));
    }

    [Test]
    public async Task TestStacking()
    {
        await Play<ToricToughness>();
        await PlayUpgraded<ToricToughness>();
        await PassToNextPlayerTurn();

        // 5 + 7
        Assert.That(GetPlayer().Creature, Has.Block(12));
    }

    [Test]
    public async Task TestStaggeredStacking()
    {
        await Play<ToricToughness>();
        await PassToNextPlayerTurn();
        await Play<ToricToughness>();
        await PassToNextPlayerTurn();
        await PassToNextPlayerTurn();

        // Only 1 toric toughness should still be around
        Assert.That(GetPlayer().Creature, Has.Block(5));
    }

    [Test]
    public async Task TestWithVambrace()
    {
        await RelicCmd.Obtain<Vambrace>(GetPlayer());
        await Play<ToricToughness>();
        await PassToNextPlayerTurn();

        // 5 + 7
        Assert.That(GetPlayer().Creature, Has.Block(10));
    }
}
