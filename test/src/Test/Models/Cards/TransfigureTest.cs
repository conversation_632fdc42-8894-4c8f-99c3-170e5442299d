using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class TransfigureTest : ModelTest
{
    [Test]
    public async Task TestCardCostsMoreAndIsPlayedTwice()
    {
        CardModel card = MockSkill();
        await CardPileCmd.Add(card, PileType.Hand);

        await Play<Transfigure>();
        await Play(card);

        Assert.That(GetPlayer(), Has.SpentEnergy(3));
        Assert.That(GetPlayer().Creature, Has.Block(10));
    }

    [Test]
    public async Task TestWithACost2Card()
    {
        CardModel card = MockSkill().MockEnergyCost(2);
        await CardPileCmd.Add(card, PileType.Hand);

        await Play<Transfigure>();
        await Play(card);

        Assert.That(GetPlayer(), Has.SpentEnergy(4));
        Assert.That(GetPlayer().Creature, Has.Block(10));
    }

    [Test]
    public async Task TestStacking()
    {
        CardModel card = MockSkill();
        await CardPileCmd.Add(card, PileType.Hand);

        for (int i = 0; i < 2; i++)
        {
            await Play<Transfigure>();
        }

        await Play(card);

        // +1 from each Transfigure.
        Assert.That(GetPlayer(), Has.SpentEnergy(5));

        // Played one extra time for each Transfigure.
        Assert.That(GetPlayer().Creature, Has.Block(15));
    }

    [Test]
    public async Task TestCardModificationCarriesOverBetweenTurns()
    {
        CardModel card = MockSkill();
        await CardPileCmd.Add(card, PileType.Hand);

        await Play<Transfigure>();

        await PassToNextPlayerTurn();
        await Play(card);

        Assert.That(GetPlayer(), Has.SpentEnergy(2));
        Assert.That(GetPlayer().Creature, Has.Block(10));
    }

    [Test]
    public async Task TestDowngrade()
    {
        CardModel card = MockSkill();
        await CardPileCmd.Add(card, PileType.Hand);
        CardCmd.Upgrade(card);

        await Play<Transfigure>();
        CardCmd.Downgrade(card);

        await Play(card);

        Assert.That(GetPlayer(), Has.SpentEnergy(3));
        Assert.That(GetPlayer().Creature, Has.Block(10));
    }

    [Test]
    public async Task TestDoesNotIncreaseCostOfUnplayableStatusCards()
    {
        CardModel card = CreateCard<Burn>();
        await CardPileCmd.Add(card, PileType.Hand);
        int oldCombatEnergyCost = card.GetEnergyCostThisCombat();

        await Play<Transfigure>();

        Assert.That(card.GetEnergyCostThisCombat(), Is.EqualTo(oldCombatEnergyCost));
    }

    [Test]
    public async Task TestDoesNotIncreaseCostOfXCostCards()
    {
        CardModel card = MockAttack().MockEnergyCostX();
        await CardPileCmd.Add(card, PileType.Hand);
        int oldCombatEnergyCost = card.GetEnergyCostThisCombat();

        await Play<Transfigure>();

        Assert.That(card.GetEnergyCostThisCombat(), Is.EqualTo(oldCombatEnergyCost));
    }
}
