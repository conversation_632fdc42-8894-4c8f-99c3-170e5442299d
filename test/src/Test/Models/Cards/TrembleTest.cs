using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class TrembleTest : ModelTest
{
    [Test]
    public async Task TestBaseVulnerable()
    {
        Creature enemy = GetEnemy();

        await Play<Tremble>(enemy);
        Assert.That(enemy, Has.PowerAmount<Vulnerable>(2));
    }
}
