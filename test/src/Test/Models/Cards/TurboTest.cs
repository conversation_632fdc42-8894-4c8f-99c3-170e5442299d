using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class TurboTest : ModelTest
{
    [Test]
    public async Task TestBaseEnergy()
    {
        await Play<Turbo>();

        Assert.That(GetPlayer(), Has.ExtraEnergy(2));
        Assert.That(GetPile(PileType.Discard), Has.Cards(typeof(Void), typeof(Turbo)));
    }

    [Test]
    public async Task TestUpgradedEnergy()
    {
        await PlayUpgraded<Turbo>();

        Assert.That(GetPlayer(), Has.ExtraEnergy(3));
    }
}
