using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Models.Orbs;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Models.Powers.Mocks;
using MegaCrit.Sts2.Core.ValueProps;
using MegaCrit.Sts2.Test.Exceptions;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class VanillaCardTest : ModelTest
{
    #region Damage

    [Test]
    public async Task TestSingleAttackDamage()
    {
        Creature enemy = GetEnemy();

        await Play<StrikeIronclad>(enemy);

        Assert.That(enemy, Has.LostHp(6));
    }

    [Test]
    public async Task TestUpgradedSingleAttackDamage()
    {
        Creature enemy = GetEnemy();

        await PlayUpgraded<StrikeIronclad>(enemy);

        Assert.That(enemy, Has.LostHp(9));
    }

    [Test]
    public async Task TestMultiAttackDamage()
    {
        Creature enemy = GetEnemy();

        await Play<Pummel>(enemy);

        Assert.That(enemy, Has.LostHp(8));
    }

    [Test]
    public async Task TestUpgradedMultiAttackDamage()
    {
        Creature enemy = GetEnemy();

        await PlayUpgraded<Pummel>(enemy);

        Assert.That(enemy, Has.LostHp(10));
    }

    [Test]
    public async Task TestAreaOfEffectDamage()
    {
        Creature enemy1 = GetEnemy();
        Creature enemy2 = await CreateEnemy<BigDummy>();

        await Play<FlickFlack>();

        Assert.That(enemy1, Has.LostHp(7));
        Assert.That(enemy2, Has.LostHp(7));
    }

    [Test]
    public async Task TestUpgradedAreaOfEffectDamage()
    {
        Creature enemy1 = GetEnemy();
        Creature enemy2 = await CreateEnemy<BigDummy>();

        await PlayUpgraded<FlickFlack>();

        Assert.That(enemy1, Has.LostHp(9));
        Assert.That(enemy2, Has.LostHp(9));
    }

    [Test]
    public async Task TestRandomTargetDamage()
    {
        await Play(MockAttack().MockTargetingType(UiTargetEnemy.Random));

        Assert.That(GetEnemy(), Has.LostHp(6));
    }

    [Test]
    public async Task TestRandomTargetWithRevivingEnemy()
    {
        Creature enemy = GetEnemy();
        await PowerCmd.Apply<MockRevivePower>(enemy, 1, null, null);
        await CreatureCmd.Kill(enemy);

        Assert.That(async () => await Play(MockAttack().MockTargetingType(UiTargetEnemy.Random)), Throws.Nothing);
    }

    [Test]
    public async Task TestPlayerHpLoss()
    {
        Creature player = GetPlayer().Creature;

        await CreatureCmd.GainBlock(player, 10, BlockProps.nonCardUnpowered, null);
        await Play<Bloodletting>();

        Assert.That(player, Has.LostHp(3));
    }

    #endregion

    #region Block

    [Test]
    public async Task TestBlock()
    {
        await Play<DefendIronclad>();

        Assert.That(GetPlayer().Creature, Has.Block(5));
    }

    [Test]
    public async Task TestUpgradedBlock()
    {
        await PlayUpgraded<DefendIronclad>();

        Assert.That(GetPlayer().Creature, Has.Block(8));
    }

    #endregion

    #region X-cost cards

    [Test]
    public async Task TestXCostWithDefaultEnergy()
    {
        await PlayerCmd.SetEnergy(3, GetPlayer());
        await Play(MockAttack().MockEnergyCostX(), GetEnemy());

        Assert.That(GetEnemy(), Has.LostHp(18));
    }

    [Test]
    public async Task TestXCostWithExtraEnergy()
    {
        await PlayerCmd.SetEnergy(4, GetPlayer());
        await Play(MockAttack().MockEnergyCostX(), GetEnemy());

        Assert.That(GetEnemy(), Has.LostHp(24));
    }

    [Test]
    public async Task TestXCostAfterSpendingEnergy()
    {
        await PlayerCmd.SetEnergy(3, GetPlayer());
        await Play<MockSkillCard>(); // Spend 1 energy.
        await Play(MockAttack().MockEnergyCostX(), GetEnemy());

        Assert.That(GetEnemy(), Has.LostHp(12));
    }

    [Test]
    public async Task TestXCostWithNoEnergy()
    {
        await PlayerCmd.SetEnergy(0, GetPlayer());
        await Play(MockAttack().MockEnergyCostX(), GetEnemy());

        Assert.That(GetEnemy(), Has.LostHp(0));
    }

    [Test]
    public async Task TestXCostAutoplay()
    {
        Player player = GetPlayer();
        await PlayerCmd.SetEnergy(3, player);

        await CardCmd.AutoPlay(new ThrowingPlayerChoiceContext(), MockAttack().MockEnergyCostX(), null);

        // Card should get auto-played with X=3, but the energy shouldn't be spent.
        Assert.That(player.PlayerCombatState!.Energy, Is.EqualTo(3));
        Assert.That(GetEnemy(), Has.LostHp(18));
    }

    #endregion

    #region Energy effects

    [Test]
    public async Task TestGainEnergy()
    {
        await Play<Wisp>();

        // Costs 1, gains 2
        Assert.That(GetPlayer(), Has.ExtraEnergy(1));
    }

    [Test]
    public async Task TestUpgradedEnergyCost()
    {
        await PlayUpgraded<DarkEmbrace>();

        // Upgrade reduces cost from 2 to 1.
        Assert.That(GetPlayer(), Has.SpentEnergy(1));
    }

    #endregion

    #region Star effects

    [Test]
    public async Task TestGainStars()
    {
        await Play<GatherLight>();
        Assert.That(GetPlayer().PlayerCombatState!.Stars, Is.EqualTo(1));
    }

    [Test]
    public async Task TestSpendingStars()
    {
        Player player = GetPlayer();

        await PlayerCmd.GainStars(5, player);
        await Play<FallingStar>(GetEnemy());

        Assert.That(player.PlayerCombatState!.Stars, Is.EqualTo(3));
    }

    #endregion

    #region Card draw

    [Test]
    public async Task TestCardDraw()
    {
        CardPile drawPile = GetPile(PileType.Draw);

        for (int i = 0; i < 4; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), drawPile);
        }

        await Play<Skim>();

        Assert.That(GetPile(PileType.Draw), Has.Cards(typeof(StrikeIronclad)));
        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(StrikeIronclad), typeof(StrikeIronclad), typeof(StrikeIronclad)));
    }

    #endregion

    #region Card discard

    [Test]
    public async Task TestSelectedDiscardWithEmptyHand()
    {
        await Play<Survivor>();

        Assert.That(GetPlayer().Creature, Has.Block(8)); // Card still works
        Assert.That(GetPile(PileType.Hand).Cards, Is.Empty);
        Assert.That(GetPile(PileType.Discard), Has.Cards(typeof(Survivor)));
    }

    [Test]
    public async Task TestSelectedDiscardWith1CardHand()
    {
        await CardPileCmd.Add(CreateCard<DefendIronclad>(), PileType.Hand);
        await Play<Survivor>();

        Assert.That(GetPile(PileType.Hand).Cards, Is.Empty);
        Assert.That(GetPile(PileType.Discard), Has.Cards(typeof(DefendIronclad), typeof(Survivor)));
    }

    [Test]
    public async Task TestSelectedDiscardWithMultiCardHand()
    {
        CardModel cardToDiscard = CreateCard<DefendIronclad>();
        await CardPileCmd.Add(cardToDiscard, PileType.Hand);
        await CardPileCmd.Add(CreateCard<StrikeIronclad>(), PileType.Hand);
        await CardPileCmd.Add(CreateCard<Bash>(), PileType.Hand);

        PrepareToSelect(cardToDiscard);
        await Play<Survivor>();

        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(StrikeIronclad), typeof(Bash)));
        Assert.That(GetPile(PileType.Discard), Has.Cards(typeof(DefendIronclad), typeof(Survivor)));
    }

    #endregion

    #region Keywords

    [Test]
    public async Task TestEthereal()
    {
        CardModel card = CreateCard<Dazed>();

        await CardPileCmd.Add(card, PileType.Hand);
        await PassToNextPlayerTurn();

        Assert.That(card, Is.InPile(PileType.Exhaust));
    }

    [Test]
    public async Task TestExhaust()
    {
        CardModel card = MockSkill().MockKeyword(CardKeyword.Exhaust);
        await Play(card);

        Assert.That(card, Is.InPile(PileType.Exhaust));
    }

    [Test]
    public async Task TestUpgradeRemovesExhaust()
    {
        CardModel card = MockSkill()
            .MockKeyword(CardKeyword.Exhaust)
            .MockUpgradeLogic(c => c.RemoveKeyword(CardKeyword.Exhaust));
        CardCmd.Upgrade(card);

        await Play(card);

        Assert.That(card, Is.InPile(PileType.Discard));
    }

    [Test]
    public async Task TestInnate()
    {
        CardPile deck = GetPile(PileType.Deck);

        for (int i = 0; i < 20; i++)
        {
            await CardPileCmd.Add(MockAttack(CardScope.Climb), deck);
        }

        await CardPileCmd.Add(MockSkill(CardScope.Climb).MockKeyword(CardKeyword.Innate), deck);

        await RestartCombat();

        // Note: This test should pass by coincidence a fair amount, but since the Innate card should _always_ start in
        // the player's opening hand, it'll eventually fail if Innate isn't working correctly.
        Assert.That(GetPile(PileType.Hand).Cards.Count(c => c.Keywords.Contains(CardKeyword.Innate)), Is.GreaterThan(0));
    }

    [Test]
    public void TestUpgradeAddsInnate()
    {
        MockCardModel card = MockSkill().MockUpgradeLogic((c) => c.AddKeyword(CardKeyword.Innate));

        Assert.That(card, Has.No.Keyword(CardKeyword.Innate));
        Assert.That(Upgrade(card), Has.Keyword(CardKeyword.Innate));
    }

    [Test]
    public async Task TestInnateUsingUpHandDraw()
    {
        CardPile deck = GetPile(PileType.Deck);

        // ALL of these should get drawn, even though hand draw count is 5.
        for (int i = 0; i < 8; i++)
        {
            await CardPileCmd.Add(MockSkill(CardScope.Climb).MockKeyword(CardKeyword.Innate), deck);
        }

        // NONE of these should get drawn, since the hand draw was used up by Innate cards.
        for (int i = 0; i < 20; i++)
        {
            await CardPileCmd.Add(MockAttack(CardScope.Climb), deck);
        }

        await RestartCombat();

        CardPile hand = GetPile(PileType.Hand);
        Assert.That(hand.Cards.Count(c => c.Keywords.Contains(CardKeyword.Innate)), Is.EqualTo(8));
        Assert.That(hand.Cards.Count(c => !c.Keywords.Contains(CardKeyword.Innate)), Is.EqualTo(0));
    }

    [Test]
    public async Task TestInnateFillingHand()
    {
        CardPile deck = GetPile(PileType.Deck);

        // These should be drawn until the hand limit is reached.
        for (int i = 0; i < CardPile.maxCardsInHand + 1; i++)
        {
            await CardPileCmd.Add(MockSkill(CardScope.Climb).MockKeyword(CardKeyword.Innate), deck);
        }

        // NONE of these should get drawn, since the hand draw was used up by Innate cards.
        for (int i = 0; i < 20; i++)
        {
            await CardPileCmd.Add(MockAttack(CardScope.Climb), deck);
        }

        await RestartCombat();

        CardPile hand = GetPile(PileType.Hand);
        Assert.That(hand.Cards.Count(c => c.Keywords.Contains(CardKeyword.Innate)), Is.EqualTo(CardPile.maxCardsInHand));
        Assert.That(hand.Cards.Count(c => !c.Keywords.Contains(CardKeyword.Innate)), Is.EqualTo(0));
    }

    [Test]
    public async Task TestRetain()
    {
        CardPile hand = GetPile(PileType.Hand);

        await CardPileCmd.Add(CreateCard<TimesUp>(), hand);
        await PassToNextPlayerTurn();

        Assert.That(hand, Has.Cards(typeof(TimesUp)));
    }

    [Test]
    public async Task TestSly()
    {
        await CardCmd.Discard(new ThrowingPlayerChoiceContext(), CreateCard<UncannyDodge>());
        Assert.That(GetPlayer().Creature, Has.Block(9));
    }

    [Test]
    public void TestUpgradeAddsRetain()
    {
        Assert.That(CreateCard<TimesUp>(), Has.No.Keyword(CardKeyword.Retain));
        Assert.That(Upgrade(CreateCard<TimesUp>()), Has.Keyword(CardKeyword.Retain));
    }

    [Test]
    public void TestUnplayable()
    {
        Assert.That(
            async () => await Play(MockSkill().MockKeyword(CardKeyword.Unplayable)),
            Throws.TypeOf<TestCardPlayException>()
        );
    }

    [Test]
    public async Task TestEtherealWithRetain()
    {
        CardModel card = CreateCard<DefendIronclad>();
        card.AddKeyword(CardKeyword.Ethereal);
        card.AddKeyword(CardKeyword.Retain);
        await CardPileCmd.Add(card, PileType.Hand);

        await PassToNextPlayerTurn();

        // From https://slay-the-spire.fandom.com/wiki/Retain:
        // "Cards with Ethereal still exhaust at the end of your turn even if you attempt to Retain them."
        Assert.That(card, Is.InPile(PileType.Exhaust));
    }

    #endregion

    #region Power application

    [Test]
    public async Task TestAppliesPowerToSelf()
    {
        await Play<Inflame>();
        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(2));
    }

    [Test]
    public async Task TestUpgradeAppliesMorePowerToSelf()
    {
        await PlayUpgraded<Inflame>();
        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(3));
    }

    [Test]
    public async Task TestAppliesPowerToSingleEnemy()
    {
        Creature enemy = GetEnemy();

        await Play<Bash>(enemy);

        Assert.That(enemy, Has.PowerAmount<Vulnerable>(2));
    }

    [Test]
    public async Task TestUpgradeAppliesMorePowerToSingleEnemy()
    {
        Creature enemy = GetEnemy();

        await PlayUpgraded<Bash>(enemy);

        Assert.That(enemy, Has.PowerAmount<Vulnerable>(3));
    }

    [Test]
    public async Task TestAppliesPowerToMultipleEnemies()
    {
        await CreateEnemy<BigDummy>();

        await Play<Shockwave>();

        IEnumerable<Creature> enemies = GetEnemies();
        Assert.That(enemies, Has.All.PowerAmount<Weak>(3));
        Assert.That(enemies, Has.All.PowerAmount<Vulnerable>(3));
    }

    [Test]
    public async Task TestPowerCardsAreRemovedFromCombat()
    {
        CardModel card = await Play<Inflame>();
        Assert.That(card.Pile, Is.Null);
    }

    [Test]
    public async Task TestPowerApplicationDuringLethalDamage()
    {
        Creature enemy = await CreateEnemy<OneHpMonster>();
        Assert.That(async () => await Play<Bash>(enemy), Throws.Nothing);
    }

    #endregion

    #region Statuses and Curses

    [Test]
    public async Task TestEndOfTurnStatusCardsDoNotTriggerIfCombatEndsFirst()
    {
        await CardPileCmd.Add(CreateCard<Burn>(), PileType.Hand);
        await Play<Combust>();
        await CreatureCmd.SetCurrentHp(GetEnemy(), 1);

        await PassToNextPlayerTurn();

        // 1 from Combust, 0 from Burn (since Combust killed the enemy and ended combat before Burn could trigger).
        Assert.That(GetPlayer().Creature, Has.LostHp(1));
    }

    #endregion

    #region Orbs

    [Test]
    public async Task TestSingleOrbChanneling()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await Play<Zap>();

        IReadOnlyList<OrbModel> orbs = player.PlayerCombatState!.OrbQueue.Orbs;
        Assert.That(orbs.Count, Is.EqualTo(1));
        Assert.That(orbs[0], Is.TypeOf<LightningOrb>());
    }

    [Test]
    public async Task TestMultiOrbChanneling()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);
        await Play<Glacier>();

        IReadOnlyList<OrbModel> orbs = player.PlayerCombatState!.OrbQueue.Orbs;

        Assert.That(orbs.Count, Is.EqualTo(2));
        Assert.That(orbs, Is.All.TypeOf<FrostOrb>());
    }

    [Test]
    public async Task TestOrbChannelingWithoutSlots()
    {
        await Play<Zap>();
        Assert.That(GetPlayer().PlayerCombatState!.OrbQueue.Orbs.Count, Is.EqualTo(1));
    }

    [Test]
    public async Task TestAutoEvokingAfterChanneling()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        for (int i = 0; i < 4; i++)
        {
            await Play<Zap>();
        }

        Assert.That(GetEnemy(), Has.LostHp(8));
    }

    #endregion

    #region Osty/Summon

    [Test]
    public async Task TestInitialSummon()
    {
        await Play<Camaraderie>();
        Assert.That(GetPlayer().Osty!.CurrentHp, Is.EqualTo(5));
    }

    [Test]
    public async Task TestSummonWithOstyAlive()
    {
        await OstyCmd.Summon(new ThrowingPlayerChoiceContext(), GetPlayer(), 1, null);
        await Play<Camaraderie>();
        Assert.That(GetPlayer().Osty!.CurrentHp, Is.EqualTo(6));
    }

    [Test]
    public async Task TestSummonWithOstyDead()
    {
        await Play<Camaraderie>();
        await CreatureCmd.Kill(GetPlayer().Osty!);
        await Play<Camaraderie>();

        Assert.That(GetPlayer().Osty!.CurrentHp, Is.EqualTo(5));
    }

    [Test]
    public async Task TestOstyAttack()
    {
        Creature enemy = GetEnemy();

        await OstyCmd.Summon(new ThrowingPlayerChoiceContext(), GetPlayer(), 1, null);
        await Play(MockAttack().MockFromOsty(), enemy);

        Assert.That(enemy, Has.LostHp(6));
    }

    [Test]
    public async Task TestOstyRandomAttack()
    {
        Creature enemy = GetEnemy();

        await OstyCmd.Summon(new ThrowingPlayerChoiceContext(), GetPlayer(), 1, null);
        await Play(MockAttack().MockFromOsty().MockTargetingType(UiTargetEnemy.Random));

        Assert.That(enemy, Has.LostHp(6));
    }

    [Test]
    public async Task TestOstyRandomAttackWithRevivingEnemy()
    {
        Creature enemy = GetEnemy();
        await PowerCmd.Apply<MockRevivePower>(enemy, 1, null, null);
        await CreatureCmd.Kill(enemy);

        await OstyCmd.Summon(new ThrowingPlayerChoiceContext(), GetPlayer(), 1, null);

        Assert.That(
            async () => await Play(MockAttack().MockFromOsty().MockTargetingType(UiTargetEnemy.Random)),
            Throws.Nothing
        );
    }

    [Test]
    public async Task TestOstyPowerApplication()
    {
        Player player = GetPlayer();
        await OstyCmd.Summon(new ThrowingPlayerChoiceContext(), player, 1, null);
        await Play<TagIn>();

        Assert.That(player.Osty, Has.PowerAmount<Strength>(2));
    }

    #endregion

    #region Soul

    [Test]
    public async Task TestSoulCardCreation()
    {
        await Play<GraveWarden>();
        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(Soul)));
    }

    #endregion

    #region Forge

    [Test]
    public async Task TestForge()
    {
        await ForgeCmd.Forge(0, GetPlayer());
        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(SovereignBlade)));
    }

    [Test]
    public async Task TestForgeDamage()
    {
        int baseDamage = CreateCard<SovereignBlade>().DynamicVars.Damage.IntValue;
        const int testForgeAmount = 5; // Arbitrary test value

        await ForgeCmd.Forge(testForgeAmount, GetPlayer());
        SovereignBlade sovereignBlade = (SovereignBlade)GetPile(PileType.Hand).Cards[0];
        await Play(sovereignBlade, GetEnemy());

        // Forge adds to the base damage
        int expectedDamage = baseDamage + testForgeAmount;
        Assert.That(GetEnemy(), Has.LostHp(expectedDamage));
    }

    [Test]
    public async Task TestForgeAfterExhaust()
    {
        int baseDamage = CreateCard<SovereignBlade>().DynamicVars.Damage.IntValue;
        const int firstForge = 5; // Arbitrary test value
        const int secondForge = 5; // Arbitrary test value

        Creature enemy = GetEnemy();
        Player player = GetPlayer();
        CardModel card = (await ForgeCmd.Forge(firstForge, player)).First();
        await CardCmd.Exhaust(new ThrowingPlayerChoiceContext(), card);
        await ForgeCmd.Forge(secondForge, player);

        SovereignBlade sovereignBlade = (SovereignBlade)PileType.Hand.GetPile(player).Cards[0];
        await Play(sovereignBlade, enemy);

        // This could fail for 2 reasons:
        // 1. Sovereign Blade was buffed in Exhaust but not returned to hand (so the card play itself would error).
        // 2. A new Sovereign Blade was created (so it would lose its first buff and only do base + second forge).
        // Expected: base + first forge + second forge
        int expectedDamage = baseDamage + firstForge + secondForge;
        Assert.That(enemy, Has.LostHp(expectedDamage));
    }

    #endregion
}
