using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class ViciousTest : ModelTest
{
    [Test]
    public async Task TestDrawCardWhenApplyingVulnerable()
    {
        for (int i = 0; i < 8; i++)
        {
            await CardPileCmd.Add(MockAttack(), GetPile(PileType.Draw));
        }
        await Play<Vicious>();
        await PowerCmd.Apply<Vulnerable>(GetEnemy(), 2, GetPlayer().Creature, null);

        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(MockAttackCard)));
    }

    [Test]
    public async Task TestDoesNothingWhenApplyingNotVulnerable()
    {
        for (int i = 0; i < 8; i++)
        {
            await CardPileCmd.Add(MockAttack(), GetPile(PileType.Draw));
        }
        await Play<Vicious>();
        await PowerCmd.Apply<Weak>(GetEnemy(), 2, GetPlayer().Creature, null);

        Assert.That(GetPile(PileType.Hand), Has.Cards());
    }

    [Test]
    public async Task TestUpgrade()
    {
        for (int i = 0; i < 8; i++)
        {
            await CardPileCmd.Add(MockAttack(), GetPile(PileType.Draw));
        }
        await PlayUpgraded<Vicious>();
        await PowerCmd.Apply<Vulnerable>(GetEnemy(), 2, GetPlayer().Creature, null);

        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(MockAttackCard), typeof(MockAttackCard)));
    }
}
