using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class VoidTest : ModelTest
{
    [Test]
    public async Task TestDraw()
    {
        CardModel card = CreateCard<Void>();

        await CardPileCmd.Add(card, PileType.Draw);
        await CardPileCmd.Draw(new ThrowingPlayerChoiceContext(), GetPlayer());
        Assert.That(GetPlayer(), Has.SpentEnergy(1));
    }

    // shouldn't lose energy if card is not "drawn"
    [Test]
    public async Task TestPullFromDiscard()
    {
        CardModel card = CreateCard<Void>();

        await CardPileCmd.Add(card, PileType.Discard);
        await Play<Hologram>();

        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(Void)));

        // 1 energy from playing hologram
        Assert.That(GetPlayer(), <PERSON>.SpentEnergy(1));
    }
}
