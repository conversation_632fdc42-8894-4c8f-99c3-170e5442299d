using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class WhiteNoiseTest : ModelTest
{
    [Test]
    public async Task TestGeneratedCardAndEnergyCost()
    {
        CardPile hand = GetPile(PileType.Hand);

        await Play<WhiteNoise>();

        CardModel card = hand.Cards[^1];

        Assert.That(card, Has.CardType(CardType.Power));
        Assert.That(card, Has.EnergyCostFree());
        Assert.That(hand.Cards.Count, Is.EqualTo(1));
        Assert.That(GetPlayer(), Has.SpentEnergy(1));
    }

    [Test]
    public async Task TestResetCost()
    {
        CardPile hand = GetPile(PileType.Hand);

        await Play<WhiteNoise>();

        CardModel card = hand.Cards[^1];

        if (card.BaseEnergyCost == 0)
        {
            // We got unlucky and pulled a zero cost card, so skip the test.
            return;
        }

        await PassToNextPlayerTurn();

        Assert.That(card, Has.CardType(CardType.Power));
        Assert.That(card, Has.EnergyCostGreaterThan(0).Or.EnergyCostX());
    }

    [Test]
    public async Task TestUpgradeDecreasesCost()
    {
        await PlayUpgraded<WhiteNoise>();
        Assert.That(GetPlayer(), Has.SpentEnergy(0));
    }
}
