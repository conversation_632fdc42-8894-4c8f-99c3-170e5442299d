using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Commands;

public class CreatureCmdTest : ModelTest
{
    [Test]
    public async Task TestNoDamageIfDealerIsDead()
    {
        Creature player = GetPlayer().Creature;
        Creature deadEnemy = await CreateEnemy<OneHpMonster>();
        await CreatureCmd.Kill(deadEnemy);

        await CreatureCmd.Damage(player, 10, DamageProps.monsterMove, deadEnemy);

        // Dead enemies can't deal damage.
        Assert.That(player, Has.LostHp(0));
    }
}
