using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Enchantments;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Enchantments;

public class AdroitTest : ModelTest
{
    [Test]
    public async Task TestAddsBlock()
    {
        CardModel card = CreateCard<DefendIronclad>();

        CardCmd.Enchant<Adroit>(card, 5);
        await Play(card);

        // 5 + 5
        Assert.That(GetPlayer().<PERSON>reature, Has.Block(10));
    }

    [Test]
    public async Task TestDexterityAppliesSeparately()
    {
        CardModel card = CreateCard<DefendIronclad>();

        await PowerCmd.Apply<Dexterity>(GetPlayer().Creature, 2, null, null);
        CardCmd.Enchant<Adroit>(card, 5);
        await Play(card);

        // 5 + 2 from Defend + Dexterity
        // 5 + 2 from Rigid + Dexterity
        Assert.That(GetPlayer().Creature, Has.Block(14));
    }
}
