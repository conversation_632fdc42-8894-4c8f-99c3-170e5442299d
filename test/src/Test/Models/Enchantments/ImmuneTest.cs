using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Afflictions;
using MegaCrit.Sts2.Core.Models.Enchantments;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Enchantments;

public class ImmuneTest : ModelTest
{
    [Test]
    public async Task TestBlocksAfflictions()
    {
        CardModel card = MockSkill();
        CardCmd.Enchant<Immune>(card, 1);

        // Card must be added to a pile for Immune's hook to trigger.
        await CardPileCmd.Add(card, PileType.Draw);

        await CardCmd.Afflict<Ringing>(card, 1);

        Assert.That(card.Affliction, Is.Null);
    }

    [Test]
    public async Task TestDoesNotBlockAfflictionsOnOtherCards()
    {
        CardModel card1 = MockSkill();
        CardModel card2 = MockSkill();
        CardCmd.Enchant<Immune>(card1, 1);

        // Cards must be added to a pile for <PERSON>mm<PERSON>'s hook to trigger.
        await CardPileCmd.Add([card1, card2], PileType.Draw);

        await CardCmd.Afflict<Ringing>(card2, 1);

        Assert.That(card2.Affliction, Is.TypeOf<Ringing>());
    }
}
