using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Enchantments;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Enchantments;

public class InstinctTest : ModelTest
{
    [Test]
    public async Task TestCost1Less()
    {
        MockCardModel cost2Card = MockAttack().MockEnergyCost(2);
        CardCmd.Enchant<Instinct>(cost2Card, 1);

        await Play(cost2Card, GetEnemy());

        Assert.That(GetPlayer(), Has.SpentEnergy(1));
    }

    [Test]
    public async Task TestOnCardThatBecameCosts0()
    {
        CardModel card = MockAttack();
        CardCmd.Enchant<Instinct>(card, 1);
        CardCmd.Upgrade(card);

        await Play(card, GetEnemy());

        Assert.That(GetPlayer(), Has.SpentEnergy(0));
    }

    [Test]
    public void TestCantEnchantXCards()
    {
        Assert.That(ModelDb.Enchantment<Instinct>().CanEnchant(MockAttack().MockEnergyCostX()), Is.False);
    }

    [Test]
    public void TestBodySlam()
    {
        CardModel card = CreateCard<BodySlam>();
        Assert.That(ModelDb.Enchantment<Instinct>().CanEnchant(card), Is.True);
    }

    [Test]
    public void TestBodySlamUpgrade()
    {
        CardModel card = CreateCard<BodySlam>();
        CardCmd.Upgrade(card);
        Assert.That(ModelDb.Enchantment<Instinct>().CanEnchant(card), Is.False);
    }

    /// <summary>
    /// There's a subtle bug where enchanting a 1-cost card with Instinct can break the start of combat. This is because
    /// Instinct turns it into a 0-cost card, which means it's no longer valid to enchant with Instinct. This type of
    /// conflict shouldn't be an issue when calls are done at the right time, but it's easy to mess that up, which is
    /// why this test exists.
    /// </summary>
    [Test]
    public async Task TestAllowsCombatStartAfterEnchanting1CostCard()
    {
        CardModel card = MockAttack(CardScope.Climb);
        CardCmd.Enchant<Instinct>(card, 1);
        await CardPileCmd.Add(card, PileType.Deck);

        Assert.That(async () => await RestartCombat(), Throws.Nothing);
    }
}
