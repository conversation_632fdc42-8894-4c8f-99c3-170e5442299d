using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Enchantments;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Enchantments;

public class RoyallyApprovedTest : ModelTest
{
    [Test]
    public async Task TestCostZeroAndExhaust()
    {
        CardModel card = MockSkill();
        CardCmd.Enchant<RoyallyApproved>(card, 1);
        await Play(card);

        Assert.That(GetPlayer(), Has.SpentEnergy(0));
        Assert.That(card, Is.InPile(PileType.Exhaust));
    }

    /// <summary>
    /// We test this even though we have a vanilla test for Innate, since this tests that it works when added by an
    /// Enchantment.
    /// </summary>
    [Test]
    public async Task TestInnate()
    {
        CardPile deck = GetPile(PileType.Deck);
        CardModel card = MockAttack(CardScope.Climb);
        CardCmd.Enchant<RoyallyApproved>(card, 1);
        await CardPileCmd.Add(card, deck);

        for (int i = 0; i < 20; i++)
        {
            await CardPileCmd.Add(MockSkill(CardScope.Climb), deck);
        }

        await RestartCombat();

        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(MockAttackCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard)));
    }
}
