using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Enchantments;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Enchantments;

public class SharpTest : ModelTest
{
    [Test]
    public async Task TestIncreasesPlayerDamage()
    {
        CardModel card = MockAttack();
        Creature enemy = GetEnemy();

        CardCmd.Enchant<Sharp>(card, 3);
        await Play(card, enemy);

        // 6 + 3
        Assert.That(enemy, Has.LostHp(9));
    }

    [Test]
    public async Task TestIncreasesOstyDamage()
    {
        Creature enemy = GetEnemy();

        await OstyCmd.Summon(new ThrowingPlayerChoiceContext(), GetPlayer(), 1, null);
        MockAttackCard card = MockAttack().MockFromOsty();
        CardCmd.Enchant<Sharp>(card, 3);

        await Play(card, enemy);

        // 6 + 3
        Assert.That(enemy, Has.LostHp(9));
    }

    [Test]
    public async Task TestDoesNotWorkOnSelfHpLoss()
    {
        Creature enemy = GetEnemy();
        MockCardModel card = MockAttack().MockSelfHpLoss(1);
        CardCmd.Enchant<Sharp>(card, 3);

        await Play(card, enemy);

        // Sharp should increase the enemy's HP loss but not self.
        Assert.That(enemy, Has.LostHp(9));
        Assert.That(GetPlayer().Creature, Has.LostHp(1));
    }

    [Test]
    public void TestDoesNotWorkOnNonAttacks()
    {
        Assert.That(ModelDb.Enchantment<Sharp>().CanEnchant(ModelDb.Card<MockSkillCard>()), Is.False);
    }
}
