using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Enchantments;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Enchantments;

public class SlitherTest : ModelTest
{
    [Test]
    public async Task TestRandomizesCost()
    {
        CardModel card = CreateCard<StrikeIronclad>();
        await CardPileCmd.Add(card, GetPile(PileType.Draw));

        Slither slither = (CardCmd.Enchant<Slither>(card, 1))!;
        slither.TestEnergyCostOverride = 3;

        await CardPileCmd.Draw(new ThrowingPlayerChoiceContext(), GetPlayer());
        await Play(card, GetEnemy());

        Assert.That(GetPlayer(), Has.SpentEnergy(3));
    }

    [Test]
    public void TestCanNotBeAddedToUnplayableCard()
    {
        Assert.That(ModelDb.Enchantment<Slither>().CanEnchant(ModelDb.Card<Writhe>()), Is.False);
    }

    [Test]
    public void TestCanNotBeAddedToXCostCard()
    {
        Assert.That(ModelDb.Enchantment<Slither>().CanEnchant(ModelDb.Card<Whirlwind>()), Is.False);
    }
}
