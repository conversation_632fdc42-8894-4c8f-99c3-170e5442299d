using System;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Modifiers;
using MegaCrit.Sts2.Core.Multiplayer;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Modifiers;

public class HoarderTest : ModelTest
{
    protected override ClimbState ConstructClimb(CharacterModel character)
    {
        return ClimbState.CreateForTest(
            players: [Player.CreateForNewClimb(character, NetSingleplayerGameService.defaultNetId)],
            modifiers: [ModelDb.Modifier<Hoarder>().ToMutable()]
        );
    }

    [Test]
    public async Task TestThatThreeCardsAreAddedWhenOneCardIsAdded()
    {
        MockAttackCard card = MockAttack(CardScope.Climb);
        await CardPileCmd.Add(card, PileType.Deck);

        Assert.That(GetPlayer().Deck, Has.Cards(typeof(MockAttackCard), typeof(MockAttackCard), typeof(MockAttackCard)));
    }

    [Test]
    public void TestPreventsRemovingCardsAtMerchant()
    {
        bool canRemoveCardAtMerchant = Hook.ShouldAllowMerchantCardRemoval(GetPlayer().ClimbState, GetPlayer());
        Assert.That(canRemoveCardAtMerchant, Is.False);
    }
}
