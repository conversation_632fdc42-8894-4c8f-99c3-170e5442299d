using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Orbs;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Orbs;

public class DarkOrbTest : ModelTest
{
    [Test]
    public async Task TestEvokeTurn1()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await OrbCmd.Channel<DarkOrb>(new NullPlayerChoiceContext(), player);
        await OrbCmd.EvokeNext(new NullPlayerChoiceContext(),player);

        Assert.That(GetEnemy(), Has.LostHp(6));
    }

    [Test]
    public async Task TestEvokeTurn3()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await OrbCmd.Channel<DarkOrb>(new NullPlayerChoiceContext(),player);

        for (int i = 0; i < 3; i++)
        {
            await PassToNextPlayerTurn();
        }

        await OrbCmd.EvokeNext(new NullPlayerChoiceContext(),player);
        Assert.That(GetEnemy(), Has.LostHp(24));
    }

    [Test]
    public async Task TestMultipleOrbs()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        for (int i = 0; i < 2; i++)
        {
            await OrbCmd.Channel<DarkOrb>(new NullPlayerChoiceContext(),player);
        }

        for (int i = 0; i < 3; i++)
        {
            await PassToNextPlayerTurn();
        }

        for (int i = 0; i < 2; i++)
        {
            await OrbCmd.EvokeNext(new NullPlayerChoiceContext(),player);
        }

        Assert.That(GetEnemy(), Has.LostHp(48));
    }

    [Test]
    public async Task TestOrbsIndependent()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await OrbCmd.Channel<DarkOrb>(new NullPlayerChoiceContext(),player);

        await PassToNextPlayerTurn();

        await OrbCmd.Channel<DarkOrb>(new NullPlayerChoiceContext(),player);


        for (int i = 0; i < 2; i++)
        {
            await PassToNextPlayerTurn();
        }

        for (int i = 0; i < 2; i++)
        {
            await OrbCmd.EvokeNext(new NullPlayerChoiceContext(), player);
        }

        // 24 + 18
        Assert.That(GetEnemy(), Has.LostHp(42));
    }

    [Test]
    public async Task TestStrengthDoesNotAffectDamage()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await OrbCmd.Channel<DarkOrb>(new NullPlayerChoiceContext(), player);
        await PowerCmd.Apply<Strength>(player.Creature, 2, player.Creature, null);

        await OrbCmd.EvokeNext(new NullPlayerChoiceContext(), player);

        Assert.That(GetEnemy(), Has.LostHp(6));
    }

    [Test]
    public async Task TestFocusTurn1()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await PowerCmd.Apply<Focus>(player.Creature, 2, player.Creature, null);

        await OrbCmd.Channel<DarkOrb>(new NullPlayerChoiceContext(), player);
        await OrbCmd.EvokeNext(new NullPlayerChoiceContext(), player);

        // Shouldn't do anything because the evoke value hasn't increased.
        Assert.That(GetEnemy(), Has.LostHp(6));
    }

    [Test]
    public async Task TestFocusTurn3()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await PowerCmd.Apply<Focus>(player.Creature, 2, player.Creature, null);

        await OrbCmd.Channel<DarkOrb>(new NullPlayerChoiceContext(), player);

        for (int i = 0; i < 3; i++)
        {
            await PassToNextPlayerTurn();
        }

        await OrbCmd.EvokeNext(new NullPlayerChoiceContext(), player);

        Assert.That(GetEnemy(), Has.LostHp(30));
    }

    [Test]
    public async Task TestNegativeFocus()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await PowerCmd.Apply<Focus>(player.Creature, -6, player.Creature, null);
        await OrbCmd.Channel<DarkOrb>(new NullPlayerChoiceContext(), player);

        for (int i = 0; i < 3; i++)
        {
            await PassToNextPlayerTurn();
        }

        await OrbCmd.EvokeNext(new NullPlayerChoiceContext(), player);

        Assert.That(GetEnemy(), Has.LostHp(6));
    }

    [Test]
    public async Task TestFocusAfterTurn2()
    {
        Player player = GetPlayer();

        await OrbCmd.AddSlots(player, 3);

        await OrbCmd.Channel<DarkOrb>(new NullPlayerChoiceContext(), player);
        await PassToNextPlayerTurn();
        await PowerCmd.Apply<Focus>(player.Creature, 2, player.Creature, null);

        for (int i = 0; i < 2; i++)
        {
            await PassToNextPlayerTurn();
        }

        await OrbCmd.EvokeNext(new NullPlayerChoiceContext(), player);

        Assert.That(GetEnemy(), Has.LostHp(28));
    }

    [Test]
    public async Task TestRemoveFocusAfterTurn2()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await PowerCmd.Apply<Focus>(player.Creature, 2, player.Creature, null);
        await OrbCmd.Channel<DarkOrb>(new NullPlayerChoiceContext(), player);
        await PassToNextPlayerTurn();
        await PowerCmd.Apply<Focus>(player.Creature, -2, player.Creature, null);

        for (int i = 0; i < 2; i++)
        {
            await PassToNextPlayerTurn();
        }

        await OrbCmd.EvokeNext(new NullPlayerChoiceContext(), player);

        Assert.That(GetEnemy(), Has.LostHp(26));
    }
}
