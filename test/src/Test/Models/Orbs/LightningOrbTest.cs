using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Orbs;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Orbs;

public class LightningOrbTest : ModelTest
{
    [Test]
    public async Task TestPassiveDamage()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);
        await OrbCmd.Channel<LightningOrb>(new NullPlayerChoiceContext(), player);

        await PassToNextPlayerTurn();

        Assert.That(player.PlayerCombatState!.OrbQueue.Orbs.Count, Is.EqualTo(1));
        Assert.That(GetEnemy(), Has.LostHp(3));
    }

    [Test]
    public async Task TestMultipleOrbs()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        for (int i = 0; i < 3; i++)
        {
            await OrbCmd.Channel<LightningOrb>(new NullPlayerChoiceContext(), player);
        }

        await PassToNextPlayerTurn();

        Assert.That(GetEnemy(), Has.LostHp(9));
    }

    [Test]
    public async Task TestEvokeOrbs()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        for (int i = 0; i < 4; i++)
        {
            await OrbCmd.Channel<LightningOrb>(new NullPlayerChoiceContext(), player);
        }

        Assert.That(GetEnemy(), Has.LostHp(8));
        Assert.That(player.PlayerCombatState!.OrbQueue.Orbs.Count, Is.EqualTo(3));
    }

    [Test]
    public async Task TestEvokeThenNextTurn()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        for (int i = 0; i < 4; i++)
        {
            await OrbCmd.Channel<LightningOrb>(new NullPlayerChoiceContext(), player);
        }

        await PassToNextPlayerTurn();

        // 3 + 3 + 3 + 8
        Assert.That(GetEnemy(), Has.LostHp(17));
        Assert.That(player.PlayerCombatState!.OrbQueue.Orbs.Count, Is.EqualTo(3));
    }

    [Test]
    public async Task TestForceEvoke()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        for (int i = 0; i < 2; i++)
        {
            await OrbCmd.Channel<LightningOrb>(new NullPlayerChoiceContext(), player);
        }

        await OrbCmd.EvokeNext(new NullPlayerChoiceContext(), player);

        Assert.That(GetEnemy(), Has.LostHp(8));
        Assert.That(player.PlayerCombatState!.OrbQueue.Orbs.Count, Is.EqualTo(1));
    }

    [Test]
    public async Task TestStrengthDoesntAffectDamage()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);
        await PowerCmd.Apply<Strength>(player.Creature, 2, player.Creature, null);

        for (int i = 0; i < 4; i++)
        {
            await OrbCmd.Channel<LightningOrb>(new NullPlayerChoiceContext(), player);
        }

        await PassToNextPlayerTurn();

        // 3 + 3 + 3 + 8
        Assert.That(GetEnemy(), Has.LostHp(17));
        Assert.That(player.PlayerCombatState!.OrbQueue.Orbs.Count, Is.EqualTo(3));
    }
}
