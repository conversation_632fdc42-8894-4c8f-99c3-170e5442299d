using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Orbs;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Orbs;

public class PlasmaOrbTest : ModelTest
{
    [Test]
    public async Task TestEnergyFirstTurn()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await OrbCmd.Channel<PlasmaOrb>(new NullPlayerChoiceContext(), player);

        Assert.That(player.PlayerCombatState!.OrbQueue.Orbs.Count, Is.EqualTo(1));
        Assert.That(player, Has.ExtraEnergy(0));
    }

    [Test]
    public async Task TestPassiveEnergy()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await OrbCmd.Channel<PlasmaOrb>(new NullPlayerChoiceContext(), player);

        await PassToNextPlayerTurn();

        Assert.That(player.PlayerCombatState!.OrbQueue.Orbs.Count, Is.EqualTo(1));
        Assert.That(player, Has.ExtraEnergy(1));
    }

    [Test]
    public async Task TestMultipleOrbs()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        for (int i = 0; i < 3; i++)
        {
            await OrbCmd.Channel<PlasmaOrb>(new NullPlayerChoiceContext(), player);
        }

        await PassToNextPlayerTurn();

        Assert.That(player.PlayerCombatState!.OrbQueue.Orbs.Count, Is.EqualTo(3));
        Assert.That(player, Has.ExtraEnergy(3));
    }

    [Test]
    public async Task TestEvokeOrbs()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        for (int i = 0; i < 4; i++)
        {
            await OrbCmd.Channel<PlasmaOrb>(new NullPlayerChoiceContext(), player);
        }

        Assert.That(player, Has.ExtraEnergy(2));
    }

    [Test]
    public async Task TestNegativeFocus()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await PowerCmd.Apply<Focus>(player.Creature, -1, player.Creature, null);
        await OrbCmd.Channel<PlasmaOrb>(new NullPlayerChoiceContext(), player);
        await PassToNextPlayerTurn();

        Assert.That(player, Has.SpentEnergy(0));
        Assert.That(player, Has.ExtraEnergy(1));
    }
}
