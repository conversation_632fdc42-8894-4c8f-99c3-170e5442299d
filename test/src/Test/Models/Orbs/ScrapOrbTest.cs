using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Orbs;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Orbs;

public class ScrapOrbTest : ModelTest
{
    [Test]
    public async Task TestPassive()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await OrbCmd.Channel<ScrapOrb>(new NullPlayerChoiceContext(), player);

        CardPile drawPile = GetPile(PileType.Hand);
        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(CreateCard<MockAttackCard>(), drawPile);
        }

        await PassToNextPlayerTurn();
        Assert.That(GetPile(PileType.Hand).Cards.Count, Is.EqualTo(6));
    }

    [Test]
    public async Task TestPassiveWithFocus()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);
        await PowerCmd.Apply<Focus>(player.Creature, 2, player.Creature, null);

        await OrbCmd.Channel<ScrapOrb>(new NullPlayerChoiceContext(), player);

        CardPile drawPile = GetPile(PileType.Hand);
        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(CreateCard<MockAttackCard>(), drawPile);
        }

        await PassToNextPlayerTurn();
        Assert.That(GetPile(PileType.Hand).Cards.Count, Is.EqualTo(8));
    }

    [Test]
    public async Task TestPassiveNegativeFocus()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);
        await PowerCmd.Apply<Focus>(player.Creature, -2, player.Creature, null);

        await OrbCmd.Channel<ScrapOrb>(new NullPlayerChoiceContext(), player);

        CardPile drawPile = GetPile(PileType.Hand);
        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(CreateCard<MockAttackCard>(), drawPile);
        }

        await PassToNextPlayerTurn();
        Assert.That(GetPile(PileType.Hand).Cards.Count, Is.EqualTo(5));
    }

    [Test]
    public async Task TestEvokeTurn()
    {
        CardPile drawPile = GetPile(PileType.Draw);
        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(CreateCard<MockAttackCard>(), drawPile);
        }

        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await OrbCmd.Channel<ScrapOrb>(new NullPlayerChoiceContext(), player);
        await OrbCmd.EvokeNext(new NullPlayerChoiceContext(), player);

        Assert.That(GetPile(PileType.Hand).Cards.Count, Is.EqualTo(1));
    }

    [Test]
    public async Task TestEvokeWithFocus()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await PowerCmd.Apply<Focus>(player.Creature, 2, player.Creature, null);

        CardPile drawPile = GetPile(PileType.Draw);
        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(CreateCard<MockAttackCard>(), drawPile);
        }

        await OrbCmd.Channel<ScrapOrb>(new NullPlayerChoiceContext(), player);
        await OrbCmd.EvokeNext(new NullPlayerChoiceContext(), player);

        // Shouldn't do anything because the evoke value hasn't increased.
        Assert.That(GetPile(PileType.Hand).Cards.Count, Is.EqualTo(3));
    }

    [Test]
    public async Task TestEvokeWithNegativeFocus()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await PowerCmd.Apply<Focus>(player.Creature, -6, player.Creature, null);
        await OrbCmd.Channel<ScrapOrb>(new NullPlayerChoiceContext(), player);

        CardPile drawPile = GetPile(PileType.Draw);
        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(CreateCard<MockAttackCard>(), drawPile);
        }

        await OrbCmd.EvokeNext(new NullPlayerChoiceContext(), player);
        Assert.That(GetPile(PileType.Hand).Cards.Count, Is.EqualTo(0));
    }
}
