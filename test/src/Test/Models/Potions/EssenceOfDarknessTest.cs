using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Orbs;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Orbs;
using MegaCrit.Sts2.Core.Models.Potions;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Potions;

public class EssenceOfDarknessTest : ModelTest
{
    [Test]
    public async Task TestFillSlots()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await UsePotion<EssenceOfDarkness>();

        OrbQueue orbQueue = player.PlayerCombatState!.OrbQueue;
        Assert.That(orbQueue.Orbs.Count, Is.EqualTo(3));
        Assert.That(orbQueue.Orbs, Is.All.TypeOf<DarkOrb>());
    }


    [Test]
    public async Task TestFillAllSlots()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 9);

        await UsePotion<EssenceOfDarkness>();

        OrbQueue orbQueue = player.PlayerCombatState!.OrbQueue;
        Assert.That(orbQueue.Orbs.Count, Is.EqualTo(9));
        Assert.That(orbQueue.Orbs, Is.All.TypeOf<DarkOrb>());
    }
}
