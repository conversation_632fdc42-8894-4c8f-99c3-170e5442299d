using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Potions;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Potions;

public class FairyInABottleTest : ModelTest
{
    [Test]
    public async Task TestPreventsDeathFromDamage()
    {
        Player player = GetPlayer();
        Creature playerCreature = player.Creature;

        await PotionCmd.TryToProcure<FairyInABottle>(player);
        await CreatureCmd.Damage(playerCreature, playerCreature.CurrentHp + 1, DamageProps.nonCardUnpowered, null, null);

        Assert.That(playerCreature, Is.Not.Dead());
        Assert.That(playerCreature.CurrentHp, Is.EqualTo(30));
        Assert.That(player.Potions.Count(), Is.Zero);
    }

    [Test]
    public async Task TestPreventsDeathFromDoom()
    {
        Player player = GetPlayer();
        Creature playerCreature = player.Creature;

        await PotionCmd.TryToProcure<FairyInABottle>(player);
        await PowerCmd.Apply<Doom>(playerCreature, playerCreature.CurrentHp + 1, null, null);
        await PassToNextPlayerTurn();

        Assert.That(playerCreature, Is.Not.Dead());
        Assert.That(playerCreature.CurrentHp, Is.EqualTo(30));
        Assert.That(player.Potions.Count(), Is.Zero);
    }

    [Test]
    public async Task TestDoesNotPreventDeathTwice()
    {
        Player player = GetPlayer();
        Creature playerCreature = player.Creature;

        await PotionCmd.TryToProcure<FairyInABottle>(player);
        await CreatureCmd.Damage(playerCreature, playerCreature.CurrentHp + 1, DamageProps.nonCardUnpowered, null, null);
        await CreatureCmd.Damage(playerCreature, playerCreature.CurrentHp + 1, DamageProps.nonCardUnpowered, null, null);

        Assert.That(playerCreature, Is.Dead());
    }
}
