using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Potions;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Potions;

public class GamblersBrewTest : ModelTest
{
    [Test]
    public async Task TestWithEmptyHand()
    {
        await UsePotion<GamblersBrew>();

        Assert.That(GetPile(PileType.Hand).Cards, Is.Empty);
    }

    [Test]
    public async Task TestDiscardAndDraw()
    {
        CardPile drawPile = GetPile(PileType.Draw);
        CardPile hand = GetPile(PileType.Hand);

        for (int i = 0; i < 3; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendIronclad>(), drawPile);
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), hand);
        }

        PrepareToSelectAtIndices(0, 1);
        await UsePotion<GamblersBrew>();

        // After selecting 2 Strikes from hand, they should be in the discard pile, replaced with 2 Defends in hand,
        // and the draw pile should be left with 1 defend.
        Assert.That(GetPile(PileType.Discard), Has.Cards(typeof(StrikeIronclad), typeof(StrikeIronclad)));
        Assert.That(hand, Has.Cards(typeof(DefendIronclad), typeof(DefendIronclad), typeof(StrikeIronclad)));
        Assert.That(drawPile, Has.Cards(typeof(DefendIronclad)));
    }

    // TODO: Uncomment when we implement Sly.
    // [Test]
    // public async Task TestWithSly()
    // {
    //     CardPile hand = GetPile(CardPileTarget.Hand);
    //
    //     await CardPileCmd.Add(CreateCard<FlickFlack>(), hand);
    //     PrepareToSelectAtIndices(0);
    //     await UsePotion<GamblersBrew>();
    //
    //     Assert.That(GetEnemy(), Has.LostHp(6));
    // }

    [Test]
    public async Task TestSingleCard()
    {
        CardPile hand = GetPile(PileType.Hand);

        CardModel card = CreateCard<StrikeIronclad>();
        await CardPileCmd.Add(card, hand);

        PrepareToSelectAtIndices(0);
        await UsePotion<GamblersBrew>();

        // If the deck is small enough. you should be able to redraw the discarded card
        Assert.That(GetPile(PileType.Hand).Cards.Count, Is.EqualTo(1));
    }


    [Test]
    public async Task TestWithEmptyDrawPile()
    {
        CardPile hand = GetPile(PileType.Hand);

        await CardPileCmd.Add(CreateCard<StrikeIronclad>(), hand);

        PrepareToSelectAtIndices(0);
        await UsePotion<GamblersBrew>();

        // After selecting Strike from hand, it should be discarded and redrawn.
        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(StrikeIronclad)));
    }

    [Test]
    public async Task TestSkippable()
    {
        CardPile drawPile = GetPile(PileType.Draw);
        CardPile hand = GetPile(PileType.Hand);
        CardModel strike = CreateCard<DefendIronclad>();
        CardModel defend = CreateCard<StrikeIronclad>();

        await CardPileCmd.Add(strike, drawPile);
        await CardPileCmd.Add(defend, hand);

        PrepareToSkipSelection();
        await UsePotion<GamblersBrew>();

        // Since selection was skipped, cards should remain in their starting piles.
        Assert.That(strike, Is.InPile(PileType.Draw));
        Assert.That(defend, Is.InPile(PileType.Hand));
    }
}
