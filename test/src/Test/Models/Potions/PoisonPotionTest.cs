using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Potions;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Potions;

public class PoisonPotionTest : ModelTest
{
    [Test]
    public async Task TestAddsPoison()
    {
        Creature enemy = GetEnemy();

        await UsePotion<PoisonPotion>(enemy);

        Assert.That(enemy, Has.<PERSON><Poison>(6));
    }
}