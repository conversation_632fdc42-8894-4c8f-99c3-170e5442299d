using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Potions;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Potions;

public class PotionShapedRockTest : ModelTest
{
    [Test]
    public async Task TestDealsDamage()
    {
        Creature enemy = GetEnemy();

        await UsePotion<PotionShapedRock>(enemy);

        Assert.That(enemy, Has.LostHp(10));
    }
}
