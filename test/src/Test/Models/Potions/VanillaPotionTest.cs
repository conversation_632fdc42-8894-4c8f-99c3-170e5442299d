using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Potions;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Potions;

public class VanillaPotionTest : ModelTest
{
    #region Card generation

    [Test]
    public async Task TestAddsCardsToHand()
    {
        await UsePotion<CunningPotion>();

        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(Shiv), typeof(Shiv), typeof(Shiv)));
    }

    #endregion

    #region Power application

    [Test]
    public async Task TestAppliesPowerToSelf()
    {
        await UsePotion<LuckyTonic>();

        Assert.That(GetPlayer().Creature, Has.PowerAmount<BufferPower>(1));
    }

    #endregion
}
