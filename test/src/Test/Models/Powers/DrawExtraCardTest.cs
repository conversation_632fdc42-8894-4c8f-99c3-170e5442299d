using System;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Powers;

public class DrawExtraCardTest : ModelTest
{
    [Test]
    public async Task TestDrawsExtraCardNextTurn()
    {
        await FillDrawPile();
        await PowerCmd.Apply<DrawExtraCard>(GetPlayer().Creature, 2, null, null);
        await PassToNextPlayerTurn();

        Type[] names = Enumerable.Repeat(typeof(DefendIronclad), 6).ToArray();
        Assert.That(GetPile(PileType.Hand), Has.Cards(names));
    }

    [Test]
    public async Task TestLasts2TurnsWhenAmountIs2()
    {
        await FillDrawPile();
        await PowerCmd.Apply<DrawExtraCard>(GetPlayer().Creature, 2, null, null);

        for (int i = 0; i < 2; i++)
        {
            await PassToNextPlayerTurn();
        }

        Type[] names = Enumerable.Repeat(typeof(DefendIronclad), 6).ToArray();
        Assert.That(GetPile(PileType.Hand), Has.Cards(names));
    }

    [Test]
    public async Task TestEndsAfter2TurnsWhenAmountIs2()
    {
        await FillDrawPile();
        await PowerCmd.Apply<DrawExtraCard>(GetPlayer().Creature, 2, null, null);

        for (int i = 0; i < 3; i++)
        {
            await PassToNextPlayerTurn();
        }

        Type[] names = Enumerable.Repeat(typeof(DefendIronclad), 5).ToArray();
        Assert.That(GetPile(PileType.Hand), Has.Cards(names));
    }

    private async Task FillDrawPile()
    {
        CardPile drawPile = GetPile(PileType.Draw);

        for (int i = 0; i < 8; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendIronclad>(), drawPile);
        }
    }
}
