using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Powers;

public class PossessPowerTest : ModelTest
{
    [Test]
    public async Task TestReturnsPower()
    {
        // need this because if there are no enemies present, then combat is over and we dont return the strength
        await CreateEnemy<OneHpMonster>();

        await PowerCmd.Apply<PossessPower>(GetEnemy(), 1, GetEnemy(), null);
        await PowerCmd.Apply<Strength>(GetPlayer().Creature, -2, GetEnemy(), null);
        await PowerCmd.Apply<Strength>(GetPlayer().Creature, -2, GetEnemy(), null);

        await CreatureCmd.Kill(GetEnemy());

        Assert.That(GetPlayer().Creature, Has.<PERSON>mount<Strength>(0));
    }

    [Test]
    public async Task TestDoesntWorkForOtherPowers()
    {
        // need this because if there are no enemies present, then combat is over and we dont return the strength
        await CreateEnemy<OneHpMonster>();

        await PowerCmd.Apply<PossessPower>(GetEnemy(), 1, GetEnemy(), null);
        await PowerCmd.Apply<Dexterity>(GetPlayer().Creature, -2, GetEnemy(), null);
        await PowerCmd.Apply<Dexterity>(GetPlayer().Creature, -2, GetEnemy(), null);

        await CreatureCmd.Kill(GetEnemy());

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(0));
    }

    [Test]
    public async Task TestArtifactDoesntGiveYouExtra()
    {
        // need this because if there are no enemies present, then combat is over and we dont return the strength
        await CreateEnemy<OneHpMonster>();

        await PowerCmd.Apply<PossessPower>(GetEnemy(), 1, GetEnemy(), null);
        await PowerCmd.Apply<Artifact>(GetPlayer().Creature, 1, GetEnemy(), null);
        await PowerCmd.Apply<Strength>(GetPlayer().Creature, -2, GetEnemy(), null);

        await CreatureCmd.Kill(GetEnemy());

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(0));
    }

    [Test]
    public async Task TestSecondEnemyDyingDoesntReturnStrength()
    {
        Creature oneHpEnemy = await CreateEnemy<OneHpMonster>();

        await PowerCmd.Apply<PossessPower>(GetEnemy(), 1, GetEnemy(), null);
        await PowerCmd.Apply<Strength>(GetPlayer().Creature, -2, GetEnemy(), null);

        await CreatureCmd.Kill(oneHpEnemy);

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(-2));
    }
}
