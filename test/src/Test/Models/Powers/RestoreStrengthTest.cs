using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Powers;

public class RestoreStrengthTest : ModelTest
{
    [Test]
    public async Task TestEnemyLosesStrengthThisTurn()
    {
        await PowerCmd.LoseStrengthThisTurn(GetEnemy(), 6, null, null);

        Assert.That(GetEnemy(), Has.PowerAmount<Strength>(-6));
    }

    [Test]
    public async Task TestEnemyRegainsStrengthNextTurn()
    {
        await PowerCmd.LoseStrengthThisTurn(GetEnemy(), 6, null, null);
        await PassToNextPlayerTurn();

        Assert.That(GetEnemy(), Has.PowerAmount<Strength>(0));
    }
}
