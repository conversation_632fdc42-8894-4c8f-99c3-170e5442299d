using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Powers;

public class RitualTest : ModelTest
{
    [Test]
    public async Task TestAddsStrengthAtEndOfTurn()
    {
        Creature player = GetPlayer().Creature;

        await PowerCmd.Apply<Ritual>(player, 2, null, null);
        await PassToNextPlayerTurn();

        Assert.That(player, Has.PowerAmount<Strength>(2));
    }
}
