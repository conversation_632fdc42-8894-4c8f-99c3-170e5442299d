using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Powers;

public class ThornsTest : ModelTest
{
    [Test]
    public async Task TestDamagesPlayerWhenAttacking()
    {
        Creature enemy = GetEnemy();

        await PowerCmd.Apply<Thorns>(enemy, 1, null, null);
        await Play<StrikeIronclad>(enemy);
        Assert.That(GetPlayer().Creature, Has.LostHp(1));
    }

    [Test]
    public async Task TestTriggersMultipleTimesAForMultiAttackCard()
    {
        Creature enemy = GetEnemy();

        await PowerCmd.Apply<Thorns>(enemy, 1, null, null);
        await Play<Pummel>(enemy);

        Assert.That(GetPlayer().Creature, Has.LostHp(4));
    }

    [Test]
    public async Task TestTriggersMultipleTimesAForMultiAttackEnemy()
    {
        Creature enemy = await CreateEnemy<MultiAttackMoveMonster>();

        await PowerCmd.Apply<Thorns>(GetPlayer().Creature, 1, null, null);
        await PassToNextPlayerTurn();

        Assert.That(enemy, Has.LostHp(MultiAttackMoveMonster.repeat));
    }

    [Test]
    public async Task TestDoesNotTriggerAttackBasedPowers()
    {
        Creature enemy = GetEnemy();

        await PowerCmd.Apply<Thorns>(enemy, 1, null, null);
        await Play<FlameBarrier>();
        await Play<StrikeIronclad>(enemy);

        // Only 6 damage from strike, none from flame barrier
        Assert.That(enemy, Has.LostHp(6));
    }

    [Test]
    public async Task TestTriggersWhenAttackKillsCreature()
    {
        Creature enemy = await CreateEnemy<OneHpMonster>();

        await PowerCmd.Apply<Thorns>(enemy, 1, null, null);
        await Play<StrikeIronclad>(enemy);

        Assert.That(enemy, Is.Dead());
        Assert.That(GetPlayer().Creature, Has.LostHp(1));
    }

    [Test]
    public async Task TestMultiAttacksStopEarlyWhenAttackerDies()
    {
        Creature player = GetPlayer().Creature;
        Creature enemy = await CreateEnemy<MultiAttackMoveMonster>();
        await CreatureCmd.SetMaxHp(enemy, 1);

        await PowerCmd.Apply<Thorns>(player, 1, null, null);
        await PassToNextPlayerTurn();

        // The player would've lost more HP, but Thorns kills the enemy on the first hit of their multi-hit attack, so
        // the remaining hits deal no damage.
        Assert.That(GetPlayer().Creature, Has.LostHp(1));
        Assert.That(enemy, Is.Dead());
    }
}
