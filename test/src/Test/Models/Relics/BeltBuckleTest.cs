using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Potions;
using MegaCrit.Sts2.Core.Models.Potions;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class BeltBuckleTest : ModelTest
{
    [Test]
    public async Task TestGivesDexterityWhenObtainedInCombatWithNoPotions()
    {
        await RelicCmd.Obtain<BeltBuckle>(GetPlayer());

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Dexterity>(2));
    }

    [Test]
    public async Task TestGivesDexterityAtCombatStartIfPlayerHasNoPotions()
    {
        await RelicCmd.Obtain<BeltBuckle>(GetPlayer());
        await RestartCombat();

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Dexterity>(2));
    }

    [Test]
    public async Task TestDoesNotGiveDexterityWhenObtainedInCombatWithPotions()
    {
        await PotionCmd.TryToProcure<SwiftPotion>(GetPlayer());
        await RelicCmd.Obtain<BeltBuckle>(GetPlayer());

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Dexterity>(0));
    }

    [Test]
    public async Task TestDoesNotGiveDexterityAtCombatStartIfPlayerHasPotions()
    {
        await PotionCmd.TryToProcure<SwiftPotion>(GetPlayer());
        await RelicCmd.Obtain<BeltBuckle>(GetPlayer());
        await RestartCombat();

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Dexterity>(0));
    }

    [Test]
    public async Task TestDexterityAddedIfPotionsAreDiscarded()
    {
        PotionProcureResult result = await PotionCmd.TryToProcure<SwiftPotion>(GetPlayer());
        await RelicCmd.Obtain<BeltBuckle>(GetPlayer());
        await PotionCmd.Discard(result.potion);

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Dexterity>(2));
    }

    [Test]
    public async Task TestDexterityAddedIfPotionsAreUsed()
    {
        PotionProcureResult result = await PotionCmd.TryToProcure<SwiftPotion>(GetPlayer());
        await RelicCmd.Obtain<BeltBuckle>(GetPlayer());
        await UsePotion(result.potion);

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Dexterity>(2));
    }

    [Test]
    public async Task TestDexterityRemovedIfPotionIsObtained()
    {
        await RelicCmd.Obtain<BeltBuckle>(GetPlayer());
        await PotionCmd.TryToProcure<SwiftPotion>(GetPlayer());

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Dexterity>(0));
    }
}
