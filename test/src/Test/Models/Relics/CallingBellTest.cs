using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class CallingBellTest : ModelTest
{
    [Test]
    public async Task TestAddsRelicRewardsAndCurse()
    {
        Player player = GetPlayer();
        List<RelicModel> nonRewardRelics = player.Relics.ToList();

        CallingBell callingBell = await RelicCmd.Obtain<CallingBell>(player);
        nonRewardRelics.Add(callingBell);
        IEnumerable<RelicRarity> rarities = player.Relics.Except(nonRewardRelics).Select(r => r.Rarity);

        Assert.That(rarities, Is.EquivalentTo(new[] { RelicRarity.Common, RelicRarity.Uncommon, RelicRarity.Rare }));
        Assert.That(GetPile(PileType.Deck), Has.Cards(typeof(CurseOfTheBell)));
    }

    [Test]
    public async Task TestWithUndulatingMass()
    {
        Player player = GetPlayer();
        List<RelicModel> nonRewardRelics = player.Relics.ToList();

        UndulatingMass undulatingMass = await RelicCmd.Obtain<UndulatingMass>(player);
        nonRewardRelics.Add(undulatingMass);
        CallingBell callingBell = await RelicCmd.Obtain<CallingBell>(player);
        nonRewardRelics.Add(callingBell);

        IEnumerable<RelicRarity> rarities = player.Relics.Except(nonRewardRelics).Select(r => r.Rarity);

        // Undulating Mass should NOT block the new relics from being obtained.
        Assert.That(rarities, Is.EquivalentTo(new[] { RelicRarity.Common, RelicRarity.Uncommon, RelicRarity.Rare }));

        // Undulating Mass should block the curse from being picked up.
        Assert.That(GetPile(PileType.Deck).Cards, Is.Empty);
    }
}
