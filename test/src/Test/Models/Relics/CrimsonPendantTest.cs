using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class CrimsonPendantTest : ModelTest
{
    [Test]
    public async Task TestGrantsIntangible()
    {
        Player player = GetPlayer();
        int startingMaxHp = player.Creature.MaxHp;

        await RelicCmd.Obtain<CrimsonPendant>(player);
        await RestartCombat();

        Assert.That(player.Creature, Has.PowerAmount<Intangible>(1));
    }
}
