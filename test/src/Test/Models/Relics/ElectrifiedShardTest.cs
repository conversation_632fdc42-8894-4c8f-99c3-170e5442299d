using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Enchantments;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class ElectrifiedShardTest : ModelTest
{
    [Test]
    public async Task TestEnchantsCard()
    {
        CardModel card = MockSkill(CardScope.Climb);
        await CardPileCmd.Add(card, PileType.Deck);
        PrepareToSelectAtIndices(0);
        await RelicCmd.Obtain<ElectrifiedShard>(GetPlayer());

        Assert.That(card.Enchantment, Is.TypeOf<Imbued>());
    }
}
