using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class EmberTeaTest : ModelTest
{
    [Test]
    public async Task TestAddsStrengthOnCombat1()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<EmberTea>(player);
        await RestartCombat();

        Assert.That(player.Creature, Has.PowerAmount<Strength>(2));
    }

    [Test]
    public async Task TestAddsStrengthOnCombat3()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<EmberTea>(player);

        for (int i = 0; i < 3; i++)
        {
            await RestartCombat();
        }

        Assert.That(player.Creature, Has.<PERSON><Strength>(2));
    }

    [Test]
    public async Task TestDoesNothingOnCombat5()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<EmberTea>(GetPlayer());

        for (int i = 0; i < 5; i++)
        {
            await RestartCombat();
        }

        Assert.That(player.Creature, Has.PowerAmount<Strength>(0));
    }
}
