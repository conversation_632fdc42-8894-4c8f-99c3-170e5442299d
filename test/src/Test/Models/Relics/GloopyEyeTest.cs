using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class GloopyEyeTest : ModelTest
{
    [Test]
    public async Task TestDoesNotTriggerWhenCardsPlayed()
    {
        await RelicCmd.Obtain<GloopyEye>(GetPlayer());

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(MockAttack(CardScope.Climb), GetPile(PileType.Deck));
        }

        await RestartCombat();

        await Play<Inflame>(); // Play a power that doesn't go into any pile once played.
        await PassToNextPlayerTurn();

        // Gloopy Eye shouldn't trigger so no cards exhausted.
        Assert.That(GetPile(PileType.Exhaust).Cards.Count, Is.EqualTo(0));
    }

    [Test]
    public async Task TestTriggerWhenNoCardsPlayed()
    {
        await RelicCmd.Obtain<GloopyEye>(GetPlayer());

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(MockAttack(CardScope.Climb), GetPile(PileType.Deck));
        }

        await RestartCombat();
        await PassToNextPlayerTurn();

        // Gloopy Eye should trigger so your 5-card hand is exhausted.
        Assert.That(GetPile(PileType.Exhaust).Cards.Count, Is.EqualTo(5));
    }

    [Test]
    public async Task TestOnlyTriggerOncePerCombat()
    {
        await RelicCmd.Obtain<GloopyEye>(GetPlayer());

        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(MockAttack(CardScope.Climb), GetPile(PileType.Deck));
        }

        await RestartCombat();
        await PassToNextPlayerTurn();
        await PassToNextPlayerTurn();

        // Gloopy Eye should trigger once, so your 5-card hand from 2 turns ago is exhausted, but your hand from last
        // turn isn't.
        Assert.That(GetPile(PileType.Exhaust).Cards.Count, Is.EqualTo(5));
    }

    [Test]
    public async Task TestSkipPoison()
    {
        await RelicCmd.Obtain<GloopyEye>(GetPlayer());

        await RestartCombat();

        Creature enemy = GetEnemy();
        await PowerCmd.Apply<Poison>(enemy, 2, null, null);
        await PassToNextPlayerTurn();

        // Since we skip enemy turn, we don't trigger Poison which happens at the start of their turn.
        Assert.That(enemy, Has.LostHp(0));
    }

    [Test]
    public async Task TestTriggerCombust()
    {
        await RelicCmd.Obtain<GloopyEye>(GetPlayer());

        await RestartCombat();
        await PowerCmd.Apply<CombustPower>(GetPlayer().Creature, 5, null, null);

        await PassToNextPlayerTurn();

        // Since we trigger end of turn, we do trigger Combust which happens at end of your turn.
        Assert.That(GetEnemy(), Has.LostHp(5));
    }

    [Test]
    public async Task TestSkipEndOfTurnCards()
    {
        await RelicCmd.Obtain<GloopyEye>(GetPlayer());

        await CardPileCmd.Add(CreateCard<Doubt>(), PileType.Hand);
        await CardPileCmd.Add(CreateCard<Toxic>(), PileType.Hand);

        await PassToNextPlayerTurn();

        // Gloopy eye should exhaust cards before they trigger at end of turn
        Assert.That(GetPlayer().Creature, Has.LostHp(0));
        Assert.That(GetPlayer().Creature, Has.PowerAmount<Weak>(0));
    }
}
