using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class HungeringPortraitTest : ModelTest
{
    [Test]
    public async Task TestAddsStrengthAfter1Turn()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<HungeringPortrait>(player);
        await PassToNextPlayerTurn();
        Assert.That(player.Creature, Has.PowerAmount<Strength>(1));
    }

    [Test]
    public async Task TestExhaust()
    {
        Player player = GetPlayer();

        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), GetPile(PileType.Draw));
        }

        await RelicCmd.Obtain<HungeringPortrait>(player);
        await PassToNextPlayerTurn();
        Assert.That(GetPile(PileType.Exhaust), Has.Cards(typeof(DefendSilent)));
    }

    [Test]
    public async Task TestNoCardsInDraw()
    {
        Player player = GetPlayer();

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), GetPile(PileType.Discard));
        }

        await RelicCmd.Obtain<HungeringPortrait>(player);

        // will draw the last 5 cards in the pile, so we'd have to shuffle
        await PassToNextPlayerTurn();

        // hungering portrait doesnt shuffle cards back in
        Assert.That(GetPile(PileType.Exhaust).Cards, Is.Empty);
    }

    [Test]
    public async Task TestAddsStrengthAfter2Turns()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<HungeringPortrait>(player);
        await PassToNextPlayerTurn();
        await PassToNextPlayerTurn();
        Assert.That(player.Creature, Has.PowerAmount<Strength>(2));
    }
}
