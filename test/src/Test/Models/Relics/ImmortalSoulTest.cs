using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class ImmortalSoulTest : ModelTest
{
    [Test]
    public async Task TestPreventsDeathFromDoom()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<ImmortalSoul>(player);
        await PowerCmd.Apply<Doom>(GetPlayer().Creature, GetPlayer().Creature.CurrentHp, null, null);

        await PassToNextPlayerTurn();
        Assert.That(GetPlayer().Creature, Is.Not.Dead());
    }
}
