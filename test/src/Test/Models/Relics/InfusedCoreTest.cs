using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Orbs;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Orbs;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class InfusedCoreTest : ModelTest
{
    [Test]
    public async Task TestAddLightningOrbAtStartOfCombat()
    {
        Player player = GetPlayer();
        player.BaseOrbSlotCount = 3;

        await RelicCmd.Obtain<InfusedCore>(player);
        await RestartCombat();

        OrbQueue orbQueue = player.PlayerCombatState!.OrbQueue;
        Assert.That(orbQueue.Orbs.Count, Is.EqualTo(3));
        Assert.That(orbQueue.Orbs, Is.All.TypeOf<LightningOrb>());
    }
}
