using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Rooms;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class JuzuBraceletTest : ModelTest
{
    [Test]
    public async Task TestPreventsRegularCombatsInUnknownMapPoints()
    {
        await RelicCmd.Obtain<JuzuBracelet>(GetPlayer());

        HashSet<RoomType> seenRoomTypes = [];
        IClimbState climbState = GetPlayer().ClimbState;

        for (int i = 0; i < 100; i++)
        {
            seenRoomTypes.Add(climbState.Odds.UnknownMapPoint.Roll([], climbState));
        }

        Assert.That(seenRoomTypes, Does.Not.Contain(RoomType.Monster));
    }

    [Test]
    public async Task TestAllowsRegularCombatsInUnknownMapPointsAfterRemoval()
    {
        JuzuBracelet relic = await RelicCmd.Obtain<JuzuBracelet>(GetPlayer());
        await RelicCmd.Remove(relic);

        HashSet<RoomType> seenRoomTypes = [];
        IClimbState climbState = GetPlayer().ClimbState;

        // Note: if this fails intermittently, it's probably safe to increase to 1000.
        for (int i = 0; i < 100; i++)
        {
            seenRoomTypes.Add(climbState.Odds.UnknownMapPoint.Roll([], climbState));
        }

        Assert.That(seenRoomTypes, Does.Contain(RoomType.Monster));
    }
}
