using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.CardPools;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Rewards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class LastingCandyTest : ModelTest
{
    [Test]
    public async Task TestDoesNotModifyCardRewardAfterFirstCombat()
    {
        await RelicCmd.Obtain<LastingCandy>(GetPlayer());
        await WinCombat();

        // We use the Ironclad card pool so we aren't stuck with the limited cards in the test pool.
        CardReward reward = new(ModelDb.CardPool<IroncladCardPool>(), CardCreationSource.RegularEncounter, 3, GetPlayer());
        await reward.Populate();

        Assert.That(reward.Cards.Count(), Is.EqualTo(3));
    }

    [Test]
    public async Task TestModifiesCardRewardAfterSecondCombat()
    {
        await RelicCmd.Obtain<LastingCandy>(GetPlayer());

        for (int i = 0; i < 2; i++)
        {
            await RestartCombat();
            await WinCombat();
        }

        // We use the Ironclad card pool so we aren't stuck with the limited cards in the test pool.
        CardReward reward = new(ModelDb.CardPool<IroncladCardPool>(), CardCreationSource.RegularEncounter, 3, GetPlayer());
        await reward.Populate();

        // Second ("every other") reward gets an extra power offered.
        Assert.That(reward.Cards.Count(), Is.EqualTo(4));
        Assert.That(reward.Cards.Last().Type, Is.EqualTo(CardType.Power));
    }

    [Test]
    public async Task TestDoesNotModifyCardRewardAfterThirdCombat()
    {
        await RelicCmd.Obtain<LastingCandy>(GetPlayer());

        for (int i = 0; i < 3; i++)
        {
            await RestartCombat();
            await WinCombat();
        }

        // We use the Ironclad card pool so we aren't stuck with the limited cards in the test pool.
        CardReward reward = new(ModelDb.CardPool<IroncladCardPool>(), CardCreationSource.RegularEncounter, 3, GetPlayer());
        await reward.Populate();

        Assert.That(reward.Cards.Count(), Is.EqualTo(3));
    }
}
