using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class LuckyFyshTest : ModelTest
{
    [Test]
    public async Task TestAddsGoldOnCardObtain()
    {
        Player player = GetPlayer();
        int originalGold = player.Gold;

        await RelicCmd.Obtain<LuckyFysh>(player);

        await CardPileCmd.Add(MockSkill(CardScope.Climb), PileType.Deck);

        Assert.That(player.Gold, Is.EqualTo(originalGold + 15));
    }

    [Test]
    public async Task TestDoesNotAddCardInAnyOtherPile()
    {
        Player player = GetPlayer();
        int originalGold = player.Gold;

        await RelicCmd.Obtain<LuckyFysh>(player);

        await CardPileCmd.Add(MockSkill(), PileType.Hand);

        Assert.That(player.Gold, <PERSON><PERSON>o(originalGold));
    }
}
