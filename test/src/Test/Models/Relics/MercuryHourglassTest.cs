using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class MercuryHourglassTest : ModelTest
{
    [Test]
    public async Task TestDamage()
    {
        await RelicCmd.Obtain<MercuryHourglass>(GetPlayer());
        await PassToNextPlayerTurn();

        Assert.That(GetEnemy(), Has.LostHp(3));
    }
}