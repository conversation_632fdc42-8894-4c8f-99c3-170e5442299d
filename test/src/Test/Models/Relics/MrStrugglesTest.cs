using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class MrStrugglesTest : ModelTest
{
    [Test]
    public async Task TestDealsTurnDamageToAllEnemies()
    {
        Creature enemy1 = GetEnemy();
        Creature enemy2 = await CreateEnemy<BigDummy>();

        await RelicCmd.Obtain<MrStruggles>(GetPlayer());

        await PassToNextPlayerTurn();
        await PassToNextPlayerTurn();
        await PassToNextPlayerTurn();
        await PassToNextPlayerTurn();
        await PassToNextPlayerTurn();

        // 2 + 3 + 4 + 5 + 6
        Assert.That(enemy1, <PERSON>.LostHp(20));
        Assert.That(enemy2, <PERSON><PERSON>Hp(20));
    }
}
