using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class MyMantleTest : ModelTest
{
    [Test]
    public async Task TestDoesNothingAfterSpending9Stars()
    {
        await RelicCmd.Obtain<MyMantle>(GetPlayer());
        await PlayerCmd.GainStars(100, GetPlayer());
        await PlayerCmd.LoseStars(9, GetPlayer());

        Assert.That(GetPlayer().Creature, Has.Block(0));
    }

    [Test]
    public async Task TestAfterSpending10Stars()
    {
        await RelicCmd.Obtain<MyMantle>(GetPlayer());
        await PlayerCmd.GainStars(100, GetPlayer());
        await PlayerCmd.LoseStars(10, GetPlayer());

        Assert.That(GetPlayer().Creature, Has.Block(5));
    }

    [Test]
    public async Task TestWhenSpending20Stars()
    {
        await RelicCmd.Obtain<MyMantle>(GetPlayer());
        await PlayerCmd.GainStars(100, GetPlayer());
        await PlayerCmd.LoseStars(20, GetPlayer());

        Assert.That(GetPlayer().Creature, Has.Block(10));
    }

    [Test]
    public async Task TestWhenYouSpend20StarsOffset()
    {
        await RelicCmd.Obtain<MyMantle>(GetPlayer());
        await PlayerCmd.GainStars(100, GetPlayer());
        await PlayerCmd.LoseStars(15, GetPlayer());
        await PlayerCmd.LoseStars(5, GetPlayer());

        Assert.That(GetPlayer().Creature, Has.Block(10));
    }
}
