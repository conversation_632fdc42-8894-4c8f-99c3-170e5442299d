using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class NyeEssenceTest : ModelTest
{
    [Test]
    public async Task TestDoesNothingFirst2Turns()
    {
        await RelicCmd.Obtain<NyeEssence>(GetPlayer());
        await PassToNextPlayerTurn();
        Assert.That(GetPlayer(), Has.ExtraEnergy(0));
    }

    [Test]
    public async Task TestGainEnergyOnTurn3()
    {
        await RelicCmd.Obtain<NyeEssence>(GetPlayer());

        for (int i = 0; i < 2; i++)
        {
            await PassToNextPlayerTurn();
        }

        Assert.That(GetPlayer(), Has.ExtraEnergy(3));
    }

    [Test]
    public async Task TestDoesNothingOnTurnsWithMultipleOf3Indices()
    {
        await RelicCmd.Obtain<NyeEssence>(GetPlayer());

        for (int i = 0; i < 5; i++)
        {
            await PassToNextPlayerTurn();
        }

        Assert.That(GetPlayer(), Has.ExtraEnergy(0));
    }
}
