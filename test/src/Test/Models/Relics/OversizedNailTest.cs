using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Merchant;
using MegaCrit.Sts2.Core.Factories;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.CardPools;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Enchantments;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class OversizedNailTest : ModelTest
{
    [Test]
    public async Task TestDoesNothingToNonAttacks()
    {
        await RelicCmd.Obtain<OversizedNail>(GetPlayer());

        CardModel card = MockSkill(CardScope.Climb);
        await CardPileCmd.Add(card, PileType.Deck);

        Assert.That(card, Is.Not.Upgraded());
    }

    [Test]
    public async Task TestEnchantsAttacks()
    {
        OversizedNail nail = await RelicCmd.Obtain<OversizedNail>(GetPlayer());

        CardModel card = MockAttack(CardScope.Climb);
        CardPileAddResult result = await CardPileCmd.Add(card, PileType.Deck);

        Assert.That(result.cardAdded.Enchantment, Is.TypeOf<Sharp>());
        Assert.That(result.modifyingModels, Is.Not.Null);
        Assert.That(result.modifyingModels, Does.Contain(nail));
    }

    [Test]
    public async Task TestUpgradesRewardCardsBeforeSelectingRewards()
    {
        OversizedNail nail = await RelicCmd.Obtain<OversizedNail>(GetPlayer());

        IEnumerable<CardModel> cardPool = ModelDb.CardPool<IroncladCardPool>().Cards.Where(c => ModelDb.Enchantment<Sharp>().CanEnchant(c));
        List<CardCreationResult> results = CardFactory.CreateForReward(GetPlayer(), cardPool, 10).ToList();

        Assert.That(results, Is.Not.Empty);

        foreach (CardCreationResult result in results)
        {
            Assert.That(result.Card.Enchantment, Is.TypeOf<Sharp>());
            Assert.That(result.ModifyingRelics, Does.Contain(nail));
        }
    }

    [Test]
    public async Task TestUpgradesMerchantCardsBeforeSelectingRewards()
    {
        // Use Ironclad so the shop can be populated from a real card pool.
        await RestartClimbAs<Ironclad>();

        OversizedNail nail = await RelicCmd.Obtain<OversizedNail>(GetPlayer());

        IEnumerable<CardCreationResult> results = CardFactory.CreateForMerchant(
            GetPlayer(),
            ModelDb.CardPool<IroncladCardPool>().Cards,
            [CardType.Attack, CardType.Power, CardType.Power]
        );

        List<MerchantCardEntry> cardEntries = [];

        foreach (CardCreationResult result in results)
        {
            cardEntries.Add(new MerchantCardEntry(result, GetPlayer()));
        }

        Assert.That(cardEntries.Count, Is.EqualTo(3));

        foreach (MerchantCardEntry entry in cardEntries)
        {
            CardModel card = entry.CreationResult!.Card;
            if (card.Type == CardType.Attack)
            {
                Assert.That(card.Enchantment, Is.TypeOf<Sharp>());
                Assert.That(entry.CreationResult!.ModifyingRelics, Does.Contain(nail));
            }
            else
            {
                Assert.That(card.Enchantment, Is.Null);
            }
        }
    }

    [Test]
    public async Task TestUpgradesCardFromByrdpip()
    {
        await RelicCmd.Obtain<OversizedNail>(GetPlayer());
        await CardPileCmd.Add(CreateCard<ByrdonisEgg>(CardScope.Climb), PileType.Deck);
        await RelicCmd.Obtain<Byrdpip>(GetPlayer());

        Assert.That(GetPile(PileType.Deck).Cards.First().Enchantment, Is.TypeOf<Sharp>());
    }
}
