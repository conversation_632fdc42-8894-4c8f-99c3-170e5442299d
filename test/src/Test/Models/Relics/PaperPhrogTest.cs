using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class PaperPhrogTest : ModelTest
{
    [Test]
    public async Task TestOnCreatureWithoutVulnerable()
    {
        Creature enemy = GetEnemy();

        await RelicCmd.Obtain<PaperPhrog>(GetPlayer());
        await Play<MockAttackCard>(enemy);

        Assert.That(enemy, Has.LostHp(6));
    }

    [Test]
    public async Task TestOnCreatureWithVulnerable()
    {
        Creature enemy = GetEnemy();

        await PowerCmd.Apply<Vulnerable>(enemy, 1, null, null);
        await RelicCmd.Obtain<PaperPhrog>(GetPlayer());
        await Play<MockAttackCard>(enemy);

        // 6 * 1.75 = 10.5, rounds down to 10.
        Assert.That(enemy, <PERSON>.LostHp(10));
    }
}
