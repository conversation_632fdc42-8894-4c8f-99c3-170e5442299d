using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class PeinHammerTest : ModelTest
{
    [Test]
    public async Task TestFirstHandDrawnIsUpgraded()
    {
        Player player = GetPlayer();
        await RelicCmd.Obtain<PeinHammer>(player);

        for (int i = 0; i < 11; i++)
        {
            await CardPileCmd.Add(MockSkill(CardScope.Climb), PileType.Deck);
        }

        await RestartCombat();

        Assert.That(PileType.Hand.GetPile(player).Cards, Is.All.Upgraded());
    }

    [Test]
    public async Task TestSecondHandDrawnIsNotUpgraded()
    {
        Player player = GetPlayer();
        await RelicCmd.Obtain<PeinHammer>(player);

        for (int i = 0; i < 11; i++)
        {
            await CardPileCmd.Add(MockSkill(CardScope.Climb), PileType.Deck);
        }

        await RestartCombat();
        await PassToNextPlayerTurn();

        Assert.That(PileType.Hand.GetPile(player).Cards, Has.None.Upgraded());
    }

    [Test]
    public async Task TestSkipsUpgradedCards()
    {
        Player player = GetPlayer();
        await RelicCmd.Obtain<PeinHammer>(player);

        for (int i = 0; i < 11; i++)
        {
            CardPileAddResult result = await CardPileCmd.Add(MockSkill(CardScope.Climb), PileType.Deck);
            CardCmd.Upgrade(result.cardAdded);
        }

        await RestartCombat();

        Assert.That(PileType.Hand.GetPile(player).Cards, Is.All.Upgraded());
    }

    [Test]
    public async Task TestSkipsUnUpgradableCards()
    {
        Player player = GetPlayer();
        await RelicCmd.Obtain<PeinHammer>(player);

        for (int i = 0; i < 11; i++)
        {
            await CardPileCmd.Add(MockSkill(CardScope.Climb).MockUnUpgradable(), PileType.Deck);
        }

        await RestartCombat();

        Assert.That(PileType.Hand.GetPile(player).Cards, Has.None.Upgraded());
    }
}
