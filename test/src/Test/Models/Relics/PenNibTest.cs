using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class PenNibTest : ModelTest
{
    [Test]
    public async Task TestDoesNothingOn9thAttack()
    {
        Creature enemy = GetEnemy();

        await RelicCmd.Obtain<PenNib>(GetPlayer());

        for (int i = 0; i < 9; i++)
        {
            await Play<StrikeIronclad>(enemy);
        }

        // 9 * 6 = 54
        Assert.That(enemy, Has.LostHp(54));
    }

    [Test]
    public async Task TestDoublesOstyDamage()
    {
        Creature enemy = GetEnemy();
        await OstyCmd.Summon(new ThrowingPlayerChoiceContext(), GetPlayer(), 1, null);
        await RelicCmd.Obtain<PenNib>(GetPlayer());

        for (int i = 0; i < 9; i++)
        {
            await Play<StrikeIronclad>(enemy);
        }

        await Play<Bodyguard>(enemy);

        // (9 * 6) + (5 * 2) = 64
        Assert.That(enemy, Has.LostHp(64));
    }

    [Test]
    public async Task TestDoesNothingOn10thCardPlayedWhenOther9WereNotAllAttacks()
    {
        Creature enemy = GetEnemy();

        await RelicCmd.Obtain<PenNib>(GetPlayer());

        for (int i = 0; i < 9; i++)
        {
            await Play<DefendIronclad>();
        }

        await Play<StrikeIronclad>(enemy);

        Assert.That(enemy, Has.LostHp(6));
    }

    [Test]
    public async Task TestDoublesDamageOn10thAttack()
    {
        Creature enemy = GetEnemy();

        await RelicCmd.Obtain<PenNib>(GetPlayer());

        for (int i = 0; i < 10; i++)
        {
            await Play<StrikeIronclad>(enemy);
        }

        // 9 * 6 + 2 * 6 = 66
        Assert.That(enemy, Has.LostHp(66));
    }

    [Test]
    public async Task TestIncrementsWhenAttackIsPlayed()
    {
        Creature enemy = GetEnemy();

        PenNib penNib = await RelicCmd.Obtain<PenNib>(GetPlayer());
        await PowerCmd.Apply<Strength>(GetPlayer().Creature, enemy.CurrentHp, null, null);
        await Play<StrikeIronclad>(enemy);
        Assert.That(penNib.DisplayAmount, Is.EqualTo(1));
    }
}
