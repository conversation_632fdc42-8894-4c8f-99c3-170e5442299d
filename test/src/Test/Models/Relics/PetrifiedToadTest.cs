using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Potions;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class PetrifiedToadTest : ModelTest
{
    [Test]
    public async Task TestProcuresPotion()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<PetrifiedToad>(player);
        await RestartCombat();

        Assert.That(player.Potions, Has.Exactly(1).TypeOf<PotionShapedRock>());
    }
}
