using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class PrismaticShardTest : ModelTest
{
    [Test]
    public async Task TestModifiesCardRewardCardPool()
    {
        await RelicCmd.Obtain<PrismaticShard>(GetPlayer());
        IEnumerable<CardModel> cards = [ModelDb.Card<Inflame>(), ModelDb.Card<Stomp>()];
        cards = Hook.ModifyCardRewardCardPool(GetPlayer().ClimbState, GetPlayer(), cards, CardCreationSource.RegularEncounter);

        foreach (CardPoolModel characterCardPool in ModelDb.UnlockedCharacterCardPools)
        {
            Assert.That(cards, Has.Some.Matches<CardModel>(c => c.Pool.GetType() == characterCardPool.GetType()));
        }
    }

    [Test]
    public async Task TestDoesNotModifyCustomRewards()
    {
        await RelicCmd.Obtain<PrismaticShard>(GetPlayer());

        IEnumerable<CardModel> cards = [ModelDb.Card<Inflame>(), ModelDb.Card<Stomp>()];
        cards = Hook.ModifyCardRewardCardPool(GetPlayer().ClimbState, GetPlayer(), cards, CardCreationSource.Custom);

        Assert.That(cards, Is.EquivalentTo((IEnumerable<CardModel>)[ModelDb.Card<Inflame>(), ModelDb.Card<Stomp>()]));
    }
}
