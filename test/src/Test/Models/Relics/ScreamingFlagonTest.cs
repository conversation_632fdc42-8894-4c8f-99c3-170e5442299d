using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class ScreamingFlagonTest : ModelTest
{
    [Test]
    public async Task TestAddsStrengthWhenHandEmptyAtEndOfTurn()
    {
        await RelicCmd.Obtain<ScreamingFlagon>(GetPlayer());
        await PassToNextPlayerTurn();

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(2));
    }

    [Test]
    public async Task TestDoesNotAddStrengthWhenHandNotEmptyAtEndOfTurn()
    {
        await RelicCmd.Obtain<ScreamingFlagon>(GetPlayer());
        await CardPileCmd.Add(CreateCard<StrikeIronclad>(), PileType.Hand);
        await PassToNextPlayerTurn();

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(0));
    }

    [Test]
    public async Task TestDoesNotAddStrengthWhenHandIsOnlyEtherealCards()
    {
        await RelicCmd.Obtain<ScreamingFlagon>(GetPlayer());
        await CardPileCmd.Add(CreateCard<Defile>(), PileType.Hand);
        await PassToNextPlayerTurn();

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(0));
    }

    [Test]
    public async Task TestDoesNotAddStrengthWhenHandIsOnlyDiscardAtEndOfTurnCards()
    {
        await RelicCmd.Obtain<ScreamingFlagon>(GetPlayer());
        await CardPileCmd.Add(CreateCard<Burn>(), PileType.Hand);
        await PassToNextPlayerTurn();

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(0));
    }
}
