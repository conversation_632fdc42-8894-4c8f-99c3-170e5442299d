using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Rewards;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.TestSupport;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class SeekingTentacleTest : ModelTest
{
    [Test]
    public async Task TestCanReRoll()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<SeekingTentacle>(player);
        IEnumerable<CardReward> rewards = (await RewardsCmd.GenerateForRoom(
            player,
            new CombatRoom(GetEncounter(RoomType.Monster), player.ClimbState)
        )).OfType<CardReward>();

        Assert.That(rewards.Count(), <PERSON><PERSON>han(0));
        Assert.That(rewards.All(r => r.<PERSON>), Is.True);
    }

    [Test]
    public async Task TestReRolling()
    {
        Player player = GetPlayer();

        CardModel? rewardBeforeReRolling = null;
        CardModel? selectedReward = null;
        await RelicCmd.Obtain<SeekingTentacle>(player);

        TestCardSelector.Instance!.PrepareToSelectCardReward((reward, alternatives) =>
        {
            rewardBeforeReRolling = reward.Cards.First();
            alternatives[1].OnSelect();
            selectedReward = reward.Cards.First();
            return selectedReward;
        });

        IEnumerable<Reward> rewards = await RewardsCmd.GenerateForRoom(
            player,
            new CombatRoom(GetEncounter(RoomType.Monster), player.ClimbState)
        );
        CardReward cardReward = rewards.OfType<CardReward>().First();
        await cardReward.OnSelectWrapper();

        CardPile deck = PileType.Deck.GetPile(player);
        Assert.That(deck.Cards.Count, Is.EqualTo(1));
        Assert.That(deck, Has.Cards(selectedReward!.GetType()));
        Assert.That(rewardBeforeReRolling, Is.Not.EqualTo(selectedReward));
    }
}
