using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.RestSite;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.TestSupport;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class ShovelTest : ModelTest
{
    [Test]
    public async Task TestAddsExtraRelicOption()
    {
        Player player = GetPlayer();
        int startingMaxHp = player.Creature.MaxHp;

        await RelicCmd.Obtain<Shovel>(player);
        IReadOnlyList<RestSiteOption> options = RestSiteOption.Generate(player);
        RestSiteOption digOption = options.First(o => o is DigRestSiteOption);

        TestRngInjector.SetRelicOverride<Strawberry>();
        bool result = await digOption.OnSelect();

        Assert.That(result, Is.True);

        // Digging should result in obtaining a Strawberry, which should gain the player 7 HP.
        Assert.That(player.Creature.MaxHp, Is.EqualTo(startingMaxHp + 7));
    }
}
