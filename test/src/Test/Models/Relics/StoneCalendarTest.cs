using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class StoneCalendarTest : ModelTest
{
    [Test]
    public async Task TestDoesNothingForFirst6Turns()
    {
        await RelicCmd.Obtain<StoneCalendar>(GetPlayer());

        for (int i = 0; i < 6; i++)
        {
            await PassToNextPlayerTurn();
        }

        Assert.That(GetEnemy(), Has.LostHp(0));
    }

    [Test]
    public async Task TestDealsDamageOnTurn7()
    {
        await RelicCmd.Obtain<StoneCalendar>(GetPlayer());

        for (int i = 0; i < 7; i++)
        {
            await PassToNextPlayerTurn();
        }

        Assert.That(GetEnemy(), Has.LostHp(52));
    }

    [Test]
    public async Task TestDoesNothingOnTurnsWithMultipleOf7Indices()
    {
        await RelicCmd.Obtain<StoneCalendar>(GetPlayer());

        for (int i = 0; i < 14; i++)
        {
            await PassToNextPlayerTurn();
        }

        // 52 from turn 7, but 0 from turn 14.
        Assert.That(GetEnemy(), Has.LostHp(52));
    }
}