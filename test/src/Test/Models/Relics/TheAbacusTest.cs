using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class TheAbacusTest : ModelTest
{
    [Test]
    public async Task TestGainBlockOnShuffle()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<TheAbacus>(player);
        await CardPileCmd.Shuffle(new ThrowingPlayerChoiceContext(), player);

        Assert.That(player.<PERSON>reature, <PERSON><PERSON>(6));
    }

    [Test]
    public async Task TestGainBlockOnMultipleShuffles()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<TheAbacus>(player);

        for (int i = 0; i < 3; i++)
        {
            await CardPileCmd.Shuffle(new ThrowingPlayerChoiceContext(), player);
        }

        Assert.That(<PERSON>.<PERSON><PERSON><PERSON>, <PERSON><PERSON>(18));
    }
}