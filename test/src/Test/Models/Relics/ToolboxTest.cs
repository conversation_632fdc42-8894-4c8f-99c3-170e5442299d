using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.CardPools;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class ToolboxTest : ModelTest
{
    [Test]
    public async Task TestAddsAColorlessCardToHandAtStartOfTurn()
    {
        await RelicCmd.Obtain<Toolbox>(GetPlayer());

        PrepareToSelectAtIndices(0);
        await RestartCombat();

        CardPile hand = GetPile(PileType.Hand);
        Assert.AreEqual(1, hand.Cards.Count);
        Assert.AreEqual(ModelDb.CardPool<ColorlessCardPool>(), hand.Cards[0].Pool);
    }
}
