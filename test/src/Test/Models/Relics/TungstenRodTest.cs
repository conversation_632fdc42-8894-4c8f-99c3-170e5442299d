using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class TungstenRodTest : ModelTest
{
    [Test]
    public async Task TestReducesUnblockedDamage()
    {
        Creature player = GetPlayer().Creature;

        await RelicCmd.Obtain<TungstenRod>(GetPlayer());
        await CreatureCmd.Damage(player, 3, DamageProps.monsterMove, GetEnemy(), null);

        Assert.That(player, Has.LostHp(2));
    }

    [Test]
    public async Task TestReducesPartiallyBlockedDamage()
    {
        Creature player = GetPlayer().Creature;

        await RelicCmd.Obtain<TungstenRod>(GetPlayer());
        await CreatureCmd.GainBlock(player, 3, BlockProps.nonCardUnpowered, null);
        await CreatureCmd.Damage(player, 8, DamageProps.monsterMove, GetEnemy(), null);

        Assert.That(player, Has.LostHp(4));
    }

    [Test]
    public async Task TestDoesNotAffectBlock()
    {
        Creature player = GetPlayer().Creature;

        await RelicCmd.Obtain<TungstenRod>(GetPlayer());
        await CreatureCmd.GainBlock(player, 9, BlockProps.nonCardUnpowered, null);
        await CreatureCmd.Damage(player, 8, DamageProps.monsterMove, GetEnemy(), null);

        Assert.That(player, Has.Block(1));
    }

    [Test]
    public async Task TestReducesHpLoss()
    {
        Creature player = GetPlayer().Creature;

        await RelicCmd.Obtain<TungstenRod>(GetPlayer());
        await CreatureCmd.Damage(player, 3, DamageProps.nonCardHpLoss, null, null);

        Assert.That(player, Has.LostHp(2));
    }

    [Test]
    public async Task TestReducesDamageFromStatusCard()
    {
        await RelicCmd.Obtain<TungstenRod>(GetPlayer());
        await CardPileCmd.Add(CreateCard<Burn>(), PileType.Hand);
        await PassToNextPlayerTurn();

        Assert.That(GetPlayer().Creature, Has.LostHp(1));
    }

    [Test]
    public async Task TestOnlyTriggersOnceWhenOstyIsKilled()
    {
        Player player = GetPlayer();
        Creature osty = (await OstyCmd.Summon(new ThrowingPlayerChoiceContext(), player, 2, null)).Creature!;
        await RelicCmd.Obtain<TungstenRod>(player);

        await CreatureCmd.Damage(player.Creature, 6, DamageProps.monsterMove, GetEnemy(), null);

        // Osty absorbs 2 damage, Tungsten Rod absorbs 1 damage, player takes remaining 2.
        Assert.That(player.Creature, Has.LostHp(3));
        Assert.That(osty, Is.Dead());
    }
}
