using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Multiplayer.Models.Cards;

public class BeaconOfHopeMultiplayerTest : MultiplayerModelTest
{
    [Test]
    public async Task TestAppliesBlockToOtherPlayers()
    {
        await Play<BeaconOfHope>(GetLocalPlayer());
        await Play<DefendIronclad>(GetLocalPlayer());

        Assert.That(GetRemotePlayer().Creature.Block, Is.EqualTo(2));
        Assert.That(GetLocalPlayer().Creature.Block, Is.EqualTo(5));
    }

    [Test]
    public async Task TestIsAffectedByDexterity()
    {
        await Play<BeaconOfHope>(GetLocalPlayer());
        await PowerCmd.Apply<Dexterity>(GetLocalPlayer().Creature, 1m, null, null);
        await Play<DefendIronclad>(GetLocalPlayer());

        Assert.That(GetRemotePlayer().Creature.Block, Is.EqualTo(3));
        Assert.That(GetLocalPlayer().Creature.Block, Is.EqualTo(6));
    }

    [Test]
    public async Task TestInteractionWithEternalArmor()
    {
        await Play<BeaconOfHope>(GetLocalPlayer());

        // Eternal armor applies a power that applies block, but the block should count as if it it is coming from a card
        await Play<EternalArmor>(GetLocalPlayer());

        await PassToNextPlayerTurn();

        Assert.That(GetRemotePlayer().Creature.Block, Is.EqualTo(5));
        Assert.That(GetLocalPlayer().Creature.Block, Is.EqualTo(11));
    }
}
