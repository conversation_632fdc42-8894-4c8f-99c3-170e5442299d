using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Encounters;
using MegaCrit.Sts2.Core.Models.Encounters.Mocks;
using MegaCrit.Sts2.Core.Rewards;
using MegaCrit.Sts2.Core.Rooms;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Multiplayer.Models.Cards;

public class SpoilsMapMultiplayerTest : MultiplayerModelTest
{
    [Test]
    public async Task TestThatMultipleSpoilsMapsGenerateTheSamePoint()
    {
        SpoilsMap spoilsMap1 = CreateCard<SpoilsMap>(GetLocalPlayer(), CardScope.Climb);
        await CardPileCmd.Add(spoilsMap1, PileType.Deck);

        SpoilsMap spoilsMap2 = CreateCard<SpoilsMap>(GetRemotePlayer(), CardScope.Climb);
        await CardPileCmd.Add(spoilsMap2, PileType.Deck);

        await ClimbManager.Instance.EnterNextAct();

        IClimbState climbState = spoilsMap1.ClimbState!;
        Assert.That(spoilsMap1.SpoilsActIndex, Is.EqualTo(spoilsMap2.SpoilsActIndex));
        Assert.That(spoilsMap1.SpoilsCoord, Is.EqualTo(spoilsMap2.SpoilsCoord!.Value));
        Assert.That(climbState.Map.GetPoint(spoilsMap1.SpoilsCoord!.Value)!.QuestCards, Is.EquivalentTo(new CardModel[] { spoilsMap1, spoilsMap2 }));
        Assert.That(climbState.Map.GetAllMapPoints().Count(p => p.QuestCards.Contains(spoilsMap1)), Is.EqualTo(1));
    }

    [Test]
    public async Task TestGeneratesExtraRewardsForBothPlayersInMarkedRoom()
    {
        SpoilsMap spoilsMap1 = CreateCard<SpoilsMap>(GetLocalPlayer(), CardScope.Climb);
        await CardPileCmd.Add(spoilsMap1, PileType.Deck);

        SpoilsMap spoilsMap2 = CreateCard<SpoilsMap>(GetRemotePlayer(), CardScope.Climb);
        await CardPileCmd.Add(spoilsMap2, PileType.Deck);

        await ClimbManager.Instance.EnterNextAct();

        IClimbState climbState = spoilsMap1.ClimbState!;
        climbState.Act.GenerateRooms(climbState.Rng.UpFront);

        await ClimbManager.Instance.EnterMapCoord(spoilsMap1.SpoilsCoord!.Value);

        IEnumerable<Reward> rewards1 = await RewardsCmd.GenerateForRoom(
            GetLocalPlayer(),
            new CombatRoom(ModelDb.Encounter<MockMonsterEncounter>().ToMutable(), GetLocalPlayer().ClimbState)
        );
        IEnumerable<Reward> rewards2 = await RewardsCmd.GenerateForRoom(
            GetRemotePlayer(),
            new CombatRoom(ModelDb.Encounter<MockMonsterEncounter>().ToMutable(), GetRemotePlayer().ClimbState)
        );

        Assert.That(rewards1, Has.Exactly(2).InstanceOf<RelicReward>());
        Assert.That(rewards2, Has.Exactly(2).InstanceOf<RelicReward>());
        Assert.That(rewards1, Has.Exactly(1).Matches<Reward>(r => r is GoldReward { Amount: >= 20 and <= 40 }));
        Assert.That(rewards2, Has.Exactly(1).Matches<Reward>(r => r is GoldReward { Amount: >= 20 and <= 40 }));
    }
}
