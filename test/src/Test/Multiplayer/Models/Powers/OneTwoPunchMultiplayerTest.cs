using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Multiplayer.Models.Powers;

public class OneTwoPunchMultiplayerTest : MultiplayerModelTest
{
    // Membership card is a little weird because merchant logic runs only for the local player.

    [Test]
    public async Task TestOneTwoPunchDuplicatesCardPlayForOwningPlayer()
    {
        await Play<OneTwoPunch>(GetLocalPlayer());
        await Play<StrikeIronclad>(GetLocalPlayer(), GetEnemy());
        Assert.That(GetEnemy(), Has.LostHp(12));
    }

    [Test]
    public async Task TestCardIsNotDuplicatedForNonOwningPlayer()
    {
        await Play<OneTwoPunch>(GetRemotePlayer());
        await Play<StrikeIronclad>(GetLocalPlayer(), GetEnemy());
        Assert.That(GetEnemy(), <PERSON>.LostHp(6));
    }
}
