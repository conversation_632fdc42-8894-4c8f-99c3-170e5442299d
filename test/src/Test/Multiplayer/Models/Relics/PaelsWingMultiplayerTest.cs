using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.CardRewardAlternatives;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Game;
using MegaCrit.Sts2.Core.Rewards;
using MegaCrit.Sts2.Core.Extensions;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Multiplayer.Models.Relics;

public class PaelsWingMultiplayerTest : MultiplayerModelTest
{
    [Test]
    public async Task TestAddExtraOptionForLocalWhenOwnedByLocal()
    {
        await RelicCmd.Obtain<PaelsWing>(GetLocalPlayer());
        IReadOnlyList<CardRewardAlternative> options = CardRewardAlternative.Generate(new CardReward(CardCreationSource.RegularEncounter, 3, GetLocalPlayer()));

        Assert.That(options.Count, Is.EqualTo(2));
    }

    [Test]
    public async Task TestDoesNotAddExtraOptionForLocalWhenOwnedByRemote()
    {
        await RelicCmd.Obtain<PaelsWing>(GetRemotePlayer());
        IReadOnlyList<CardRewardAlternative> options = CardRewardAlternative.Generate(new CardReward(CardCreationSource.RegularEncounter, 3, GetLocalPlayer()));

        Assert.That(options.Count, Is.EqualTo(1));
    }

    [Test]
    public async Task TestPickingOptionSendsMessage()
    {
        await RelicCmd.Obtain<PaelsWing>(GetLocalPlayer());
        IReadOnlyList<CardRewardAlternative> options = CardRewardAlternative.Generate(new CardReward(CardCreationSource.RegularEncounter, 3, GetLocalPlayer()));
        await options.First(o => o.OptionId == PaelsWing.sacrificeAlternativeKey).OnSelect();

        PaelsWingSacrificeMessage? message = _gameService.SentMessages.OfType<PaelsWingSacrificeMessage>().FirstOrDefault();

        Assert.That(message, Is.Not.Null);
    }

    [Test]
    public async Task TestRemoteMessageIncrementsPaelsWing()
    {
        PaelsWing paelsWing = await RelicCmd.Obtain<PaelsWing>(GetRemotePlayer());
        PaelsWingSacrificeMessage message = new()
        {
            Location = ClimbManager.Instance.DebugOnlyGetState()!.CurrentLocation,
            relicIndex = (uint)GetRemotePlayer().Relics.IndexOf(paelsWing)
        };

        _gameService.ReceiveMessage(message, GetRemotePlayer().NetId);

        Assert.That(paelsWing.RewardsSacrificed, Is.EqualTo(1));
    }
}
