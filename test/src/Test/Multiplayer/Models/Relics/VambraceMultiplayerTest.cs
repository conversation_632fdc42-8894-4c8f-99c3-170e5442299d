using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Multiplayer.Models.Relics;

public class VambraceMultiplayerTest : MultiplayerModelTest
{
    [Test]
    public async Task TestThatVambraceTriggersForOwningPlayer()
    {
        await RelicCmd.Obtain<Vambrace>(GetLocalPlayer());
        await Play<MockSkillCard>(GetLocalPlayer());
        Assert.That(GetLocalPlayer().<PERSON>reature, <PERSON>.Block(10));
    }

    [Test]
    public async Task TestThatVambraceDoesNotTriggerForNonOwningPlayer()
    {
        await RelicCmd.Obtain<Vambrace>(GetLocalPlayer());
        await Play<MockSkillCard>(GetRemotePlayer());
        Assert.That(GetRemotePlayer().<PERSON><PERSON><PERSON>, <PERSON><PERSON>(5));
    }
}
