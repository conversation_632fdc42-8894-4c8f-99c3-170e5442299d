{"acts": [{"id": "ACT.OVERGROWTH", "rooms": {"ancient_id": "EVENT.NEOW", "boss_id": "ENCOUNTER.CEREMONIAL_BEAST_BOSS", "elite_encounter_ids": ["ENCOUNTER.BYRDONIS_ELITE", "ENCOUNTER.PHROG_PARASITE_ELITE", "ENCOUNTER.BYGONE_EFFIGY_ELITE", "ENCOUNTER.BYRDONIS_ELITE", "ENCOUNTER.BYGONE_EFFIGY_ELITE", "ENCOUNTER.PHROG_PARASITE_ELITE", "ENCOUNTER.BYGONE_EFFIGY_ELITE", "ENCOUNTER.BYRDONIS_ELITE", "ENCOUNTER.PHROG_PARASITE_ELITE", "ENCOUNTER.BYGONE_EFFIGY_ELITE", "ENCOUNTER.PHROG_PARASITE_ELITE", "ENCOUNTER.BYRDONIS_ELITE", "ENCOUNTER.PHROG_PARASITE_ELITE", "ENCOUNTER.BYRDONIS_ELITE", "ENCOUNTER.BYGONE_EFFIGY_ELITE"], "elite_encounters_visited": 0, "event_ids": ["EVENT.POTION_COURIER", "EVENT.AROMA_OF_CHAOS", "EVENT.BYRDONIS_NEST", "EVENT.RELIC_TRADER", "EVENT.THE_LEGENDS_WERE_TRUE", "EVENT.BURIED_SEED", "EVENT.THE_SUNKEN_STATUE", "EVENT.FORSAKEN_CITADEL", "EVENT.WOOD_CARVINGS", "EVENT.SYMBIOTE", "EVENT.WELLSPRING", "EVENT.PERILOUS_PROSPECTS", "EVENT.SELF_HELP_BOOK", "EVENT.SLIPPERY_BRIDGE", "EVENT.TEA_MASTER", "EVENT.DOLL_ROOM", "EVENT.ROOM_FULL_OF_CHEESE", "EVENT.WELCOME_TO_WONGOS", "EVENT.CURSED_CHOICE"], "events_visited": 0, "normal_encounter_ids": ["ENCOUNTER.NIBBITS_WEAK", "ENCOUNTER.FUZZY_WURM_CRAWLER_WEAK", "ENCOUNTER.FLYCONID_WEAK", "ENCOUNTER.VINE_SHAMBLER_NORMAL", "ENCOUNTER.RUBY_RAIDERS_NORMAL", "ENCOUNTER.FOGMOG_NORMAL", "ENCOUNTER.OVERGROWTH_CRAWLERS", "ENCOUNTER.CUBEX_CONSTRUCT_NORMAL", "ENCOUNTER.MAWLER_NORMAL", "ENCOUNTER.NIBBITS_NORMAL", "ENCOUNTER.SLIMES_NORMAL", "ENCOUNTER.INKLETS_NORMAL", "ENCOUNTER.SNAPPING_JAXFRUIT_NORMAL", "ENCOUNTER.INKLETS_NORMAL", "ENCOUNTER.VINE_SHAMBLER_NORMAL"], "normal_encounters_visited": 0}}, {"id": "ACT.HIVE", "rooms": {"ancient_id": "EVENT.TEZCATARA", "boss_id": "ENCOUNTER.THE_INSATIABLE_BOSS", "elite_encounter_ids": ["ENCOUNTER.OVICOPTER_ELITE", "ENCOUNTER.ENTOMANCER_ELITE", "ENCOUNTER.DECIMILLIPEDE_ELITE", "ENCOUNTER.OVICOPTER_ELITE", "ENCOUNTER.DECIMILLIPEDE_ELITE", "ENCOUNTER.ENTOMANCER_ELITE", "ENCOUNTER.DECIMILLIPEDE_ELITE", "ENCOUNTER.OVICOPTER_ELITE", "ENCOUNTER.ENTOMANCER_ELITE", "ENCOUNTER.OVICOPTER_ELITE", "ENCOUNTER.DECIMILLIPEDE_ELITE", "ENCOUNTER.ENTOMANCER_ELITE", "ENCOUNTER.DECIMILLIPEDE_ELITE", "ENCOUNTER.ENTOMANCER_ELITE", "ENCOUNTER.OVICOPTER_ELITE"], "elite_encounters_visited": 0, "event_ids": ["EVENT.LIVING_COCOON", "EVENT.TEA_MASTER", "EVENT.TELEPATHIC_SPIDERS", "EVENT.BRAINBUG", "EVENT.RELIC_TRADER", "EVENT.ROOM_FULL_OF_CHEESE", "EVENT.POTION_COURIER", "EVENT.BEDLAM_BEACON", "EVENT.INFESTED_AUTOMATON", "EVENT.SLIPPERY_BRIDGE", "EVENT.BUGSLAYER", "EVENT.CURSED_CHOICE", "EVENT.SELF_HELP_BOOK", "EVENT.THE_LEGENDS_WERE_TRUE", "EVENT.SYMBIOTE", "EVENT.DOLL_ROOM", "EVENT.WELCOME_TO_WONGOS"], "events_visited": 0, "normal_encounter_ids": ["ENCOUNTER.BOWLBUGS_WEAK", "ENCOUNTER.TUNNELER_WEAK", "ENCOUNTER.THIEVING_HOPPER_WEAK", "ENCOUNTER.SLUMBERING_BEETLE_NORMAL", "ENCOUNTER.LOUSE_PROGENITOR_NORMAL", "ENCOUNTER.TUNNELER_NORMAL", "ENCOUNTER.EXOSKELETONS_NORMAL", "ENCOUNTER.BOWLBUGS_NORMAL", "ENCOUNTER.MYTES_NORMAL", "ENCOUNTER.LOUSE_PROGENITOR_NORMAL", "ENCOUNTER.BOWLBUGS_NORMAL", "ENCOUNTER.HUNTER_KILLER_NORMAL", "ENCOUNTER.TUNNELER_NORMAL"], "normal_encounters_visited": 0}}, {"id": "ACT.GLORY", "rooms": {"ancient_id": "EVENT.TANX", "boss_id": "ENCOUNTER.QUEEN_BOSS", "elite_encounter_ids": ["ENCOUNTER.MECHA_KNIGHT_ELITE", "ENCOUNTER.OWL_MAGISTRATE_ELITE", "ENCOUNTER.SOUL_NEXUS_ELITE", "ENCOUNTER.MECHA_KNIGHT_ELITE", "ENCOUNTER.OWL_MAGISTRATE_ELITE", "ENCOUNTER.SOUL_NEXUS_ELITE", "ENCOUNTER.MECHA_KNIGHT_ELITE", "ENCOUNTER.OWL_MAGISTRATE_ELITE", "ENCOUNTER.SOUL_NEXUS_ELITE", "ENCOUNTER.MECHA_KNIGHT_ELITE"], "elite_encounters_visited": 0, "event_ids": ["EVENT.ROOM_FULL_OF_CHEESE", "EVENT.WELCOME_TO_WONGOS", "EVENT.FIELD_OF_THE_FORGOTTEN", "EVENT.DOLL_ROOM", "EVENT.RELIC_TRADER", "EVENT.SLIPPERY_BRIDGE", "EVENT.CURSED_CHOICE", "EVENT.SYMBIOTE", "EVENT.THE_LEGENDS_WERE_TRUE", "EVENT.SELF_HELP_BOOK", "EVENT.TEA_MASTER", "EVENT.HUNGRY_FOR_MUSHROOMS", "EVENT.THE_ROUND_TEA_PARTY", "EVENT.REFLECTIONS", "EVENT.POTION_COURIER", "EVENT.STONE_OF_ALL_TIME", "EVENT.COLORFUL_PHILOSOPHERS", "EVENT.THE_TRIAL", "EVENT.TINKER_TIME"], "events_visited": 0, "normal_encounter_ids": ["ENCOUNTER.KNIGHTS_WEAK", "ENCOUNTER.AXEBOTS_WEAK", "ENCOUNTER.SCROLLS_OF_BITING_WEAK", "ENCOUNTER.TURRET_OPERATOR_NORMAL", "ENCOUNTER.SCROLLS_OF_BITING_NORMAL", "ENCOUNTER.SLIMED_BERSERKER_NORMAL", "ENCOUNTER.GLOBE_HEAD_NORMAL", "ENCOUNTER.KNIGHTS_NORMAL", "ENCOUNTER.FABRICATOR_NORMAL", "ENCOUNTER.DEVOTED_SCULPTOR_NORMAL", "ENCOUNTER.TURRET_OPERATOR_NORMAL", "ENCOUNTER.FROG_KNIGHT_NORMAL", "ENCOUNTER.KNIGHTS_NORMAL"], "normal_encounters_visited": 0}}], "modifiers": [], "current_act_index": 0, "extra_fields": {"started_with_neow": true}, "room_state": 0, "odds": {"unknown_map_point_monster_odds_value": 0.1, "unknown_map_point_shop_odds_value": 0.03, "unknown_map_point_treasure_odds_value": 0.02}, "players": [{"character_id": "CHARACTER.IRONCLAD", "current_hp": 80, "deck": [{"id": "CARD.STRIKE_IRONCLAD", "is_removable": false}, {"id": "CARD.STRIKE_IRONCLAD"}, {"id": "CARD.STRIKE_IRONCLAD"}, {"id": "CARD.STRIKE_IRONCLAD"}, {"id": "CARD.STRIKE_IRONCLAD"}, {"id": "CARD.DEFEND_IRONCLAD"}, {"id": "CARD.DEFEND_IRONCLAD"}, {"id": "CARD.DEFEND_IRONCLAD"}, {"id": "CARD.DEFEND_IRONCLAD"}, {"enchantment": {"amount": 3, "id": "ENCHANTMENT.SHARP"}, "id": "CARD.BASH"}], "gold": 99, "max_energy": 3, "max_hp": 80, "max_potion_slot_count": 4, "net_id": 1, "odds": {"card_rarity_odds_value": -0.05, "potion_reward_odds_value": 0.4}, "relic_grab_bag": {"relic_id_lists": {"shop": ["RELIC.MEMBERSHIP_CARD", "RELIC.ORRERY", "RELIC.DOLLYS_MIRROR", "RELIC.LASTING_CANDY", "RELIC.LEES_WAFFLE", "RELIC.CAULDRON", "RELIC.JUZU_BRACELET", "RELIC.AMETHYST_BANGLE", "RELIC.THE_ABACUS", "RELIC.SLING_OF_COURAGE", "RELIC.SCREAMING_FLAGON", "RELIC.TOOLBOX"], "common": ["RELIC.INK_BOTTLE", "RELIC.ORICHALCUM", "RELIC.WHETSTONE", "RELIC.KUSARIGAMA", "RELIC.HAPPY_FLOWER", "RELIC.CENTENNIAL_PUZZLE", "RELIC.ART_OF_WAR", "RELIC.LANTERN", "RELIC.ODDLY_SMOOTH_STONE", "RELIC.WAR_PAINT", "RELIC.BAG_OF_MARBLES", "RELIC.BAG_OF_PREPARATION", "RELIC.VAMBRACE", "RELIC.POTION_BELT", "RELIC.MEAL_TICKET", "RELIC.ANCIENT_TEA_SET", "RELIC.CHAIN_MAIL_PANTS", "RELIC.BLOOD_VIAL", "RELIC.REPTILE_TRINKET", "RELIC.PEN_NIB", "RELIC.RED_SKULL", "RELIC.REGAL_PILLOW", "RELIC.NUNCHAKU", "RELIC.ANCHOR", "RELIC.STRAWBERRY", "RELIC.VAJRA"], "rare": ["RELIC.GAMBLING_CHIP", "RELIC.PRAYER_WHEEL", "RELIC.CALLING_BELL", "RELIC.SHOVEL", "RELIC.CHARONS_ASHES", "RELIC.TUNGSTEN_ROD", "RELIC.THE_COURIER", "RELIC.UNCEASING_TOP", "RELIC.LUCKY_FYSH", "RELIC.POCKETWATCH", "RELIC.ICE_CREAM", "RELIC.LIZARD_TAIL", "RELIC.ORACLE_BONE", "RELIC.BOOK_OF_FIVE_RINGS", "RELIC.STONE_CALENDAR", "RELIC.CAPTAINS_WHEEL", "RELIC.OLD_COIN", "RELIC.MANGO", "RELIC.MUMMIFIED_HAND", "RELIC.RAINBOW_RING", "RELIC.PETRIFIED_TOAD"], "uncommon": ["RELIC.STRIKE_DUMMY", "RELIC.TOXIC_EGG", "RELIC.PANTOGRAPH", "RELIC.ETERNAL_FEATHER", "RELIC.FROZEN_EGG", "RELIC.JOSS_PAPER", "RELIC.KUNAI", "RELIC.MOLTEN_EGG", "RELIC.INTIMIDATING_HELMET", "RELIC.MEAT_ON_THE_BONE", "RELIC.MERCURY_HOURGLASS", "RELIC.SELF_FORMING_CLAY", "RELIC.LETTER_OPENER", "RELIC.PEAR", "RELIC.SHURIKEN", "RELIC.HORN_CLEAT", "RELIC.ORNAMENTAL_FAN", "RELIC.GREMLIN_HORN"]}}, "relics": [{"id": "RELIC.BURNING_BLOOD"}], "rng": {"counters": {"Rewards": 0, "Shops": 0}, "seed": 1234}}], "pre_finished_room": null, "rng": {"counters": {"UpFront": 228, "Shuffle": 0, "UnknownMapPoint": 0, "CombatCardGeneration": 0, "CombatPotionGeneration": 0, "CombatCardSelection": 0, "CombatEnergyCosts": 0, "CombatTargets": 0, "MonsterAi": 0, "Niche": 0, "CombatOrbs": 0, "TreasureRoomRelics": 0}, "seed": 1234}, "climb_time": 0, "save_time": 1727907485, "schema_version": 0, "start_time": 1727907485, "shared_relic_grab_bag": {"relic_id_lists": {"shop": ["RELIC.MEMBERSHIP_CARD", "RELIC.ORRERY", "RELIC.DOLLYS_MIRROR", "RELIC.LASTING_CANDY", "RELIC.LEES_WAFFLE", "RELIC.CAULDRON", "RELIC.JUZU_BRACELET", "RELIC.AMETHYST_BANGLE", "RELIC.THE_ABACUS", "RELIC.SLING_OF_COURAGE", "RELIC.SCREAMING_FLAGON", "RELIC.TOOLBOX"], "common": ["RELIC.INK_BOTTLE", "RELIC.ORICHALCUM", "RELIC.WHETSTONE", "RELIC.KUSARIGAMA", "RELIC.HAPPY_FLOWER", "RELIC.CENTENNIAL_PUZZLE", "RELIC.ART_OF_WAR", "RELIC.LANTERN", "RELIC.ODDLY_SMOOTH_STONE", "RELIC.WAR_PAINT", "RELIC.BAG_OF_MARBLES", "RELIC.BAG_OF_PREPARATION", "RELIC.VAMBRACE", "RELIC.POTION_BELT", "RELIC.MEAL_TICKET", "RELIC.ANCIENT_TEA_SET", "RELIC.CHAIN_MAIL_PANTS", "RELIC.BLOOD_VIAL", "RELIC.REPTILE_TRINKET", "RELIC.PEN_NIB", "RELIC.RED_SKULL", "RELIC.REGAL_PILLOW", "RELIC.NUNCHAKU", "RELIC.ANCHOR", "RELIC.STRAWBERRY", "RELIC.VAJRA"], "rare": ["RELIC.GAMBLING_CHIP", "RELIC.PRAYER_WHEEL", "RELIC.CALLING_BELL", "RELIC.SHOVEL", "RELIC.CHARONS_ASHES", "RELIC.TUNGSTEN_ROD", "RELIC.THE_COURIER", "RELIC.UNCEASING_TOP", "RELIC.LUCKY_FYSH", "RELIC.POCKETWATCH", "RELIC.ICE_CREAM", "RELIC.LIZARD_TAIL", "RELIC.ORACLE_BONE", "RELIC.BOOK_OF_FIVE_RINGS", "RELIC.STONE_CALENDAR", "RELIC.CAPTAINS_WHEEL", "RELIC.OLD_COIN", "RELIC.MANGO", "RELIC.MUMMIFIED_HAND", "RELIC.RAINBOW_RING", "RELIC.PETRIFIED_TOAD"], "uncommon": ["RELIC.STRIKE_DUMMY", "RELIC.TOXIC_EGG", "RELIC.PANTOGRAPH", "RELIC.ETERNAL_FEATHER", "RELIC.FROZEN_EGG", "RELIC.JOSS_PAPER", "RELIC.KUNAI", "RELIC.MOLTEN_EGG", "RELIC.INTIMIDATING_HELMET", "RELIC.MEAT_ON_THE_BONE", "RELIC.MERCURY_HOURGLASS", "RELIC.SELF_FORMING_CLAY", "RELIC.LETTER_OPENER", "RELIC.PEAR", "RELIC.SHURIKEN", "RELIC.HORN_CLEAT", "RELIC.ORNAMENTAL_FAN", "RELIC.GREMLIN_HORN"]}}}