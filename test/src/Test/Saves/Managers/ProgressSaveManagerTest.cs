using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Climbs;
using MegaCrit.Sts2.Core.Saves.Managers;
using MegaCrit.Sts2.Core.Saves.Migrations;
using MegaCrit.Sts2.Core.TestSupport;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Saves.Managers;

[TestFixture]
public class ProgressSaveManagerTest
{
    private const string _testSaveDir = "user://test_progress_saves";
    private static readonly string _godotProjectDir = ProjectSettings.GlobalizePath("res://");
    private static readonly DirectoryInfo _solutionDir = new DirectoryInfo(_godotProjectDir).Parent!;
    private readonly string _fixturesPath = Path.Combine(_solutionDir.FullName, "test", "src", "Test", "Saves", "Fixtures");
    private MockGodotFileIo _mockSaveStore = default!;
    private ProgressSaveManager _progressSaveManager = default!;

    [SetUp]
    public void Setup()
    {
        _mockSaveStore = new MockGodotFileIo(_testSaveDir);
        _progressSaveManager = new ProgressSaveManager(_mockSaveStore, new MigrationManager(_mockSaveStore));
    }

    [TearDown]
    public void TearDown()
    {
        // Clean up any resources
        // While MockGodotFileIo is in-memory, this ensures consistency with other test classes
        _mockSaveStore = null!;
        _progressSaveManager = null!;
    }

    /// <summary>
    /// If there's no save file, it should report file not found as a status and create a fresh save.
    /// </summary>
    [Test]
    public void TestLoadProgressCreatesNewFileWhenMissing()
    {
        // Act
        ProgressSave result = _progressSaveManager.LoadProgress();

        // Assert
        // Auto-recovery should create valid progress data
        Assert.That(result.SchemaVersion, Is.GreaterThan(0));
        Assert.That(_progressSaveManager.Progress, Is.Not.Null);
        Assert.That(_progressSaveManager.Progress.UniqueId, Is.Not.Empty);

        // Verify file was created
        string progressPath = _mockSaveStore.GetFullPath(ProgressSaveManager.fileName);
        Assert.That(_mockSaveStore.FileExists(progressPath), Is.True);
    }

    [Test]
    public void TestLoadProgressLoadsSuccessfullyWithValidFile()
    {
        // Read from fixture file
        string validProgressPath = _fixturesPath.PathJoin("valid_progress.save");
        string validProgressJson = File.ReadAllText(validProgressPath);

        // Write directly to the mock file store (it doesn't actually write to the file system)
        string progressPath = _mockSaveStore.GetFullPath(ProgressSaveManager.fileName);
        _mockSaveStore.WriteFile(progressPath, validProgressJson);

        // Act
        ProgressSave result = _progressSaveManager.LoadProgress();

        // Assert
        Assert.That(result, Is.Not.Null);
        // Should load successfully
        Assert.That(result.SchemaVersion, Is.GreaterThan(0));
        Assert.That(_progressSaveManager.Progress, Is.Not.Null);
    }

    [Test]
    public void TestLoadProgressCreatesNewFileWithCorruptedJson()
    {
        // Create an invalid JSON string directly in the test
        const string badJson = "{x}";

        // Write directly to the mock file store
        string progressPath = _mockSaveStore.GetFullPath(ProgressSaveManager.fileName);
        _mockSaveStore.WriteFile(progressPath, badJson);

        // Capture rename operation
        string? capturedSourcePath = null;
        string? capturedDestPath = null;
        _mockSaveStore.RenameFileAction = (source, dest) =>
        {
            capturedSourcePath = source;
            capturedDestPath = dest;

            // Perform the rename in our mock
            string? content = _mockSaveStore.ReadFile(source);
            _mockSaveStore.DeleteFile(source);
            if (content != null)
            {
                _mockSaveStore.WriteFile(dest, content);
            }
        };

        // Act
        ProgressSave result = _progressSaveManager.LoadProgress();

        // Assert
        Assert.That(result, Is.Not.Null);
        // Auto-recovery should create valid progress data
        Assert.That(result.SchemaVersion, Is.GreaterThan(0));

        // Verify original file was renamed and new one created
        Assert.That(_mockSaveStore.FileExists(progressPath), Is.True);

        // Check that the RenameFileAction was called
        Assert.That(capturedSourcePath, Is.Not.Null);
        Assert.That(capturedDestPath, Is.Not.Null);

        // Verify source path is the original progress file
        Assert.That(capturedSourcePath, Is.EqualTo(progressPath));

        // Ensure destination follows the new naming pattern: progress.{timestamp}.{char}.save
        Assert.That(capturedDestPath, Does.Match(@"\.\d+\.JSN\.corrupt$"));
        Assert.That(capturedDestPath, Does.EndWith(".corrupt"));

        // Check that both files exist in our mock file system
        string[] files = _mockSaveStore.GetFilesInDirectory(_testSaveDir);
        // Should have 2 files: the renamed corrupt file and the new progress.save
        Assert.That(files.Length, Is.EqualTo(2), $"Expected 2 files but found {files.Length}: [{string.Join(", ", files)}]");
        Assert.That(files, Has.One.Matches<string>(f => Regex.IsMatch(f, @"\d+\.[A-Z]{3}\.corrupt")));
        Assert.That(files, Has.One.EqualTo("progress.save"));
    }

    [Test]
    public void TestRenameFileConstructsCorrectCorruptFilePath()
    {
        // This test ensures the corrupt file path is constructed correctly with proper "user://" prefix

        // Arrange
        // Create an invalid JSON string directly in the test
        const string badJson = "{x}";

        // Write the bad JSON to the original file store to set up the test
        string badJsonPath = _mockSaveStore.GetFullPath(ProgressSaveManager.fileName);
        _mockSaveStore.WriteFile(badJsonPath, badJson);

        // Mock functionality to intercept the rename call
        string capturedSourcePath = string.Empty;
        string capturedDestPath = string.Empty;

        // Create the mock
        MockGodotFileIo mockSaveStore = new(_testSaveDir);

        // Set up the action after creating the mock
        mockSaveStore.RenameFileAction = (source, dest) =>
        {
            capturedSourcePath = source;
            capturedDestPath = dest;

            // Also update the mock's internal state
            if (!mockSaveStore.FileExists(source)) return;
            string? content = mockSaveStore.ReadFile(source);
            mockSaveStore.DeleteFile(source);
            if (content != null)
            {
                mockSaveStore.WriteFile(dest, content);
            }
        };

        // Write the same bad JSON to the new mock
        mockSaveStore.WriteFile(badJsonPath, badJson);

        ProgressSaveManager manager = new(mockSaveStore, new MigrationManager(mockSaveStore));

        // Act
        ProgressSave result = manager.LoadProgress();

        // Assert
        Assert.That(result, Is.Not.Null);
        // Auto-recovery should create valid progress data
        Assert.That(result.SchemaVersion, Is.GreaterThan(0));

        // Verify both paths start with "user://"
        Assert.That(capturedSourcePath, Does.StartWith("user://"));
        Assert.That(capturedDestPath, Does.StartWith("user://"));

        // Verify destination path format follows new pattern: progress.{timestamp}.{char}.save
        Assert.That(capturedDestPath, Does.Match(@"user://test_progress_saves/progress\.\d+\.JSN\.corrupt"));
    }

    [Test]
    public void TestLoadProgressRenamesFileWithUnmappedProperties()
    {
        // Read from fixture file and add an unmapped property
        string validProgressPath = _fixturesPath.PathJoin("valid_progress.save");
        string fixtureContent = File.ReadAllText(validProgressPath);

        // Insert new unmapped_property field before the last closing brace
        string jsonWithUnmappedProperties = fixtureContent.Replace("{", "{\n  \"unmapped_property\": \"test value\",\n");

        // Write directly to the mock file store (it doesn't actually write to the file system)
        string progressPath = _mockSaveStore.GetFullPath(ProgressSaveManager.fileName);
        _mockSaveStore.WriteFile(progressPath, jsonWithUnmappedProperties);

        // Capture rename operation
        string? capturedSourcePath = null;
        string? capturedDestPath = null;
        _mockSaveStore.RenameFileAction = (source, dest) =>
        {
            capturedSourcePath = source;
            capturedDestPath = dest;

            // Perform the rename in our mock
            string? content = _mockSaveStore.ReadFile(source);
            _mockSaveStore.DeleteFile(source);
            if (content != null)
            {
                _mockSaveStore.WriteFile(dest, content);
            }
        };

        // Act
        ProgressSave result = _progressSaveManager.LoadProgress();

        // Assert
        Assert.That(result, Is.Not.Null);
        // Auto-recovery should create valid progress data
        Assert.That(result.SchemaVersion, Is.GreaterThan(0));

        // Verify original file was renamed and new one created
        Assert.That(_mockSaveStore.FileExists(progressPath), Is.True);

        // Check that the RenameFileAction was called
        Assert.That(capturedSourcePath, Is.Not.Null);
        Assert.That(capturedDestPath, Is.Not.Null);

        // Verify source path is the original progress file
        Assert.That(capturedSourcePath, Is.EqualTo(progressPath));

        // Ensure destination follows the new naming pattern: progress.{timestamp}.{char}.save
        Assert.That(capturedDestPath, Does.Match(@"\.\d+\.[A-Z]{3}\.corrupt$"));
        Assert.That(capturedDestPath, Does.EndWith(".corrupt"));

        // Check that both files exist in our mock file system  
        // Should have: 1) corrupt renamed file, 2) new progress.save file
        string[] files = _mockSaveStore.GetFilesInDirectory(_testSaveDir);
        Assert.That(files.Length, Is.EqualTo(2), $"Unexpected number of files returned: {files.Length} files=[{string.Join(", ", files)}]");
        Assert.That(files, Has.One.Matches<string>(f => Regex.IsMatch(f, @"\d+\.[A-Z]{3}\.corrupt")));
        Assert.That(files, Has.One.EqualTo("progress.save"));
    }

    [Test]
    public void TestGetCharacterStatsReturnsExistingStats()
    {
        // Arrange
        ModelId characterId = new("character", "ironclad");
        SetUpProgressWithCharacter(characterId);

        // Act
        CharacterStats stats = _progressSaveManager.GetCharacterStats(characterId);

        // Assert
        Assert.That(stats, Is.Not.Null);
        Assert.That(stats.Id, Is.EqualTo(characterId));
    }

    [Test]
    public void TestGetCharacterStatsCreatesNewStats()
    {
        // Arrange
        ModelId characterId = new("character", "silent");

        // Act
        CharacterStats stats = _progressSaveManager.GetCharacterStats(characterId);

        // Assert
        Assert.That(stats, Is.Not.Null);
        Assert.That(stats.Id, Is.EqualTo(characterId));
        Assert.That(_progressSaveManager.Progress.CharStats, Contains.Item(stats));
    }

    [Test]
    public void TestGetCardStatsReturnsExistingStats()
    {
        // Arrange
        ModelId cardId = new("card", "strike");
        SetUpProgressWithCard(cardId);

        // Act
        CardStats stats = _progressSaveManager.GetCardStats(cardId);

        // Assert
        Assert.That(stats, Is.Not.Null);
        Assert.That(stats.Id, Is.EqualTo(cardId));
    }

    [Test]
    public void TestGetCardStatsCreatesNewStats()
    {
        // Arrange
        ModelId cardId = new("card", "defend");

        // Act
        CardStats stats = _progressSaveManager.GetCardStats(cardId);

        // Assert
        Assert.That(stats, Is.Not.Null);
        Assert.That(stats.Id, Is.EqualTo(cardId));
        Assert.That(_progressSaveManager.Progress.CardStats, Contains.Item(stats));
    }

    [Test]
    public void TestSeenFtueReturnsTrueWhenFtuesDisabled()
    {
        // Arrange
        _progressSaveManager.Progress.EnableFtues = false;

        // Act & Assert
        Assert.That(_progressSaveManager.SeenFtue("any_ftue"), Is.True);
    }

    [Test]
    public void TestSeenFtueReturnsTrueForExistingFtue()
    {
        // Arrange
        const string ftueId = "test_ftue";
        _progressSaveManager.Progress.EnableFtues = true;
        _progressSaveManager.Progress.FtueCompleted.Add(ftueId);

        // Act & Assert
        Assert.That(_progressSaveManager.SeenFtue(ftueId), Is.True);
    }

    [Test]
    public void TestSeenFtueReturnsFalseForNonexistentFtue()
    {
        // Arrange
        _progressSaveManager.Progress.EnableFtues = true;

        // Temporarily disable TestMode.IsOn to test actual behavior
        bool originalIsOn = TestMode.IsOn;
        TestMode.IsOn = false;

        try
        {
            // Act & Assert
            Assert.That(_progressSaveManager.SeenFtue("nonexistent_ftue"), Is.False);
        }
        finally
        {
            // Restore original value
            TestMode.IsOn = originalIsOn;
        }
    }

    [Test]
    public void TestMarkFtueAsCompleteAddsNewFtue()
    {
        // Arrange
        const string ftueId = "new_ftue";
        _progressSaveManager.Progress.FtueCompleted.Clear();

        // Act
        _progressSaveManager.MarkFtueAsComplete(ftueId);

        // Assert
        Assert.That(_progressSaveManager.Progress.FtueCompleted, Contains.Item(ftueId));

        // Verify file was saved
        string progressPath = _mockSaveStore.GetFullPath(ProgressSaveManager.fileName);
        Assert.That(_mockSaveStore.FileExists(progressPath), Is.True);
    }

    [Test]
    public void TestMarkFtueAsCompleteDoesNotAddDuplicate()
    {
        // Arrange
        const string ftueId = "existing_ftue";
        _progressSaveManager.Progress.FtueCompleted.Add(ftueId);

        // Act
        _progressSaveManager.MarkFtueAsComplete(ftueId);

        // Assert - should still only have one entry
        Assert.That(_progressSaveManager.Progress.FtueCompleted.Count, Is.EqualTo(1));
        Assert.That(_progressSaveManager.Progress.FtueCompleted, Contains.Item(ftueId));
    }

    [Test]
    public void TestSetFtuesEnabledSavesWhenChanged()
    {
        // Arrange
        _progressSaveManager.Progress.EnableFtues = true;

        // Act
        _progressSaveManager.SetFtuesEnabled(false);

        // Assert
        Assert.That(_progressSaveManager.Progress.EnableFtues, Is.False);

        // Verify file was saved
        string progressPath = _mockSaveStore.GetFullPath(ProgressSaveManager.fileName);
        Assert.That(_mockSaveStore.FileExists(progressPath), Is.True);
    }

    [Test]
    public void TestSetFtuesEnabledDoesNotSaveWhenUnchanged()
    {
        // Arrange
        _progressSaveManager.Progress.EnableFtues = false;

        // Get initial count of WriteFile calls to compare later
        int initialWriteCount = CountWriteFileCalls(_mockSaveStore);

        // Act
        _progressSaveManager.SetFtuesEnabled(false);

        // Assert
        Assert.That(_progressSaveManager.Progress.EnableFtues, Is.False);

        // Verify no file was saved by checking no additional WriteFile calls were made
        int finalWriteCount = CountWriteFileCalls(_mockSaveStore);
        Assert.That(finalWriteCount, Is.EqualTo(initialWriteCount),
            "Expected no additional WriteFile calls when setting the same value");
    }

    [Test]
    public void TestResetFtuesClearsAndEnablesFtues()
    {
        // Arrange
        _progressSaveManager.Progress.EnableFtues = false;
        _progressSaveManager.Progress.FtueCompleted.Add("ftue1");
        _progressSaveManager.Progress.FtueCompleted.Add("ftue2");

        // Act
        _progressSaveManager.ResetFtues();

        // Assert
        Assert.That(_progressSaveManager.Progress.EnableFtues, Is.True);
        Assert.That(_progressSaveManager.Progress.FtueCompleted, Is.Empty);

        // Verify file was saved
        string progressPath = _mockSaveStore.GetFullPath(ProgressSaveManager.fileName);
        Assert.That(_mockSaveStore.FileExists(progressPath), Is.True);
    }

    [Test]
    public void TestUpdateWithClimbDataIncrementsAscensionAfterWin()
    {
        _progressSaveManager.Progress.CharStats.Add(new CharacterStats { Id = ModelDb.Character<Ironclad>().Id, MaxAscension = 1 });
        _progressSaveManager.UpdateWithClimbData(
            new SerializableClimb { Players = [CreateSerializablePlayer<Ironclad>(1)], Ascension = 1 },
            true,
            NullClimbState.Instance
        );

        Assert.That(_progressSaveManager.Progress.CharStats[0].MaxAscension, Is.EqualTo(2));
    }

    [Test]
    public void TestUpdateWithClimbDataSavesProgressAfterWin()
    {
        _progressSaveManager.UpdateWithClimbData(
            new SerializableClimb { Players = [CreateSerializablePlayer<Ironclad>(1)], Ascension = 0 },
            true,
            NullClimbState.Instance
        );

        string progressPath = _mockSaveStore.GetFullPath(ProgressSaveManager.fileName);
        Assert.That(_mockSaveStore.FileExists(progressPath), Is.True);
    }

    [Test]
    public void TestUpdateWithClimbDataDoesNotIncrementAscensionAfterLoss()
    {
        _progressSaveManager.Progress.CharStats.Add(new CharacterStats { Id = ModelDb.Character<Ironclad>().Id, MaxAscension = 1 });
        _progressSaveManager.UpdateWithClimbData(
            new SerializableClimb { Players = [CreateSerializablePlayer<Ironclad>(1)], Ascension = 1 },
            false,
            NullClimbState.Instance
        );

        Assert.That(_progressSaveManager.Progress.CharStats[0].MaxAscension, Is.EqualTo(1));
    }

    [Test]
    public void TestUpdateWithClimbDataDoesNotIncrementAscensionBelowMax()
    {
        _progressSaveManager.Progress.CharStats.Add(new CharacterStats { Id = ModelDb.Character<Ironclad>().Id, MaxAscension = 9 });
        _progressSaveManager.UpdateWithClimbData(
            new SerializableClimb { Players = [CreateSerializablePlayer<Ironclad>(1)], Ascension = 1 },
            false,
            NullClimbState.Instance
        );

        Assert.That(_progressSaveManager.Progress.CharStats[0].MaxAscension, Is.EqualTo(9));
    }

    [Test]
    public void TestUpdateWithClimbDataDoesNotIncrementOtherCharacterAscension()
    {
        _progressSaveManager.Progress.CharStats.Add(new CharacterStats { Id = ModelDb.Character<Ironclad>().Id, MaxAscension = 1 });
        _progressSaveManager.Progress.CharStats.Add(new CharacterStats { Id = ModelDb.Character<Silent>().Id, MaxAscension = 1 });
        _progressSaveManager.UpdateWithClimbData(
            new SerializableClimb { Players = [CreateSerializablePlayer<Ironclad>(1)], Ascension = 1 },
            false,
            NullClimbState.Instance
        );

        Assert.That(_progressSaveManager.Progress.CharStats[1].MaxAscension, Is.EqualTo(1));
    }

    [Test]
    public void TestUpdateWithClimbDataDoesNotIncrementMultiplayerAscensionForSingleplayer()
    {
        _progressSaveManager.Progress.MaxMultiplayerAscension = 1;
        _progressSaveManager.Progress.CharStats.Add(new CharacterStats { Id = ModelDb.Character<Ironclad>().Id, MaxAscension = 1 });
        _progressSaveManager.UpdateWithClimbData(
            new SerializableClimb { Players = [CreateSerializablePlayer<Ironclad>(1)], Ascension = 1 },
            false,
            NullClimbState.Instance
        );

        Assert.That(_progressSaveManager.Progress.MaxMultiplayerAscension, Is.EqualTo(1));
    }

    [Test]
    public void TestUpdateWithClimbDataIncrementsMultiplayerAscensionAfterMultiplayerWin()
    {
        _progressSaveManager.Progress.MaxMultiplayerAscension = 1;
        List<SerializablePlayer> players = [CreateSerializablePlayer<Silent>(1), CreateSerializablePlayer<Necrobinder>(1000)];
        _progressSaveManager.UpdateWithClimbData(
            new SerializableClimb { Players = players, Ascension = 1 },
            true,
            NullClimbState.Instance
        );

        Assert.That(_progressSaveManager.Progress.MaxMultiplayerAscension, Is.EqualTo(2));
    }

    [Test]
    public void TestUpdateWithClimbDataDoesNotIncrementSingleplayerAscensionForMultiplayer()
    {
        _progressSaveManager.Progress.MaxMultiplayerAscension = 1;
        _progressSaveManager.Progress.CharStats.Add(new CharacterStats { Id = ModelDb.Character<Ironclad>().Id, MaxAscension = 1 });
        List<SerializablePlayer> players = [CreateSerializablePlayer<Ironclad>(1), CreateSerializablePlayer<Ironclad>(1000)];
        _progressSaveManager.UpdateWithClimbData(
            new SerializableClimb { Players = players, Ascension = 1 },
            false,
            NullClimbState.Instance
        );

        Assert.That(_progressSaveManager.Progress.CharStats[0].MaxAscension, Is.EqualTo(1));
    }

    // Helper methods
    private void SetUpProgressWithCharacter(ModelId characterId)
    {
        CharacterStats stats = new() { Id = characterId };
        _progressSaveManager.Progress.CharStats.Add(stats);
    }

    private void SetUpProgressWithCard(ModelId cardId)
    {
        CardStats stats = new() { Id = cardId };
        _progressSaveManager.Progress.CardStats.Add(stats);
    }

    private static int CountWriteFileCalls(MockGodotFileIo mockStore)
    {
        return mockStore.Calls.Count(call => call.Method == MockGodotFileIo.Methods.writeFile);
    }

    public static SerializablePlayer CreateSerializablePlayer<T>(ulong netId) where T : CharacterModel
    {
        return new SerializablePlayer
        {
            CharacterId = ModelDb.Character<T>().Id,
            NetId = netId,
            Rng = new SerializablePlayerRngSet(),
            Odds = new SerializablePlayerOddsSet(),
            RelicGrabBag = new SerializableRelicGrabBag(),
            ExtraFields = new SerializableExtraPlayerFields()
        };
    }
}
