using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Platform;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Managers;
using MegaCrit.Sts2.Core.Saves.Migrations;
using MegaCrit.Sts2.Test.Saves.Migrations;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Saves.Managers;

/// <summary>
/// Consolidated tests for verifying that all save managers set the latest schema version
/// when saving their respective data types.
/// </summary>
[TestFixture]
public class SaveManagerSchemaVersionTests
{
    private MockSaveStore _mockSaveStore = default!;
    private MigrationManager _migrationManager = default!;

    [SetUp]
    public void Setup()
    {
        _mockSaveStore = new MockSaveStore();
        _migrationManager = new MigrationManager(_mockSaveStore);
    }

    [Test]
    public void TestClimbHistorySaveManagerSetsLatestSchemaVersion()
    {
        // Arrange
        ClimbHistorySaveManager manager = new(_mockSaveStore, _migrationManager);
        ClimbHistory history = new()
        {
            SchemaVersion = 0, // Start with version 0 to test that it gets updated
            PlatformType = PlatformType.Steam,
            GameMode = GameMode.Daily,
            Win = true,
            Seed = "12345",
            StartTime = 1234567890L,
            ClimbTime = 123.45f,
            Ascension = 5,
            BuildId = "test-build"
        };
        int latestVersion = _migrationManager.GetLatestVersion<ClimbHistory>();

        // Act
        manager.SaveHistory(history);

        // Assert
        string expectedPath = $"history/{history.StartTime}.climb";
        string savedContent = _mockSaveStore.ReadFile(expectedPath) ?? string.Empty;
        Assert.That(savedContent, Is.Not.Null.And.Not.Empty, "History save file should be written to disk");
        Assert.That(history.SchemaVersion, Is.EqualTo(latestVersion),
            $"Saved climb history should have latest schema version ({latestVersion}) but has {history.SchemaVersion}");
    }

    [Test]
    public void TestClimbSaveManagerSetsLatestSchemaVersionInJson()
    {
        // Arrange - Create a SerializableClimb with old schema version
        SerializableClimb testClimb = new()
        {
            SchemaVersion = 0 // Old version
        };

        // Manually test the schema version assignment logic that should happen in SaveClimb
        int latestVersion = _migrationManager.GetLatestVersion<SerializableClimb>();
        testClimb.SchemaVersion = latestVersion;

        // Act - Serialize to JSON (similar to what SaveClimb does)
        string json = JsonSerializationUtility.ToJson(testClimb);

        // Assert
        // Verify that the JSON actually contains the schema_version field
        Assert.That(json, Contains.Substring("\"schema_version\""), "JSON should contain schema_version field");
        Assert.That(json, Contains.Substring($"\"schema_version\": {latestVersion}"),
            $"JSON should contain the latest version number {latestVersion}");
    }

    [Test]
    public void TestProgressSaveManagerSetsLatestSchemaVersion()
    {
        // Arrange
        ProgressSaveManager manager = new(_mockSaveStore, _migrationManager);
        int latestVersion = _migrationManager.GetLatestVersion<ProgressSave>();

        // Act - Load progress, which will create a new save since none exists
        ProgressSave result = manager.LoadProgress();

        // Assert
        Assert.That(result.SchemaVersion, Is.GreaterThan(0), "Should have valid schema version");
        Assert.That(manager.Progress, Is.Not.Null, "Progress save should be created");
        Assert.That(manager.Progress.SchemaVersion, Is.EqualTo(latestVersion),
            $"New save file should have latest schema version ({latestVersion}) but has {manager.Progress.SchemaVersion}");

        // Verify the save was written to disk
        string savedContent = _mockSaveStore.ReadFile(ProgressSaveManager.fileName) ?? string.Empty;
        Assert.That(savedContent, Is.Not.Null.And.Not.Empty, "Save file should be written to disk");
    }

    [Test]
    public void TestSettingsSaveManagerSetsLatestSchemaVersion()
    {
        // Arrange
        SettingsSaveManager manager = new(_mockSaveStore, _migrationManager);
        int latestVersion = _migrationManager.GetLatestVersion<SettingsSave>();

        // Initialize Settings property before SaveSettings() call
        manager.Settings = new SettingsSave();

        // Act - SaveSettings() should set the latest schema version before saving
        manager.SaveSettings();

        // Assert
        string savedContent = _mockSaveStore.ReadFile(SettingsSaveManager.settingsSaveFileName) ?? string.Empty;
        Assert.That(savedContent, Is.Not.Null.And.Not.Empty, "Settings save file should be written to disk");

        // Verify the saved settings have the correct schema version
        Assert.That(manager.Settings, Is.Not.Null, "Settings should be accessible");
        Assert.That(manager.Settings.SchemaVersion, Is.EqualTo(latestVersion),
            $"Saved settings should have latest schema version ({latestVersion}) but has {manager.Settings.SchemaVersion}");
    }
}
