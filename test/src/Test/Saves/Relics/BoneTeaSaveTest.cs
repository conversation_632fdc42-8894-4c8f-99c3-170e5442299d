using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Climbs;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Saves.Relics;

[TestFixture]
public class BoneTeaSaveTest
{
    [Test]
    public void TestSerializesState()
    {
        BoneTea initialRelic = (BoneTea)ModelDb.Relic<BoneTea>().ToMutable();
        SerializableClimb initialSave = new() { Players = [new SerializablePlayer()] };

        initialRelic.CombatsLeft--;
        initialSave.Players[0].Relics.Add(initialRelic.ToSerializable());
        string json = SaveManager.ToJson(initialSave);

        SerializableClimb deserializedSave = SaveManager.FromJson<SerializableClimb>(json).SaveData!;
        BoneTea deserializedRelic = (BoneTea)RelicModel.FromSerializable(deserializedSave.Players[0].Relics[0]);

        Assert.That(deserializedRelic.CombatsLeft, Is.EqualTo(4));
    }
}
