using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Climbs;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Saves.Relics;

[TestFixture]
public class SilverCrucibleSaveTest
{
    [Test]
    public void TestSerializesState()
    {
        SilverCrucible initialRelic = (SilverCrucible)ModelDb.Relic<SilverCrucible>().ToMutable();
        SerializableClimb initialSave = new() { Players = [new SerializablePlayer()] };

        initialRelic.TimesUsed++;
        initialSave.Players[0].Relics.Add(initialRelic.ToSerializable());
        string json = SaveManager.ToJson(initialSave);

        SerializableClimb deserializedSave = SaveManager.FromJson<SerializableClimb>(json).SaveData!;
        SilverCrucible deserializedRelic = (SilverCrucible)RelicModel.FromSerializable(deserializedSave.Players[0].Relics[0]);

        Assert.That(deserializedRelic.TimesUsed, Is.EqualTo(1));
    }
}
