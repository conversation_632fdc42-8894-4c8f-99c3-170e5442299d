using System.IO;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Managers;
using MegaCrit.Sts2.Core.Saves.Migrations;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Saves;

[TestFixture]
public class SaveManagerTest
{
    private const string _saveDir = "user://tmp";

    private DirectoryInfo _fixturesDir = default!;
    private MockGodotFileIo _mockSaveStore = default!;
    private MigrationManager _migrationManager = default!;

    [OneTimeSetUp]
    public void SetupAll()
    {
        string godotProjectDir = ProjectSettings.GlobalizePath("res://");
        DirectoryInfo solutionDir = new DirectoryInfo(godotProjectDir).Parent!;
        string fixturesPath = Path.Combine(solutionDir.FullName, "test", "src", "Test", "Saves", "Fixtures");
        _fixturesDir = new DirectoryInfo(fixturesPath);
    }

    [SetUp]
    public void Setup()
    {
        _mockSaveStore = new MockGodotFileIo(_saveDir);
        _migrationManager = new MigrationManager(_mockSaveStore);
    }

    [TearDown]
    public void TearDown()
    {
        // Clean up resources
        ClimbManager.Instance.CleanUp();
        _mockSaveStore = null!;
        _migrationManager = null!;
    }

    [Test]
    public void TestLoadClimbSaveFromNonExistentDirReturnsFileNotFound()
    {
        const string nonExistentDir = "user://nonexistent";
        MockGodotFileIo nonExistentMock = new(nonExistentDir);
        MigrationManager nonExistentMigrationManager = new(nonExistentMock);
        ClimbSaveManager climbSaveManager = new(nonExistentMock, nonExistentMigrationManager);

        ReadSaveResult<SerializableClimb> result = climbSaveManager.LoadClimbSave();

        Assert.That(climbSaveManager.HasClimbSave, Is.False);
        // ReadSaveResult returns error when file not found
        Assert.That(result.Success, Is.False);
        Assert.That(result.Status, Is.EqualTo(ReadSaveStatus.FileNotFound));
        Assert.That(result.SaveData, Is.Null);
    }

    [Test]
    public void TestLoadClimbSaveWithNoFileReturnsFileNotFound()
    {
        // Create a climb save manager with the test directory but no files
        ClimbSaveManager climbSaveManager = new(_mockSaveStore, _migrationManager);

        ReadSaveResult<SerializableClimb> result = climbSaveManager.LoadClimbSave();

        // No fixture has been loaded, so this should result in a "file not found"
        Assert.That(climbSaveManager.HasClimbSave, Is.False);
        // ReadSaveResult returns error when file not found
        Assert.That(result.Success, Is.False);
        Assert.That(result.Status, Is.EqualTo(ReadSaveStatus.FileNotFound));
        Assert.That(result.SaveData, Is.Null);
    }

    [Test]
    public void TestLoadClimbSaveWithMalformedJsonReturnsParseError()
    {
        ClimbSaveManager climbSaveManager = new(_mockSaveStore, _migrationManager);

        // Write malformed JSON directly to the save file
        const string badJson = "{x}";
        string climbPath = _mockSaveStore.GetFullPath(ClimbSaveManager.climbSaveFileName);
        _mockSaveStore.WriteFile(climbPath, badJson);

        // Verify that the climb save exists
        Assert.That(climbSaveManager.HasClimbSave, Is.True);

        ReadSaveResult<SerializableClimb> result = climbSaveManager.LoadClimbSave();

        // The file contains malformed JSON syntax that cannot be parsed.
        Assert.That(result.Success, Is.False);
        Assert.That(result.Status, Is.EqualTo(ReadSaveStatus.JsonParseError));
        Assert.That(result.SaveData, Is.Null);
    }

    [Test]
    public void TestLoadClimbSaveWithWrongSchemaReturnsMigrationFailed()
    {
        ClimbSaveManager climbSaveManager = new(_mockSaveStore, _migrationManager);

        // Valid JSON with wrong schema that doesn't map to ClimbSave
        const string invalidClimbJson = """
                                        {
                                          "wrong_field": "value",
                                          "another_field": 123,
                                          "schema_version": 0,
                                          "nested_object": {
                                            "invalid_property": true
                                          }
                                        }
                                        """;
        string climbPath = _mockSaveStore.GetFullPath(ClimbSaveManager.climbSaveFileName);
        _mockSaveStore.WriteFile(climbPath, invalidClimbJson);

        // Verify that the climb save exists
        Assert.That(climbSaveManager.HasClimbSave, Is.True);

        ReadSaveResult<SerializableClimb> result = climbSaveManager.LoadClimbSave();

        // The file contains valid JSON but with fields that don't map to the ClimbSave schema.
        // Since it has schema_version: 0, migration is attempted but fails during deserialization
        Assert.That(result.Success, Is.False);
        Assert.That(result.Status, Is.EqualTo(ReadSaveStatus.MigrationFailed));
        Assert.That(result.SaveData, Is.Null);

        // Since the save was recreated due to error, we shouldn't try to convert it to a Climb
        // as it would have null/default values that may cause exceptions
    }

    /// <summary>
    /// This tests what happens when a save is loaded that's mostly valid, but one of the serialized models contains
    /// an unmapped member (in this test's case, CardModel.IsRemovable).
    /// </summary>
    [Test]
    public void TestLoadClimbSaveWithUnmappedMemberReturnsMigrationFailed()
    {
        ClimbSaveManager climbSaveManager = new(_mockSaveStore, _migrationManager);
        SetUpFixture("unmapped_member_climb.save", ClimbSaveManager.climbSaveFileName);

        // Verify that the climb save exists
        Assert.That(climbSaveManager.HasClimbSave, Is.True);

        ReadSaveResult<SerializableClimb> result = climbSaveManager.LoadClimbSave();

        // The file contains JSON whose members map to ClimbSave correctly, but one of the members of one of the
        // nested card objects ("is_removable") does not map to any member in the CardModel save struct.
        // Since it has schema_version: 0, migration is attempted but fails during deserialization
        Assert.That(result.Success, Is.False);
        Assert.That(result.Status, Is.EqualTo(ReadSaveStatus.MigrationFailed));
        Assert.That(result.SaveData, Is.Null);

        // Since the save was recreated due to error, we shouldn't try to convert it to a Climb
    }

    [Test]
    public void TestLoadValidClimbSaveSucceedsWithMigration()
    {
        ClimbSaveManager climbSaveManager = new(_mockSaveStore, _migrationManager);
        SetUpFixture("valid_climb.save", ClimbSaveManager.climbSaveFileName);

        // Verify that we have a climb save
        Assert.That(climbSaveManager.HasClimbSave, Is.True);

        ReadSaveResult<SerializableClimb> result = climbSaveManager.LoadClimbSave();
        Assert.That(result.SaveData, Is.Not.Null);
        ClimbState climbState = ClimbState.FromSerializable(result.SaveData!);
        ClimbManager.Instance.SetUpSavedSinglePlayer(climbState, result.SaveData!);

        // Everything should be successful.
        Assert.That(result.Success, Is.True);
        Assert.That(result.Status, Is.EqualTo(ReadSaveStatus.MigrationRequired));
        Assert.That(ClimbManager.Instance, Is.Not.Null, result.SaveData?.ToString() ?? "null");
    }


    [Test]
    public void TestLoadValidProgressSaveReturnsProgressWithSchemaVersion()
    {
        ProgressSaveManager progressSaveManager = new(_mockSaveStore, _migrationManager);
        SetUpFixture("valid_progress.save", ProgressSaveManager.fileName);

        ProgressSave result = progressSaveManager.LoadProgress();

        // Check that we loaded the file successfully
        Assert.That(result, Is.Not.Null);
        Assert.That(result.SchemaVersion, Is.GreaterThan(0));
    }

    [Test]
    public void TestLoadValidSettingsSaveReturnsSettingsWithSchemaVersion()
    {
        SettingsSaveManager settingsSaveManager = new(_mockSaveStore, _migrationManager);
        SetUpFixture("valid_settings.save", SettingsSaveManager.settingsSaveFileName);

        SettingsSave result = settingsSaveManager.LoadSettings();

        // Check that we loaded the file successfully
        Assert.That(result, Is.Not.Null);
        Assert.That(result.SchemaVersion, Is.GreaterThan(0));
    }

    [Test]
    public void TestSaveOperationsCreateFilesInMock()
    {
        // Create save managers with mock store
        ProgressSaveManager progressSaveManager = new(_mockSaveStore, _migrationManager);
        SettingsSaveManager settingsSaveManager = new(_mockSaveStore, _migrationManager);

        // Mark an FTUE as complete using the progress manager
        progressSaveManager.MarkFtueAsComplete("test_ftue");

        // Verify file was created in mock
        string progressFilePath = _mockSaveStore.GetFullPath(ProgressSaveManager.fileName);
        Assert.That(_mockSaveStore.FileExists(progressFilePath), Is.True);

        // Verify the content
        string? progressContent = _mockSaveStore.ReadFile(progressFilePath);
        Assert.That(progressContent, Is.Not.Null);
        Assert.That(progressContent, Contains.Substring("test_ftue"));

        // Save settings using the settings manager
        // First load or create settings to ensure Settings property is initialized
        settingsSaveManager.LoadSettings();
        settingsSaveManager.SaveSettings();

        // Verify settings file was created in mock
        string settingsFilePath = _mockSaveStore.GetFullPath(SettingsSaveManager.settingsSaveFileName);
        Assert.That(_mockSaveStore.FileExists(settingsFilePath), Is.True);
    }

    private void SetUpFixture(string sourceFileName, string targetFileName)
    {
        string json = File.ReadAllText(Path.Combine(_fixturesDir.FullName, sourceFileName));
        string targetPath = _mockSaveStore.GetFullPath(targetFileName);
        _mockSaveStore.WriteFile(targetPath, json);
    }
}
