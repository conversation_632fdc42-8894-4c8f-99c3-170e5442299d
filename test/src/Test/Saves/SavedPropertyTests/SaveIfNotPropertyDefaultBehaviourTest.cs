using System.Text.Json;
using JetBrains.Annotations;
using MegaCrit.Sts2.Core.Saves.Climbs;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Saves.SavedPropertyTests;

[TestFixture]
public class SaveIfNotPropertyDefaultBehaviourTest
{
    private static readonly JsonSerializerOptions _serializerOptions = new() { IncludeFields = true };

    private enum TestEnum
    {
        ValueOne,
        ValueTwo,
        ValueThree
    }

    private class TestClass
    {
        [SavedProperty(SerializationCondition.SaveIfNotPropertyDefault)]
        public string? SaveIfNotNullString { get; init; }

        [SavedProperty(SerializationCondition.SaveIfNotPropertyDefault)]
        public string SaveIfNotTestValueString { get; init; } = "testValue";

        [SavedProperty(SerializationCondition.SaveIfNotPropertyDefault)]
        public int SaveIfNotZeroInt { get; init; }

        [SavedProperty(SerializationCondition.SaveIfNotPropertyDefault)]
        public int SaveIfNotElevenInt { get; init; } = 11;

        [SavedProperty(SerializationCondition.SaveIfNotPropertyDefault)]
        public TestEnum SaveIfNotValueTwoEnum { get; init; } = TestEnum.ValueTwo;

        [SavedProperty(SerializationCondition.SaveIfNotPropertyDefault)]
        public bool SaveIfNotTrueBool { get; init; } = true;

        [SavedProperty(SerializationCondition.SaveIfNotPropertyDefault)]
        public bool SaveIfNotFalseBool { get; init; }

        // [UsedImplicitly] because we check the serialized version.
        [SavedProperty(SerializationCondition.SaveIfNotPropertyDefault)]
        public string? DummyProperty { [UsedImplicitly] get; set; }
    }

    [SetUp]
    public void Setup()
    {
        SavedPropertiesTypeCache.InjectTypeIntoCache(typeof(TestClass));
    }

    [Test]
    public void TestNoPropertiesSetGeneratesNullSavedProperties()
    {
        TestClass test = new();
        SavedProperties? props = SavedProperties.FromInternal(test, null);

        Assert.That(props, Is.Null);
    }

    [Test]
    public void TestDoesNotSaveAnyDefaultProperties()
    {
        TestClass test = new()
        {
            DummyProperty = "dummy"
        };

        SavedProperties? props = SavedProperties.FromInternal(test, null);
        string json = JsonSerializer.Serialize(props, _serializerOptions);

        Assert.That(props, Is.Not.Null);
        Assert.That(json, Does.Not.Contain(nameof(TestClass.SaveIfNotNullString)));
        Assert.That(json, Does.Not.Contain(nameof(TestClass.SaveIfNotTestValueString)));
        Assert.That(json, Does.Not.Contain(nameof(TestClass.SaveIfNotZeroInt)));
        Assert.That(json, Does.Not.Contain(nameof(TestClass.SaveIfNotElevenInt)));
        Assert.That(json, Does.Not.Contain(nameof(TestClass.SaveIfNotValueTwoEnum)));
        Assert.That(json, Does.Not.Contain(nameof(TestClass.SaveIfNotTrueBool)));
        Assert.That(json, Does.Not.Contain(nameof(TestClass.SaveIfNotFalseBool)));
    }

    [Test]
    public void TestDeserializesDefaultValuesCorrectly()
    {
        TestClass test = new()
        {
            DummyProperty = "dummy"
        };

        SavedProperties? props = SavedProperties.FromInternal(test, null);
        string json = JsonSerializer.Serialize(props, _serializerOptions);

        SavedProperties deserializedProps = JsonSerializer.Deserialize<SavedProperties>(json, _serializerOptions)!;
        TestClass deserialized = new();
        deserializedProps.FillInternal(deserialized);

        Assert.That(deserialized.SaveIfNotNullString, Is.Null);
        Assert.That(deserialized.SaveIfNotTestValueString, Is.EqualTo("testValue"));
        Assert.That(deserialized.SaveIfNotZeroInt, Is.EqualTo(0));
        Assert.That(deserialized.SaveIfNotElevenInt, Is.EqualTo(11));
        Assert.That(deserialized.SaveIfNotValueTwoEnum, Is.EqualTo(TestEnum.ValueTwo));
        Assert.That(deserialized.SaveIfNotTrueBool, Is.True);
        Assert.That(deserialized.SaveIfNotFalseBool, Is.False);
    }

    [Test]
    public void TestSavesNonDefaultProperties()
    {
        TestClass test = new()
        {
            SaveIfNotNullString = "testValue",
            SaveIfNotTestValueString = "otherTestValue",
            SaveIfNotZeroInt = 123,
            SaveIfNotElevenInt = 1,
            SaveIfNotValueTwoEnum = TestEnum.ValueThree,
            SaveIfNotTrueBool = false,
            SaveIfNotFalseBool = true
        };

        SavedProperties? props = SavedProperties.FromInternal(test, null);
        string json = JsonSerializer.Serialize(props, _serializerOptions);

        Assert.That(props, Is.Not.Null);
        Assert.That(json, Does.Contain(nameof(TestClass.SaveIfNotNullString)));
        Assert.That(json, Does.Contain(nameof(TestClass.SaveIfNotTestValueString)));
        Assert.That(json, Does.Contain(nameof(TestClass.SaveIfNotZeroInt)));
        Assert.That(json, Does.Contain(nameof(TestClass.SaveIfNotElevenInt)));
        Assert.That(json, Does.Contain(nameof(TestClass.SaveIfNotValueTwoEnum)));
        Assert.That(json, Does.Contain(nameof(TestClass.SaveIfNotTrueBool)));
        Assert.That(json, Does.Contain(nameof(TestClass.SaveIfNotFalseBool)));
    }

    [Test]
    public void TestDeserializesNonDefaultPropertiesCorrectly()
    {
        TestClass test = new()
        {
            SaveIfNotNullString = "testValue",
            SaveIfNotTestValueString = "otherTestValue",
            SaveIfNotZeroInt = 123,
            SaveIfNotElevenInt = 1,
            SaveIfNotValueTwoEnum = TestEnum.ValueThree,
            SaveIfNotTrueBool = false,
            SaveIfNotFalseBool = true
        };

        SavedProperties? props = SavedProperties.FromInternal(test, null);
        string json = JsonSerializer.Serialize(props, _serializerOptions);

        SavedProperties deserializedProps = JsonSerializer.Deserialize<SavedProperties>(json, _serializerOptions)!;
        TestClass deserialized = new();
        deserializedProps.FillInternal(deserialized);

        Assert.That(deserialized.SaveIfNotNullString, Is.EqualTo("testValue"));
        Assert.That(deserialized.SaveIfNotTestValueString, Is.EqualTo("otherTestValue"));
        Assert.That(deserialized.SaveIfNotZeroInt, Is.EqualTo(123));
        Assert.That(deserialized.SaveIfNotElevenInt, Is.EqualTo(1));
        Assert.That(deserialized.SaveIfNotValueTwoEnum, Is.EqualTo(TestEnum.ValueThree));
        Assert.That(deserialized.SaveIfNotTrueBool, Is.EqualTo(false));
        Assert.That(deserialized.SaveIfNotFalseBool, Is.EqualTo(true));
    }
}
