using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text.Json.Serialization;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Saves;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Saves;

/// <summary>
/// This test suite detects breaking changes in save file schemas.
/// If any of these tests fail, it means a developer has made a change that could break save file compatibility.
/// To fix a failure:
/// 1. Create a new migration to handle the schema change
/// 2. Update the schema snapshot in this test to match the new schema
/// 3. Ensure old save files can still be loaded through the migration system
/// </summary>
[TestFixture]
public class SchemaBreakingChangeDetectionTest
{
    /// <summary>
    /// Schema snapshots for each save type. These represent the expected serializable properties.
    /// When properties are added/removed/changed, this test will fail to alert developers.
    /// </summary>
    private static readonly Dictionary<Type, HashSet<string>> _criticalPropertySnapshots = new()
    {
        // Critical properties that must not be removed or renamed without migration
        [typeof(SerializableClimb)] = new HashSet<string>
        {
            "schema_version", "acts", "modifiers", "dailyTime", "current_act_index",
            "events_seen", "pre_finished_room", "odds", "shared_relic_grab_bag",
            "players", "rng", "visited_map_coords", "map_point_history",
            "save_time", "start_time", "climb_time", "ascension", "spawned_neow",
            "platform_type", "map_drawings", "extra_fields"
        },

        [typeof(ProgressSave)] = new HashSet<string>
        {
            "schema_version", "unique_id", "character_stats", "card_stats",
            "encounter_stats", "enemy_stats", "enable_ftues", "epochs",
            "ftue_completed", "achievement_metadata", "discovered_cards", "discovered_relics",
            "discovered_events", "discovered_potions", "total_playtime", "floors_climbed",
            "wongo_points", "preferred_multiplayer_ascension", "max_multiplayer_ascension",
            "test_subject_kills"
        },

        [typeof(SettingsSave)] = new HashSet<string>
        {
            "schema_version", "fps_limit", "language", "window_position", "window_size",
            "resize_windows", "vsync", "msaa", "volume_bgm", "volume_master",
            "volume_sfx", "volume_ambience", "fullscreen", "aspect_ratio",
            "target_display", "fast_mode", "screenshake", "show_run_timer",
            "show_card_indices", "upload_data", "mute_in_background", "mod_settings"
        },

        [typeof(ClimbHistory)] = new HashSet<string>
        {
            "schema_version", "build_id", "platform_type", "game_mode", "win",
            "seed", "players", "map_point_history", "start_time", "climb_time",
            "ascension", "modifiers", "acts", "was_abandoned", "killed_by_encounter",
            "killed_by_event"
        }
    };

    [Test]
    [TestCase(typeof(SerializableClimb))]
    [TestCase(typeof(ProgressSave))]
    [TestCase(typeof(SettingsSave))]
    [TestCase(typeof(ClimbHistory))]
    public void TestSaveSchemaDetectsBreakingChanges(Type saveType)
    {
        // Get the expected critical properties
        HashSet<string> expectedProperties = _criticalPropertySnapshots[saveType];

        // Get the actual schema from reflection
        HashSet<string> actualProperties = ExtractJsonPropertyNames(saveType);

        // Check for removed critical properties (breaking change)
        List<string> removedProperties = expectedProperties.Except(actualProperties).ToList();
        if (removedProperties.Any())
        {
            Assert.Fail($"BREAKING CHANGE DETECTED in {saveType.Name}!\n\n" +
                $"The following critical properties were removed or renamed without a migration:\n" +
                string.Join("\n", removedProperties.Select(p => $"  - {p}")) +
                "\n\nTo fix this:\n" +
                "1. If renamed: Create a migration to handle the old property name\n" +
                "2. If removed: Create a migration to handle saves with this property\n" +
                "3. Update the schema snapshot in this test after adding the migration");
        }

        // Check for new properties (informational only)
        List<string> newProperties = actualProperties.Except(expectedProperties).ToList();
        if (newProperties.Any())
        {
            TestContext.WriteLine($"New properties detected in {saveType.Name} (not a breaking change):\n" +
                string.Join("\n", newProperties.Select(p => $"  - {p}")) +
                "\n\nConsider updating the schema snapshot if these are critical properties.");
        }
    }

    /// <summary>
    /// Extracts JSON property names from a save type using reflection
    /// </summary>
    private static HashSet<string> ExtractJsonPropertyNames(Type saveType)
    {
        HashSet<string> jsonNames = new();

        // Get all public properties
        PropertyInfo[] properties = saveType.GetProperties(BindingFlags.Public | BindingFlags.Instance);

        foreach (PropertyInfo property in properties)
        {
            // Skip properties marked with JsonIgnore
            if (property.GetCustomAttribute<JsonIgnoreAttribute>() != null)
                continue;

            // Get JSON property name
            JsonPropertyNameAttribute? jsonNameAttr = property.GetCustomAttribute<JsonPropertyNameAttribute>();
            if (jsonNameAttr?.Name != null)
            {
                jsonNames.Add(jsonNameAttr.Name);
            }
        }

        return jsonNames;
    }

    [Test]
    public void TestAllSaveTypesHaveSchemaVersionProperty()
    {
        Type[] saveTypes = { typeof(SerializableClimb), typeof(ProgressSave), typeof(SettingsSave), typeof(ClimbHistory) };

        foreach (Type saveType in saveTypes)
        {
            PropertyInfo? schemaVersionProperty = saveType.GetProperty("SchemaVersion");
            Assert.That(schemaVersionProperty, Is.Not.Null,
                $"{saveType.Name} must have a SchemaVersion property");
            Assert.That(schemaVersionProperty!.PropertyType, Is.EqualTo(typeof(int)),
                $"{saveType.Name}.SchemaVersion must be of type int");

            // Verify it has the correct JSON name
            JsonPropertyNameAttribute? jsonNameAttr = schemaVersionProperty.GetCustomAttribute<JsonPropertyNameAttribute>();
            Assert.That(jsonNameAttr?.Name, Is.EqualTo("schema_version"),
                $"{saveType.Name}.SchemaVersion must have JsonPropertyName 'schema_version'");
        }
    }

    [Test]
    public void TestAllSaveTypesImplementISaveSchemaInterface()
    {
        Type[] saveTypes = { typeof(SerializableClimb), typeof(ProgressSave), typeof(SettingsSave), typeof(ClimbHistory) };

        foreach (Type saveType in saveTypes)
        {
            Assert.That(typeof(ISaveSchema).IsAssignableFrom(saveType), Is.True,
                $"{saveType.Name} must implement ISaveSchema interface");
        }
    }

    [Test]
    public void TestGenerateSchemaReportOutputsAllProperties()
    {
        // This test generates a report of all properties for each save type
        // Useful for documenting the current schema
        Type[] saveTypes = { typeof(SerializableClimb), typeof(ProgressSave), typeof(SettingsSave), typeof(ClimbHistory) };

        foreach (Type saveType in saveTypes)
        {
            TestContext.WriteLine($"\n=== {saveType.Name} Schema ===");

            PropertyInfo[] properties = saveType.GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .Where(p => p.GetCustomAttribute<JsonIgnoreAttribute>() == null)
                .OrderBy(p => p.Name)
                .ToArray();

            foreach (PropertyInfo property in properties)
            {
                JsonPropertyNameAttribute? jsonNameAttr = property.GetCustomAttribute<JsonPropertyNameAttribute>();
                string jsonName = jsonNameAttr?.Name ?? "(no JSON name)";
                TestContext.WriteLine($"  {property.Name,-30} : {property.PropertyType.Name,-20} -> {jsonName}");
            }
        }
    }
}
