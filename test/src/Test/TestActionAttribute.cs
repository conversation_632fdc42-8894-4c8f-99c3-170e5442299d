using System;
using NUnit.Framework;
using NUnit.Framework.Interfaces;

namespace MegaCrit.Sts2.Test;

[AttributeUsage(AttributeTargets.Assembly)]
public class TestActionAttribute : Attribute, ITestAction
{
    public ActionTargets Targets => ActionTargets.Test | ActionTargets.Suite;

    public void BeforeTest(ITest test)
    {
        Log("Running", test);
    }

    public void AfterTest(ITest test)
    {
        Log("Finished", test);
    }

    private void Log(string eventMessage, ITest details)
    {
        string testType = details.IsSuite ? "suite" : "test";
        string fixture = details.Fixture != null ? details.Fixture.GetType().Name : "{no fixture}";
        string method = details.Method != null ? details.Method.Name : "{no method}";
        Core.Logging.Log.Info($"{eventMessage} {testType}: {fixture} {method}");
    }
}
