using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Powers.Mocks;
using MegaCrit.Sts2.Core.Random;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Unit;

/// <summary>
/// See https://linear.app/megacrit/issue/PRG-1013/new-energy-cost-color-rules#comment-2bce4a60 for the origin of these
/// rules.
/// </summary>
public class EnergyCostColorTest
{
    /// <summary>
    /// If a card's cost is modified for the remainder of the combat (eg Touch of Insanity, Transfigure):
    /// If the card's cost is modified to be higher than its base cost, color the cost BLUE.
    /// </summary>
    [Test]
    public void TestHigherForCombat()
    {
        CombatState state = new();
        Player player = MockPlayer(state);

        MockCardModel card = MockCard(player, state);
        card.SetEnergyCostThisCombat(card.BaseEnergyCost + 1);

        Assert.That(EnergyHelper.GetCostColor(card, state), Is.EqualTo(EnergyCostColor.Increased));
    }

    /// <summary>
    /// If a card's cost is modified for the remainder of the combat (eg Touch of Insanity, Transfigure):
    /// If the card's cost is the same or lower than its base, render the cost WHITE. *There may be weird edge cases here still!
    /// </summary>
    [Test]
    public void TestSameForCombat()
    {
        CombatState state = new();
        Player player = MockPlayer(state);

        MockCardModel card = MockCard(player, state);
        card.SetEnergyCostThisCombat(card.BaseEnergyCost);

        Assert.That(EnergyHelper.GetCostColor(card, state), Is.EqualTo(EnergyCostColor.Unmodified));
    }

    /// <summary>
    /// If a card's cost is modified for the remainder of the combat (eg Touch of Insanity, Transfigure):
    /// If the card's cost is the same or lower than its base, render the cost WHITE. *There may be weird edge cases here still!
    /// </summary>
    [Test]
    public void TestLowerForCombat()
    {
        CombatState state = new();
        Player player = MockPlayer(state);

        MockCardModel card = MockCard(player, state);
        card.SetEnergyCostThisCombat(card.BaseEnergyCost - 1);

        Assert.That(EnergyHelper.GetCostColor(card, state), Is.EqualTo(EnergyCostColor.Unmodified));
    }

    /// <summary>
    /// If a card's cost is modified for the turn:
    /// And the cost of the card is above its base cost, the color should be BLUE.
    /// </summary>
    [Test]
    public void TestHigherForTurn()
    {
        CombatState state = new();
        Player player = MockPlayer(state);

        MockCardModel card = MockCard(player, state);
        card.SetEnergyCostThisTurn(card.BaseEnergyCost + 1);

        Assert.That(EnergyHelper.GetCostColor(card, state), Is.EqualTo(EnergyCostColor.Increased));
    }

    /// <summary>
    /// If a card's cost is modified for the turn:
    /// If the cost of the card is above its base cost, but it's an X-cost card, the color should be WHITE.
    /// </summary>
    [Test]
    public void TestHigherForTurnOnXCostCard()
    {
        CombatState state = new();
        Player player = MockPlayer(state);

        MockCardModel card = MockCard(player, state).MockEnergyCostX();
        card.SetEnergyCostThisTurn(card.BaseEnergyCost + 1);

        Assert.That(EnergyHelper.GetCostColor(card, state), Is.EqualTo(EnergyCostColor.Unmodified));
    }

    /// <summary>
    /// If a card's cost is modified for the turn:
    /// If the cost of the card is BELOW or THE SAME as its base cost, the color should be GREEN.
    /// </summary>
    [Test]
    public void TestSameForTurn()
    {
        CombatState state = new();
        Player player = MockPlayer(state);

        MockCardModel card = MockCard(player, state);
        card.SetEnergyCostThisTurn(card.BaseEnergyCost);

        Assert.That(EnergyHelper.GetCostColor(card, state), Is.EqualTo(EnergyCostColor.Decreased));
    }

    /// <summary>
    /// If a card's cost is modified for the turn:
    /// If the cost of the card is BELOW or THE SAME as its base cost, the color should be GREEN.
    /// </summary>
    [Test]
    public void TestLowerForTurn()
    {
        CombatState state = new();
        Player player = MockPlayer(state);

        MockCardModel card = MockCard(player, state);
        card.SetEnergyCostThisTurn(card.BaseEnergyCost - 1);

        Assert.That(EnergyHelper.GetCostColor(card, state), Is.EqualTo(EnergyCostColor.Decreased));
    }

    /// <summary>
    /// If a card's cost is modified for the turn:
    /// If the cost of the card is BELOW or THE SAME as its base cost, but it's an X-cost card, the color should be
    /// WHITE.
    /// </summary>
    [Test]
    public void TestLowerForTurnOnXCostCard()
    {
        CombatState state = new();
        Player player = MockPlayer(state);

        MockCardModel card = MockCard(player, state).MockEnergyCostX();
        card.SetEnergyCostThisTurn(card.BaseEnergyCost - 1);

        Assert.That(EnergyHelper.GetCostColor(card, state), Is.EqualTo(EnergyCostColor.Unmodified));
    }

    /// <summary>
    /// If a card's cost is modified temporarily due to Powers (eg Unrelenting):
    /// And the cost of the card is above its base cost, the color should be BLUE.
    /// </summary>
    [Test]
    public void TestHigherFromPower()
    {
        CombatState state = new();
        Player player = MockPlayer(state);

        MockCardModel card = MockCard(player, state);
        PowerModel power = ModelDb.Power<MockModifyEnergyCostPower>().ToMutable();
        power.ApplyInternal(player.Creature, 1);

        Assert.That(EnergyHelper.GetCostColor(card, state), Is.EqualTo(EnergyCostColor.Increased));
    }

    /// <summary>
    /// If a card's cost is modified temporarily due to Powers (eg Unrelenting):
    /// If the cost of the card is BELOW or THE SAME as its base cost, the color should be GREEN.
    /// </summary>
    [Test]
    public void TestSameFromPower()
    {
        CombatState state = new();
        Player player = MockPlayer(state);

        MockCardModel card = MockCard(player, state).MockEnergyCost(0);
        PowerModel power = ModelDb.Power<MockFreeCardsPower>().ToMutable();
        power.ApplyInternal(player.Creature, 1);

        Assert.That(EnergyHelper.GetCostColor(card, state), Is.EqualTo(EnergyCostColor.Decreased));
    }

    /// <summary>
    /// If a card's cost is modified temporarily due to Powers (eg Unrelenting):
    /// If the cost of the card is BELOW or THE SAME as its base cost, the color should be GREEN.
    /// </summary>
    [Test]
    public void TestLowerFromPower()
    {
        CombatState state = new();
        Player player = MockPlayer(state);

        MockCardModel card = MockCard(player, state);

        PowerModel power = ModelDb.Power<MockModifyEnergyCostPower>().ToMutable();
        power.ApplyInternal(player.Creature, -1);

        Assert.That(EnergyHelper.GetCostColor(card, state), Is.EqualTo(EnergyCostColor.Decreased));
    }

    /// <summary>
    /// If a card's cost is raised for the turn AND temporarily lowered due to Powers:
    /// The color should be GREEN, even if the final cost is still higher than the original cost, because the power
    /// modification rule takes precedence over the "raised for the turn" rule.
    /// </summary>
    [Test]
    public void TestPowerModifyWinsOverTurnModify()
    {
        CombatState state = new();
        Player player = MockPlayer(state);

        MockCardModel card = MockCard(player, state);
        card.SetEnergyCostThisTurn(5);

        PowerModel power = ModelDb.Power<MockModifyEnergyCostPower>().ToMutable();
        power.ApplyInternal(player.Creature, -1);

        Assert.That(EnergyHelper.GetCostColor(card, state), Is.EqualTo(EnergyCostColor.Decreased));
    }

    /// <summary>
    /// If the player does not have energy to play the card (checked last):
    /// The cost is rendered RED.
    /// </summary>
    [Test]
    public void TestNotEnoughEnergy()
    {
        CombatState state = new();
        Player player = MockPlayer(state);
        player.PlayerCombatState!.Energy = 0;

        MockCardModel card = MockCard(player, state);

        Assert.That(EnergyHelper.GetCostColor(card, state), Is.EqualTo(EnergyCostColor.Unplayable));
    }

    /// <summary>
    /// If a card cannot be played due to a power, affliction, etc (checked last):
    /// The cost is rendered RED + the red line is drawn through it (line is handled elsewhere).
    /// </summary>
    [Test]
    public void TestUnplayable()
    {
        CombatState state = new();
        Player player = MockPlayer(state);
        MockCardModel card = MockCard(player, state).MockKeyword(CardKeyword.Unplayable);

        Assert.That(EnergyHelper.GetCostColor(card, state), Is.EqualTo(EnergyCostColor.Unplayable));
    }

    private static MockCardModel MockCard(Player owner, CombatState state)
    {
        return state.CreateCard<MockSkillCard>(owner);
    }

    private static Player MockPlayer(CombatState state)
    {
        Player player = Player.CreateForNewClimb<Deprived>(0);
        state.AddPlayer(player);
        player.ResetCombatState();
        player.PopulateCombatState(Rng.Chaotic, state);
        player.PlayerCombatState!.Energy = Really.bigNumber;

        return player;
    }
}
