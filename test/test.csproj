<Project Sdk="Godot.NET.Sdk/4.4.1">

    <PropertyGroup>
        <AssemblyName>test</AssemblyName>
        <TargetFramework>net8.0</TargetFramework>
        <EnableDynamicLoading>true</EnableDynamicLoading>
        <LangVersion>12</LangVersion>
        <Nullable>enable</Nullable>
        <RootNamespace>MegaCrit.Sts2</RootNamespace>
        <GodotUseNETFrameworkRefAssemblies>true</GodotUseNETFrameworkRefAssemblies>
        <DefineConstants>$(DefineConstants);DISABLE_STEAMWORKS</DefineConstants>
        <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
        <!-- <GenerateAssemblyInfo>false</GenerateAssemblyInfo> -->
        <RestorePackagesWithLockFile>true</RestorePackagesWithLockFile>
    </PropertyGroup>

    <PropertyGroup>
        <OutputPath>.mono/tests/bin/$(Configuration)</OutputPath>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="GodotTestDriver" Version="2.1.0"/>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0"/>
        <PackageReference Include="NUnit" Version="3.14.0"/>
        <PackageReference Include="NUnit.Engine" Version="3.16.3"/>
        <!-- <PackageReference Include="NUnit.Analyzers" Version="3.9.0"/>
        <PackageReference Include="coverlet.collector" Version="6.0.0"/> -->

    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\sts2\sts2.csproj"/>
    </ItemGroup>

    <ItemGroup>
      <Content Include="src\Test\Saves\Fixtures\bad_json.save" />
      <Content Include="src\Test\Saves\Fixtures\unmapped_member_climb.save" />
      <Content Include="src\Test\Saves\Fixtures\valid_progress.save" />
      <Content Include="src\Test\Saves\Fixtures\valid_climb.save" />
      <Content Include="src\Test\Saves\Fixtures\valid_settings.save" />
    </ItemGroup>
</Project>
